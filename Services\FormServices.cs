﻿using DocumentFormat.OpenXml.Spreadsheet;
using EWA.Models;
using FastReport.Format;
using Microsoft.CodeAnalysis;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Globalization;
using static EWA.Models.REP_Models;
using static EWA.Services.UIService;

namespace EWA.Services
{
    public class FormServices
    {
        #region [Валидация форм]

        private static Dictionary<string, string> ValidatorColor = new Dictionary<string, string> { { "valid", "--rz-info" },
                                                                                                    { "invalid", "--rz-danger" }};

        private static Dictionary<string, string> ValidatorMessage = new Dictionary<string, string> { { "valid", "*поле обязательно для заполнения" },
                                                                                                      { "invalid", " - поле обязательно для заполнения" }};

        #region [Параметры]
        public void InitValidatorColor(IEnumerable<REP_Models.baseColParam> metadata, Dictionary<string, EWA.Models.REP_Models.Rep_Validator> validator)
        {
            foreach (var meta in metadata.Where(x => x.ISMANDATORY == 1 ))
            {
                if (meta is REP_Models.ColumnMetadata)
                {
                    if (((REP_Models.ColumnMetadata)meta).VIEWVISIBLE != 1)
                    {
                        continue;
                    }
                }
                EWA.Models.REP_Models.Rep_Validator vv = new() { color = ValidatorColor["valid"], message = ValidatorMessage["valid"] };
                validator.Add(meta.CODE, vv);
            }
        }

        public bool CheckValidator(IEnumerable<REP_Models.baseColParam> metadata, Dictionary<string, EWA.Models.REP_Models.Rep_Validator> validator,
                                   Dictionary<string, object> formValues)
        {
            bool isValidator = true;
            foreach (var meta in metadata.Where(x => x.ISMANDATORY == 1 ))
            {
                if (meta is REP_Models.ColumnMetadata)
                {
                    if (((REP_Models.ColumnMetadata)meta).VIEWVISIBLE != 1)
                    {
                        continue;
                    }
                }
                if (formValues[meta.CODE] == null || formValues[meta.CODE] == DBNull.Value || string.IsNullOrEmpty(formValues[meta.CODE]?.ToString()))
                {
                    validator[meta.CODE].color = ValidatorColor["invalid"];
                    validator[meta.CODE].message = meta.NAME + ValidatorMessage["invalid"];
                    isValidator = false;

                }
                else
                {
                    validator[meta.CODE].color = ValidatorColor["valid"];
                    validator[meta.CODE].message = ValidatorMessage["valid"];

                }
            }

            return isValidator;
        }
        #endregion       
        #endregion

     


        #region [Get Set Value]


        public string GetValue(string key, Dictionary<string, object> _formValues)
        {
            return _formValues.TryGetValue(key, out var value) && value is string str ? str : string.Empty;
        }

        public void SetValue(string key, string value, Dictionary<string, object> _formValues, IEnumerable<REP_Models.baseColParam> _metadata)
        {
            if (_formValues.ContainsKey(key))
            {
                _formValues[key] = string.IsNullOrWhiteSpace(value) ? null : value;
            } 
            else
            {
                _formValues.Add(key, string.IsNullOrWhiteSpace(value) ? null : value);
            }

            foreach (var prm in _metadata.Where(x=>x.Parent==key))
            {
                _formValues[prm.CODE] = string.IsNullOrWhiteSpace(value) ? null : value;
            }

        }
        public void SetValueNum(string key, decimal? value, Dictionary<string, object> _formValues)
        {
            _formValues[key] = value;
        }
/*
        public void SetValueNum(string key, object value, Dictionary<string, object> _formValues, IEnumerable<REP_Models.baseColParam> _metadata)
        {
            decimal result = 0;
            bool fl = true;
            try
            {
               result = Convert.ToDecimal(value);
                
            }
            catch (Exception ex)
            {
                fl = false;
                if (_formValues.ContainsKey("STRING_VALUE"))
                {
                    _formValues["STRING_VALUE"] = value.ToString();
                }
                else
                {
                    _formValues.Add("STRING_VALUE", value.ToString());
                }
                StateHasChanged();
                return;
            }

           
                if (_formValues.ContainsKey(key))
                {
                    _formValues[key] = value;
                }
                else
                {
                    _formValues.Add(key, value);
                }
            
            var customFormat = new NumberFormatInfo
            {
                NumberDecimalSeparator = ","
            };

            foreach (var prm in _metadata.Where(x => x.Parent == key ) )
            {
                if (fl)
                {
                    customFormat.NumberDecimalSeparator = prm.DecimalSeparator;
                    _formValues[prm.CODE] = result.ToString(prm.Pattern, customFormat);
                }
                else
                {
                    _formValues[prm.CODE] = null;
                }
            }

        }
*/
        public void SetValueLong(string key, long? value, Dictionary<string, object> _formValues)
        {
            _formValues[key] = value;
        }

        public void SetValueLong(string key, object value, Dictionary<string, object> _formValues, IEnumerable<REP_Models.baseColParam> _metadata)
        {
            long result = 0;
            bool fl = false;
             try
                {
                    result = Convert.ToInt64(value);
                    fl=true;

                }
                catch (Exception ex)
                {
                    fl = false;
                }
          
            if (_formValues.ContainsKey(key))
            {
                _formValues[key] = value;
            }
            else
            {
                _formValues.Add(key, value);
            }

            var customFormat = new NumberFormatInfo
            {
                NumberDecimalSeparator = ","
            };

            foreach (var prm in _metadata.Where(x => x.Parent == key))
            {
                if (fl)
                {
                    customFormat.NumberDecimalSeparator = prm.DecimalSeparator;
                    _formValues[prm.CODE] = result.ToString(prm.Pattern, customFormat);
                }
                else
                {
                    _formValues[prm.CODE] = null;
                }
            }

        }

        public void SetValue(string key, string value, Dictionary<string, object> _formValues)
        {
            if (_formValues.ContainsKey(key))
            {
                _formValues[key] = string.IsNullOrWhiteSpace(value) ? null : value;
            }
            else
            {
                _formValues.Add(key, string.IsNullOrWhiteSpace(value) ? null : value);
            } 
            


        }
        public bool? GetValueBool(string key, Dictionary<string, object> _formValues)
        {
            if (!_formValues.ContainsKey(key))
                return null;
            if (_formValues[key] == DBNull.Value || _formValues[key] == null)
                return null;
            bool rez = false;
            rez = _formValues[key].ToString() == "1";
            return rez;
        }

        public void SetValueBool(string key, bool? value, Dictionary<string, object> _formValues)
        {
            decimal? decimalValue = null;
            if (value!=null)
            {
                decimalValue = value==true ? 1 : 0;
            }
            _formValues[key] = decimalValue;
        }
        public decimal? GetValueNum(string key, Dictionary<string, object> _formValues)
        {
            if (!_formValues.ContainsKey(key))
                return null;
            if (_formValues[key] == DBNull.Value || _formValues[key] == null)
                return null;

          

            try
            {
                return Convert.ToDecimal(_formValues[key]);

            }
            catch (Exception ex)
            {
                return null;
            }

            
           
        }
        public long? GetValueLong(string key, Dictionary<string, object> _formValues)
        {
            if (!_formValues.ContainsKey(key))
                return null;
            if (_formValues[key] == DBNull.Value || _formValues[key] == null)
                return null;

            long result = 0;
           
            try
            {
                result = Convert.ToInt64(_formValues[key]);

            }
            catch (Exception ex)
            {
                return null;
            }

            return result;

        }




        public DateTime? GetValueDT(string key, Dictionary<string, object> _formValues)
        {
            if(_formValues.ContainsKey(key) )
            {
                if (_formValues[key] == null)
                    return null;
                DateTime dt;
                /*if (DateTime.TryParseExact(_formValues[key].ToString(),
                                            "dd.MM.yyyy",
                                            CultureInfo.InvariantCulture,
                                            DateTimeStyles.None,
                    out dt)) */ //формат входной даты дата + время - возвращает false
                if (_formValues.TryGetValue(key, out var valueDT) && valueDT is DateTime date)

                {
                    return date;
                }
                else
                if (DateTime.TryParseExact(_formValues[key].ToString(),
                                            "dd.MM.yyyy",
                                            CultureInfo.InvariantCulture,
                                            DateTimeStyles.None,
                    out dt))
                {
                    return dt;
                }    
                
                return null;

                
            }
            return null;
           
        }

      


        public void SetValueDT(string key, DateTime valueDT, Dictionary<string, object> _formValues)
        {
            if (_formValues.ContainsKey(key))
            {
                _formValues[key] = valueDT;
            }
            else
            {
                _formValues.Add(key, valueDT);
            }
        }

        private readonly string[] dateformats = { "dd.MM.yyyy H:mm:ss", "dd.MM.yyyy HH:mm:ss", "dd/MM/yyyy", "MM/dd/yyyy", "yyyy-MM-dd", "MM.dd.yy", "dd.MM.yyyy" };

        private static bool TryParseDate(string input, out DateTime dateResult, string[] dateformats)
        {
            return DateTime.TryParseExact(
                input,
                dateformats,
                CultureInfo.InvariantCulture,
                DateTimeStyles.None,
                out dateResult
            );
        }
        public void SetValueDT(string key, object valueDT, Dictionary<string, object> _formValues, IEnumerable<REP_Models.baseColParam> _metadata)
        {
            // Обновляем основное значение в словаре
            if (_formValues.ContainsKey(key))
            {
                _formValues[key] = valueDT;
            }
            else
            {
                _formValues.Add(key, valueDT);
            }

            // Получаем набор дочерних параметров
            var childParams = _metadata.Where(x => x.Parent == key).ToList();

            // Если значение пустое, сбрасываем значения всех дочерних параметров
            if (valueDT == null)
            {
                foreach (var prm in childParams)
                {
                    _formValues[prm.CODE] = null;
                }
            }
            // Если значение уже DateTime, преобразуем сразу
            else if (valueDT is DateTime date)
            {
                foreach (var prm in childParams)
                {
                    _formValues[prm.CODE] = date.ToString(prm.Pattern);
                }
            }
            // Иначе пробуем распарсить как строку
            else
            {
                if (TryParseDate(valueDT.ToString(), out DateTime result, dateformats))
                {
                    foreach (var prm in childParams)
                    {
                        _formValues[prm.CODE] = result.ToString(prm.Pattern);
                    }
                }

                
                else
                {
                    foreach (var prm in childParams)
                    {
                        _formValues[prm.CODE] = null;
                    }
                }
            }
        }
 /*      
        public void SetValueDT(string key, object valueDT, Dictionary<string, object> _formValues, IEnumerable<REP_Models.baseColParam> _metadata)
        {
            if (_formValues.ContainsKey(key))
            {
                _formValues[key] = valueDT;
            }
            else
            {
                _formValues.Add(key, valueDT);
            }

            DateTime val ;
            if (valueDT==null)
            {
                foreach (var prm in _metadata.Where(x => x.Parent == key))
                {

                    _formValues[prm.CODE] = null;

                }

            }
            else if (valueDT is DateTime date )

            {
                foreach (var prm in _metadata.Where(x => x.Parent == key))
                {
                    
                        _formValues[prm.CODE] = date.ToString(prm.Pattern);
                   
                }
            }
            
            else
                if (DateTime.TryParseExact(valueDT.ToString(),
                                            "dd.MM.yyyy",
                                            CultureInfo.InvariantCulture,
                                            DateTimeStyles.None,
                    out val) && valueDT != null)
            {
                foreach (var prm in _metadata.Where(x => x.Parent == key))
                {

                    _formValues[prm.CODE] = val.ToString(prm.Pattern);
                    
                }
            }            
            
        }
*/
        public void SetValueDT(string key, object valueDT, Dictionary<string, object> _formValues)
        {
            if (_formValues.ContainsKey(key))
            {
                _formValues[key] = valueDT;
            }
            else
            {
                _formValues.Add(key, valueDT);
            }
            
        }
        public void SetValueDT_(string key, DateTime valueDT, Dictionary<string, object> _formValues, IEnumerable<REP_Models.baseColParam> _metadata)
        {
            string formattedDate = valueDT.ToString("dd.MM.yyyy HH:mm:ss");
            if (_formValues.ContainsKey(key))
            {
                _formValues[key] = formattedDate;
            }
            else
            {
                _formValues.Add(key, formattedDate);
            }
            foreach (var prm in _metadata.Where(x => x.Parent == key))
            {

                _formValues[prm.CODE] = valueDT.ToString(prm.RegExPattern);

            }
        }
        #endregion

        #region [Form Type]

        public string GetColumnPropertyExpression(string name, Type type)
        {
            var expression = $@"it[""{name}""].ToString()";

            if (type == typeof(int))
            {
                return $"int.Parse({expression})";
            }
            else if (type == typeof(DateTime))
            {
                return $"DateTime.Parse({expression})";
            }
            else if (type.IsEnum)
            {
                return $@"Int32(Enum.Parse(it[""{name}""].GetType(), {expression}))";
            }

            return expression;
        }

        public Type GetColumnType(string typeName)
        {
            try
            {
                return typeName.ToUpper() switch
                {
                    "NUMBER" => typeof(long), // or typeof(decimal)
                    "VARCHAR2" => typeof(string),
                    "BOOL" => typeof(bool),
                    "DATE" => typeof(DateTime),
                    _ => typeof(string),
                };
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }

            return typeof(string);
        }

        #endregion
        #region
        public enum FormAction { ADD, EDIT, DEL, FILTR }


        #endregion


        private Dictionary<string, object> OutPutDict = new Dictionary<string, object> { {"VARCHAR2", String.Empty },
                                                                                         {"CHAR", String.Empty },
                                                                                         {"DATE", new DateTime()   },
                                                                                         {"NUMBER", new Decimal()   },
                                                                                         {"INT", new Decimal()   },
                                                                                   };
              

        public async Task<(Dictionary<string, Rep_Param> parameters, bool res, string errormessage)>
         SetParam(Dictionary<string, object> formValues,
                  IEnumerable<REP_Models.baseColParam> metadata,
                  Dictionary<string, Rep_Param> globalFilter,
                  Dictionary<string, object> selectedRowData = null
                 )
        {
            Dictionary<string, Rep_Param> parameters = new();
            string errormessage = string.Empty;
            bool res = true;

            try {
                foreach (ColumnMetadata meta in metadata.Where(x => x.CODE != "ERRORINFO"))
                {
                    if (meta.IS_PK == 1)
                    {
                        parameters.Add("OUT_" + meta.CODE, new Rep_Param(meta.ISMANDATORY, meta.DATATYPE, ParameterDirection.Output, meta.DATALENGTH,
                                                meta.DATAPRECISION, null));

                    }

                    if (formValues.ContainsKey(meta.CODE))
                    {
                        parameters.Add("IN_" + meta.CODE, new Rep_Param(meta.ISMANDATORY, meta.DATATYPE, ParameterDirection.Input, meta.DATALENGTH,
                                                meta.DATAPRECISION, formValues[meta.CODE]));
                    }

                    if (selectedRowData.ContainsKey(meta.CODE))
                    {
                        parameters.Add("INO_" + meta.CODE, new Rep_Param(meta.ISMANDATORY, meta.DATATYPE, ParameterDirection.InputOutput, meta.DATALENGTH,
                                                meta.DATAPRECISION, selectedRowData[meta.CODE]));
                    }

                }

                foreach (var prm in globalFilter)
                {
                    parameters.Add("INP_" + prm.Key, prm.Value);

                }
                
            }
            catch (Exception ex)
            {
                res = false;
                errormessage = $"General Error: {ex.Message}";
            }
            return (parameters, res, errormessage);

        }


    }




}
