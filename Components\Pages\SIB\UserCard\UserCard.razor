﻿@attribute [Authorize]
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Ra<PERSON>zen
@using Radzen.Blazor
@using EWA.Services
@using System
@using System.Collections.Generic
@using System.Linq
@using System.Threading.Tasks


<RadzenCard class=" rz-border-0" Variant="Variant.Text">
    <PersonInfo user = "@Security.User"/>
    <RadzenCard class="rz-shadow-0 rz-border-0 rz-p-0" >
        <RadzenTabs Style="width:100%; height:460px">
            <Tabs>
                <RadzenTabsItem Text="Общая информация">
                    <ContactInfo user ="@Security.User"/>
                </RadzenTabsItem>
                <RadzenTabsItem Text="Доступные модули" >
                    <UserRoles user ="@Security.User"/>
                </RadzenTabsItem> 
                <RadzenTabsItem Visible="!Security.User.ISdomain" Text="Пароль">
                        <ChangePassword user="@Security.User"/>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </RadzenCard>
    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.End" class="rz-pt-4">
        <RadzenButton ButtonType="ButtonType.Submit" ButtonStyle="ButtonStyle.Light" Text="Закрыть" Variant="Variant.Flat" Click="@CancelClick" />
    </RadzenStack>
</RadzenCard>

@code{
    [Inject]
    protected DialogService DialogService { get; set; }
    [Inject]
    protected SIBService.SecurityService Security { get; set; }

    private void CancelClick()
    {
        DialogService.Close();
    }
}