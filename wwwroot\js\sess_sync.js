﻿window.authSync = {
    setLogin: function () {
        localStorage.setItem('loginEvent', Date.now());
    },
    setLogout: function () {
        localStorage.setItem('logoutEvent', Date.now());
    },
    addLogoutListener: function (dotnetHelper) {
        window.addEventListener('storage', function (e) {
            if (e.key === 'logoutEvent') {
                dotnetHelper.invokeMethodAsync('OnLogoutDetected');
            }
        });
    },
    addLoginListener: function (dotnetHelper) {
        window.addEventListener('storage', function (e) {
            if (e.key === 'loginEvent') {
                dotnetHelper.invokeMethodAsync('OnLoginDetected');
            }
        });
    }
};