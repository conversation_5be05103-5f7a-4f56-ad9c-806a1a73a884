﻿using EWA.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using static EWA.Models.REP_Models;

namespace EWA.Models
{
    public class TabData
    {
        public string TabNameShrt => $"{(NameObj?.Length > 10 ? NameObj.Substring(0, 10) + "..." : NameObj)}";
        public TabData ParentTab { get; set; }
        /// <summary>
        /// Признак построилась вкладка
        /// </summary>
        public bool is_complete { get; set; } = false;
        /// <summary>
        /// статус получения данных
        /// 0- норм
        /// 1- временная грузится
        /// 2- временная загрузилась трубуется обновление после его выполнения =0
        /// 
        /// </summary>
        public int load_state { get; set; } = 0;
        public string NameGrid { get; set; }
        public string CodeObj { get; set; }
        public string NameObj { get; set; }
        public string Main_App_Code { get; set; }
        public int Index { get; set; }

        public string PathIndex { get; set; }
        public int selectedIndex { get; set; }
        public bool Show { get; set; }
        public bool IsSelect { get; set; }

        public TabPageKind Kind { get; set; } = TabPageKind.Standart;
        
        public int CountLvl { get; set; }
        public Dictionary<int, TabData> SubTabs { get; set; } = new();
        public Dictionary<string, object> RowData { get; set; } = new(StringComparer.InvariantCultureIgnoreCase);
        public Dictionary<string, Rep_Param> ParamData { get; set; } = new(StringComparer.InvariantCultureIgnoreCase);

        public List<REP_Models.ParamMetadata> TransitionParams { get; set; } = new();
        public Dictionary<string, string> LinkedParam { get; set; } = new();
        public string VisibilityFormula { get; set; }
     
        public Dictionary<string, object> Prm { get; set; } = new(StringComparer.InvariantCultureIgnoreCase);
    
        public bool isLoad { get; set; } = false;
        public bool isLoading { get; set; } = false;

        public void SetSelectedIndex(int key)
        {
            selectedIndex = Array.IndexOf(SubTabs.Keys.ToArray(), key);
            SetIsSelectFalse();
            SetIsSelectTrue();

            foreach (var item in SubTabs)
            {
                item.Value.isLoad = true;
            }
            TabData _parentTab = new();
            TabData _tab = new();
            int index = 0;
            if (ParentTab != null)
            {
                _parentTab.ParentTab = ParentTab;
            }
            {
                _parentTab = this;
            }
            while (_parentTab.ParentTab != null)
            {
                _parentTab = _parentTab.ParentTab;
            }

            _tab = _parentTab;
            while (_tab.SubTabs.Count() > 0)
            {
                index = index + 1;
                _tab = _tab.SubTabs.First(x => x.Value.IsSelect == true).Value;
            }

            _tab = _parentTab;
            while (_tab.SubTabs.Count(x => x.Value.Show == true) > 0)
            {
                _tab.CountLvl = index;
                _tab = _tab.SubTabs.First(x => x.Value.IsSelect == true).Value;
            }
            _tab.CountLvl = index;
        }

        protected void SetIsSelectFalse()
        {
            foreach (var tab in SubTabs)
            {
                tab.Value.IsSelect = false;
                tab.Value.SetIsSelectFalse();
            }
        }

        protected void SetIsSelectTrue()
        {
            foreach (var tab in SubTabs.Where(x => Array.IndexOf(SubTabs.Keys.ToArray(), x.Key) == selectedIndex))
            {
                tab.Value.IsSelect = true;
                tab.Value.SetIsSelectTrue();
            }
        }

        private string Get_Main_App_Code(string main_app_Code)
        {
            if (ParentTab.CodeObj == "head")
            {
                return main_app_Code;
            }
            else
            {
                return ParentTab.Main_App_Code;
            }
        }

        private int AddSubTabImpl(string nameGrid, string codeObj, string nameobj,
                              List<REP_Models.ParamMetadata> transitionParams,
                              Dictionary<string, string> linkedParam,
                              string visibilityFormula, string main_app_Code, TabPageKind kind)
        {
            int newKey = SubTabs.Count > 0 ? SubTabs.Keys.Max() + 1 : 0;

            SubTabs[newKey] = new TabData
            {
                ParentTab = this,
                NameGrid = nameGrid,
                CodeObj = codeObj,
                NameObj = nameobj,
                Index = newKey,
                Show = true,
                IsSelect = true,
                CountLvl = 1,
                TransitionParams = transitionParams,
                LinkedParam = linkedParam,
                VisibilityFormula = visibilityFormula,
                Kind= kind
            };
            SubTabs[newKey].Main_App_Code = SubTabs[newKey].Get_Main_App_Code(main_app_Code);

            if (SubTabs[newKey].ParentTab.CodeObj == "head")
            {
                SubTabs[newKey].PathIndex = SubTabs[newKey].ParentTab.PathIndex + SubTabs[newKey].Main_App_Code + "->" + codeObj + "_" + newKey;
            }
            else
            {
                SubTabs[newKey].PathIndex = SubTabs[newKey].ParentTab.PathIndex + "->" + codeObj + "_" + newKey;
            }

            return newKey;
          
        }
        public void AddSubTab(string nameGrid, string codeObj, string nameobj,
                              List<REP_Models.ParamMetadata> transitionParams,
                              Dictionary<string, string> linkedParam,
                              string visibilityFormula, string main_app_Code, TabPageKind kind)
        {
            int newKey = AddSubTabImpl(nameGrid, codeObj, nameobj, transitionParams, linkedParam, visibilityFormula, main_app_Code, kind);
            SetSelectedIndex(newKey);
            CalcButtonPrm(newKey);
        }

        public TabData AddModalSubTab(string nameGrid, string codeObj, string nameobj,
                                   List<REP_Models.ParamMetadata> transitionParams,
                                   Dictionary<string, string> linkedParam,
                                   string visibilityFormula, string main_app_Code)
        {
            int newKey = AddSubTabImpl(nameGrid, codeObj, nameobj, transitionParams, linkedParam, visibilityFormula, main_app_Code, TabPageKind.Modal);
           
            CalcButtonPrm(newKey);

            return SubTabs[newKey];
        }

        public void RemoveModalTab()
        {
            

            if (ParentTab != null)
            {
                int key = ParentTab.SubTabs.Where(x => x.Value == this).Select(x => x.Key).FirstOrDefault(-1) ;
                if (key!=-1)
                {
                    ParentTab.SubTabs.Remove(key);
                }
            }
        }

        public void RemoveTab(int index)
        {
            SubTabs[index].Show = false;
            SubTabs[index].IsSelect = false;
            SubTabs[index].SubTabs.Clear();
            
            if (SubTabs.Where(x => x.Value.Show == true).Count() == 0)
            {
                SubTabs.Clear();
                if (ParentTab != null)
                {
                    int key = ParentTab.SubTabs.Where(x => x.Value == this).Max(x => x.Key);
                    ParentTab.SetSelectedIndex(key);
                }

            }
            else
            {

                int key = SubTabs.Where(x => x.Value.Show == true).Max(x => x.Key);

                foreach (var tab in SubTabs.Where(x => x.Key > key))
                {
                    SubTabs.Remove(tab.Key);

                }

                selectedIndex = Array.IndexOf(SubTabs.Keys.ToArray(), key);
                SetSelectedIndex(key);
            }

        }
        public void ChangeSelectedRow(IDictionary<string, object> rowdata, IDictionary<string, Rep_Param> paramdata)
        {
            RowData.Clear();
            if (rowdata != null)
            {
                foreach (var row in rowdata)
                {
                    RowData.Add(row.Key, row.Value);
                }
            }
            ParamData.Clear();
            if (paramdata != null)
            {
                foreach (var row in paramdata)
                {
                    ParamData.Add(row.Key, row.Value);
                }
            }

            foreach (var sub in SubTabs.Where(x=>x.Value.Kind==TabPageKind.CntPage).Reverse())
            {
                RemoveTab(sub.Value.Index);
            }
            CalcButtonPrm(-2);

            foreach (var sub in SubTabs)
            {
                sub.Value.SubTabs.Clear();
                sub.Value.isLoad = true;
            }

        }

        private void CalcButtonPrm(int subtabkey)
        {
            foreach (var sub in SubTabs.Where(x => x.Key == subtabkey || subtabkey == -2))
            {
                sub.Value.Prm.Clear();
                //  Dictionary<string, object> prm = new Dictionary<string, object>(StringComparer.InvariantCultureIgnoreCase);
                foreach (var m in sub.Value.LinkedParam)
                {
                    if (RowData.ContainsKey(m.Key))
                    {
                        sub.Value.Prm.Add(m.Value, RowData.First(x => x.Key == m.Key).Value);
                    }
                    else
                    if (ParamData.ContainsKey(m.Key))
                    {
                        sub.Value.Prm.Add(m.Value, ParamData.First(x => x.Key == m.Key).Value.Val);
                    }
                    else
                    {
                        sub.Value.Prm.Add(m.Value, null);
                    }

                }

            }

        }

    }
}
