﻿@inject GridServices GridService
@inject RepService _repService
@inject EWA.Services.RepService.GET_SPR _getspr
@using EWA.Models
@using EWA.Services
@using System.Diagnostics
@using Blazor.Diagrams
@using Blazor.Diagrams.Components
@using Blazor.Diagrams.Components.Controls
@using Blazor.Diagrams.Components.Renderers
@using Blazor.Diagrams.Components.Widgets
@using Blazor.Diagrams.Core
@using Blazor.Diagrams.Core.Anchors
@using Blazor.Diagrams.Core.Geometry
@using Blazor.Diagrams.Core.Models
@using Blazor.Diagrams.Core.Models.Base
@using Blazor.Diagrams.Core.PathGenerators
@using Blazor.Diagrams.Core.Routers
@using Blazor.Diagrams.Core.Events
@using Blazor.Diagrams.Core.Controls
@using Blazor.Diagrams.Core.Extensions
@using Blazor.Diagrams.Core.Layers
@using Blazor.Diagrams.Core.Options
@using Blazor.Diagrams.Core.Utils
@using Blazor.Diagrams.Core.Controls.Default
@using Blazor.Diagrams.Extensions
@using Blazor.Diagrams.Options
@using System.Text.Json
@using System.Diagnostics.Tracing

@using Microsoft.AspNetCore.Components



<style>
    .diagram-container {
        width: 100%;
        height: @(gridHeight)px;
        border: 1px solid black;

    }

</style>

<RadzenStack Orientation="Orientation.Horizontal">
        <RadzenDataGrid @ref="grid"
                    TItem="GRID_OBJECT"
                        Density="Density.Compact"
                        Data="@objects"
                        AllowPaging="true"
                        PageSize="10"
                        ColumnWidth="100px"
                        Style="width:100%; height:420px;"
                        AllowFiltering="true"
                        AllowSorting="true"
                        @bind-Value=@selectedItems>
            <HeaderTemplate>
                <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween">
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="2rem">
                        <RadzenColumn>
                            <RadzenButton Icon="refresh"
                                          Click="@LoadDataGrid"
                                          Variant="Variant.Text"
                                          ButtonStyle="ButtonStyle.Base" />
                        </RadzenColumn>
                    </RadzenStack>
                </RadzenStack>
            </HeaderTemplate>
            <Columns>
                <RadzenDataGridColumn Title="Действие" Frozen="true" FrozenPosition="FrozenColumnPosition.Right" Width="30px" >
                    <Template Context="item">
                        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween">
                            <RadzenColumn>
                                <RadzenButton Icon="add"
                                              Click="@(() => AddNode(codeNode: item.Code, is_but: 1 ))"
                                          Disabled="@(selectedItems.Count ==0 || selectedItems[0].Id != item.Id || add_node.Any(n => n.Id == item.Id))"
                                              Variant="Variant.Text"
                                              ButtonStyle="ButtonStyle.Base"
                                              Size="ButtonSize.ExtraSmall" />
                                
                            </RadzenColumn>

                        </RadzenStack>
                    </Template>
                  
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="Id" Title="ID" Frozen="true" />
                <RadzenDataGridColumn Property="Code" Title="Код" Frozen="true" />
                <RadzenDataGridColumn Property="Name" Title="Название" Frozen="true" />
            </Columns>
        </RadzenDataGrid>

</RadzenStack>
<RadzenStack>
    <RadzenSlider @bind-Value="gridHeight" Min="100" Max="2000" Step="50" ShowValue="true" />
</RadzenStack>
<RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End" Gap="0.5rem" Style="margin-top: 1rem;" class="diagram-container">
    <CascadingValue Value="Diagram" IsFixed="true">
        <DiagramCanvas>
            <Widgets>
                <NavigatorWidget Width="200" Height="120"
                                 Class="border border-black bg-white absolute"
                                 Style="bottom: 15px; right: 15px;" />
                <SelectionBoxWidget />
            </Widgets>

        </DiagramCanvas>
    </CascadingValue>

</RadzenStack>

<RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End" Gap="0.5rem" Style="margin-top: 1rem;">
    <RadzenButton ButtonType="ButtonType.Submit" Icon="save" Text="Сохранить" Click="@SaveClick" Variant="Variant.Flat" />
    <RadzenButton ButtonStyle="ButtonStyle.Light" Text="Отмена" Click="@CancelClick" Variant="Variant.Flat" />
</RadzenStack>


@code
{
    private int gridHeight = 400;
    [Inject]
    protected IJSRuntime JSRuntime { get; set; }
    [Inject]
    protected DialogService DialogService { get; set; }
    [Inject]
    private EWA.Services.AMLService _amlserv { get; set; }
    [Inject] protected EWA.Services.SIBService.SecurityService Security { get; set; }

    [Parameter]
    public string _Code { get; set; }
    [Parameter]
    public string _DBname { get; set; }
    [Parameter]
    public Dictionary<string, string> metaALG { get; set; }
    [Parameter]
    public Dictionary<string, object> metaALGData { get; set; }

    private REP_Models.RepositoryInfo repositoryInfo = new REP_Models.RepositoryInfo();
    Dictionary<string, Node> _in_nodes = new Dictionary<string, Node>();
    Dictionary<string, Link_NODE> _in_lnk_nodes = new Dictionary<string, Link_NODE>();
    private Dictionary<string, object> selectedRowData = new Dictionary<string, object>();
    private Dictionary<string, IEnumerable<REP_Models.SPRShort>> SprShortDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprShortSelDict> SprSelShotDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprLongFiltrMeta> SprLongDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprLongSelDict> SprSelLongDict = new();

    private BlazorDiagram Diagram { get; set; } = null!;
    RadzenDataGrid<GRID_OBJECT> grid;
    private Dictionary<string, IEnumerable<IDictionary<string, object>>> dataDict = new();
    private IList<GRID_OBJECT> objects = new List<GRID_OBJECT>();
    private IList<GRID_OBJECT> add_node = new List<GRID_OBJECT>();
    private IList<GRID_OBJECT> selectedItems = new List<GRID_OBJECT>();
    private GRID_OBJECT selectedRow;

    Dictionary<string, Link_NODE> lnk_dict = new Dictionary<string, Link_NODE>();
    Dictionary<string, Node> nodes_dict = new Dictionary<string, Node>();
    Dictionary<string, object> metaALGData_OUT = new Dictionary<string, object>();
    private Dictionary<string, int> SprLongCountDict = new();
    private Dictionary<string, string> SprLongFiltrDict = new();

    protected override async Task OnInitializedAsync()
    {
        string in_nodes = string.Empty;
        string in_lnk_nodes = string.Empty;

        foreach (var kvp in metaALGData)
        {
            if (kvp.Key == "WFL_NODES")
            {
                in_nodes = kvp.Value.ToString();
            }
            else if (kvp.Key == "WFL_NODES_LNK")
            {
                in_lnk_nodes = kvp.Value.ToString();
            }
        }

        var jsnoptions = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
        if (!string.IsNullOrEmpty(in_nodes))
        {
            _in_nodes = JsonSerializer.Deserialize<Dictionary<string, Node>>(in_nodes, jsnoptions);
        }

        if (!string.IsNullOrEmpty(in_lnk_nodes))
        {
            _in_lnk_nodes = JsonSerializer.Deserialize<Dictionary<string, Link_NODE>>(in_lnk_nodes, jsnoptions);
        }

        var options = new BlazorDiagramOptions
            {
                AllowMultiSelection = true,
                Zoom = { Enabled = false },
                Links =
                {
                DefaultRouter = new NormalRouter()
                }
            };
        Diagram = new BlazorDiagram(options);
        Diagram.Options.Links.Factory = (d, s, ta) =>
        {
            var link = new LinkModel(new SinglePortAnchor(s as PortModel)
                {
                    UseShapeAndAlignment = false
                }, ta)
               {
                   SourceMarker = LinkMarker.Square,
                   TargetMarker = LinkMarker.Arrow
               };
            return link;
        };
        Diagram.Options.Zoom.Enabled = true;

        Diagram.Nodes.Removed += async (node) => await OnDiagramNodeRem(node);

        if (_in_nodes.Count > 0)
        {
            await LoadDataGrid();
            add_node = objects.Where(o => _in_nodes.Values.Any(n => n.Code == o.Code)).ToList();
            foreach (var n in _in_nodes)
            {
                AddNode(codeNode: n.Value.Code, posX: n.Value.PosX, posY: n.Value.PosY, is_but : 0);
            }
            foreach (var l in _in_lnk_nodes)
            {
                if (l.Value?.SrcNode != null && l.Value?.SrcPort?.Code != null && l.Value?.TrgNode != null && l.Value?.TrgPort?.Code != null)
                {
                    AddLink(SRCNode: l.Value.SrcNode, SRCPort: l.Value.SrcPort.Code, TRGNode: l.Value.TrgNode, TRGPort: l.Value.TrgPort.Code);
                }
            }
        }
        StateHasChanged();
        Diagram.PointerDoubleClick += async (m, e) => await InfoRow(m?.Id);

    }
    protected readonly List<string> events = new List<string>();

    private async Task InfoRow(string code)
    {
        string nodecode = string.Empty;
        string nid = string.Empty;
        selectedRowData.Clear();
        nid = code;
        var ndata = Diagram.Nodes.FirstOrDefault(n => n.Id == nid);
        var ndata_prop = ndata.GetType().GetProperties();
        foreach (var n_prop in ndata_prop)
        {
            if (n_prop.Name == "Code")
            {
                nodecode = n_prop.GetValue(ndata).ToString();
            }
        }

        var formValues = new Dictionary<string, object>();
        bool formRes = false;

        repositoryInfo = await _repService.GetRepositoryInfo(Security.User,"EQ_AML_CFG_IND");
        foreach ((string dimcode, string param, string datatype) in repositoryInfo.Column.Where(x => x.DIMCODE != null).Select(x => (x.DIMCODE, x.CODE, x.DATATYPE)).Concat(repositoryInfo.Param.Where(x => x.DIMCODE != null).Select(x => (x.DIMCODE, x.CODE, x.DATATYPE))))
        {

            if (SprShortDict.ContainsKey(dimcode) == false && SprLongDict.ContainsKey(dimcode) == false)
            {
                var (_sprMetadata, _isShortDim, _SprLongFiltrMeta) = await _getspr.GetSPRDataAsync1(dimcode, repositoryInfo.Dbname_Query, datatype);
                if (_isShortDim)
                {
                    SprShortDict.Add(dimcode, _sprMetadata);
                }
                else
                {
                    SprLongDict.Add(dimcode, _SprLongFiltrMeta);
                }
            }
            if (SprLongDict.ContainsKey(dimcode))
            {

                SprSelLongDict.Add(param, new REP_Models.Rep_SprLongSelDict { KeySprLongDict = dimcode, ValPrm = null });
                IEnumerable<IDictionary<string, object>> data; //= Enumerable.Empty<IDictionary<string, object>>();
                dataDict.Add(param, null);
                SprLongCountDict.Add(param, 0);
                SprLongFiltrDict.Add(param, "");
            }
            else
            {
                SprSelShotDict.Add(param, new EWA.Models.REP_Models.Rep_SprShortSelDict { KeySprShortDict = dimcode, ValPrm = null });
            }
        }

        var val = GridService.getSibFormInfo("EDIT", "DiagramForm", "Редактирование " + nodecode);

    }


    private async Task OnDiagramNodeRem(NodeModel node)
    {
        var itemToRemove = add_node.FirstOrDefault(n => n.Code == node.Title);
        if (itemToRemove != null)
        {
            add_node.Remove(itemToRemove);
            await grid.Reload();
        }
        await Task.CompletedTask;
    }
    protected async Task LoadDataGrid()
    {
        var result = await _amlserv.GetIndDiagram();
        if (result != null)
        {
            objects = result.Select(kvp => new GRID_OBJECT
                {
                    Id = kvp.Value.id,
                    Code = kvp.Value.code,
                    Name = kvp.Value.name
                }).ToList();
        }
        await grid.Reload();
        StateHasChanged();

    }

    private void AddNode(string codeNode = null, double? posX = null, double? posY = null, int? is_but = 0 )
    {
        string NCode = string.Empty;
        string NName = string.Empty;
        double NPosX = 100;
        double NPosY = 200;

        if (is_but == 1)
        {
            if (selectedItems.Count > 0)
            {
                selectedRow = selectedItems[0];
            }
            else
            {
                selectedRow = objects.FirstOrDefault(o => o.Code == codeNode);
            }
            NCode = selectedRow.Code;
            NName = selectedRow.Name;
        }
        else
        {
            var foundObject = add_node.FirstOrDefault(node => node.Code == codeNode);
            NName = foundObject?.Name;
            NCode = codeNode;
            NPosX = posX ?? 100;
            NPosY = posY ?? 200;
        }

        var newNode = new EWANodeModel(NName, NCode)
        {
                Position = new Blazor.Diagrams.Core.Geometry.Point(NPosX, NPosY),
                Title = NCode
        };

        Diagram.Nodes.Add(newNode);
        Diagram.Controls.AddFor(newNode)
                                .Add(new RemoveControl(1, 0));
        if (is_but == 1)
        {
            add_node.Add(selectedRow);
        }
    }

    private void AddLink(string SRCNode, string TRGNode, string SRCPort, string TRGPort)
    {
        PortAlignment alignment_src = (PortAlignment)Enum.Parse(typeof(PortAlignment), SRCPort);
        PortAlignment alignment_trg = (PortAlignment)Enum.Parse(typeof(PortAlignment), TRGPort);
        PortModel src_prt = new PortModel(null!);
        PortModel trg_prt = new PortModel(null!);

        foreach (var _lnode in Diagram.Nodes)
        {
            if (_lnode.Title == SRCNode)
            {
                src_prt = _lnode.GetPort(alignment_src);
            }
            if (_lnode.Title == TRGNode)
            {
                trg_prt = _lnode.GetPort(alignment_trg);
            }
        }
        var link = new LinkModel(src_prt, trg_prt);
        link.SourceMarker = LinkMarker.Square;
        link.TargetMarker = LinkMarker.Arrow;
        Diagram.Links.Add(link);
    }

    public void SaveClick()
    {
        GetLinks();
        GetNodes();
        string ind_lst = string.Empty;
        ind_lst = string.Join(",", nodes_dict.Values
                                             .Select(n => n.Code)
                                             .Where(code => !string.IsNullOrWhiteSpace(code))
                                             .Distinct());
        string lnkJson = JsonSerializer.Serialize(lnk_dict, new JsonSerializerOptions { WriteIndented = true });
        string nodesJson = JsonSerializer.Serialize(nodes_dict, new JsonSerializerOptions { WriteIndented = true });
        metaALGData_OUT.Clear();
        foreach (var kvp in metaALG)
        {
            if (kvp.Value == "WFL_NODES")
            {
                metaALGData_OUT.Add(kvp.Key, nodesJson);
            }
            else if (kvp.Value == "WFL_NODES_LNK")
            {
                metaALGData_OUT.Add(kvp.Key, lnkJson);
            }
            if (kvp.Value == "WFL_NODE_STR")
            {
                metaALGData_OUT.Add(kvp.Key, ind_lst);
            }
        }
        DialogService.Close(new { status = 1, r_metaData = metaALGData_OUT });
    }
    public void CancelClick()
    {
        metaALGData_OUT.Clear();
        foreach (var kvp in metaALG)
        {
            metaALGData_OUT.Add(kvp.Key, null);
        }

        DialogService.Close(new { status = 0, r_metaData = metaALGData_OUT });
    }
    public void GetLinks()
    {
        lnk_dict.Clear();

        foreach (var lnk in Diagram.Links)
        {
            string linkId;
            linkId = lnk.Id;
            var res_src = GetValLink(lnk.Source);
            var res_trg = GetValLink(lnk.Target);
            lnk_dict[linkId] = new Link_NODE { SrcNode = res_src._code, 
                                               SrcPort = new Node_Ports{ Code = res_src._port, 
                                                                         PosX = res_src._posx, 
                                                                         PosY = res_src._posy }, 
                                               TrgNode = res_trg._code,
                                               TrgPort = new Node_Ports{  Code = res_trg._port,
                                                                          PosX = res_trg._posx,
                                                                          PosY = res_trg._posy}
                                             };
        }
        StateHasChanged();
    }
    public void GetNodes()
    {
        nodes_dict.Clear();
        double posy = 0;
        double posx = 0;
        string nodeid = string.Empty;
        string nodecode = string.Empty;
        object nodedata;
        List<Node_Ports> posts_node = new List<Node_Ports>();
        foreach (var ndata in Diagram.Nodes)
        {
            nodeid = ndata.Id;
            var ndata_prop = ndata.GetType().GetProperties();
            foreach (var n_prop in ndata_prop)
            {
                if (n_prop.Name == "Code")
                {
                    nodecode = n_prop.GetValue(ndata).ToString();
                }
                if (n_prop.Name == "Ports")
                {
                    var props_pos = n_prop.GetValue(ndata);
                    var ports = props_pos as List<PortModel>;
                    foreach (var t in ports)
                    {
                        double p_posx = 0;
                        double p_posy = 0;
                        string key_port = string.Empty;

                        key_port = t.Alignment.ToString();
                        p_posx = t.Position.X;
                        p_posy = t.Position.Y;

                        if (key_port != null)
                        {
                            posts_node.Add(new Node_Ports { Code = key_port, PosX = p_posx, PosY = p_posy });
                        }
                    }
                }
                if (n_prop.Name == "Position")
                {
                    var props_pos = n_prop.GetValue(ndata);
                    foreach (var p_pos in props_pos.GetType().GetProperties())
                    {
                        if (p_pos.Name == "X")
                        {
                            var t_posx = p_pos.GetValue(props_pos);
                            posx = Convert.ToDouble(t_posx);
                        }
                        if (p_pos.Name == "Y")
                        {
                            var t_posy = p_pos.GetValue(props_pos);
                            posy = Convert.ToDouble(t_posy);
                        }
                    }
                }
            }
            nodes_dict[nodeid] = new Node { Code = nodecode, PosX = posx, PosY = posy, Ports = new List<Node_Ports>(posts_node) };
            posts_node.Clear();
        }
        StateHasChanged();
    }

    private (string _code, string _port, double _posx, double _posy) GetValLink(object in_data)
    {
        string out_data = string.Empty;
        string code_node = string.Empty;
        string alig_port = string.Empty;
        double posx = 0;
        double posy = 0;
        if (in_data != null)
        {
            var src_properties = in_data.GetType().GetProperties();
            foreach (var property in src_properties)
            {
                if (property.Name == "Model")
                {
                    var model_prop = property.GetValue(in_data);

                    foreach (var val_model in model_prop.GetType().GetProperties())
                    {
                        if (val_model.Name == "Alignment")
                        {
                            alig_port = val_model.GetValue(model_prop).ToString();
                        }
                        if (val_model.Name == "Parent")
                        {
                            var parent_prop = val_model.GetValue(model_prop);
                            foreach (var val_perent in parent_prop.GetType().GetProperties())
                            {
                                if (val_perent.Name == "Code")
                                {
                                    code_node = val_perent.GetValue(parent_prop).ToString();
                                }
                            }
                        }
                        if (val_model.Name == "Position")
                        {
                            var props_pos = val_model.GetValue(model_prop);
                            foreach (var p_pos in props_pos.GetType().GetProperties())
                            {
                                if (p_pos.Name == "X")
                                {
                                    var t_posx = p_pos.GetValue(props_pos);
                                    posx = Convert.ToDouble(t_posx);
                                }
                                if (p_pos.Name == "Y")
                                {
                                    var t_posy = p_pos.GetValue(props_pos);
                                    posy = Convert.ToDouble(t_posy);
                                }
                            }
                        }
                    }
                }
            }
        }
        return (_code: code_node, _port: alig_port, _posx: posx , _posy: posy );
    }
    public class GRID_OBJECT
    {
        public long Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
    }
    public class EWANodeModel : NodeModel
    {
        public string Name { get; set; }
        public string Code { get; set; }

        public EWANodeModel(string name, string code) : base(Guid.NewGuid().ToString())
        {
            Name = name;
            Code = code;

            AddPort(new PortModel(this, PortAlignment.Left));
            AddPort(new PortModel(this, PortAlignment.Right));
        }
    }
    public class Link_NODE
    {
        public string SrcNode { get; set; }
        public Node_Ports SrcPort { get; set; }
        public string TrgNode { get; set; }
        public Node_Ports TrgPort { get; set; }
    }

    public class Node
    {
        public string Code { get; set; }
        public double PosX { get; set; }
        public double PosY { get; set; }
        public List<Node_Ports> Ports { get; set; } = new List<Node_Ports>();
    }

    public class Node_Ports
    {
        public string Code { get; set; }
        public double PosX { get; set; }
        public double PosY { get; set; }
    }

}