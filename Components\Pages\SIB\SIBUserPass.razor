﻿@attribute [Authorize]
@using System
@using System.Collections.Generic
@using System.Linq
@using System.Threading.Tasks
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@using Microsoft.AspNetCore.Identity
@using EWA.Components.Pages.SIB.UserCard

<RadzenCard>
<RadzenRow>
    <RadzenColumn SizeMD="12">
        <RadzenTemplateForm Method="post" TItem="EWA.Models.SIB_Models.SIB_USERS" Data="@user" Visible="@(user != null)" Submit="@FormSubmit">
           @*  <RadzenAlert Title="Cannot create user" Shade="Shade.Lighter" Variant="Variant.Flat" Size="AlertSize.Small" AlertStyle="AlertStyle.Danger" Visible="@errorVisible">@error</RadzenAlert>
 *@
            <RadzenAlert Size="AlertSize.Small" Shade="Shade.Light" AlertStyle="AlertStyle.Danger" Variant="Variant.Flat" Title="Пароль не изменен"
                         Visible="@errorVisible">Неверно введен текущий пароль</RadzenAlert>
            <RadzenAlert Size="AlertSize.Small" Shade="Shade.Light" AlertStyle="AlertStyle.Success" Variant="Variant.Flat" Visible="@successVisible">
                Пароль успешно изменен.
            </RadzenAlert>

            <RadzenStack style="margin-bottom: 1rem;">
                <RadzenFormField Text="Новый пароль" Variant="Variant.Text">
                    <ChildContent>
                        <RadzenPassword @bind-Value="@(user.NEWPASSWORD)" style="display: block; width: 100%" Name="NewPassword" />
                    </ChildContent>
                    <Helper>
                        <RadzenRequiredValidator Component="NewPassword" Text="Не заполнен пароль" />
                    </Helper>
                </RadzenFormField>
                <RadzenFormField Text="Подтверждение пароля" Variant="Variant.Text">
                    <ChildContent>
                        <RadzenPassword style="display: block; width: 100%" @bind-Value="@(user.CONFIRMPASSWORD)" Name="ConfirmPassword" />
                    </ChildContent>
                    <Helper>
                        <RadzenRequiredValidator Component="ConfirmPassword" Text="Не заполнено подтверждение пароля" />
                        <RadzenCompareValidator Component="ConfirmPassword" Text="Пароли должны совпадать" Value="@(user.NEWPASSWORD)" />
                    </Helper>
                </RadzenFormField>
            </RadzenStack>
            <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End" Gap="0.5rem">
                <RadzenButton ButtonType="ButtonType.Submit" Text="Сохранить" Variant="Variant.Flat" />
                <RadzenButton ButtonStyle="ButtonStyle.Light" Text="Отмена" Click="@CancelClick" Variant="Variant.Flat" />
            </RadzenStack>
        </RadzenTemplateForm>
    </RadzenColumn>
</RadzenRow>
</RadzenCard>

@code {
    [Inject] protected IJSRuntime JSRuntime { get; set; }
    [Inject] protected NavigationManager NavigationManager { get; set; }
    [Inject] protected DialogService DialogService { get; set; }
    [Inject] protected TooltipService TooltipService { get; set; }
    [Inject] protected ContextMenuService ContextMenuService { get; set; }
    [Inject] protected NotificationService NotificationService { get; set; }
    [Inject] protected EWA.Services.SIBService.SecurityService Security { get; set; }
    [Inject] protected EWA.Services.SIBService.UserService UServ { get; set; }
    [Inject] protected IPasswordHasher<EWA.Models.SIB_Models.SIB_USERS> PasswordHasher { get; set; }
    [Inject] public EWA.Services.RepService.ISequenceService Seq { get; set; }

    [Parameter] public decimal InUserId { get; set; }

    protected IEnumerable<EWA.Models.SIB_Models.SIB_ROLES> roles;
    protected EWA.Models.SIB_Models.SIB_USERS user;
    protected IEnumerable<string> userRoles = Enumerable.Empty<string>();
    protected string error;
    protected bool errorVisible;
    protected bool successVisible;
    bool form_res = false;
    private Dictionary<string, object> _formValues = new Dictionary<string, object>();
    Dictionary<string, object> formres = new Dictionary<string, object>();

    protected override async Task OnInitializedAsync()
    {
        user = await UServ.GetUserById(InUserId);
    }

    protected async Task<(Dictionary<string, object> vals, bool isres, string errormess)> FormSubmit(EWA.Models.SIB_Models.SIB_USERS user)
    {
        bool isres = false;
        string errmess = string.Empty;
        try
        {
            user.PASSWORDHASH = PasswordHasher.HashPassword(user, user.NEWPASSWORD);
            user.TECHN_PASS = user.NEWPASSWORD;
            user.IS_TECHN_PASS = 1;
            user.USER_CHANGE = Security.User.CODE;
            user.DT_CHANGE = DateTime.Now;
            var res = await UServ.UpdateUserPass(user);
            if (res.form_res)
            {
                formres = res.res_data;
                isres = res.form_res;
            }
            else
            {
                errorVisible = true;
                error = $"Ошибка смены пароля. {res.errorMessage}";
            }
        }
        catch (Exception ex)
        {
            errorVisible = true;
            error = ex.Message;
        }
        DialogService.Close(new { Values = formres, Result = isres, errorMessage = error });
        return (formres, isres, error);
    }
    protected async Task<(Dictionary<string, object> vals, bool isres, string errormess)> CancelClick()
    {
        DialogService.Close(new { Values = formres, Result = false, errorMessage = error });
        return (formres, false, error);
    }
}