@using static EWA.Services.SIBService
@inject SessionTimeoutService SessionTimeout
@inject AuthMonitorService AuthMonitorService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject SecurityService Security
@implements IDisposable
@using <PERSON><PERSON><PERSON>
@using Ra<PERSON>zen.Blazor

@if (isLocked)
{
    <div class="session-lock-overlay" @onclick:preventDefault @onclick:stopPropagation>
        <RadzenCard class="session-lock-dialog rz-shadow-5">
            <RadzenText TextStyle="TextStyle.H6" class="rz-mb-4" Style="text-align: center; color: white;">
                Сеанс заблокирован из-за отсутствия активности.
            </RadzenText>
            <RadzenText TextStyle="TextStyle.Body1" Style="text-align: center; color: white;">
                Введите пароль для продолжения работы.
            </RadzenText>

            <RadzenPassword @bind-Value="password" Placeholder="Пароль"
                           Style="width: 100%;"
                            @oninput="@(() => showError = false)"
                            Change="@(args => { showError = false; })"
                            @onkeyup="HandleKeyPress" />         

            <div style="min-height: 20px;">
                @if (showError)
                {
                    <RadzenText Style="color: red !important; margin: 0; font-size: 0.875rem; text-align: left;">
                        Неверный пароль
                    </RadzenText>
                }
            </div>
            
            <div style="display: flex; justify-content: center; gap: 1rem; margin-top: 1rem;">
                <RadzenButton Text="Разблокировать" 
                             Click="@UnlockSession" Style="width: 45%;" />
                <RadzenButton Text="Выйти" ButtonStyle="ButtonStyle.Light" 
                             Click="@Logout" Style="width: 45%;" />
            </div>
        </RadzenCard>
    </div>
}

@code {
    private string password;
    private bool isLocked = false;
    private bool isFirstRender = true;
    private bool showError = false;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // подписка на события блокировки и разблокировки
            SessionTimeout.OnSessionLocked += OnSessionLocked;
            SessionTimeout.OnSessionUnlocked += OnSessionUnlocked; 
            isLocked = await SessionTimeout.IsSessionLocked();
            if (isLocked)
            {
                await OnSessionLocked();
            }

            isFirstRender = false;
        }
    }

    private async Task OnSessionLocked()
    {
        isLocked = true;
        try
        {
            await InvokeAsync(StateHasChanged);
            await Task.Delay(100);
            await BlockUI();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in OnSessionLocked: {ex.Message}");
        }
    }

    private async Task BlockUI()
    {
        try 
        {
            await JSRuntime.InvokeVoidAsync("blockUI");
        }
        catch (JSDisconnectedException)
        {
            // соединение разорвано, игнорируем ошибку
            Console.WriteLine("js connection lost during BlockUI");
        }
    }

    private async Task HandleKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            if (!string.IsNullOrEmpty(password))
            {
                await UnlockSession();
            }
        }
    }

    private async Task UnlockSession()
    {
        showError = false;
        if (await SessionTimeout.UnlockSession(password, Security.User))
        {
            password = string.Empty;
            await JSRuntime.InvokeVoidAsync("unblockUI");
            isLocked = false;
            await InvokeAsync(StateHasChanged);
        }
        else
        {
            showError = true;
            StateHasChanged();
        }
    }

    private async Task Logout()
    {
        await SessionTimeout.ClearSession();
        await AuthMonitorService.setLogout();
        Navigation.NavigateTo("Account/Logout", true);
    }

    private async Task OnSessionUnlocked()
    {
        Console.WriteLine("SessionLock: OnSessionUnlocked called");
        if (isLocked)
        {
            password = string.Empty;
            showError = false;
            isLocked = false;
            try
            {
                await JSRuntime.InvokeVoidAsync("unblockUI");
                await InvokeAsync(StateHasChanged);
                Console.WriteLine("SessionLock: UI unblocked");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in OnSessionUnlocked: {ex.Message}");
            }
        }
    }

    public void Dispose()
    {
        // отписка от событий блокировки и разблокировкм
        SessionTimeout.OnSessionLocked -= OnSessionLocked;
        SessionTimeout.OnSessionUnlocked -= OnSessionUnlocked;
    }
}
