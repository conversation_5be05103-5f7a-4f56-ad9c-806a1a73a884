@inherits LayoutComponentBase
@inject NavigationManager NavManager
@inject IConfiguration Configuration
@inject SessionTimeoutService SessionTimeout
@inject EWA.Services.RepService AllRepService
@inject SIBService.EncrpServHelper _diagEncService
@inject AuthMonitorService AuthMonitorService
@inject EWAAuthenticateService EWAAuthenticateService
@using static EWA.Models.REP_Models
@using System.Net.Http
@using System.Reflection
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using Radzen
@using Radzen.Blazor
@using EWA.Services
@using EWA.Components
@using EWA.Components.Pages
@using EWA.Components.Pages.SIB
@using EWA.Components.Pages.SIB.UserCard
@using EWA.Components.Pages.GLOBAL
@using EWA.Components.Pages.AUTH
@using EWA.Components.Pages.EXT
@using EWA.Enums
@using static EWA.Services.SIBService


<style>

    .rz-sidebar{
        background-color: var(--rz-header-background-color);
        color: var(--rz-header-color);
    }

    .rz-panel-menu{
        background-color: var(--rz-header-background-color);
        color: var(--rz-header-color);
    }

    .rz-tabview-nav li a, .rz-tabview-nav li a:not([href]):not([class]) {
        padding-block: 0.25rem;
        padding-inline: 0.3rem;
    }


    .rz-tabview{
        background-color: var(--rz-tabs-background-color);
        border: var(--rz-tabs-border);
        box-shadow: var(--rz-tabs-shadow);
    }

    .rz-tabview-panel {
        padding: 0.5rem;
    }

    .rz-custom-header {
        flex: 1;
    }

    .rz-navigation-menu {
        right: 0;
    }
</style>

<PageTitle>EWA</PageTitle>
<RadzenComponents />

<SessionLock />

<RadzenLayout style="grid-template-areas: 'rz-sidebar rz-header' 'rz-sidebar rz-body';">
    <RadzenHeader Style="min-height:75px">
        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.SpaceBetween" Gap="0" Style="height:100%;" Wrap="FlexWrap.Wrap">

            <RadzenStack Orientation="Orientation.Horizontal">
                @if (Security.User.UserModules.Any(ur => ur.Modules != null && ur.Modules.CODE == "DEV") || Security.User.ISadmin)
                {
                    <RadzenButton class=@GetButtonClass(1) Text="Общие каталоги" Click="@(args => OnApplicationClick("action", 1))" />
                }
                @if (Security.User.UserModules.Any(ur => ur.Modules != null && ur.Modules.CODE == "DWH") || Security.User.ISadmin)
                {
                    <RadzenButton class=@GetButtonClass(5) Text="КХД" Click="@(args => OnApplicationClick("action", 5))" />
                }
                @if (Security.User.UserModules.Any(ur => ur.Modules != null && ur.Modules.CODE == "GC") || Security.User.ISadmin)
                {
                    <RadzenButton class=@GetButtonClass(6) Text="ЕРК" Click="@(args => OnApplicationClick("action", 6))" />
                }
                @if (Security.User.UserModules.Any(ur => ur.Modules != null && ur.Modules.CODE == "AML") || Security.User.ISadmin)
                {
                    <RadzenButton class=@GetButtonClass(2) Text="AML" Click="@(args => OnApplicationClick("action", 2))" />
                }
                @if (Security.User.UserModules.Any(ur => ur.Modules != null && ur.Modules.CODE == "PLANNER") || Security.User.ISadmin)
                {
                    <RadzenButton class=@GetButtonClass(7) Text ="Планировщик" Click="@(args => OnApplicationClick("action", 7))" />
                }
                @if (Security.User.UserModules.Any(ur => ur.Modules != null && ur.Modules.CODE == "LOG") || Security.User.ISadmin)
                {
                    <RadzenButton class=@GetButtonClass(3) Text="Логирование" Click="@(args => OnApplicationClick("action", 3))" />
                } 
                @if (Security.User.UserModules.Any(ur => ur.Modules != null && (ur.Modules.CODE == "Administrator" || ur.Modules.CODE == "ADMIN")) || Security.User.ISadmin)
                {
                    <RadzenButton class=@GetButtonClass(4) Text ="Администрирование" Click="@(args => OnApplicationClick("action", 4))" />
                }

            </RadzenStack>
            <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center">
                <Clock />
                <RadzenAppearanceToggle/>

                <RadzenProfileMenu Visible="@Security.IsAuthenticated()" Click="@OnServiceMenuClick">
                    <ChildContent>
                        <RadzenProfileMenuItem Text="Сервисы" Value="WFLServ" Icon="procedure"/>
                        <RadzenProfileMenuItem Text="Шифрование" Value="Encrpt" Icon="encrypted"/>
                    </ChildContent>
                    <Template>
                        Сервисы
                    </Template>
                </RadzenProfileMenu>

                <RadzenProfileMenu Visible="@Security.IsAuthenticated()" Click="@ProfileMenuClick">
                    <ChildContent>
                        <RadzenProfileMenuItem Text="Профиль" Value="UserCard" Icon="person" />
                        <RadzenProfileMenuItem Text="Выход" Value="Logout" Icon="power_settings_new" />
                    </ChildContent>
                    <Template>
                        <RadzenImage Path="@userPhoto" Style="width: 50px; height: 50px; border-radius: 70%;" />
                        @Security.User.NAME
                    </Template>
                </RadzenProfileMenu>
            </RadzenStack>
        </RadzenStack>
    </RadzenHeader>
    <RadzenSidebar Expanded="@sidebarExpanded" style="z-index: 2; display:flex; flex-direction:column; justify-content:space-between">
        <RadzenStack AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" class="rz-py-4 rz-py-lg-6" Style="height:75px;  " Gap="0.2rem">
            <RadzenImage @onclick="ReloadPage" Path="images/logo.png" style="width: 50%;" />
            <RadzenText Text="ExonWA" TextStyle="Radzen.Blazor.TextStyle.Subtitle1" class="rz-mb-0"  />
        </RadzenStack>
        <RadzenPanelMenu Style="height: 100%; padding-top: 0.5rem; padding-left: 0.25rem">
            <MainTreeMenu TreeType="@aplicstate" />
        </RadzenPanelMenu>
        <RadzenFooter Style="height: 70px; width:100% ">
            <RadzenStack AlignItems="Radzen.AlignItems.Center" Gap="0" Style="padding: var(--rz-panel-menu-item-padding); height: 50px">
                <RadzenText Text="@Configuration["AppInfo:Version"]" TextStyle="Radzen.Blazor.TextStyle.Caption" TagName="Radzen.Blazor.TagName.P" TextAlign="Radzen.TextAlign.Center" />
                <RadzenText Text="Copyright Ⓒ 2025 Exon IT" TextStyle="Radzen.Blazor.TextStyle.Overline" class="rz-mb-0" TagName="Radzen.Blazor.TagName.P" TextAlign="Radzen.TextAlign.End"  />
            </RadzenStack>
        </RadzenFooter>
    </RadzenSidebar>
    <RadzenBody Expanded="@sidebarExpanded">
        <RadzenRow>
           <MainTabs />
        </RadzenRow>
    </RadzenBody>
</RadzenLayout>

@code {
    [Inject] protected IJSRuntime JSRuntime { get; set; }
    [Inject] protected NavigationManager NavigationManager { get; set; }
    [Inject] protected DialogService DialogService { get; set; }
    [Inject] protected TooltipService TooltipService { get; set; }
    [Inject] protected ContextMenuService ContextMenuService { get; set; }
    [Inject] protected NotificationService NotificationService { get; set; }
    [Inject] protected SIBService.SecurityService Security { get; set; }
    [Inject] protected SIBService.UserService UsrServ { get; set; }
    bool sidebarExpanded = false;
    int aplicstate = 0;
    private int? selectedButton = null;
    private bool isFirstRender = true;
    private string userPhoto = "images/person.svg";
    string Val_Tech = string.Empty;

    private string GetButtonClass(int buttonId)
    {
        return buttonId == selectedButton ? "rz-shadow-1 rz-button-selected rz-variant-text rz-base" : "rz-shadow-0 rz-button rz-variant-text rz-base";
    }

    private void OnApplicationClick(string action, int value)
    {

        if (value == aplicstate)
        {
            aplicstate = 0;
            sidebarExpanded = false;
            selectedButton = 0;
        }
        else
        {
            aplicstate = value;
            sidebarExpanded = true;
            selectedButton = value;
        }
    }
    void SidebarToggleClick()
    {
        sidebarExpanded = !sidebarExpanded;
    }
    //private bool isChangePasswordVisible;
    //private bool isUserCardVisible;
    private void ReloadPage()
    {
        NavManager.NavigateTo(NavManager.Uri, true);
    }
    protected void OnServiceMenuClick(RadzenProfileMenuItem args)
    {
        if (Enum.TryParse<SeriveMenuAction>(args.Value, out var action))
        {
            switch (action)
            {
                case SeriveMenuAction.Encrpt:
                    EncService();
                    break;
                case SeriveMenuAction.WFLServ:
                    ViewService();
                    break;
            }
        }
    }

    protected void ProfileMenuClick(RadzenProfileMenuItem args)
    {
        if (Enum.TryParse<ProfileMenuActions>(args.Value, out var action))
        {
            switch (action)
            {
                case ProfileMenuActions.Logout:
                    Security.Logout();
                    break;
                case ProfileMenuActions.UserCard:
                    UCard();
                    break;
            }
        }
    }
    private async Task EncService()
    {
        await _diagEncService.OpenEncryptDialogAsync();
    }

    private async Task UCard()
    {
        await DialogService.OpenAsync<UserCard>("Профиль пользователя", null,
                                new DialogOptions { Draggable = true, Resizable = true }
        );
    }

    protected async Task ChngPwd_Tech()
    {
        var parameters = new Dictionary<string, object>()
        {
            { "user", Security.User },
            { "isTpaswd", true }
        };
        var tpass = await DialogService.OpenAsync<ChangePassword>("Cмена технического пароля", parameters, new DialogOptions { Draggable = true, Resizable = true }
        );
        if (tpass == null)
        {
            await Security.Logout();
        }
    }
    protected async Task ChngPswd()
    {
        bool NeedChngPSWD = false;

        if (!Security.User.ISdomain)
        {
            NeedChngPSWD = await EWAAuthenticateService.GetPswdDt(Security.User.ID);
        }
        if (NeedChngPSWD)
        {
            var parameters = new Dictionary<string, object>()
            {
                { "user", Security.User },
                { "isNeedChng", true }
            };
            var chngPass = await DialogService.OpenAsync<ChangePassword>("Cмена истекшего пароля", parameters, new DialogOptions { Draggable = true, Resizable = true }
            );
            if (chngPass == null)
            {
                await Security.Logout();
            }
        }
    }


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await GetPhoto();        
        if (firstRender)
        {
            await AuthMonitorService.InitializeAsync();
            await SessionTimeout.InitializeSession();
            await AuthMonitorService.setLogin();
            isFirstRender = false;
            if (!Security.User.IStechPSWD && !Security.User.ISdomain)
            {
                await ChngPswd();
            }
            if (Security.User.IStechPSWD && !Security.User.ISdomain)
            {
                await ChngPwd_Tech();
            }
        }
        else
        {
            if (Security.User.IStechPSWD && !Security.User.ISdomain)
            {
                await ChngPwd_Tech();
            }
        }
    }
    private async Task ViewService()
    {
        var options = new DialogOptions()
            {
                Draggable = true,
                Resizable = true
            };
        var parameters = new Dictionary<string, object>();
        var result = await DialogService.OpenAsync<Pages.WRFL.WFLServControl>("Статус сервиса воркфлоу", parameters, options);
    }

    private async Task GetPhoto()
    {
        var prfPhoto = Security.User.PROFILE_PHOTO;
        string oldPhoto = string.Empty;
        oldPhoto = userPhoto;
        if (!string.IsNullOrEmpty(prfPhoto))
        {
            userPhoto = await UsrServ.GetUserPhotoBase64Async(prfPhoto);
        }
        else
        {
            userPhoto = "images/person.svg";
        }
        if (oldPhoto != userPhoto)
            StateHasChanged();
    }
}
