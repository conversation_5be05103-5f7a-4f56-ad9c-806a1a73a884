﻿@page "/tst"
@using Radzen


<RadzenCard>
    <RadzenColumn>
    <RadzenTextBox @oninput=@(args => OnChange(args.Value.ToString())) Style="width: 100%" />
    </RadzenColumn>
    <RadzenColumn>
        <RadzenButton Text="Тест" Click="MTest" Style="margin-bottom: 10px;" />
    </RadzenColumn>
</RadzenCard>
<RadzenCard Style="width: 80%;">
    <RadzenTextArea Value="@sqlo" Style="width: 100%; height: 200px; resize:vertical" ReadOnly="true" />
</RadzenCard>


@code {
    [Inject]
    private EWA.Services.AMLService _amlserv { get; set; }
    private string myval = string.Empty;
    private string res_m = string.Empty;
    private string sqlo = string.Empty;
    void OnChange(string value)
    {
        myval = value;
    }
    private async Task MTest()
    {
        var result = await _amlserv.GetSQLQuery(myval, "04.11.2024", "04.11.2024");

        if (result.Count > 0)
        {
            if (result.TryGetValue(myval, out var value) && value.is_err)
            {
                res_m =  value.mess;
            }
            sqlo = value.sql_out;
            StateHasChanged();
        }
    }
}
