{
  "AppInfo": {
    "Version": "ewa v3.1.34 09.09.2025"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "Kestrel": {
    "Endpoints": {
      "MyHttpEndpoint": {
        "Url": "http://0.0.0.0:8080"
      }
    }
  },
  "AllowedHosts": "*",
  "EWADomainSet": {
    "DomainConf": {
      "DomainCode": "odo115.local",
      "LdapServer": "odo115.local",
	  //"LdapServer": "*************",
      "LdapPort": 389,
      "BaseDN": "DC=odo115,DC=local",
      "Username": "<EMAIL>",
      "Password": "466f/Wp/yW5/TScGGXydtA==",
      "Enc": true
    }
  },
  "EWAConnections": {
    "EWAAML": {
      "UserId": "exo_ewa_rep",
      "Password": "UtbVsrZjI06VO12rGUnqYg==",
      "Enc": true,
      "DBName": "ORCL",
      "HostName": "***************",
      "HostPort": "1521",
      "ConnTimeout": "30"
    },
    "EWADWH": {
      "UserId": "dwh",
      "Password": "+NCOMjbh272zNNp7z8HiZA==",
      "Enc": true,
      "DBName": "smdev",
      "HostName": "*************",
      "HostPort": "11521",
      "ConnTimeout": "30"
    },
    "EWAGC": {
      "UserId": "dwh",
      "Password": "+NCOMjbh272zNNp7z8HiZA==",
      "Enc": true,
      "DBName": "smdev",
      "HostName": "*************",
      "HostPort": "11521",
      "ConnTimeout": "30"
    },
    //обязательное имя коннекта к репозиторию
    "EWAREP": {
      "UserId": "exo_ewa_rep",
      "Password": "UtbVsrZjI06VO12rGUnqYg==",
      "Enc": true,
      "DBName": "ORCL",
      "HostName": "***************",
      "HostPort": "1521",
      "ConnTimeout": "30"
    }
 
  },
  "DetailedErrors": true,
  "SessionSettings": {
    "TimeoutInMinutes": 10
  }
}