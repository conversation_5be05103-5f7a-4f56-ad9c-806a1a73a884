﻿using System.Security.Claims;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using Ra<PERSON>zen;
using EWA.Models;
using static EWA.Models.SIB_Models;
using Oracle.ManagedDataAccess.Client;
using System.Data;
using EWA.Components.Pages.AUTH;
using Microsoft.AspNetCore.Identity;
using System.DirectoryServices;
using System.DirectoryServices.Protocols;
using System.Security.Cryptography;
using System.Text;
using System.Net;
using static EWA.Models.REP_Models;
using EWA.Enums;
using static EWA.Services.SIBService;
using System.Text.RegularExpressions;
using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Wordprocessing;
using Radzen.Blazor.Markdown;
using static EWA.Services.RepService;
using Microsoft.Extensions.FileSystemGlobbing.Internal;

namespace EWA.Services
{
    public class SIBService
    {
        public class ConnectionStringProvider
        {
            private readonly IConfiguration _configuration;
            private readonly AesEncryptionService _encryptionService;

            public ConnectionStringProvider(IConfiguration configuration, AesEncryptionService encryptionService)
            {
                _configuration = configuration;
                _encryptionService = encryptionService;
            }

            public string GetConnectionString(string connectionName)
            {
                string connectionString = string.Empty;
                var section = _configuration.GetSection($"EWAConnections:{connectionName}");
                var settings = section.Get<EWAConnectionSettings>();

                if (settings == null)
                {
                    return null;
                }
                var password = settings.Enc
                    ? _encryptionService.Decrypt(settings.Password)
                    : settings.Password;

                connectionString = $"Data Source={settings.HostName}:{settings.HostPort}/{settings.DBName};" +
                                   $"Connection Timeout={settings.ConnTimeout};" +
                                   $"User Id={settings.UserId};Password={password};";

                return connectionString;
            }
        }
        public class EncrpServHelper
        {
            private readonly DialogService _dialogService;
            public EncrpServHelper(DialogService dialogService)
            {
                _dialogService = dialogService;
            }
            public async Task OpenEncryptDialogAsync()
            {
                var parameters = new Dictionary<string, object>();

                var options = new DialogOptions
                {
                    Draggable = true,
                    Resizable = true
                };
                await _dialogService.OpenAsync<Encrypt>("Шифрование строк", parameters, options);
            }
        }
        public class AesEncryptionService
        {
            static string EWAKey = "SuperPUPER#PRODkey20!#2025Centry"; //32 символа
            static string EWAVec = "!PRODVec10!#2025"; //16 символов

            private readonly byte[] _key = Encoding.UTF8.GetBytes(EWAKey);
            private readonly byte[] _iv = Encoding.UTF8.GetBytes(EWAVec);

            public string Encrypt(string plainText)
            {
                using var aes = Aes.Create();
                aes.Key = _key;
                aes.IV = _iv;

                using var encryptor = aes.CreateEncryptor();
                using var ms = new MemoryStream();
                using var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write);
                using var sw = new StreamWriter(cs);
                sw.Write(plainText);
                sw.Close();

                return Convert.ToBase64String(ms.ToArray());
            }
            public string Decrypt(string cipherText)
            {
                var buffer = Convert.FromBase64String(cipherText);
                using var aes = Aes.Create();
                aes.Key = _key;
                aes.IV = _iv;

                using var decryptor = aes.CreateDecryptor();
                using var ms = new MemoryStream(buffer);
                using var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read);
                using var sr = new StreamReader(cs);
                return sr.ReadToEnd();
            }
        }
        public class EWAAuthenticateService
        {
            string dbname = string.Empty;
            private DBService dbService;
            private readonly RepService _repService;
            private readonly IPasswordHasher<EWA.Models.SIB_Models.SIB_USERS> _PSWDdHasher;
            public EWAAuthenticateService(RepService repService, IPasswordHasher<SIB_USERS> pSWDdHasher)
            {
                _repService = repService;
                _PSWDdHasher = pSWDdHasher;
            }

            public static readonly Dictionary<string, string> MessTemplates = new()
            {
                { "AUTH_INVALID", "Неверный логин или пароль" },
                { "AUTH_TEMP_LOCKED", "Совершено {0} неуспешных входов. Пользователь временно заблокирован. Попробуйте через {1} минут." },
                { "AUTH_USER_LOCKED_ADM", "Пользователь {0} заблокирован. Обратитесь к администратору." },
                { "AUTH_USER_LOCKED", "Пользователь заблокирован. Обратитесь к администратору." },
                { "AUTH_USER_LOCKED_LL", "Пользователь заблокирован. Обратитесь к администратору." },
                { "AUTH_USER_LOCKED_SC", "Пользователь заблокирован. Обратитесь к администратору." },
            };
            public async Task<EWAPswdCfg> GetPswdCfg()
            {
                EWAPswdCfg result = new EWAPswdCfg();
                string sql_query = "select cfg_param,cfg_value from glb_config where upper(cfg_group) = 'SIB' and upper(cfg_code) = 'PSWD'";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(sql_query);
                    if (!string.IsNullOrEmpty(errorMessage))
                        throw new Exception(errorMessage);

                    foreach (var item in items)
                    {
                        string param = item["CFG_PARAM"]?.ToString();
                        string value = item["CFG_VALUE"]?.ToString();

                        switch (param)
                        {
                            //decimal
                            case "MinLength":
                                result.MinLength = TryParseDecimal(value);
                                break;
                            case "MxDActivPswd":
                                result.MxDActivPswd = TryParseDecimal(value);
                                break;
                            case "UniquePswdCnt":
                                result.UniquePswdCnt = TryParseDecimal(value);
                                break;
                            case "HaveUniqueChars":
                                result.HaveUniqueChars = TryParseDecimal(value);
                                break;
                            case "UniquePswdPeriod":
                                result.UniquePswdPeriod = TryParseDecimal(value);
                                break;
                            //bool
                            case "HaveLowerCase":
                                result.HaveLowerCase = value == "true" || value?.ToLower() == "true";
                                break;
                            case "HaveDigit":
                                result.HaveDigit = value == "true" || value?.ToLower() == "true";
                                break;
                            case "HaveNAlphanum":
                                result.HaveNAlphanum = value == "true" || value?.ToLower() == "true";
                                break;
                            case "HaveUpperCase":
                                result.HaveUpperCase = value == "true" || value?.ToLower() == "true";
                                break;
                            //string
                            case "ForbiddenPatterns":
                                result.ForbiddenPatterns = value.ToString();
                                break;
                            case "LstNAlphanum":
                                result.LstNAlphanum = value.ToString();
                                break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetPswdCfg.", ex);
                }
                return result;
            }
            public async Task<List<string>> GetPswdHist(decimal iduser,decimal cntpswd)
            {
                List<string> result = new();
                string sql_query = $"select pswdh from (select passwordhash pswdh from SIB_PASS_HISTORY where id_user = {iduser} order by dt_change desc) where rownum <= {cntpswd}";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(sql_query);
                    if (!string.IsNullOrEmpty(errorMessage))
                        throw new Exception(errorMessage);

                    foreach (var item in items)
                    {
                        if (item.ContainsKey("PSWDH") && item["PSWDH"] != null)
                        {
                            result.Add(item["PSWDH"].ToString());
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetPswdHist.", ex);
                }
                return result;
            }
            public async Task<bool> GetPswdDt(decimal iduser)
            {
                bool result = false;
                string sql_query = $"select nvl(max(dt_change),sysdate) dt from SIB_PASS_HISTORY where id_user = {iduser}";
                DateTime lstDt;
                EWAPswdCfg PSWDcfg = new EWAPswdCfg();
                PSWDcfg = await GetPswdCfg();
                dbname = await _repService.GetConnectionStringAsync("REP");
                if (PSWDcfg.MxDActivPswd > 0)
                {
                    dbService = new DBService(dbname);
                    try
                    {
                        var (items, errorMessage) = await dbService.GetDataSimple(sql_query);
                        if (!string.IsNullOrEmpty(errorMessage))
                            throw new Exception(errorMessage);
                        var row = items.FirstOrDefault();
                        if (row != null && row.ContainsKey("DT") && row["DT"] != null)
                        {
                            lstDt = Convert.ToDateTime(row["DT"]);
                            result = (DateTime.Now - lstDt).TotalDays > (double)PSWDcfg.MxDActivPswd;
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Ошибка GetPswdDt.", ex);
                    }
                }
                return result;
            }

            public async Task<(bool iscorrect, string message)> CheckPassword(SIB_Models.SIB_USERS user,string checkpassword)
            {
                bool iscorrect =true;
                string message = string.Empty;
                int cnt_err = 0;
                EWAPswdCfg PSWDcfg = new EWAPswdCfg();
                PSWDcfg = await GetPswdCfg();
                string PSWDhash = _PSWDdHasher.HashPassword(user,checkpassword);

                if (string.IsNullOrEmpty(checkpassword))
                {
                    message = "Пароль не может быть пустым.";
                    cnt_err++;
                }
                if (checkpassword.Length < PSWDcfg.MinLength)
                {
                    message += $"Пароль должен содержать минимум {PSWDcfg.MinLength} символов.{Environment.NewLine}";
                    cnt_err++;
                }
                if (PSWDcfg.HaveDigit && !checkpassword.Any(char.IsDigit))
                {
                    message += "Пароль должен содержать хотя бы одну цифру." + Environment.NewLine;
                    cnt_err++;
                }
                if (PSWDcfg.HaveNAlphanum)
                {
                    if (string.IsNullOrEmpty(PSWDcfg.LstNAlphanum))
                    {
                        if (!checkpassword.Any(ch => !char.IsLetterOrDigit(ch)))
                        {
                            message += "Пароль должен содержать хотя бы один спецсимвол." + Environment.NewLine;
                            cnt_err++;
                        }
                    }
                    else
                    {
                        if (!checkpassword.Any(ch => PSWDcfg.LstNAlphanum.Contains(ch)))
                        {
                            message += $"Пароль должен содержать хотя бы один из символов: {PSWDcfg.LstNAlphanum}" + Environment.NewLine;
                            cnt_err++;
                        }
                    }
                }
                if (PSWDcfg.HaveUpperCase && !checkpassword.Any(char.IsUpper))
                {
                    message += "Пароль должен содержать хотя бы одну заглавную букву." + Environment.NewLine;
                    cnt_err++;
                }
                if (PSWDcfg.HaveLowerCase && !checkpassword.Any(char.IsLower))
                {
                    message += "Пароль должен содержать хотя бы одну строчную букву." + Environment.NewLine;
                    cnt_err++;
                }
                if (PSWDcfg.HaveUniqueChars > 0)
                {
                    var uniqueCount = checkpassword.Distinct().Count();
                    if (uniqueCount < PSWDcfg.HaveUniqueChars)
                    {
                        message += $"Пароль должен содержать минимум {PSWDcfg.HaveUniqueChars} уникальных символов.{Environment.NewLine}";
                        cnt_err++;
                    }
                }
                if (!string.IsNullOrEmpty(PSWDcfg.ForbiddenPatterns))
                {
                    var patterns = PSWDcfg.ForbiddenPatterns.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    foreach (var pattern in patterns)
                    {
                        if (checkpassword.IndexOf(pattern, StringComparison.OrdinalIgnoreCase) >= 0)
                        {
                            message += $"Пароль не должен содержать запрещённое слово/шаблон: {pattern}{Environment.NewLine}";
                            cnt_err++;
                            break;
                        }
                    }
                }

                if (PSWDcfg.UniquePswdCnt > 0)
                {
                    bool PSWDexists = false;
                    var listPSWD = await GetPswdHist(user.ID, PSWDcfg.UniquePswdCnt);
                    
                    foreach (var oldpswd in listPSWD)
                    {
                        var result = _PSWDdHasher.VerifyHashedPassword(user, oldpswd, checkpassword);
                        if (result == PasswordVerificationResult.Success)
                        {
                            message += $"Пароль не должен повторяться {Environment.NewLine}";
                            cnt_err++;
                        }

                    }
                }
                
                if (cnt_err > 0)
                {
                    iscorrect = false;
                }
                return (iscorrect, message);
            }


            private decimal TryParseDecimal(string input)
            {
                return decimal.TryParse(input, out decimal result) ? result : 0;
            }
            public async Task<EWABlckCfg> GetBlckCfg()
            {
                EWABlckCfg result = new EWABlckCfg();
                string sql_query = "select cfg_param,cfg_value from glb_config where cfg_group = 'SIB' and cfg_code = 'BLCK'";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(sql_query);
                    if (!string.IsNullOrEmpty(errorMessage))
                        throw new Exception(errorMessage);

                    foreach (var item in items)
                    {
                        string param = item["CFG_PARAM"]?.ToString();
                        string value = item["CFG_VALUE"]?.ToString();

                        switch (param)
                        {
                            case "LockoutEnabled":
                                result.LockoutEnabled = value == "true" || value?.ToLower() == "true";
                                break;
                            case "TLockOutEnabled":
                                result.TLockOutEnabled = value == "true" || value?.ToLower() == "true";
                                break;
                            case "TLockOutEnd":
                                result.TLockOutEnd = TryParseDecimal(value);
                                break;
                            case "TAccessFailedCount":
                                result.TAccessFailedCount = TryParseDecimal(value);
                                break;
                            case "AccessFailedCount":
                                result.AccessFailedCount = TryParseDecimal(value);
                                break;
                            case "MxDInactiveLL":
                                result.MxDInactiveLL = TryParseDecimal(value);
                                break;
                            case "MxDInactiveSC":
                                result.MxDInactiveSC = TryParseDecimal(value);
                                break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetBlckCfg.", ex);
                }
                return result;
            }
            public async Task<(bool isblock, SIB_USERS user, string message)> AutoBlock(SIB_USERS user)
            {
                bool isblock = false;
                DateTime DTBlock = DateTime.Now;
                string message = null;
                var cfg = await GetBlckCfg();

                decimal BlckLastLogin = (cfg.MxDInactiveLL == 0) ? 0 : cfg.MxDInactiveLL;
                decimal BlckNonLogin = (cfg.MxDInactiveSC == 0) ? 0 : cfg.MxDInactiveSC;
                
                if (user.DT_LAST_LOGIN != null)
                {
                    DateTime lastRelevantDate = (user.DT_LAST_LOGIN ?? DateTime.MinValue) > (user.DT_BLOCK ?? DateTime.MinValue)
                                              ? (user.DT_LAST_LOGIN ?? DateTime.MinValue) : (user.DT_BLOCK ?? DateTime.MinValue);

                    isblock = (DateTime.Now - lastRelevantDate).TotalDays > (double)BlckLastLogin;
                    if (isblock)
                    {
                        DTBlock = user.DT_LAST_LOGIN.Value.AddDays((double)BlckLastLogin);
                        message = "AUTH_USER_LOCKED_LL";
                    }

                }
                else
                {
                    DateTime lastRelevantDate = (user.DT_CREATE) > (user.DT_BLOCK ?? DateTime.MinValue)
                            ? (user.DT_CREATE)
                            : (user.DT_BLOCK ?? DateTime.MinValue);
                    isblock = (DateTime.Now - lastRelevantDate).TotalDays > (double)BlckNonLogin;
                    if (isblock)
                    {
                        DTBlock = lastRelevantDate.AddDays((double)BlckNonLogin);
                        message = "AUTH_USER_LOCKED_SC";
                    }
                }
                if (isblock)
                {
                    user.TYPE_BLOCK = BlockUser.Auto.ToString();
                    user.DT_BLOCK = DTBlock;
                    user.IS_ACTIVE = 0;
                    user.IS_BLOCK = 1;
                }
                else 
                {
                    user.IS_ACTIVE = 1;
                }
                return (isblock, user, message);
            }
            public async Task<(bool ispermit, SIB_USERS user, string message)> ProcessLoginAttempt(SIB_USERS user, bool isPasswordCorrect)
            {
                string message = string.Empty;
                decimal _blcMin = 0;
                bool ispermit = false;
                var cfg = await GetBlckCfg();

                _blcMin = user.DT_BLOCK.HasValue ? (decimal)(DateTime.Now - user.DT_BLOCK.Value).TotalMinutes : 0;
                //проверим темповую блокировку и сбросим если можно
                if (user.TYPE_BLOCK == BlockUser.Temp.ToString() && _blcMin > cfg.TLockOutEnd)
                {
                    user.TYPE_BLOCK = null;
                    user.DT_BLOCK = null;
                    user.IS_BLOCK = 0;
                }
                
                if (!isPasswordCorrect)
                {
                    user.LOGIN_FAIL_COUNT += 1;
                    if (!user.ISblock)
                    {
                        if (cfg.LockoutEnabled)
                        {
                            if (user.LOGIN_FAIL_COUNT >= cfg.AccessFailedCount)
                            {
                                user.IS_BLOCK = 1;
                                user.DT_BLOCK = DateTime.Now;
                                user.TYPE_BLOCK = BlockUser.Auto.ToString();
                                message = string.Format(MessTemplates["AUTH_USER_LOCKED_ADM"], user.CODE);
                                return (ispermit, user, message);
                            }
                        }
                        if (cfg.TLockOutEnabled)
                        {
                            if (user.LOGIN_FAIL_COUNT >= cfg.TAccessFailedCount && (cfg.TAccessFailedCount == 0 || user.LOGIN_FAIL_COUNT % cfg.TAccessFailedCount == 0))
                            {
                                user.IS_BLOCK = 1;
                                user.DT_BLOCK = DateTime.Now;
                                user.TYPE_BLOCK = BlockUser.Temp.ToString();

                                int minutes = (int)cfg.TLockOutEnd;
                                message = string.Format(
                                    MessTemplates["AUTH_TEMP_LOCKED"],
                                    user.LOGIN_FAIL_COUNT,
                                    minutes
                                );
                                return (ispermit, user, message);
                            }
                        }
                        message = string.Format(MessTemplates["AUTH_INVALID"]);
                        return (ispermit, user, message);
                    }
                    if (user.ISblock)
                    {
                        if (user.TYPE_BLOCK == BlockUser.Manual.ToString() || user.TYPE_BLOCK == BlockUser.Auto.ToString())
                        {
                            message = string.Format(MessTemplates["AUTH_USER_LOCKED_ADM"], user.CODE);
                            return (ispermit, user, message);
                        }
                        if (user.TYPE_BLOCK == BlockUser.Temp.ToString())
                        {
                            if (cfg.LockoutEnabled)
                            {
                                if (user.LOGIN_FAIL_COUNT >= cfg.AccessFailedCount)
                                {
                                    user.IS_BLOCK = 1;
                                    user.DT_BLOCK = DateTime.Now;
                                    user.TYPE_BLOCK = BlockUser.Auto.ToString();
                                    message = string.Format(MessTemplates["AUTH_USER_LOCKED_ADM"], user.CODE);
                                    return (ispermit, user, message);
                                }
                            }
                            if (cfg.TLockOutEnabled)
                            {
                                if (user.LOGIN_FAIL_COUNT >= cfg.TAccessFailedCount && (cfg.TAccessFailedCount == 0 || user.LOGIN_FAIL_COUNT % cfg.TAccessFailedCount == 0))
                                {
                                    user.IS_BLOCK = 1;
                                    user.DT_BLOCK = DateTime.Now;
                                    user.TYPE_BLOCK = BlockUser.Temp.ToString();

                                    int minutes = (int)cfg.TLockOutEnd;
                                    if (cfg.TLockOutEnd == 0)
                                    {
                                        message = string.Format(MessTemplates["AUTH_INVALID"]);
                                    }
                                    else
                                    {
                                        message = string.Format(
                                            MessTemplates["AUTH_TEMP_LOCKED"],
                                            user.LOGIN_FAIL_COUNT,
                                            minutes
                                        );
                                    }
                                    return (ispermit, user, message);
                                }
                                else
                                {
                                    int minutes = (int)cfg.TLockOutEnd - (int)_blcMin;
                                    if (cfg.TLockOutEnd == 0)
                                    {
                                        message = string.Format(MessTemplates["AUTH_INVALID"]);
                                    }
                                    else
                                    {
                                        message = string.Format(
                                            MessTemplates["AUTH_TEMP_LOCKED"],
                                            user.LOGIN_FAIL_COUNT,
                                            minutes
                                        );
                                    }
                                    return (ispermit, user, message);
                                }
                            }
                        }
                        message = string.Format(MessTemplates["AUTH_USER_LOCKED"]);
                        return (ispermit, user, message);
                    }
                }
                if (isPasswordCorrect)
                {
                    if (!user.ISblock)
                    {
                        //проверим автоблокировку
                        if(!user.ISadmin)
                        { 
                            var autBlock = await AutoBlock(user);
                            if (autBlock.isblock)
                            {
                                user.IS_ACTIVE = autBlock.user.IS_ACTIVE;
                                user.IS_BLOCK = autBlock.user.IS_BLOCK;
                                user.TYPE_BLOCK = autBlock.user.TYPE_BLOCK;
                                user.DT_BLOCK = autBlock.user.DT_BLOCK;
                                message = string.Format(MessTemplates[autBlock.message], user.CODE);
                                return (ispermit, user, message);
                            }
                        }
                        user.IS_ACTIVE = 1;
                        user.LOGIN_FAIL_COUNT = 0;
                        user.TYPE_BLOCK = null;
                        user.DT_BLOCK = null;
                        user.DT_LAST_LOGIN = DateTime.Now;
                        ispermit = true;
                        return (ispermit, user, message);
                    }
                    if (user.ISblock)
                    {
                        if (user.TYPE_BLOCK == BlockUser.Manual.ToString() || user.TYPE_BLOCK == BlockUser.Auto.ToString())
                        {
                            message = string.Format(MessTemplates["AUTH_USER_LOCKED_ADM"], user.CODE);
                            return (ispermit, user, message);
                        }
                        if (user.TYPE_BLOCK == BlockUser.Temp.ToString())
                        {
                            int minutes = (int)cfg.TLockOutEnd - (int)_blcMin;
                            if (cfg.TLockOutEnd == 0)
                            {
                                message = string.Format(MessTemplates["AUTH_INVALID"]);
                            }
                            else
                            {
                                message = string.Format(
                                    MessTemplates["AUTH_TEMP_LOCKED"],
                                    user.LOGIN_FAIL_COUNT,
                                    minutes
                                );
                            }
                            return (ispermit, user, message);
                        }
                        message = string.Format(MessTemplates["AUTH_USER_LOCKED"]);
                        return (ispermit, user, message);
                    }
                }
                return (ispermit, user, message);
            }
            public async Task<bool> UpdateUserBlckData(SIB_USERS user)
            {
                string sql_query = "UPDATE SIB_USERS set IS_BLOCK = :IN_IS_BLOCK,DT_BLOCK = :IN_DT_BLOCK, TYPE_BLOCK = :IN_TYPEBLC," + Environment.NewLine
                                  + "                 LOGIN_FAIL_COUNT = :IN_LOGINCNT, DT_CHANGE = :CURDATE, USER_CHANGE = :USERCODE where id = :IN_ID";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                Dictionary<string, object> res_data = new Dictionary<string, object>();
                DateTime dtnow = DateTime.Now;
                bool form_res = true;
                string errorMessage = null;

                Dictionary<string, object> outPrm = new Dictionary<string, object>();
                Dictionary<string, Rep_Param> parameters = new(StringComparer.InvariantCultureIgnoreCase);
                parameters.Add("IN_IS_BLOCK", new Rep_Param(1, "DECIMAL", ParameterDirection.Input, 32,
                                                0, user.IS_BLOCK));
                parameters.Add("IN_ID", new Rep_Param(1, "DECIMAL", ParameterDirection.Input, 32,
                                                0, user.ID));
                parameters.Add("IN_LOGINCNT", new Rep_Param(1, "DECIMAL", ParameterDirection.Input, 32,
                                                0, user.LOGIN_FAIL_COUNT));
                parameters.Add("IN_TYPEBLC", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 10,
                                                0, user.TYPE_BLOCK));
                parameters.Add("USERCODE", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100,
                                                0, user.USER_CHANGE));
                parameters.Add("CURDATE", new Rep_Param(1, "DATE", ParameterDirection.Input, 100,
                                                0, dtnow));
                parameters.Add("IN_DT_BLOCK", new Rep_Param(1, "DATE", ParameterDirection.Input, 100,
                                                0, user.DT_BLOCK));
                try
                {
                    var (res, errormes, outparam) = await dbService.ExecuteDataWithOut(sql_query, parameters);
                    form_res = true;

                    if (!string.IsNullOrEmpty(errormes))
                    {
                        form_res = false;
                        errorMessage = errormes;
                    }
                }
                catch (Exception e)
                {
                    form_res = false;
                    errorMessage = e.Message;
                }
                return (form_res);
            }
        }

        public class DomainUserService
        {
            private readonly IConfiguration _configuration;
            private readonly string _connectionString;
            private readonly AesEncryptionService _encryptionService;
            string dbname = string.Empty;
            private DBService dbService;
            private readonly RepService _repService;
            public DomainUserService(RepService repService, string connectionString, IConfiguration configuration, AesEncryptionService encryptionService)
            {
                _repService = repService;
                _connectionString = connectionString;
                _configuration = configuration;
                _encryptionService = encryptionService;
            }
            public EWADomainSettings GetSettings()
            {
                var section = _configuration.GetSection($"EWADomainSet:DomainConf");
                var settings = section.Get<EWADomainSettings>();
                if (settings != null && settings.Enc)
                {
                    settings.Password = _encryptionService.Decrypt(settings.Password);
                }
                return settings;
            }
            private LdapConnection GetLdapConnection()
            {
                var domSet = GetSettings();
                var identifier = new LdapDirectoryIdentifier(domSet.LdapServer, domSet.LdapPort);
                var credential = new NetworkCredential(domSet.Username, domSet.Password);
                var connection = new LdapConnection(identifier, credential, AuthType.Basic);
                connection.SessionOptions.ProtocolVersion = 3;
                connection.Bind();
                return connection;
            }
            public async Task<bool> AuthenticateUser(string userLogin, string password)
            {
                try
                {
                    var domSet = GetSettings();
                    var identifier = new LdapDirectoryIdentifier(domSet.LdapServer, domSet.LdapPort);
                    var userCredential = new NetworkCredential($"{userLogin}@{domSet.DomainCode}", password);
                    using var connection = new LdapConnection(identifier, userCredential, AuthType.Basic);
                    connection.SessionOptions.ProtocolVersion = 3;
                    connection.Bind();
                    return true;
                }
                catch
                {
                    return false;
                }
            }
            string ExtractGroupName(string distinguishedName)
            {
                if (string.IsNullOrEmpty(distinguishedName))
                    return string.Empty;

                var parts = distinguishedName.Split(',');
                foreach (var part in parts)
                {
                    if (part.StartsWith("CN=", StringComparison.OrdinalIgnoreCase))
                    {
                        return part.Substring(3);
                    }
                }
                return distinguishedName;
            }
            public async Task<EWADomainUser> GetUserInfoAD(string userLogin)
            {
                EWADomainUser user = new EWADomainUser();
                EWADomainGrop usergrp = new EWADomainGrop();
                List<EWADomainGrop> usergroup = new List<EWADomainGrop>();

                var domSet = GetSettings();
                var connection = GetLdapConnection();
                string filter = $"(sAMAccountName={userLogin})";
                var request = new SearchRequest(
                    domSet.BaseDN,
                    filter,
                    System.DirectoryServices.Protocols.SearchScope.Subtree,
                    new[] { "memberOf", "sAMAccountName", "displayName", "userPrincipalName" }
                );

                var response = (SearchResponse)connection.SendRequest(request);
                if (response.Entries.Count == 0) return user;

                var entry = response.Entries[0];

                user.ADUserName = entry.Attributes["displayName"]?[0]?.ToString();
                user.ADLogin = entry.Attributes["sAMAccountName"]?[0]?.ToString();
                user.ADFullLogin = entry.Attributes["userPrincipalName"]?[0]?.ToString();
                usergroup.Clear();
                if (entry.Attributes["memberOf"] != null)
                {
                    foreach (var group in entry.Attributes["memberOf"])
                    {
                        usergrp = new EWADomainGrop();
                        string groupDn = group is byte[] bytes
                            ? Encoding.UTF8.GetString(bytes)
                            : group.ToString();
                        usergrp.ADGroupName = ExtractGroupName(groupDn);
                        usergroup.Add(usergrp);
                    }
                }
                var ewa_group = await GetGroupRep();
                var result = (from ug in usergroup
                              join eg in ewa_group
                              on ug.CodeGroupNormal
                                 equals eg.CodeGroupNormal
                              select ug).Any();
                if (result)
                {
                    user.ADUserGroups = usergroup;
                }
                else
                {
                    user = new EWADomainUser();
                }
                return user;
            }
            public async Task<List<SIBDomainGroupInfo>> GetGroupRep()
            {
                var result = new List<SIBDomainGroupInfo>();
                string sql_query = "select CODE as CodeGroup, NAME as NameGroup from sib_ad_spr where type_Attr = 'GROUP'";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(sql_query);
                    result = items.Select(item => new SIBDomainGroupInfo
                    {
                        CodeGroup = item.ContainsKey("CODEGROUP") ? item["CODEGROUP"]?.ToString() : null,
                        NameGroup = item.ContainsKey("NAMEGROUP") ? item["NAMEGROUP"]?.ToString() : null
                    }).ToList();
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetGroupRep.", ex);
                }
                return result;
            }

            public async Task<List<SIBDomainGroupAppGroup>> GetGroupRepAss()
            {
                var result = new List<SIBDomainGroupAppGroup>();
                string sql_query = "select sa.CODE as CodeGroup, sr.code as CodeSIB, 'Role' as TypeSib" + Environment.NewLine
                                  + "  from sib_ad_spr sa " + Environment.NewLine
                                  + "  join sib_ad_role_lnk sl on sl.id_ad = sa.id " + Environment.NewLine
                                  + "  join sib_roles sr on sr.id = sl.id_role " + Environment.NewLine
                                  + " where sa.type_Attr = 'GROUP'  union " + Environment.NewLine
                                  + "select sa.CODE as CodeGroup, sm.code as CodeSIB, 'Module' as TypeSib " + Environment.NewLine
                                  + "  from sib_ad_spr sa " + Environment.NewLine
                                  + "  join sib_ad_module_lnk sl on sl.id_ad = sa.id " + Environment.NewLine
                                  + "  join sib_modules sm on sm.id = sl.id_module " + Environment.NewLine
                                  + " where sa.type_Attr = 'GROUP'";

                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(sql_query);
                    result = items.Select(item => new SIBDomainGroupAppGroup
                    {
                        CodeGroup = item.ContainsKey("CODEGROUP") ? item["CODEGROUP"]?.ToString() : null,
                        CodeSIB = item.ContainsKey("CODESIB") ? item["CODESIB"]?.ToString() : null,
                        TypeSIB = item.ContainsKey("TYPESIB") ? item["TYPESIB"]?.ToString() : null
                    }).ToList();
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetGroupRepAss.", ex);
                }
                return result;
            }
            public async Task<SIB_USERS> GetUserByCodeSib(string usercode)
            {
                SIB_USERS user = null;

                using (var connection = new OracleConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new OracleCommand(@"SELECT ID,USER_CODE,USER_NAME,DT_CREATE,EXT_LOGIN,
                                                                     POSITION,DEPARTMENT,PHONE,EMAIL,
                                                                    DT_CHANGE, USER_CREATE, USER_CHANGE, IS_ACTIVE, IS_TECHN_PASS  
                                                               FROM SIB_USERS 
                                                              WHERE upper(USER_CODE) = upper(:UserCode)", connection))
                    {
                        command.BindByName = true;
                        command.Parameters.Add(new OracleParameter("UserCode", OracleDbType.Varchar2) { Value = usercode });

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                user = new SIB_USERS
                                {
                                    ID = reader.GetDecimal(reader.GetOrdinal("ID")),
                                    CODE = reader.GetString(reader.GetOrdinal("USER_CODE")),
                                    NAME = !reader.IsDBNull(reader.GetOrdinal("USER_NAME")) ? reader.GetString(reader.GetOrdinal("USER_NAME")) : null,
                                    DT_CREATE = reader.GetDateTime(reader.GetOrdinal("DT_CREATE")),
                                    POSITION = !reader.IsDBNull(reader.GetOrdinal("POSITION")) ? reader.GetString(reader.GetOrdinal("POSITION")) : null,
                                    DEPARTMENT = !reader.IsDBNull(reader.GetOrdinal("DEPARTMENT")) ? reader.GetString(reader.GetOrdinal("DEPARTMENT")) : null,
                                    PHONE = !reader.IsDBNull(reader.GetOrdinal("PHONE")) ? reader.GetString(reader.GetOrdinal("PHONE")) : null,
                                    EMAIL = !reader.IsDBNull(reader.GetOrdinal("EMAIL")) ? reader.GetString(reader.GetOrdinal("EMAIL")) : null,
                                    DT_CHANGE = reader.GetDateTime(reader.GetOrdinal("DT_CHANGE")),
                                    USER_CREATE = reader.GetString(reader.GetOrdinal("USER_CREATE")),
                                    USER_CHANGE = reader.GetString(reader.GetOrdinal("USER_CHANGE")),
                                    IS_ACTIVE = reader.GetDecimal(reader.GetOrdinal("IS_ACTIVE")),
                                    IS_TECHN_PASS = reader.GetDecimal(reader.GetOrdinal("IS_TECHN_PASS")),
                                    EXT_LOGIN = reader.GetString(reader.GetOrdinal("EXT_LOGIN"))
                                };
                            }
                        }
                    }
                }

                return user;
            }
            public async Task<IEnumerable<string>> GetUserSib()
            {
                var result = new List<string>();
                string sql_query = "select upper(EXT_LOGIN) as EXT_LOGIN from sib_users s where s.ext_login is not null ";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(sql_query);
                    result = items.Select(item => item.TryGetValue("EXT_LOGIN", out var val)
                                                       ? val?.ToString() : null)
                                  .Where(v => !string.IsNullOrWhiteSpace(v))
                                  .ToList();
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetUserSib.", ex);
                }
                return result;
            }
            private string EscapeLdapSearchFilter(string input)
            {
                return input
                    .Replace(@"\", @"\5c")
                    .Replace("*", @"\2a")
                    .Replace("(", @"\28")
                    .Replace(")", @"\29")
                    .Replace("\0", @"\00");
            }


            public async Task<List<SIBDomainUserInfo>> GetAllUsersAD(string? userCode = null, string? userName = null)
            {
                List<SIBDomainUserInfo> users = new List<SIBDomainUserInfo>();
                EWADomainUser user = new EWADomainUser();
                EWADomainGrop usergrp = new EWADomainGrop();
                List<EWADomainGrop> usergroup = new List<EWADomainGrop>();

                try
                {
                    var ewa_group = await GetGroupRep();
                    var domSet = GetSettings();
                    var connection = GetLdapConnection();
                    string filter = "";
                    var filters = new List<string>();

                    if (!string.IsNullOrWhiteSpace(userCode))
                        filters.Add($"(sAMAccountName=*{userCode}*)");

                    if (!string.IsNullOrWhiteSpace(userName))
                        filters.Add($"(displayName=*{userName}*)");
                    if (filters.Count > 0)
                    {
                        filter = "(&(|" + string.Join("", filters) + ")(memberOf=*))";
                    }
                    else
                    {
                        filter = "(&(sAMAccountName=*)(memberOf=*))";
                    }

                    var request = new SearchRequest(
                        domSet.BaseDN,
                        filter,
                        System.DirectoryServices.Protocols.SearchScope.Subtree,
                        new[] { "memberOf", "sAMAccountName", "displayName", "userPrincipalName", "mail", "department", "title" }
                     );

                    SearchResponse response = (SearchResponse)connection.SendRequest(request);
                    if (response.Entries.Count == 0) return users;
                    foreach (SearchResultEntry entry in response.Entries)
                    {
                        user = new EWADomainUser();
                        user.ADUserName = entry.Attributes["displayName"]?[0]?.ToString();
                        user.ADLogin = entry.Attributes["sAMAccountName"]?[0]?.ToString();
                        user.ADFullLogin = entry.Attributes["userPrincipalName"]?[0]?.ToString();
                        user.ADUserEMail = entry.Attributes["mail"]?[0]?.ToString();
                        user.ADUserDepart = entry.Attributes["department"]?[0]?.ToString();
                        user.ADUserTitle = entry.Attributes["title"]?[0]?.ToString();
                        usergroup.Clear();
                        if (entry.Attributes["memberOf"] != null)
                        {
                            foreach (var group in entry.Attributes["memberOf"])
                            {
                                usergrp = new EWADomainGrop();
                                string groupDn = group is byte[] bytes
                                        ? Encoding.UTF8.GetString(bytes)
                                        : group.ToString();
                                usergrp.ADGroupName = ExtractGroupName(groupDn);
                                usergroup.Add(usergrp);
                            }
                            var result = (from ug in usergroup
                                          join eg in ewa_group
                                            on ug.CodeGroupNormal
                                        equals eg.CodeGroupNormal
                                          select ug).Any();
                            if (result)
                            {
                                user.ADUserGroups = usergroup;
                            }
                            else
                            {
                                user = new EWADomainUser();
                            }
                        }
                        if (user != null)
                        {
                            var ADuser = new SIBDomainUserInfo
                            {
                                Username = user.ADLogin,
                                DisplayName = user.ADUserName,
                                Email = user.ADUserEMail,
                                Department = user.ADUserDepart,
                                Title = user.ADUserTitle,
                                Domain = domSet.DomainCode,
                                LstGroup = user.ADUserGroups.Select(g => new SIBDomainGroupInfo
                                {
                                    CodeGroup = g.ADGroupName,
                                    NameGroup = g.CodeGroupNormal
                                }).ToList()
                            };
                            users.Add(ADuser);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Ошибка при получении данных AD: {ex.Message}");
                    return users;
                }
                return users;
            }

            private string GetProperty(SearchResult result, string property)
            {
                if (result.Properties.Contains(property))
                    return result.Properties[property][0]?.ToString();
                return string.Empty;
            }


            private Dictionary<string, string> GetUserGroupsWithSamAccountNames(SearchResult result)
            {
                var groups = new Dictionary<string, string>();
                if (!result.Properties.Contains("memberOf"))
                    return groups;

                foreach (var groupDnObj in result.Properties["memberOf"])
                {
                    string groupDn = groupDnObj.ToString();
                    var domConf = GetSettings();
                    try
                    {
                        using (DirectoryEntry groupEntry = new DirectoryEntry($"LDAP://{domConf.LdapServer}/{groupDn}", domConf.Username, domConf.Password, AuthenticationTypes.Secure))
                        {
                            groupEntry.RefreshCache(new[] { "sAMAccountName", "cn" });

                            var sam = groupEntry.Properties["sAMAccountName"]?.Value?.ToString();
                            var cn = groupEntry.Properties["cn"]?.Value?.ToString();

                            if (!string.IsNullOrWhiteSpace(sam) && !string.IsNullOrWhiteSpace(cn))
                            {
                                groups[sam] = cn;
                            }
                            else
                            {
                                Console.WriteLine($"Пустой sAMAccountName или cn для DN: {groupDn}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Ошибка при получении группы '{groupDn}': {ex.Message}");
                    }
                }

                return groups;
            }
            public async Task<(bool form_res, string errorMessage)> CreateUserAD(SIB_USERS user)
            {
                bool form_res = true;
                string errorMessage = null;
                using (var connection = new OracleConnection(_connectionString))
                {
                    try
                    {
                        await connection.OpenAsync();
                        using (var command = new OracleCommand(@"INSERT INTO SIB_USERS (ID
                                                                                  , CODE
                                                                                  , NAME
                                                                                  , DT_CREATE
                                                                                  , POSITION
                                                                                  , DEPARTMENT
                                                                                  , PHONE
                                                                                  , EMAIL
                                                                                  , DT_CHANGE
                                                                                  , USER_CREATE
                                                                                  , USER_CHANGE
                                                                                  , IS_ACTIVE 
                                                                                  , IS_TECHN_PASS
                                                                                  , EXT_LOGIN
                                                                                  , EXT_DOMAIN)       
                                                                           VALUES (:UserId
                                                                                 , :UserCode
                                                                                 , :UserName
                                                                                 , :CreateDt
                                                                                 , :Position
                                                                                 , :Department
                                                                                 , :Phone
                                                                                 , :Email
                                                                                 , :ChangeDt
                                                                                 , :UserCreate
                                                                                 , :UserChange
                                                                                 , :IsActive
                                                                                 , :TechPass
                                                                                 , :ExtLogin
                                                                                 , :ExtDomain)"
                                                                                    , connection))
                        {
                            command.BindByName = true;
                            command.Parameters.Add(new OracleParameter("UserId", OracleDbType.Decimal) { Value = user.ID });
                            command.Parameters.Add(new OracleParameter("UserCode", OracleDbType.Varchar2) { Value = user.CODE });
                            command.Parameters.Add(new OracleParameter("UserName", OracleDbType.Varchar2) { Value = user.NAME });
                            command.Parameters.Add(new OracleParameter("CreateDt", OracleDbType.Date) { Value = user.DT_CREATE });
                            command.Parameters.Add(new OracleParameter("Position", OracleDbType.Varchar2) { Value = user.POSITION });
                            command.Parameters.Add(new OracleParameter("Department", OracleDbType.Varchar2) { Value = user.DEPARTMENT });
                            command.Parameters.Add(new OracleParameter("Phone", OracleDbType.Varchar2) { Value = user.PHONE });
                            command.Parameters.Add(new OracleParameter("Email", OracleDbType.Varchar2) { Value = user.EMAIL });
                            command.Parameters.Add(new OracleParameter("ChangeDt", OracleDbType.Date) { Value = DateTime.Now });
                            command.Parameters.Add(new OracleParameter("UserCreate", OracleDbType.Varchar2) { Value = user.USER_CREATE });
                            command.Parameters.Add(new OracleParameter("UserChange", OracleDbType.Varchar2) { Value = user.USER_CHANGE });
                            command.Parameters.Add(new OracleParameter("ExtLogin", OracleDbType.Varchar2) { Value = user.EXT_LOGIN });
                            command.Parameters.Add(new OracleParameter("ExtDomain", OracleDbType.Varchar2) { Value = user.EXT_DOMAIN });
                            command.Parameters.Add(new OracleParameter("IsActive", OracleDbType.Int32) { Value = user.IS_ACTIVE });
                            command.Parameters.Add(new OracleParameter("TechPass", OracleDbType.Int32) { Value = user.IS_TECHN_PASS });


                            await command.ExecuteNonQueryAsync();
                        }

                        foreach (var userRole in user.UserRoles)
                        {
                            using (var command = new OracleCommand(@"insert into SIB_USERROLES_LNK(ID_USER,ID_ROLE,DT_CHANGE,USER_CHANGE)
                                                    VALUES(:UserId, :RoleId, :DtChange, :UserChange)", connection))
                            {
                                command.BindByName = true;
                                command.Parameters.Add(new OracleParameter("UserId", OracleDbType.Varchar2) { Value = userRole.ID_USER });
                                command.Parameters.Add(new OracleParameter("RoleId", OracleDbType.Varchar2) { Value = userRole.ID_ROLE });
                                command.Parameters.Add(new OracleParameter("DtChange", OracleDbType.Date) { Value = userRole.DT_CHANGE });
                                command.Parameters.Add(new OracleParameter("UserChange", OracleDbType.Varchar2) { Value = userRole.USER_CHANGE });

                                await command.ExecuteNonQueryAsync();
                            }
                        }
                        foreach (var userModule in user.UserModules)
                        {
                            using (var command = new OracleCommand(@"insert into SIB_USERMODULES_LNK(ID_USER,ID_MODULE,DT_CHANGE,USER_CHANGE)
                                                    VALUES(:UserId, :ModuleId, :DtChange, :UserChange)", connection))
                            {
                                command.BindByName = true;
                                command.Parameters.Add(new OracleParameter("UserId", OracleDbType.Varchar2) { Value = userModule.ID_USER });
                                command.Parameters.Add(new OracleParameter("ModuleId", OracleDbType.Varchar2) { Value = userModule.ID_MODULE });
                                command.Parameters.Add(new OracleParameter("DtChange", OracleDbType.Date) { Value = userModule.DT_CHANGE });
                                command.Parameters.Add(new OracleParameter("UserChange", OracleDbType.Varchar2) { Value = userModule.USER_CHANGE });

                                await command.ExecuteNonQueryAsync();
                            }
                        }
                        return (form_res, errorMessage);
                    }
                    catch (OracleException ex)
                    {
                        errorMessage = $"Oracle Error: {ex.Message}";
                        form_res = false;
                        return (form_res, errorMessage);
                    }
                    catch (Exception ex)
                    {
                        errorMessage = $"General Error: {ex.Message}";
                        form_res = false;
                        return (form_res, errorMessage);
                    }
                }
            }
            public async Task<(bool form_res, string errorMessage)> UpdateUserAD(SIB_USERS user)
            {
                bool form_res = true;
                string errorMessage = null;

                using (var connection = new OracleConnection(_connectionString))
                {

                    try
                    {
                        await connection.OpenAsync();

                        using (var command = new OracleCommand(@"UPDATE SIB_USERS 
                                                                SET USER_NAME = :IN_USER_NAME, DT_CREATE = :IN_CREATE_DT, 
                                                                    POSITION = :IN_USER_POSITION, DEPARTMENT = :IN_USER_DEPARTMENT, 
                                                                    PHONE = :IN_USER_PHONE, EMAIL = :IN_USER_EMAIL, DT_CHANGE = :IN_CHANGE_DT, 
                                                                    USER_CREATE = :IN_CREATE_USER, USER_CHANGE = :IN_CHANGE_USER, 
                                                                    IS_ACTIVE = :IN_IS_ACTIVE, IS_TECHN_PASS = :IN_IS_TECHN_PASS, EXT_LOGIN = :IN_EXT_LOGIN
                                                              WHERE ID = :IN_USER_ID", connection))
                        {
                            command.BindByName = true;
                            command.Parameters.Add(new OracleParameter("IN_USER_ID", OracleDbType.Decimal) { Value = user.ID });
                            command.Parameters.Add(new OracleParameter("IN_USER_NAME", OracleDbType.Varchar2) { Value = user.NAME });
                            command.Parameters.Add(new OracleParameter("IN_CREATE_DT", OracleDbType.Date) { Value = user.DT_CREATE });
                            command.Parameters.Add(new OracleParameter("IN_USER_POSITION", OracleDbType.Varchar2) { Value = user.POSITION });
                            command.Parameters.Add(new OracleParameter("IN_USER_DEPARTMENT", OracleDbType.Varchar2) { Value = user.DEPARTMENT });
                            command.Parameters.Add(new OracleParameter("IN_USER_PHONE", OracleDbType.Varchar2) { Value = user.PHONE });
                            command.Parameters.Add(new OracleParameter("IN_USER_EMAIL", OracleDbType.Varchar2) { Value = user.EMAIL });
                            command.Parameters.Add(new OracleParameter("IN_CHANGE_DT", OracleDbType.Date) { Value = user.DT_CHANGE });
                            command.Parameters.Add(new OracleParameter("IN_CREATE_USER", OracleDbType.Varchar2) { Value = user.USER_CREATE });
                            command.Parameters.Add(new OracleParameter("IN_CHANGE_USER", OracleDbType.Varchar2) { Value = user.USER_CHANGE });
                            command.Parameters.Add(new OracleParameter("IN_IS_ACTIVE", OracleDbType.Double) { Value = user.IS_ACTIVE });
                            command.Parameters.Add(new OracleParameter("IN_IS_TECHN_PASS", OracleDbType.Double) { Value = user.IS_TECHN_PASS });
                            command.Parameters.Add(new OracleParameter("IN_EXT_LOGIN", OracleDbType.Varchar2) { Value = user.EXT_LOGIN });
                            await command.ExecuteNonQueryAsync();


                        }
                    }
                    catch (OracleException ex)
                    {
                        errorMessage = $"Oracle Error: {ex.Message}";
                        form_res = false;
                    }
                    catch (Exception ex)
                    {
                        errorMessage = $"General Error: {ex.Message}";
                        form_res = false;
                    }
                }
                return (form_res, errorMessage);
            }

        }
        public class RulesService
        {
            private readonly string _connectionString;
            string dbname = string.Empty;
            private DBService dbService;
            private readonly RepService _repService;

            public RulesService(string connectionString, RepService repService)
            {
                _connectionString = connectionString;
                _repService = repService;
            }
            public async Task<List<RuleAtrSpr>> GetAllAttrSPR(DBService dBService)
            {
                List<RuleAtrSpr> result = new();
                string l_err = string.Empty;
                string query = "SELECT TYPE_ATTR||'_'||UPPER(code) as Code, TYPE_ATTR as TypeCode FROM SIB_ATTR_SPR";
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(query);
                    result = items.Select(item => new RuleAtrSpr
                    {
                        Code = item.ContainsKey("CODE") ? item["CODE"].ToString() : null,
                        TypeCode = item.ContainsKey("TYPECODE") ? item["TYPECODE"].ToString() : null
                        //Code = item.ContainsKey(nameof(RuleAtrSpr.Code)) ? item[nameof(RuleAtrSpr.Code)].ToString() : null,
                        //TypeCode = item.ContainsKey(nameof(RuleAtrSpr.TypeCode)) ? item[nameof(RuleAtrSpr.TypeCode)].ToString() : null
                    }).ToList();
                }
                catch (Exception ex)
                {
                    l_err = ex.Message;
                }
                return result;
            }
            public async Task<List<SIB_RULES>> GetAllRules(DBService dbService, decimal? ruleId = null)
            {
                List<SIB_RULES> result = new List<SIB_RULES>();
                string sql_query = "SELECT ID, CODE, UPPER(RULE) as RULE, ACCESS_TYPE,  UPPER(USER_ATTRS) as USER_ATTRS, UPPER(OBJECT_ATTRS) as OBJECT_ATTRS, " + Environment.NewLine
                                  + "       ACTION_USE, ACTION_ADD, ACTION_EDIT, ACTION_DEL " + Environment.NewLine
                                  + "  FROM SIB_RULES WHERE IS_ACTIVE = 1" + Environment.NewLine
                                  + "   AND (:P_RULE_ID IS NULL OR ID = :P_RULE_ID)";
                var parameters = new Dictionary<string, object>();
                parameters.Add("P_RULE_ID", ruleId);
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(sql_query, parameters);
                    result = items.Select(item => new SIB_RULES
                    {
                        ID = item.ContainsKey(nameof(SIB_RULES.ID)) && item[nameof(SIB_RULES.ID)] != null ? Convert.ToDecimal(item[nameof(SIB_RULES.ID)]) : 0,
                        CODE = item.ContainsKey(nameof(SIB_RULES.CODE)) ? item[nameof(SIB_RULES.CODE)].ToString() : null,
                        RULE = item.ContainsKey(nameof(SIB_RULES.RULE)) ? item[nameof(SIB_RULES.RULE)].ToString() : null,
                        ACCESS_TYPE = item.ContainsKey(nameof(SIB_RULES.ACCESS_TYPE)) ? item[nameof(SIB_RULES.ACCESS_TYPE)].ToString() : null,
                        USER_ATTRS = item.ContainsKey(nameof(SIB_RULES.USER_ATTRS)) ? item[nameof(SIB_RULES.USER_ATTRS)].ToString() : null,
                        OBJECT_ATTRS = item.ContainsKey(nameof(SIB_RULES.OBJECT_ATTRS)) ? item[nameof(SIB_RULES.OBJECT_ATTRS)].ToString() : null,
                        ACTION_USE = item.ContainsKey(nameof(SIB_RULES.ACTION_USE)) && item[nameof(SIB_RULES.ACTION_USE)] != null ? Convert.ToInt32(item[nameof(SIB_RULES.ACTION_USE)]) : 0,
                        ACTION_ADD = item.ContainsKey(nameof(SIB_RULES.ACTION_ADD)) && item[nameof(SIB_RULES.ACTION_ADD)] != null ? Convert.ToInt32(item[nameof(SIB_RULES.ACTION_ADD)]) : 0,
                        ACTION_EDIT = item.ContainsKey(nameof(SIB_RULES.ACTION_EDIT)) && item[nameof(SIB_RULES.ACTION_EDIT)] != null ? Convert.ToInt32(item[nameof(SIB_RULES.ACTION_EDIT)]) : 0,
                        ACTION_DEL = item.ContainsKey(nameof(SIB_RULES.ACTION_DEL)) && item[nameof(SIB_RULES.ACTION_DEL)] != null ? Convert.ToInt32(item[nameof(SIB_RULES.ACTION_DEL)]) : 0
                    }).ToList();
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetAllRules.", ex);
                }
                return result;
            }
            public string CreateInsertQuery(RuleModelIns inData)
            {
                string result = "INSERT INTO SIB_POLICY(ID_USER, ID_OBJECT, ID_RULE, ACCESS_TYPE, ACTION_GET, ACTION_ADD, ACTION_EDIT, ACTION_DEL, DT_CHANGE, USER_CHANGE) " + Environment.NewLine
                       + $"SELECT DISTINCT ID_USER, ID_OBJECT, {inData.fRuleId}, NVL('{inData.fAccessType}', 'DENY'), " + Environment.NewLine
                       + $"       CASE WHEN ISrule = 1 THEN {inData.fActionUse} ELSE 0 END ACT_USE, " + Environment.NewLine
                       + $"       CASE WHEN ISrule = 1 THEN {inData.fActionAdd} ELSE 0 END ACT_ADD, " + Environment.NewLine
                       + $"       CASE WHEN ISrule = 1 THEN {inData.fActionEdit} ELSE 0 END ACT_EDIT," + Environment.NewLine
                       + $"       CASE WHEN ISrule = 1 THEN {inData.fActionDel} ELSE 0 END ACT_DEL, " + Environment.NewLine
                       + $"       SYSDATE,'{inData.subjCode}' FROM( " + Environment.NewLine
                       + $"       SELECT CASE WHEN {inData.fRule} THEN 1 ELSE 0 END AS ISrule, r.* FROM( " + Environment.NewLine
                        + "               WITH obj AS (SELECT o.id as ID_OBJECT, UPPER(o.code) AS CODE_OBJECT, " + Environment.NewLine
                        + "                                   'O_' || UPPER(s.code) AS CODE_ATTR_OBJ, " + Environment.NewLine
                        + "                                   NVL(UPPER(oa.value_attr), ' ') AS VALUE_OBJECT " + Environment.NewLine
                        + "                              FROM REP_OBJECT o " + Environment.NewLine
                        + "                              JOIN SIB_ATTR_SPR s ON s.type_attr = 'O' " + Environment.NewLine
                       + $"                               AND 'O_' || UPPER(s.code) IN({inData.parsedOAttrsL}) " + Environment.NewLine
                        + "                         LEFT JOIN SIB_OBJECT_ATTR OA ON oa.id_object = o.id AND oa.id_attr = s.id), " + Environment.NewLine
                       + $"              obj_attr  AS (SELECT * FROM obj PIVOT (MAX(VALUE_OBJECT) FOR (CODE_ATTR_OBJ) IN ({inData.parsedObjAttrs}))), " + Environment.NewLine
                        + "                    usr AS (SELECT u.id as ID_USER, UPPER(u.code) AS CODE_USER, " + Environment.NewLine
                        + "                                   'U_' || UPPER(s.code) AS CODE_ATTR_USR," + Environment.NewLine
                        + "                                   NVL(UPPER(ua.value_attr), ' ') AS VALUE_USER" + Environment.NewLine
                        + "                              FROM SIB_USERS U" + Environment.NewLine
                        + "                              JOIN SIB_ATTR_SPR s ON s.type_attr = 'U' " + Environment.NewLine
                       + $"                               AND 'U_' || UPPER(s.code) IN({inData.parsedUAttrsL}) " + Environment.NewLine
                        + "                         LEFT JOIN SIB_USERS_ATTR ua ON ua.id_user = u.id AND ua.id_attr = s.id " + Environment.NewLine
                       + $"                         {(string.IsNullOrEmpty(inData.UserId.ToString()) ? "" : $"WHERE u.id = {inData.UserId}")} ), " + Environment.NewLine
                       + $"               usr_attr AS (SELECT* FROM usr PIVOT (MAX(VALUE_USER) FOR (CODE_ATTR_USR) IN ({inData.parsedUserAttrs}))) " + Environment.NewLine
                       + "               SELECT * FROM OBJ_ATTR o " + Environment.NewLine
                       + "                        JOIN USR_ATTR u ON 1 = 1 ) r " + Environment.NewLine
                       + ") WHERE ISrule = 1";

                return result;
            }
            public string ParseVal(string p_val, string p_split_in, string p_split_out)
            {
                string def_val = "''";
                if (string.IsNullOrEmpty(p_val))
                    return def_val;

                var parsedValues = p_val.Split(p_split_in)
                    .Select(val => $"'{val.Trim()}' {val.Trim()}")
                    .ToArray();

                return string.Join(p_split_out, parsedValues);
            }
            public string Parse_ValOnly(string p_val, string p_split_in, string p_split_out)
            {
                string def_val = "''";
                if (string.IsNullOrEmpty(p_val))
                    return def_val;
                var parsedValues = p_val.Split(new[] { p_split_in }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(val => $"'{val.Trim()}'")
                    .ToArray();

                return string.Join(p_split_out, parsedValues);
            }
            public async Task<(bool res, string errmess)> InsertPolicyAsync(DBService dbService, string userCode, int? subjId = null, int? ruleId = null)
            {
                bool res = false;
                string errmess = string.Empty;
                string query = string.Empty;
                RuleModelIns InsData = new();
                List<SIB_RULES> _allrules = new List<SIB_RULES>();
                List<RuleAtrSpr> _allAttrs = new List<RuleAtrSpr>();
                string parsedObjAttrs = string.Empty;
                string parsedUserAttrs = string.Empty;
                string parsedOAttrsL = string.Empty;
                string parsedUAttrsL = string.Empty;
                Dictionary<string, Rep_Param> parameters = new(StringComparer.InvariantCultureIgnoreCase);
                try
                {
                    _allAttrs = await GetAllAttrSPR(dbService);
                    _allrules = await GetAllRules(dbService, ruleId);
                    if (_allrules.Count > 0)
                    {
                        foreach (var itemrul in _allrules)
                        {
                            parsedOAttrsL = string.Empty;
                            parsedObjAttrs = string.Empty;
                            parsedUAttrsL = string.Empty;
                            parsedUserAttrs = string.Empty;
                            if (string.IsNullOrEmpty(itemrul.USER_ATTRS))
                            {
                                var tmp = string.Join(",", _allAttrs
                                                .Where(attr => attr.TypeCode == "U")
                                                .Select(attr => attr.Code));
                                parsedUserAttrs = ParseVal(tmp, ",", ",");
                                parsedUAttrsL = Parse_ValOnly(tmp, ",", ",");
                            }
                            else
                            {
                                parsedUserAttrs = ParseVal(itemrul.USER_ATTRS, ",", ",");
                                parsedUAttrsL = Parse_ValOnly(itemrul.USER_ATTRS, ",", ",");
                            }
                            if (string.IsNullOrEmpty(itemrul.OBJECT_ATTRS))
                            {
                                var tmp = string.Join(",", _allAttrs
                                                .Where(attr => attr.TypeCode == "O")
                                                .Select(attr => attr.Code));
                                parsedObjAttrs = ParseVal(tmp, ",", ",");
                                parsedOAttrsL = Parse_ValOnly(tmp, ",", ",");
                            }
                            else
                            {
                                parsedObjAttrs = ParseVal(itemrul.OBJECT_ATTRS, ",", ",");
                                parsedOAttrsL = Parse_ValOnly(itemrul.OBJECT_ATTRS, ",", ",");
                            }
                            InsData = new();
                            InsData.fRuleId = itemrul.ID;
                            InsData.fAccessType = itemrul.ACCESS_TYPE;
                            InsData.fActionUse = itemrul.ACTION_USE;
                            InsData.fActionAdd = itemrul.ACTION_ADD;
                            InsData.fActionEdit = itemrul.ACTION_EDIT;
                            InsData.fActionDel = itemrul.ACTION_DEL;
                            InsData.UserId = subjId;
                            InsData.fRule = itemrul.RULE;
                            InsData.parsedOAttrsL = parsedOAttrsL;
                            InsData.parsedObjAttrs = parsedObjAttrs;
                            InsData.parsedUAttrsL = parsedUAttrsL;
                            InsData.parsedUserAttrs = parsedUserAttrs;
                            InsData.subjCode = userCode;
                            query = CreateInsertQuery(InsData);
                            var (isres, errormes, outparam) = await dbService.ExecuteDataWithOut(query, parameters);
                            res = true;
                            if (!string.IsNullOrEmpty(errormes))
                            {
                                res = false;
                                errmess = errormes;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    errmess = ex.Message;
                    res = false;
                }
                return (res, errmess);
            }

            public async Task<(bool res, string errmess)> DeletePolicy(DBService dbService, int? subjId = null, int? ruleId = null)
            {
                string deleteQuery = string.Empty;
                int? id = 0;
                bool res = false;
                string errmess = string.Empty;
                Dictionary<string, Rep_Param> parameters = new(StringComparer.InvariantCultureIgnoreCase);
                try
                {
                    if (subjId.HasValue)
                    {
                        id = subjId;
                        deleteQuery = $"DELETE FROM SIB_POLICY WHERE ID_USER = {id}";
                    }
                    else if (ruleId.HasValue)
                    {
                        id = ruleId;
                        deleteQuery = $"DELETE FROM SIB_POLICY WHERE ID_RULE = {id}";
                    }
                    else
                    {
                        deleteQuery = "TRUNCATE TABLE SIB_POLICY";
                    }
                    var (isres, errormes, outparam) = await dbService.ExecuteDataWithOut(deleteQuery, parameters);
                    res = true;
                    if (!string.IsNullOrEmpty(errormes))
                    {
                        res = false;
                        errmess = errormes;
                    }

                }
                catch (Exception ex)
                {
                    errmess = ex.Message;
                    res = false;
                }
                return (res, errmess);
            }
            public async Task UpdatePolicy(string userCode, string code_tab, Dictionary<string, object> Values)
            {
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);

                int? Id = 0;
                if (code_tab.ToUpper() == "SUSERS" || code_tab.ToUpper() == "SUSERSATTR")
                {
                    if (code_tab.ToUpper() == "SUSERS")
                    {
                        if (Values.TryGetValue("ID", out var value) && value != null && value != DBNull.Value)
                        {
                            Id = Convert.ToInt32(value);
                        }
                    }
                    else if (code_tab.ToUpper() == "SUSERSATTR")
                    {
                        if (Values.TryGetValue("ID_USER", out var value) && value != null && value != DBNull.Value)
                        {
                            Id = Convert.ToInt32(value);
                        }
                    }
                    var del = await DeletePolicy(dbService: dbService, subjId: Id);
                    var ins = await InsertPolicyAsync(dbService: dbService, userCode: userCode, subjId: Id);
                }
                else if (code_tab.ToUpper() == "SRULES")
                {
                    if (Values.Count == 0)
                    {
                        var del = await DeletePolicy(dbService: dbService);
                        var ins = await InsertPolicyAsync(dbService: dbService, userCode);
                    }
                    else
                    {

                        if (Values.TryGetValue("ID", out var value) && value != null && value != DBNull.Value)
                        {
                            Id = Convert.ToInt32(value);
                        }
                        var del = await DeletePolicy(dbService: dbService, ruleId: Id);
                        var ins = await InsertPolicyAsync(dbService: dbService, userCode, ruleId: Id);
                    }
                }
                else if (code_tab.ToUpper() == "SPOLICY")
                {
                    var del = await DeletePolicy(dbService: dbService);
                    var ins = await InsertPolicyAsync(dbService: dbService, userCode);
                }
                else if (code_tab.ToUpper() == "SOBJECT" || code_tab.ToUpper() == "SOBJECTATTR")
                {
                    if (Values.TryGetValue("ID", out var value) && value != null && value != DBNull.Value)
                    {
                        Id = Convert.ToInt32(value);
                    }
                }

            }
            public async Task<List<SIB_POLICY>> GetAllPolicyAsync()
            {
                var data = new List<SIB_POLICY>();

                using (var connection = new OracleConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new OracleCommand(@"select USER_CODE,OBJECT_CODE,RULE_ID,
                                                                  ACCESS_TYPE,ACTION_GET,ACTION_ADD,ACTION_EDIT,ACTION_DEL,
                                                                  DT 
                                                                from SIB_POLICY ", connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                data.Add(new SIB_POLICY
                                {
                                    ID_USER = reader.GetDecimal(reader.GetOrdinal("USER_CODE")),
                                    ID_OBJECT = reader.GetDecimal(reader.GetOrdinal("OBJECT_CODE")),
                                    ID_RULE = reader.GetDecimal(reader.GetOrdinal("RULE_ID")),
                                    ACCESS_TYPE = reader.GetString(reader.GetOrdinal("ACCESS_TYPE")),
                                    ACTION_GET = reader.GetDecimal(reader.GetOrdinal("ACTION_GET")),
                                    ACTION_ADD = reader.GetDecimal(reader.GetOrdinal("ACTION_ADD")),
                                    ACTION_EDIT = reader.GetDecimal(reader.GetOrdinal("ACTION_EDIT")),
                                    ACTION_DEL = reader.GetDecimal(reader.GetOrdinal("ACTION_DEL")),
                                    DT_CHANGE = reader.GetDateTime(reader.GetOrdinal("DT_CHANGE")),
                                    USER_CHANGE = reader.GetString(reader.GetOrdinal("USER_CHANGE")),
                                });
                            }
                        }
                    }
                }
                return data;
            }

        }
        public class UserService
        {
            private readonly string _connectionString;
            string dbname = string.Empty;
            private DBService dbService;
            private readonly RepService _repService;
            private readonly ISequenceService _sequenceService;
            public UserService(string connectionString, RepService repService, ISequenceService seqService)
            {
                _connectionString = connectionString;
                _repService = repService;
                _sequenceService = seqService;
            }
            public async Task<(bool form_res, string errorMessage)> CreateUpdateUser(SIB_USERS user, string query)
            {
                bool form_res = true;
                string errorMessage = null;
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                Dictionary<string, object> outPrm = new Dictionary<string, object>();
                Dictionary<string, Rep_Param> parameters = new(StringComparer.InvariantCultureIgnoreCase);
                parameters.Add("IN_ID", new Rep_Param(1, "DECIMAL", ParameterDirection.Input, 32,
                                                            0, user.ID));
                parameters.Add("IN_CODE", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 30,
                                                            0, user.CODE));
                parameters.Add("IN_NAME", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100,
                                                            0, user.NAME));
                parameters.Add("IN_IS_ADMIN", new Rep_Param(1, "NUMBER", ParameterDirection.Input, 1,
                                                            0, user.IS_ADMIN));
                parameters.Add("IN_POSITION", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100,
                                                            0, user.POSITION));
                parameters.Add("IN_DEPARTMENT", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100,
                                                            0, user.DEPARTMENT));
                parameters.Add("IN_PHONE", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100,
                                                            0, user.PHONE));
                parameters.Add("IN_EMAIL", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100,
                                                            0, user.EMAIL));
                parameters.Add("IN_DT_CREATE", new Rep_Param(1, "DATE", ParameterDirection.Input, 30,
                                                            0, user.DT_CREATE));
                parameters.Add("IN_DT_CHANGE", new Rep_Param(1, "DATE", ParameterDirection.Input, 30,
                                                            0, user.DT_CHANGE));
                parameters.Add("IN_USER_CREATE", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 30,
                                                            0, user.USER_CREATE));
                parameters.Add("IN_USER_CHANGE", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 30,
                                                            0, user.USER_CHANGE));
                parameters.Add("IN_IS_ACTIVE", new Rep_Param(1, "NUMBER", ParameterDirection.Input, 1,
                                                            0, user.IS_ACTIVE));
                parameters.Add("IN_IS_BLOCK", new Rep_Param(1, "NUMBER", ParameterDirection.Input, 1,
                                                            0, user.IS_BLOCK));
                parameters.Add("IN_IS_TECHN_PASS", new Rep_Param(1, "NUMBER", ParameterDirection.Input, 1,
                                                            0, user.IS_TECHN_PASS));
                parameters.Add("IN_TECHN_PASS", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 1000,
                                                            0, user.TECHN_PASS));
                parameters.Add("IN_PASSWORDHASH", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 1000,
                                                            0, user.PASSWORDHASH));
                string roleIds = string.Join(",", user.UserRoles.Select(item => item.ID_ROLE));
                string moduleIds = string.Join(",", user.UserModules.Select(item => item.ID_MODULE));

                parameters.Add("IN_MODULES", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 1000,
                                                            0, moduleIds));
                parameters.Add("IN_ROLES", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 1000,
                                                            0, roleIds));
                try
                {
                    var (res, errormes, outparam) = await dbService.ExecuteDataWithOut(query, parameters);
                    if (!string.IsNullOrEmpty(errormes))
                    {
                        form_res = false;
                        errorMessage = errormes;
                    }

                }
                catch (Exception e)
                {
                    form_res = false;
                    errorMessage = e.Message;
                }
                return (form_res, errorMessage);
            }
            public async Task<(bool form_res, string errorMessage)> CreateUpdateRule(SIB_RULES rule, string query)
            {
                bool form_res = true;
                string errorMessage = null;
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                Dictionary<string, object> outPrm = new Dictionary<string, object>();
                Dictionary<string, Rep_Param> parameters = new(StringComparer.InvariantCultureIgnoreCase);
                parameters.Add("IN_ID", new Rep_Param(1, "NUMBER", ParameterDirection.Input, 32,
                                                0, rule.ID));
                parameters.Add("IN_CODE", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 30,
                                                            0, rule.CODE));
                parameters.Add("IN_RULE", new Rep_Param(1, "CLOB", ParameterDirection.Input, 100,
                                                            0, rule.RULE));
                parameters.Add("IN_RULE_J", new Rep_Param(1, "CLOB", ParameterDirection.Input, 1,
                                                            0, rule.RULE_J));
                parameters.Add("IN_ACCESS_TYPE", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100,
                                                            0, rule.ACCESS_TYPE));
                parameters.Add("IN_DT_CREATE", new Rep_Param(1, "DATE", ParameterDirection.Input, 100,
                                                            0, rule.DT_CREATE));
                parameters.Add("IN_DT_CHANGE", new Rep_Param(1, "DATE", ParameterDirection.Input, 100,
                                                            0, rule.DT_CHANGE));
                parameters.Add("IN_USER_CREATE", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100,
                                                            0, rule.USER_CREATE));
                parameters.Add("IN_USER_CHANGE", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 30,
                                                            0, rule.USER_CHANGE));
                parameters.Add("IN_IS_ACTIVE", new Rep_Param(1, "NUMBER", ParameterDirection.Input, 30,
                                                            0, rule.IS_ACTIVE));
                parameters.Add("IN_DESCRIPTION", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 1000,
                                                            0, rule.DESCRIPTION));
                parameters.Add("IN_USER_ATTRS", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 1000,
                                                            0, rule.USER_ATTRS));
                parameters.Add("IN_OBJECT_ATTRS", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 1000,
                                                            0, rule.OBJECT_ATTRS));
                parameters.Add("IN_ACTION_USE", new Rep_Param(1, "NUMBER", ParameterDirection.Input, 1,
                                                            0, rule.ACTION_USE));
                parameters.Add("IN_ACTION_ADD", new Rep_Param(1, "NUMBER", ParameterDirection.Input, 1,
                                                            0, rule.ACTION_ADD));
                parameters.Add("IN_ACTION_EDIT", new Rep_Param(1, "NUMBER", ParameterDirection.Input, 1,
                                                            0, rule.ACTION_EDIT));
                parameters.Add("IN_ACTION_DEL", new Rep_Param(1, "NUMBER", ParameterDirection.Input, 1,
                                                            0, rule.ACTION_DEL));
                try
                {
                    var (res, errormes, outparam) = await dbService.ExecuteDataWithOut(query, parameters);
                    if (!string.IsNullOrEmpty(errormes))
                    {
                        form_res = false;
                        errorMessage = errormes;
                    }
                }
                catch (Exception e)
                {
                    form_res = false;
                    errorMessage = e.Message;
                }
                return (form_res, errorMessage);
            }
            public async Task<Dictionary<string, (string Value, string tattr)>> GetAttrAsync()
            {
                var result = new Dictionary<string, (string Value, string tattr)>();
                string query = "select TYPE_ATTR||'_'||CODE CODE, NAME VALUE, TYPE_ATTR TATTR from SIB_ATTR_SPR";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(query);
                    if (string.IsNullOrEmpty(errorMessage))
                    {
                        foreach (var row in items)
                        {
                            string key = row["CODE"]?.ToString();
                            string value = row["VALUE"]?.ToString();
                            string tattr = row["TATTR"]?.ToString();

                            if (!string.IsNullOrEmpty(key))
                            {
                                result[key] = (value, tattr);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetAttrAsync.", ex);
                }
                return result;
            }
            public async Task<bool> GetResetStatus()
            {
                bool result = false;
                string query = "select CFG_VALUE from glb_config where upper(CFG_GROUP) = 'SIB' and upper(cfg_code) = 'BLCK' and upper(cfg_param) = 'RESETLOGINFAILCOUNT'";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(query);
                    if (!string.IsNullOrEmpty(errorMessage))
                    {
                        result = false;
                    }
                    else
                    {
                        var row = items.FirstOrDefault();
                        if (row != null && row.ContainsKey("CFG_VALUE"))
                        {
                            string value = row["CFG_VALUE"]?.ToString();
                            result = value == "true" || value?.ToLower() == "true";
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetAttrAsync.", ex);
                }
                return result;
            }

            public async Task<string> GetUserPhotoBase64Async(string photo)
            {//конвертер base64 для отрисовки фото из Clob
                var base64 = photo;
                if (!base64.StartsWith("data:image"))
                {
                    base64 = $"data:image/png;base64,{base64}";
                }
                return base64;
            }
            public async Task<SIB_USERS> GetUserById(decimal userId)
            {
                SIB_USERS result = null;
                string sql_query = "select s.id,s.code,s.name,s.position,s.department,s.phone,s.email from sib_users s where s.id = :in_id";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                var parameters = new Dictionary<string, object>();
                parameters.Add("in_id", userId);

                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(sql_query, parameters);
                    var item = items.FirstOrDefault();
                    if (item != null)
                    {
                        result = new SIB_USERS
                        {
                            ID = item.ContainsKey(nameof(SIB_USERS.ID)) && item[nameof(SIB_USERS.ID)] != null ? Convert.ToDecimal(item[nameof(SIB_USERS.ID)]) : 0,
                            CODE = item.ContainsKey(nameof(SIB_USERS.CODE)) ? item[nameof(SIB_USERS.CODE)].ToString() : null,
                            NAME = item.ContainsKey(nameof(SIB_USERS.NAME)) ? item[nameof(SIB_USERS.NAME)].ToString() : null,
                            POSITION = item.ContainsKey(nameof(SIB_USERS.POSITION)) ? item[nameof(SIB_USERS.POSITION)].ToString() : null,
                            DEPARTMENT = item.ContainsKey(nameof(SIB_USERS.DEPARTMENT)) ? item[nameof(SIB_USERS.DEPARTMENT)].ToString() : null,
                            PHONE = item.ContainsKey(nameof(SIB_USERS.PHONE)) ? item[nameof(SIB_USERS.PHONE)].ToString() : null,
                            EMAIL = item.ContainsKey(nameof(SIB_USERS.EMAIL)) ? item[nameof(SIB_USERS.EMAIL)].ToString() : null
                        };
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetUserById.", ex);
                }
                return result;
            }
            public async Task<SIB_MODULES> GetModuleById(decimal Id)
            {
                SIB_MODULES result = null;
                string sql_query = "select ID, CODE, NAME, DT_CHANGE,USER_CHANGE" + Environment.NewLine
                                  + "  from sib_modules" + Environment.NewLine
                                  + " where ID = :in_id";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                var parameters = new Dictionary<string, object>();
                parameters.Add("in_id", Id);
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(sql_query, parameters);
                    var item = items.FirstOrDefault();
                    if (item != null)
                    {
                        result = new SIB_MODULES
                        {
                            ID = item.ContainsKey(nameof(SIB_MODULES.ID)) && item[nameof(SIB_MODULES.ID)] != null ? Convert.ToDecimal(item[nameof(SIB_USERS.ID)]) : 0,
                            CODE = item.ContainsKey(nameof(SIB_MODULES.CODE)) ? item[nameof(SIB_MODULES.CODE)].ToString() : null,
                            NAME = item.ContainsKey(nameof(SIB_MODULES.NAME)) ? item[nameof(SIB_MODULES.NAME)].ToString() : null,
                            DT_CHANGE = item.ContainsKey(nameof(SIB_MODULES.DT_CHANGE)) && item[nameof(SIB_MODULES.DT_CHANGE)] is DateTime dt ? dt : DateTime.Now,
                            USER_CHANGE = item.ContainsKey(nameof(SIB_MODULES.USER_CHANGE)) ? item[nameof(SIB_MODULES.USER_CHANGE)].ToString() : null
                        };
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetModuleById.", ex);
                }
                return result;
            }
            public async Task<SIB_ROLES> GetRoleById(decimal Id)
            {
                SIB_ROLES result = null;
                string sql_query = "select ID, CODE, NAME, DT_CHANGE,USER_CHANGE" + Environment.NewLine
                                  + "  from sib_roles" + Environment.NewLine
                                  + " where ID = :in_id";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                var parameters = new Dictionary<string, object>();
                parameters.Add("in_id", Id);
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(sql_query, parameters);
                    var item = items.FirstOrDefault();
                    if (item != null)
                    {
                        result = new SIB_ROLES
                        {
                            ID = item.ContainsKey(nameof(SIB_ROLES.ID)) && item[nameof(SIB_ROLES.ID)] != null ? Convert.ToDecimal(item[nameof(SIB_USERS.ID)]) : 0,
                            CODE = item.ContainsKey(nameof(SIB_ROLES.CODE)) ? item[nameof(SIB_ROLES.CODE)].ToString() : null,
                            NAME = item.ContainsKey(nameof(SIB_ROLES.NAME)) ? item[nameof(SIB_ROLES.NAME)].ToString() : null,
                            DT_CHANGE = item.ContainsKey(nameof(SIB_ROLES.DT_CHANGE)) && item[nameof(SIB_ROLES.DT_CHANGE)] is DateTime dt ? dt : DateTime.Now,
                            USER_CHANGE = item.ContainsKey(nameof(SIB_ROLES.USER_CHANGE)) ? item[nameof(SIB_ROLES.USER_CHANGE)].ToString() : null
                        };
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetRoleById.", ex);
                }
                return result;
            }
            public async Task<SIB_RULES> GetRuleById(decimal ruleId)
            {
                SIB_RULES result = null;
                string sql_query = "select ID,CODE,RULE,RULE_J" + Environment.NewLine
                                  + ",ACCESS_TYPE,DT_CREATE,DT_CHANGE,USER_CREATE,USER_CHANGE" + Environment.NewLine
                                  + ",IS_ACTIVE,DESCRIPTION,USER_ATTRS,OBJECT_ATTRS,ACTION_USE" + Environment.NewLine
                                  + ",ACTION_ADD,ACTION_EDIT,ACTION_DEL" + Environment.NewLine
                                  + "from SIB_RULES where ID = :RuleId";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                var parameters = new Dictionary<string, object>();
                parameters.Add("RuleId", ruleId);

                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(sql_query, parameters);
                    var item = items.FirstOrDefault();
                    if (item != null)
                    {
                        result = new SIB_RULES
                        {
                            ID = item.ContainsKey(nameof(SIB_RULES.ID)) && item[nameof(SIB_RULES.ID)] != null ? Convert.ToDecimal(item[nameof(SIB_RULES.ID)]) : 0,
                            CODE = item.ContainsKey(nameof(SIB_RULES.CODE)) ? item[nameof(SIB_RULES.CODE)].ToString() : null,
                            RULE = item.ContainsKey(nameof(SIB_RULES.RULE)) ? item[nameof(SIB_RULES.RULE)].ToString() : null,
                            RULE_J = item.ContainsKey(nameof(SIB_RULES.RULE_J)) ? item[nameof(SIB_RULES.RULE_J)].ToString() : null,
                            ACCESS_TYPE = item.ContainsKey(nameof(SIB_RULES.ACCESS_TYPE)) ? item[nameof(SIB_RULES.ACCESS_TYPE)].ToString() : null,
                            DT_CREATE = item.ContainsKey(nameof(SIB_RULES.DT_CREATE)) && item[nameof(SIB_RULES.DT_CREATE)] != null ? Convert.ToDateTime(item[nameof(SIB_RULES.DT_CREATE)]) : default,
                            DT_CHANGE = item.ContainsKey(nameof(SIB_RULES.DT_CHANGE)) && item[nameof(SIB_RULES.DT_CHANGE)] != null ? Convert.ToDateTime(item[nameof(SIB_RULES.DT_CHANGE)]) : default,
                            USER_CREATE = item.ContainsKey(nameof(SIB_RULES.USER_CREATE)) ? item[nameof(SIB_RULES.USER_CREATE)].ToString() : null,
                            USER_CHANGE = item.ContainsKey(nameof(SIB_RULES.USER_CHANGE)) ? item[nameof(SIB_RULES.USER_CHANGE)].ToString() : null,
                            IS_ACTIVE = item.ContainsKey(nameof(SIB_RULES.IS_ACTIVE)) && item[nameof(SIB_RULES.IS_ACTIVE)] != null ? Convert.ToInt32(item[nameof(SIB_RULES.IS_ACTIVE)]) : 0,
                            DESCRIPTION = item.ContainsKey(nameof(SIB_RULES.DESCRIPTION)) ? item[nameof(SIB_RULES.DESCRIPTION)].ToString() : null,
                            USER_ATTRS = item.ContainsKey(nameof(SIB_RULES.USER_ATTRS)) ? item[nameof(SIB_RULES.USER_ATTRS)].ToString() : null,
                            OBJECT_ATTRS = item.ContainsKey(nameof(SIB_RULES.OBJECT_ATTRS)) ? item[nameof(SIB_RULES.OBJECT_ATTRS)].ToString() : null,
                            ACTION_USE = item.ContainsKey(nameof(SIB_RULES.ACTION_USE)) && item[nameof(SIB_RULES.IS_ACTIVE)] != null ? Convert.ToInt32(item[nameof(SIB_RULES.ACTION_USE)]) : 0,
                            ACTION_ADD = item.ContainsKey(nameof(SIB_RULES.ACTION_ADD)) && item[nameof(SIB_RULES.IS_ACTIVE)] != null ? Convert.ToInt32(item[nameof(SIB_RULES.ACTION_ADD)]) : 0,
                            ACTION_EDIT = item.ContainsKey(nameof(SIB_RULES.ACTION_EDIT)) && item[nameof(SIB_RULES.IS_ACTIVE)] != null ? Convert.ToInt32(item[nameof(SIB_RULES.ACTION_EDIT)]) : 0,
                            ACTION_DEL = item.ContainsKey(nameof(SIB_RULES.ACTION_DEL)) && item[nameof(SIB_RULES.IS_ACTIVE)] != null ? Convert.ToInt32(item[nameof(SIB_RULES.ACTION_DEL)]) : 0
                        };
                    }

                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetRuleById.", ex);
                }
                return result;
            }
            public async Task<List<decimal>> GetUserRolesById(decimal userId)
            {
                var result = new List<decimal>();
                string query = "SELECT ID_ROLE FROM SIB_USERROLES_LNK WHERE ID_USER = :UserId";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);

                var parameters = new Dictionary<string, object>();
                parameters.Add("UserId", userId);
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(query, parameters);
                    result = items.Where(item => item.ContainsKey("ID_ROLE") && item["ID_ROLE"] != null)
                                  .Select(item => Convert.ToDecimal(item["ID_ROLE"]))
                                  .ToList();
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetUserRolesById.", ex);
                }
                return result;
            }
            public async Task<List<decimal>> GetUserModulesById(decimal userId)
            {
                var result = new List<decimal>();
                string query = "SELECT ID_MODULE FROM SIB_USERMODULES_LNK WHERE ID_USER = :UserId";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);

                var parameters = new Dictionary<string, object>();
                parameters.Add("UserId", userId);
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(query, parameters);
                    result = items.Where(item => item.ContainsKey("ID_MODULE") && item["ID_MODULE"] != null)
                                  .Select(item => Convert.ToDecimal(item["ID_MODULE"]))
                                  .ToList();
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetUserModulesById.", ex);
                }
                return result;
            }
            public async Task<List<SIB_MODULES>> GetAllModules()
            {
                List<SIB_MODULES> result = new List<SIB_MODULES>();
                string sql_query = "SELECT ID, CODE, NAME, DT_CHANGE, USER_CHANGE FROM SIB_MODULES";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(sql_query);
                    result = items.Select(item => new SIB_MODULES
                    {
                        ID = item.ContainsKey(nameof(SIB_MODULES.ID)) && item[nameof(SIB_MODULES.ID)] != null ? Convert.ToDecimal(item[nameof(SIB_MODULES.ID)]) : 0,
                        CODE = item.ContainsKey(nameof(SIB_MODULES.CODE)) ? item[nameof(SIB_MODULES.CODE)].ToString() : null,
                        NAME = item.ContainsKey(nameof(SIB_MODULES.NAME)) ? item[nameof(SIB_MODULES.NAME)].ToString() : null,
                        DT_CHANGE = item.ContainsKey(nameof(SIB_MODULES.DT_CHANGE)) && item[nameof(SIB_MODULES.DT_CHANGE)] != null ? Convert.ToDateTime(item[nameof(SIB_MODULES.DT_CHANGE)]) : default,
                        USER_CHANGE = item.ContainsKey(nameof(SIB_MODULES.USER_CHANGE)) ? item[nameof(SIB_MODULES.USER_CHANGE)].ToString() : null
                    }).ToList();
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetAllModules.", ex);
                }
                return result;
            }
            public async Task<List<SIB_ROLES>> GetAllRoles()
            {
                List<SIB_ROLES> result = new List<SIB_ROLES>();
                string sql_query = "SELECT ID, CODE, NAME, DT_CHANGE, USER_CHANGE FROM SIB_ROLES";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(sql_query);
                    result = items.Select(item => new SIB_ROLES
                    {
                        ID = item.ContainsKey(nameof(SIB_ROLES.ID)) && item[nameof(SIB_ROLES.ID)] != null ? Convert.ToDecimal(item[nameof(SIB_ROLES.ID)]) : 0,
                        CODE = item.ContainsKey(nameof(SIB_ROLES.CODE)) ? item[nameof(SIB_ROLES.CODE)].ToString() : null,
                        NAME = item.ContainsKey(nameof(SIB_ROLES.NAME)) ? item[nameof(SIB_ROLES.NAME)].ToString() : null,
                        DT_CHANGE = item.ContainsKey(nameof(SIB_ROLES.DT_CHANGE)) && item[nameof(SIB_ROLES.DT_CHANGE)] != null ? Convert.ToDateTime(item[nameof(SIB_ROLES.DT_CHANGE)]) : default,
                        USER_CHANGE = item.ContainsKey(nameof(SIB_ROLES.USER_CHANGE)) ? item[nameof(SIB_ROLES.USER_CHANGE)].ToString() : null
                    }).ToList();
                }
                catch (Exception ex)
                {
                    throw new Exception("Ошибка GetAllRoles.", ex);
                }
                return result;
            }
            public async Task<bool> UpdateUserTech(SIB_USERS user)
            {
                string sql_query = "UPDATE SIB_USERS set is_techn_pass = 0, techn_pass = null where id = :IN_ID";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                bool form_res = true;
                string errorMessage = null;
                Dictionary<string, object> outPrm = new Dictionary<string, object>();
                Dictionary<string, Rep_Param> parameters = new(StringComparer.InvariantCultureIgnoreCase);
                parameters.Add("IN_ID", new Rep_Param(1, "DECIMAL", ParameterDirection.Input, 32,
                                                0, user.ID));
                try
                {
                    var (res, errormes, outparam) = await dbService.ExecuteDataWithOut(sql_query, parameters);
                    if (!string.IsNullOrEmpty(errormes))
                    {
                        form_res = false;
                        errorMessage = errormes;
                    }
                    form_res = true;
                }
                catch (Exception e)
                {
                    form_res = false;
                    errorMessage = e.Message;
                }
                return (form_res);
            }
            public async Task<bool> UpdatePhotoProfile(SIB_USERS user)
            {
                string sql_query = "update sib_users s set s.profile_photo = :IN_PHOTO,s.dt_change = :IN_DT_CHANGE ,s.user_change = :IN_USER_CODE where s.id = :IN_ID";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                bool form_res = true;
                string errorMessage = null;
                Dictionary<string, object> outPrm = new Dictionary<string, object>();
                Dictionary<string, Rep_Param> parameters = new(StringComparer.InvariantCultureIgnoreCase);
                parameters.Add("IN_ID", new Rep_Param(1, "DECIMAL", ParameterDirection.Input, 32,
                                                0, user.ID));
                parameters.Add("IN_USER_CODE", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 32,
                                                0, user.CODE));
                parameters.Add("IN_DT_CHANGE", new Rep_Param(1, "DATE", ParameterDirection.Input, 32,
                                                0, user.DT_CHANGE));
                parameters.Add("IN_PHOTO", new Rep_Param(1, "CLOB", ParameterDirection.Input, 32,
                                                0, user.PROFILE_PHOTO));
                try
                {
                    var (res, errormes, outparam) = await dbService.ExecuteDataWithOut(sql_query, parameters);
                    if (!string.IsNullOrEmpty(errormes))
                    {
                        form_res = false;
                        errorMessage = errormes;
                    }
                    form_res = true;
                }
                catch (Exception e)
                {
                    form_res = false;
                    errorMessage = e.Message;
                }
                return (form_res);
            }
            public async Task<(bool form_res, Dictionary<string, object> res_data, string errorMessage)> UpdateUserPass(SIB_USERS user)
            {
                string sql_query = "UPDATE SIB_USERS SET " + Environment.NewLine
                                  + "                 PASSWORDHASH = :UserPass," + Environment.NewLine
                                  + "                 DT_CHANGE = :CURDATE," + Environment.NewLine
                                  + "                 USER_CHANGE = :USERCODE," + Environment.NewLine
                                  + "                 IS_TECHN_PASS = 1," + Environment.NewLine
                                  + "                 TECHN_PASS = :TechPSWD," + Environment.NewLine
                                  + "                 LOGIN_FAIL_COUNT = 0" + Environment.NewLine
                                  + " WHERE ID = :UserId";
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                bool form_res = true;
                string errorMessage = null;
                Dictionary<string, object> res_data = new Dictionary<string, object>();

                Dictionary<string, object> outPrm = new Dictionary<string, object>();
                Dictionary<string, Rep_Param> parameters = new(StringComparer.InvariantCultureIgnoreCase);
                parameters.Add("UserId", new Rep_Param(1, "DECIMAL", ParameterDirection.Input, 32,
                                                0, user.ID));
                parameters.Add("TechPSWD", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100,
                                                0, user.TECHN_PASS));
                parameters.Add("UserPass", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100,
                                                0, user.PASSWORDHASH));
                parameters.Add("USERCODE", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100,
                                                0, user.USER_CHANGE));
                parameters.Add("CURDATE", new Rep_Param(1, "DATE", ParameterDirection.Input, 100,
                                                0, user.DT_CHANGE));

                try
                {
                    var (res, errormes, outparam) = await dbService.ExecuteDataWithOut(sql_query, parameters);
                    form_res = true;
                    if (!string.IsNullOrEmpty(errormes))
                    {
                        form_res = false;
                        errorMessage = errormes;
                    }

                    res_data["IS_TECHN_PASS"] = 1;
                    res_data["TECHN_PASS"] = user.TECHN_PASS;
                    res_data["DT_CHANGE"] = user.DT_CHANGE;
                    res_data["USER_CHANGE"] = user.USER_CHANGE;
                }
                catch (Exception e)
                {
                    form_res = false;
                    errorMessage = e.Message;
                }
                return (form_res, res_data, errorMessage);
            }
            public async Task<(bool form_res, Dictionary<string, object> res_data, string errorMessage)> UpdateUserAccess(decimal in_User_Id, decimal in_val, string in_user, BlockUser blockType, bool is_blck)
            {
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                bool rstCNT = await GetResetStatus();
                string resQuery = string.Empty;
                Dictionary<string, object> res_data = new Dictionary<string, object>();
                DateTime dtnow = DateTime.Now;
                bool form_res = true;
                string errorMessage = null;
                decimal is_block = in_val;
                string typeBlck = String.Empty;
                if (is_blck)
                {
                    typeBlck = Enum.GetName(typeof(BlockUser), blockType);
                }
                if (rstCNT && !is_blck)
                {
                    resQuery = ", login_fail_count = 0 ";
                }
                string sql_query = "UPDATE SIB_USERS set IS_BLOCK = :IN_IS_BLOCK,DT_BLOCK = :IN_DT_BLOCK, TYPE_BLOCK = :IN_TYPEBLC," + Environment.NewLine
                                  + $"                 DT_CHANGE = :CURDATE, USER_CHANGE = :USERCODE {resQuery} where id = :IN_ID";


                Dictionary<string, object> outPrm = new Dictionary<string, object>();
                Dictionary<string, Rep_Param> parameters = new(StringComparer.InvariantCultureIgnoreCase);
                parameters.Add("IN_IS_BLOCK", new Rep_Param(1, "DECIMAL", ParameterDirection.Input, 32,
                                                0, is_block));
                parameters.Add("IN_ID", new Rep_Param(1, "DECIMAL", ParameterDirection.Input, 32,
                                                0, in_User_Id));
                parameters.Add("IN_TYPEBLC", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 10,
                                0, typeBlck));
                parameters.Add("USERCODE", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100,
                                                0, in_user));
                parameters.Add("CURDATE", new Rep_Param(1, "DATE", ParameterDirection.Input, 100,
                                                0, dtnow));
                parameters.Add("IN_DT_BLOCK", new Rep_Param(1, "DATE", ParameterDirection.Input, 100,
                                                0, dtnow));
                try
                {
                    var (res, errormes, outparam) = await dbService.ExecuteDataWithOut(sql_query, parameters);
                    form_res = true;

                    if (!string.IsNullOrEmpty(errormes))
                    {
                        form_res = false;
                        errorMessage = errormes;
                    }
                    res_data["IS_BLOCK"] = is_block;
                    res_data["DT_BLOCK"] = dtnow;
                    res_data["TYPE_BLOCK"] = typeBlck;
                    res_data["DT_CHANGE"] = dtnow;
                    res_data["USER_CHANGE"] = in_user;
                    if (rstCNT && !is_blck)
                    {
                        res_data["LOGIN_FAIL_COUNT"] = 0;
                    }
                }
                catch (Exception e)
                {
                    form_res = false;
                    errorMessage = e.Message;
                }
                return (form_res, res_data, errorMessage);
            }
            public async Task<(bool form_res, string errorMessage)> UpdateUserPassHist(SIB_USERS user)
            {
                
                string sql_query = "insert into SIB_PASS_HISTORY(ID, ID_USER, PASSWORDHASH, DT_CHANGE) "+Environment.NewLine
                                  + "select :IN_ID,id,passwordhash,:IN_DT from sib_users  where id = :IN_ID_USER";
                decimal id_row = _sequenceService.GetNextValueAsync("EWA_ROW_SEQ").GetAwaiter().GetResult();
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);
                bool form_res = true;
                string errorMessage = null;

                Dictionary<string, object> outPrm = new Dictionary<string, object>();
                Dictionary<string, Rep_Param> parameters = new(StringComparer.InvariantCultureIgnoreCase);
                parameters.Add("IN_ID", new Rep_Param(1, "DECIMAL", ParameterDirection.Input, 32,
                                                0, id_row));
                parameters.Add("IN_ID_USER", new Rep_Param(1, "DECIMAL", ParameterDirection.Input, 32,
                                                0, user.ID));
                parameters.Add("IN_DT", new Rep_Param(1, "DATE", ParameterDirection.Input, 100,
                                                0, DateTime.Now));
                try
                {
                    var (res, errormes, outparam) = await dbService.ExecuteDataWithOut(sql_query, parameters);
                    form_res = true;
                    if (!string.IsNullOrEmpty(errormes))
                    {
                        form_res = false;
                        errorMessage = errormes;
                    }
                }
                catch (Exception e)
                {
                    form_res = false;
                    errorMessage = e.Message;
                }
                return (form_res, errorMessage);
            }
        }
        public class ApplicationAuthenticationStateProvider : AuthenticationStateProvider
        {
            private readonly SecurityService securityService;
            private EWAAuthenticationState authenticationState;
            //private readonly IServiceProvider _serviceProvider;

            public ApplicationAuthenticationStateProvider(SecurityService securityService/*, IServiceProvider serviceProvider*/)
            {
                this.securityService = securityService;
                //_serviceProvider = serviceProvider;

            }

            public override async Task<AuthenticationState> GetAuthenticationStateAsync()
            {
                var identity = new ClaimsIdentity();

                try
                {
                    var state = await GetApplicationAuthenticationStateAsync();

                    if (state.IsAuthenticated)
                    {
                        identity = new ClaimsIdentity(state.Claims.Select(c => new Claim(c.Type, c.Value)), "EWA");
                    }
                }
                catch (HttpRequestException ex)
                {
                }

                var result = new AuthenticationState(new ClaimsPrincipal(identity));

                await securityService.InitializeAsync(result);

                return result;
            }

            private async Task<EWAAuthenticationState> GetApplicationAuthenticationStateAsync()
            {
                if (authenticationState == null)
                {
                    authenticationState = await securityService.GetAuthenticationStateAsync();
                }

                return authenticationState;
            }
        }

        public partial class SecurityService
        {
            private readonly HttpClient httpClient;
            private readonly Uri baseUri;
            private readonly NavigationManager navigationManager;
            private readonly SessionTimeoutService _sessionTimeout;
            private readonly AuthMonitorService _authMonitorService;
            private readonly UserService _userService;
            private readonly EWAAuthenticateService _ewaAutServ;
            public SIB_Models.SIB_USERS User { get; private set; } = new SIB_Models.SIB_USERS { NAME = "Anonymous" };
            public SIB_Models.SIB_ROLES Role { get; private set; } = new SIB_Models.SIB_ROLES { NAME = "Anonymous" };
            public SIB_Models.SIB_MODULES Module { get; private set; } = new SIB_Models.SIB_MODULES { NAME = "Anonymous" };
            public ClaimsPrincipal Principal { get; private set; }
            public string SessionId { get; private set; }
            public string SessionInfo { get; private set; }


            public SecurityService(UserService userService, NavigationManager navigationManager, IHttpClientFactory factory, SessionTimeoutService sessionTimeout, AuthMonitorService authMonitorService, EWAAuthenticateService ewaAuthenticateService)
            {
                this.baseUri = new Uri($"{navigationManager.BaseUri}odata/Identity/");
                this.httpClient = factory.CreateClient("EWA");
                this.navigationManager = navigationManager;
                _sessionTimeout = sessionTimeout;
                _userService = userService;
                _authMonitorService = authMonitorService;
                _ewaAutServ = ewaAuthenticateService;
        }
            public bool IsInRole(params string[] roles)
            {
                if (roles.Contains("Everybody"))
                {
                    return true;
                }

                if (!IsAuthenticated())
                {
                    return false;
                }

                if (roles.Contains("Authenticated"))
                {
                    return true;
                }

                return roles.Any(role => Principal.IsInRole(role));
            }

            public bool IsAuthenticated()
            {
                return Principal?.Identity.IsAuthenticated == true;
            }
            public async Task GetInfoUser(decimal id)
            {
                string userId = id.ToString();
                var authInfo = await GetAuthenticationStateAsync();
                SessionId = authInfo?.SessionId;
                SessionInfo = authInfo?.SessionInfo;
                var t_user = await GetUserById(userId);
                if (t_user.UserRoles != null && t_user.UserRoles.Count > 0)
                {
                    var roleIds = t_user.UserRoles.Select(ur => ur.ID_ROLE).ToList();
                    var roles = new List<SIB_Models.SIB_ROLES>();
                    foreach (var roleId in roleIds)
                    {
                        var role = await GetRoleById(roleId);
                        roles.Add(role);
                    }
                    if (roles != null)
                    {
                        foreach (var userRole in t_user.UserRoles)
                        {
                            var role = roles.FirstOrDefault(r => r.ID == userRole.ID_ROLE);
                            if (role != null)
                            {
                                userRole.Roles = role;
                            }
                        }
                    }
                }
                if (t_user.UserModules != null && t_user.UserModules.Count > 0)
                {
                    var moduleIds = t_user.UserModules.Select(ur => ur.ID_MODULE).ToList();
                    var modules = new List<SIB_Models.SIB_MODULES>();
                    foreach (var moduleId in moduleIds)
                    {
                        var module = await GetMoudleById(moduleId);
                        modules.Add(module);
                    }
                    if (modules != null)
                    {
                        foreach (var userModule in t_user.UserModules)
                        {
                            var module = modules.FirstOrDefault(r => r.ID == userModule.ID_MODULE);

                            if (module != null)
                            {
                                userModule.Modules = module;
                            }
                        }
                    }
                }
                User = t_user;
                User.SessionId = SessionId;
                User.SessionInfo = SessionInfo;
            }

            public async Task<bool> InitializeAsync(AuthenticationState result)
            {
                Principal = result.User;
                var userId = Principal?.FindFirstValue(ClaimTypes.NameIdentifier);
                var authInfo = await GetAuthenticationStateAsync();
                SessionId = authInfo?.SessionId;
                SessionInfo = authInfo?.SessionInfo;

                if (userId != null && User?.ID.ToString() != userId)
                {
                    var t_user = await GetUserById(userId);
                    if (t_user.UserRoles != null && t_user.UserRoles.Count > 0)
                    {
                        var roleIds = t_user.UserRoles.Select(ur => ur.ID_ROLE).ToList();
                        var roles = new List<SIB_Models.SIB_ROLES>();

                        foreach (var roleId in roleIds)
                        {
                            var role = await GetRoleById(roleId);
                            roles.Add(role);
                        }
                        if (roles != null)
                        {
                            foreach (var userRole in t_user.UserRoles)
                            {
                                var role = roles.FirstOrDefault(r => r.ID == userRole.ID_ROLE);

                                if (role != null)
                                {
                                    userRole.Roles = role;
                                }
                            }
                        }
                    }
                    if (t_user.UserModules != null && t_user.UserModules.Count > 0)
                    {
                        var moduleIds = t_user.UserModules.Select(ur => ur.ID_MODULE).ToList();
                        var modules = new List<SIB_Models.SIB_MODULES>();
                        foreach (var moduleId in moduleIds)
                        {
                            var module = await GetMoudleById(moduleId);
                            modules.Add(module);
                        }
                        if (modules != null)
                        {
                            foreach (var userModule in t_user.UserModules)
                            {
                                var module = modules.FirstOrDefault(r => r.ID == userModule.ID_MODULE);

                                if (module != null)
                                {
                                    userModule.Modules = module;
                                }
                            }
                        }
                    }
                    User = t_user;
                    User.SessionId = SessionId;
                    User.SessionInfo = SessionInfo;
                }
                return IsAuthenticated();
            }

            public async Task<EWAAuthenticationState> GetAuthenticationStateAsync()
            {
                var uri = new Uri($"{navigationManager.BaseUri}Account/CurrentUser");
                var response = await httpClient.SendAsync(new HttpRequestMessage(HttpMethod.Post, uri));
                return await response.ReadAsync<EWAAuthenticationState>();
            }
            
            public async Task Logout()
            {
                await _sessionTimeout.ClearSession();
                await _authMonitorService.setLogout();
                navigationManager.NavigateTo("Account/Logout", true);
            }
            public async Task<SIB_Models.SIB_MODULES> GetMoudleById(decimal Id)
            {
                var result = await _userService.GetModuleById(Id);
                if (result != null)
                {
                    return result;
                }
                else
                {
                    return null;
                }
            }
            public async Task<SIB_Models.SIB_ROLES> GetRoleById(decimal Id)
            {
                var result = await _userService.GetRoleById(Id);
                if (result != null)
                {
                    return result;
                }
                else
                {
                    return null;
                }
            }
            public async Task<SIB_Models.SIB_USERS> GetUserById(string id)
            {
                var uri = new Uri(baseUri, $"susers/{id}");
                Console.WriteLine($"Request URI: {id}");
                var response = await httpClient.GetAsync(uri);
                Console.WriteLine($"Request URI: {uri}");
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return null;
                }
                return await response.ReadAsync<SIB_Models.SIB_USERS>();
            }
            public async Task ChangePassword(SIB_Models.SIB_USERS user,string oldPassword, string newPassword)
            {
                var checkPSWD = await _ewaAutServ.CheckPassword(user,newPassword);
                if (!checkPSWD.iscorrect)
                {
                    var message = checkPSWD.message;
                    throw new ApplicationException(message);
                }

                var uri = new Uri($"{navigationManager.BaseUri}Account/ChangePassword");

                var content = new FormUrlEncodedContent(new Dictionary<string, string> {
                    { "oldPassword", oldPassword },
                    { "newPassword", newPassword }
                });
                var response = await httpClient.PostAsync(uri, content);
                if (!response.IsSuccessStatusCode)
                {
                    var message = await response.Content.ReadAsStringAsync();
                    throw new ApplicationException(message);
                }
            }
        }

        public class SessionTimeoutService : IAsyncDisposable, IDisposable
        {
            private readonly TimeSpan sessionTimeout;
            private readonly IJSRuntime _jsRuntime;
            private readonly NavigationManager _navigationManager;
            private readonly UserManager<SIB_Models.SIB_USERS> _userManager;
            private readonly DomainUserService _domainUserService;
            private readonly EWAAuthenticateService _ewaAutServ;
            private DotNetObjectReference<SessionTimeoutService>? _dotNetRef;
            private const string IS_LOCKED_KEY = "isSessionLocked";

            public event Func<Task> OnSessionLocked;
            public event Func<Task> OnSessionUnlocked;

            public SessionTimeoutService(
                IConfiguration configuration,
                IJSRuntime jsRuntime,
                NavigationManager navigationManager,
                UserManager<SIB_Models.SIB_USERS> userManager,
                DomainUserService domainUserService,
                EWAAuthenticateService ewaAutServ)
            {
                sessionTimeout = TimeSpan.FromMinutes(configuration.GetValue<int>("SessionSettings:TimeoutInMinutes", 1));
                _jsRuntime = jsRuntime;
                _navigationManager = navigationManager;
                _userManager = userManager;
                _domainUserService = domainUserService;
                _ewaAutServ = ewaAutServ;
            }

            private record SessionState(bool IsLocked, string LastActivityTime);

            public async Task InitializeSession()
            {
                try
                {
                    // состояние сессии
                    var sessionState = await _jsRuntime.InvokeAsync<SessionState>("getSessionState");

                    if (sessionState.IsLocked || (sessionState.LastActivityTime != null && IsSessionTimedOut(sessionState.LastActivityTime)))
                    {
                        await _jsRuntime.InvokeVoidAsync("clearSessionStorage");
                        _navigationManager.NavigateTo("Account/Logout", true);
                        return;
                    }

                    _dotNetRef = DotNetObjectReference.Create(this);
                    await _jsRuntime.InvokeVoidAsync("initializeActivityTracking",
                        _dotNetRef, sessionTimeout.TotalMinutes);
                }
                catch (TaskCanceledException ex)
                {
                    Console.WriteLine($"Task was canceled in InitializeSession: {ex.Message}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error initializing session: {ex.Message}");
                }
            }

            private bool IsSessionTimedOut(string lastActivityStr)
            {
                if (string.IsNullOrEmpty(lastActivityStr))
                    return true;

                var lastActivity = long.Parse(lastActivityStr);
                var now = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                var elapsed = now - lastActivity;

                return elapsed >= sessionTimeout.TotalMilliseconds;
            }

            [JSInvokable]
            public async Task OnSessionTimeout()
            {
                Console.WriteLine("OnSessionTimeout invoked from JS");
                if (OnSessionLocked != null)
                {
                    await OnSessionLocked.Invoke();
                }
            }

            [JSInvokable]
            public async Task HandleSessionUnlock()
            {
                Console.WriteLine("HandleSessionUnlock invoked from JS");
                if (OnSessionUnlocked != null)
                {
                    await OnSessionUnlocked.Invoke();
                }
            }

            public async Task<bool> IsSessionLocked()
            {
                try
                {
                    var isLocked = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", IS_LOCKED_KEY);
                    return isLocked == "true";
                }
                catch (TaskCanceledException)
                {
                    Console.WriteLine("JS connection lost while checking session lock");
                    return false; // Безопасное значение по умолчанию
                }
                catch (JSException ex)
                {
                    Console.WriteLine($"Error during JS Interop in IsSessionLocked: {ex.Message}");
                    return false; // Безопасное значение по умолчанию
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error checking session lock: {ex.Message}");
                    return false;
                }
            }

            public async Task<bool> UnlockSession(string password, SIB_Models.SIB_USERS user)
            {
                bool passwordValid = false;
                try
                {
                    if (user == null || string.IsNullOrEmpty(password))
                        return false;
                    if (user.ISdomain)
                    {
                        passwordValid = await _domainUserService.AuthenticateUser(user.EXT_LOGIN, password);
                    }
                    else
                    {
                        passwordValid = await _userManager.CheckPasswordAsync(user, password);
                    }
                    var chkblck = await _ewaAutServ.ProcessLoginAttempt(user, passwordValid);
                    passwordValid = chkblck.ispermit;
                    var tres = await _ewaAutServ.UpdateUserBlckData(chkblck.user);

                    if (passwordValid)
                    {
                        // Вызываем JS-функцию, которая обновит localStorage и вызовет событие storage
                        await _jsRuntime.InvokeVoidAsync("unlockSession");

                        // Вызываем C# событие для немедленного обновления текущей вкладки
                        if (OnSessionUnlocked != null)
                        {
                            await OnSessionUnlocked.Invoke();
                        }

                        Console.WriteLine($"Session unlocked at {DateTime.Now}");
                        return true;
                    }

                    Console.WriteLine("Password verification failed");
                    return false;
                }
                catch (JSException ex)
                {
                    Console.WriteLine($"Error during JS Interop in UnlockSession: {ex.Message}");
                    return false;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error in UnlockSession: {ex.Message}");
                    return false;
                }
            }

            public async Task ClearSession()
            {
                try
                {
                    await _jsRuntime.InvokeVoidAsync("clearSessionStorage");
                    Console.WriteLine("Session storage cleared");
                }
                catch (TaskCanceledException) { Console.WriteLine("Task canceled during ClearSession"); }
                catch (JSException ex) { Console.WriteLine($"JS Error during ClearSession: {ex.Message}"); }
                catch (Exception ex) { Console.WriteLine($"Error clearing session: {ex.Message}"); }
            }
            public void Dispose()
            {
                // Нужен только для совместимости с DI
            }

            public async ValueTask DisposeAsync()
            {
                try
                {
                    if (_dotNetRef != null)
                    {
                        _dotNetRef.Dispose();
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error disposing session service: {ex.Message}");
                }
            }
        }
        public class AuthMonitorService : IAsyncDisposable
        {
            private readonly IJSRuntime _js;
            private readonly NavigationManager _navigation;
            private DotNetObjectReference<AuthMonitorService>? _objRef;

            public AuthMonitorService(IJSRuntime js, NavigationManager navigation)
            {
                _js = js;
                _navigation = navigation;
            }
            public async Task InitializeAsync()
            {
                _objRef = DotNetObjectReference.Create(this);
                await _js.InvokeVoidAsync("authSync.addLogoutListener", _objRef);
                await _js.InvokeVoidAsync("authSync.addLoginListener", _objRef);
            }
            [JSInvokable]
            public void OnLoginDetected()
            {
                _navigation.NavigateTo("/", true);
            }
            [JSInvokable]
            public void OnLogoutDetected()
            {
                _navigation.NavigateTo("/", true);
            }

            public async ValueTask DisposeAsync()
            {
                _objRef?.Dispose();
                await Task.CompletedTask;
            }
            public async Task setLogout()
            {
                try
                {
                    await _js.InvokeVoidAsync("authSync.setLogout");
                    await _js.InvokeVoidAsync("localStorage.removeItem", "loginDone");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error setLogout: {ex.Message}");
                }
            }
            public async Task setLogin()
            {
                try
                {
                    var alreadyLogged = await _js.InvokeAsync<string>("localStorage.getItem", "loginDone");
                    if (string.IsNullOrEmpty(alreadyLogged))
                    {
                        await _js.InvokeVoidAsync("authSync.setLogin");
                        await _js.InvokeVoidAsync("localStorage.setItem", "loginDone", "true");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error setLogin: {ex.Message}");
                }
            }
        }
    }
}
