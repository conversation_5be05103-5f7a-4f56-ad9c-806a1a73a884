<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <NoWarn>CS0168,CS1998,BL9993,CS0649,CS0436,0436</NoWarn>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.102.3" />
    <PackageReference Include="ClosedXML.Report" Version="0.2.11" />
    <PackageReference Include="FastReport.Data.OracleODPCore" Version="2025.2.0" />
    <PackageReference Include="FastReport.OpenSource" Version="2025.2.0" />
    <PackageReference Include="FastReport.OpenSource.Export.PdfSimple" Version="2025.2.0" />
    <PackageReference Include="FastReport.OpenSource.Web" Version="2025.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Negotiate" Version="8.0.18" />
    <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.8.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.8.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.VisualBasic" Version="4.8.0" />
    <PackageReference Include="NCalcSync" Version="5.3.1" />
    <PackageReference Include="Radzen.Blazor" Version="6.6.1" />
    <PackageReference Include="Oracle.ManagedDataAccess.Core" Version="3.21.130" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.3" />
    <PackageReference Include="Oracle.EntityFrameworkCore" Version="8.21.121" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.HeaderPropagation" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.OData" Version="8.2.5" />
    <PackageReference Include="System.DirectoryServices.AccountManagement" Version="9.0.6" />
    <PackageReference Include="Z.Blazor.Diagrams" Version="3.0.3" />
    <PackageReference Include="Z.Blazor.Diagrams.Core" Version="3.0.3" />
    <Content Include="Reports\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
</Project>
