﻿@attribute [Authorize]

@inject TooltipService tooltipService
@inject SIBService.UserService UServ
@inject SIBService.RulesService RServ
@inject DialogService DialogService

@using <PERSON><PERSON><PERSON>
@using System.Collections
@using EWA.Enums
@using EWA.Models
@using EWA.Services


@if (TabRef.CodeObj == "SUSERS")
{
    <RadzenButton EWAType = "Button" EWAID ="SIB1" ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="domain_add" Click="@ViewADUSRRow" MouseEnter="@(args => ShowTooltip(args,("Пользователи из домена" ),new TooltipOptions(){ Delay = 100, Duration = 1000 }))" />
    
    @if (selectedItems?.Count > 0)
    {
        <RadzenButton EWAType="Button" EWAID="SIB2" Visible=" ISVisible(1)" ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="sync_lock" Click="@ChngPass" MouseEnter="@(args => ShowTooltip(args,("Смена пароля" ),new TooltipOptions(){ Delay = 100, Duration = 1000 }))" />
    
    var isBlocked = int.Parse(selectedItems[0]["IS_BLOCK"].ToString()) == 1;
        var icon = isBlocked ? "lock_person" : "lock_open";
    var tooltipText = isBlocked ? "Разблокировать пользователя" : "Заблокировать пользователя";

        <RadzenButton EWAType="Button" EWAID="SIB3"  ButtonStyle="ButtonStyle.Base"
                  Variant="Variant.Text"
                  Icon="@icon"
                  MouseEnter="@(args => ShowTooltip(args, tooltipText))"
                  Click="ChangeUserAccess" />
    }
}
@if (TabRef.CodeObj == "SRULES")
{
    <RadzenButton EWAType="Button" EWAID="SIB4" ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="policy" Click="@UpdPolicy"
                  MouseLeave="TooltipService.Close"
                  MouseEnter="@(args => ShowTooltip(args, "пересчет по всем правилам", new TooltipOptions(){ Position = TooltipPosition.Bottom}))" />
}
@if (TabRef.CodeObj == "SPOLICY" && TabRef.ParentTab.CodeObj != "head")
{
    @if (TabRef.ParentTab.RowData.Count > 0)
    {
        <RadzenButton EWAType="Button" EWAID="SIB5" ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="policy" Click="@UpdPolicy" />
    }
}

@code {
    [Parameter] public IList<IDictionary<string, object>> selectedItems { get; set; }
    [Parameter] public TabData TabRef { get; set; }
    [Parameter] public RadzenDataGrid<IDictionary<string, object>> GridRef { get; set; }
    [Parameter] public EventCallback OnChanged { get; set; }
    [Parameter] public Dictionary<string, object> selectedRowData { get; set; }
    [Parameter] public Func<Task> ReloadDef { get; set; }
    [Parameter] public IEnumerable<IDictionary<string, object>> queryResults { get; set; }
    [Parameter] public int Count { get; set; }
    [Parameter] public FormParams InParams {get;set; }
    [Parameter] public EventCallback<(IEnumerable<IDictionary<string, object>>, int)> QueryResultsChanged { get; set; }

    [Inject]
    protected SIBService.SecurityService Security { get; set; }
    [Inject]
    protected TooltipService TooltipService { get; set; }

    void ShowTooltip(ElementReference elementReference, string buttomName, TooltipOptions options = null) => tooltipService.Open(elementReference, buttomName, options);

    private bool ISVisible(int val)
    {
        bool bres = false;
        if (val == 1)
        {
            string sval = string.Empty;
            sval = selectedItems?.FirstOrDefault()?["EXT_LOGIN"]?.ToString() ?? string.Empty;
            if (string.IsNullOrEmpty(sval))
            {
                bres = true;    
            }
        }
        return bres;

    }
    private async Task ChangeUserAccess()
    {
        decimal in_User_Id = Convert.ToDecimal(selectedItems[0]["ID"]);
        decimal in_Is_Active = int.Parse(selectedItems[0]["IS_BLOCK"].ToString()) == 1 ? 0 : 1;
        string in_user = Security.User.CODE;
        bool isblck = in_Is_Active == 1;
        var res = await UServ.UpdateUserAccess(in_User_Id,in_Is_Active,in_user,BlockUser.Manual,isblck);

        if (res.form_res)
        {
            UpdItems(res.res_data);
            await NotifyChangedAsync();
        }
    }
    private async Task ChngPass()
    {
        var formValues = new Dictionary<string, object>();
        selectedRowData.Clear();
        if (selectedItems != null && selectedItems.Count > 0)
        {
            foreach (var kvp in selectedItems[0])
            {
                string key = kvp.Key;
                object value = kvp.Value;
                if (kvp.Key == "ID")
                {
                    selectedRowData.Add("InUserId", value);
                }
            }
        }
        if (selectedRowData.ContainsKey("InUserId"))
        {
            var res = await DialogService.OpenAsync<SIBUserPass>("Изменение пароля пользователя",
                                            selectedRowData,
                                            new DialogOptions { Draggable = true, Resizable = true }
            );
            if (res != null)
            {
                if (res.Result)
                {
                    UpdItems(res.Values);
                    await NotifyChangedAsync();
                }
            }
        }
    }
    private async Task ViewADUSRRow()
    {
        var res = await DialogService.OpenAsync<SIBDomainUserList>("Список пользователей домена",
                                                null,
                                                new DialogOptions { Draggable = true, Resizable = true }
                          );
        if (res != null)
        {

            if (res.Result)
            {
                var values = ((IEnumerable)res.Values).Cast<IDictionary<string, object>>();
                queryResults = values.Concat(queryResults);
                Count += values.Count();
                foreach (var row in queryResults)
                {
                    if (row.ContainsKey("CN"))
                    {
                        row["CN"] = Count;
                    }
                }


                await QueryResultsChanged.InvokeAsync((queryResults, Count));
                //await NotifyChangedAsync();
            }
        }
    }
    private async Task NotifyChangedAsync()
    {
        if (GridRef is not null)
            await GridRef.Reload();

        if (OnChanged.HasDelegate)
            await OnChanged.InvokeAsync();
    }

    private async Task UpdPolicy()
    {
        Dictionary<string, object> itemData = (selectedRowData != null && selectedRowData.Any()) ? selectedRowData : new Dictionary<string, object>();

        await RServ.UpdatePolicy(userCode: Security.User.CODE, code_tab: TabRef.CodeObj, Values: itemData);
        if (ReloadDef != null)
        {
            await ReloadDef.Invoke();
        }
    }
    private void UpdItems(Dictionary<string, object> itemdata)
    {
        foreach (var item in selectedItems)
        {
            foreach (var key in itemdata.Keys)
            {
                if (item.ContainsKey(key))
                {
                    item[key] = itemdata[key];
                }
            }
        }
        
    }
}