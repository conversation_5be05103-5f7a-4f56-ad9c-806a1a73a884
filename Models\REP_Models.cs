﻿using EWA.Enums;
using Microsoft.AspNetCore.Components.Web.Virtualization;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Text.Json.Serialization;

namespace EWA.Models
{
    public class REP_Models
    {
      
        public class SibFormInfo        
        {
            public Type AddForm { get; set; }
            public string AddTitle { get; set; }
            public Type UpdForm { get; set; }
            public string UpdTitle { get; set; }

            public Type DelForm { get; set; }
            public string DelTitle { get; set; }
        }

       
        public class Rep_Param
        {
            public int? ISMANDATORY { get; set; }            
            public string DATATYPE { get; set; }

            public ParameterDirection Direct { get; set; } = ParameterDirection.Input;
            public int? DATALENGTH { get; set; }
            public int? DATAPRECISION { get; set; }
            public object? Val { get; set; }

            public Rep_Param(int? _ismandatory, string _datatype, ParameterDirection _direct, int? _datalength, int? _dataprecision, object? val)
            { ISMANDATORY = _ismandatory; DATATYPE = _datatype; Direct = _direct; DATALENGTH = _datalength; 
              DATAPRECISION = _dataprecision; Val = val;
            }

        }
        public class Rep_Validator
        {
            public string color { get; set; }
            public string message { get; set; }
        }
        public class Rep_SprLongFiltrMeta
        {
            public List<ColumnMetadata> Column { get; set; }
            public List<ParamMetadata> Param { get; set; }
            public string sqlQuery { get; set; }
        }

        public class SPRShort
        {
            public object ID { get; set; }
            public string Code { get; set; }
            public string Name { get; set; }
        }

        public class Rep_SprShortSelDict
        {
            public string KeySprShortDict { get; set; }
            public object ValPrm { get; set; }
        }
        public class Rep_SprLongSelDict
        {
            public string KeySprLongDict { get; set; }
            public IDictionary<string, object> ValPrm { get; set; }
        }
       
        public class RepositoryInfo
        {
            
            
            private string _code = "";
            public string Code
            {
                get { return _code; }
                set { _code = value.ToUpper(); }
            }
            public bool Dummy { get; set; }
            public string Title { get; set; }
            public string TypeObject { get; set; }
            public string AppCode { get; set; }

            public string Query { get; set; }
            public string AddSql { get; set; }
            public string UpdSql { get; set; }
            public string DelSql { get; set; }

            public bool ColAdd { get; set; }
            public bool ColUpd { get; set; }
            public bool ColDel { get; set; }
            public string Dbname_Query { get; set; }

            public bool is_tempory { get; set; }
            public List<ColumnMetadata> Column { get; set; } = new List<ColumnMetadata>();
            public List<ParamMetadata> Param { get; set; } = new List<ParamMetadata>();
            public List<REP_Models.CfgData> Cfg { get; set; } = new List<CfgData>();
            public IEnumerable<GRID_Menu> MenuGrid { get; set; } = Enumerable.Empty<EWA.Models.REP_Models.GRID_Menu>();

        }
        
        public class REP_OBJECT_TYPE
        {
            [Key]
            [Column("CODE_TYPE")]
            public string CodeType { get; set; }

            [Column("NAME_TYPE")]
            public string NameType { get; set; }
            public ICollection<REP_OBJECT> RepObjects { get; set; }
        }
        public class REP_OBJECT
        {
            private string _code = "";
            [Key]
            [Column("ID")]
            public long Id { get; set; }
            [Column("CODE")]
            public string Code
            {
                get { return _code; }
                set { _code = value.ToUpper(); }
            }
            [Column("NAME")]
            public string Name { get; set; }
            [Column("TYPE_OBJECT")]
            public string TypeObject { get; set; }
            [Column("CREATE_DT")]
            public DateTime? CreateDt { get; set; }
            [Column("CHANGE_DT")]
            public DateTime? ChangeDt { get; set; }
            [Column("CREATE_USER")]
            public string CreateUser { get; set; }
            [Column("CHANGE_USER")]
            public string ChangeUser { get; set; }
            [Column("APP_CODE")]
            public string AppCode { get; set; }
            
            public REP_OBJECT_TYPE RepObjectType { get; set; }
            public REP_TABLES RepTables { get; set; }
        }
        public interface IMetadataObject
        {
            string ParamQuery { get; set; }
        }
        public class REP_TABLES : IMetadataObject
        {
            [Key]
            [Column("ID_REP_TABLES")]
            public long IdRepTables { get; set; }
            [Column("COL_QUERY")]
            public string ColQuery { get; set; }
            [Column("PARAM_QUERY")]
            public string ParamQuery { get; set; }
            [Column("SQL_QUERY")]
            public string SqlQuery { get; set; }
            [Column("INSERTSQL")]
            public string InsertSql { get; set; }
            [Column("DELETESQL")]
            public string DeleteSql { get; set; }
            [Column("UPDATESQL")]
            public string UpdateSql { get; set; }
            [Column("ENABLEINSERT")]
            public bool EnableInsert { get; set; }
            [Column("ENABLEUPDATE")]
            public bool EnableUpdate { get; set; }
            [Column("ENABLEDELETE")]
            public bool EnableDelete { get; set; }
            [Column("MENUDATA")]
            public string MenuData { get; set; }
            [Column("ISSHORTDIM")]
            public int? IsShortDim { get; set; }

            [Column("VARS")]
            public string Vars { get; set; }

            [Column("CFG_DATA")]
            public string CfgData { get; set; }
            public REP_OBJECT RepObject { get; set; }
        }
        public class REP_OBJECT_REL
        {
            [Key]
            [Column("ID_REL")]
            public long IdRel { get; set; }

            [Column("ID_OBJECT_PAR")]
            public long IdObjectPar { get; set; }

            [Column("ID_OBJECT_CHI")]
            public long IdObjectChi { get; set; }

            [Column("KIND")]
            public int Kind { get; set; }

            [Column("NAME")]
            public string Name { get; set; }

            [Column("CONFIRMTEXT")]
            public string Confirmtext { get; set; }

            [Column("VISIBILITYFORMULA")]
            public string Visibilityformula { get; set; }

            [Column("SHORTCUTNOTE")]
            public string Shortcutnote { get; set; }

            [Column("ORDERNUM")]
            public int Ordernum { get; set; }

            [Column("PARAMS")]
            public string Params { get; set; }

        }

         public class REP_DATA_SRC
         {
            private string _code = "";
            [Key]
            [Column("ID_REP_DATA_SRC")]
            public long IdRepDataSrc { get; set; }

            [Column("CODE")]
            public string Code
            {
                get { return _code; }
                set { _code = value.ToUpper(); }
            }

            [Column("ID_OBJECT")]
            public long IdObject { get; set; }

            [Column("ID_DATASET")]
            public long IdDataset { get; set; }

            [Column("PARAMS")]
            public string Params { get; set; }
        }
        public class ActionAccess
        { 
            public decimal ID_USER { get; set; }
            public decimal ID_OBJECT { get; set; }
            public decimal ACTION_ADD { get; set; }
            public decimal ACTION_EDIT { get; set; }
            public decimal ACTION_DEL { get; set; }
        }
        public class MenuInfo
        {
            private string _code_child = "";
            private string _code_parent = "";
            private string _code_object = "";

            public string CODE_CHILD
            {
                get { return _code_child; }
                set { _code_child = value.ToUpper(); }
            }
            public string CODE_PARENT
            {
                get { return _code_parent; }
                set { _code_parent = value.ToUpper(); }
            }
            public string CODE_OBJECT
            {
                get { return _code_object; }
                set { _code_object = value.ToUpper(); }
            }
            public string NAME_OBJECT { get; set; }
            public string TYPE_OBJECT { get; set; }
            public decimal IS_AUTOEXECUTE { get; set; }
            public bool IsAutoexec => IS_AUTOEXECUTE == 1;

        }

        public class GRID_Menu
        {
            private string _code_child = "";
            private string _code_parent = "";
            private string _code_object = "";
            public long ID { get; set; }
            public string CODE_CHILD
            {
                get { return _code_child; }
                set { _code_child = value.ToUpper(); }
            }
            public string CODE_PARENT
            {
                get { return _code_parent; }
                set { _code_parent = value.ToUpper(); }
            }
            public string CODE_OBJECT
            {
                get { return _code_object; }
                set { _code_object = value.ToUpper(); }
            }

            public string NAME_OBJECT { get; set; }
            public string TYPE_OBJECT { get; set; }
            public int KIND { get; set; }
            public string CONFIRMTEXT { get; set; }
            public string VISIBILITYFORMULA { get; set; }

            public int AUTO_MODE { get; set; } = 0;
        }

        public class GLB_CONFIG
        {
            [Key]
            [Column(Order = 1)]
            public string CFG_GROUP { get; set; }
            [Key]
            [Column(Order = 2)]
            public string CFG_CODE { get; set; }
            [Key]
            [Column(Order = 3)]
            public string CFG_PARAM { get; set; }
            public string CFG_NAME { get; set; }
            public string CFG_VALUE { get; set; }
            public DateTime? DT_CHANGE { get; set; }
            public string? USER_CHANGE { get; set; }
        }

        public class PaginationResult
        {
            public IEnumerable<IDictionary<string, object>> Items { get; set; }
            public int Count { get; set; }
            public string ErrorMessage { get; set; }
        }

        public class baseColParam
        {           
            public virtual string CODE { get; set; }
            public virtual string NAME { get; set; }
            public virtual string DATATYPE { get; set; }
            public virtual int? DATALENGTH { get; set; }
            public virtual int? DATAPRECISION { get; set; }
            public virtual string? LINKEDPARAMCODE { get; set; }
            public virtual string LINKEDCOLUMNCODE { get; set; }
            public string? DIMCODE { get; set; }
            public string? DIMNAME { get; set; }
            public virtual string? DOMAINCODE { get; set; }
            public int? ISMANDATORY { get; set; }
            public int IS_SYSTEM { get; set; }
            public string TYPE_SYSTEM { get; set; }
            /// <summary>
            /// родительский параметр или колонка
            /// </summary>
            public string Parent { get; set; }
            /// <summary>
            /// формат разделителя
            /// </summary>
            public string DecimalSeparator { get; set; }
            /// <summary>
            /// шаблон преобразования значения родителя в нас
            /// </summary>
            public string Pattern {  get; set; }
            /// <summary>
            /// регулярное выражение для проверки значения
            /// </summary>
            public string RegExPattern { get; set; }


        }
        public class ColumnMetadata: baseColParam
        {
            private int? _dataprecision=0;
            private string _code = "";
            private string _linkedcolumncode = null;
            private string _linkedparamncode = null;

            [JsonPropertyName("COL_CODE")]
            public override string CODE 
            {
                  get { return _code; }
                  set { _code = value.ToUpper(); }
             }
            [JsonPropertyName("COL_NAME")]
            public override string NAME { get; set; }
            [JsonPropertyName("COL_TYPE")]
            public override string DATATYPE { get; set; }
            [JsonPropertyName("COL_SIZE")]
            public override int? DATALENGTH { get; set; }
            [JsonPropertyName("COL_PRECISION")]
            public override int? DATAPRECISION
            {
                get{ return _dataprecision; }
                set {if (value != null){ _dataprecision = value;}}
            }

            [JsonPropertyName("LINKCOLUM")]
            public override string LINKEDCOLUMNCODE
            {
                get { return _linkedcolumncode; }
                set { _linkedcolumncode = value.ToUpper(); }
            }

            [JsonPropertyName("LINKPARAM")]
            public override string LINKEDPARAMCODE
            {
                get { return _linkedparamncode; }
                set { _linkedparamncode = value.ToUpper(); }
            }
            public string LINKDIMPARAM { get; set; }           
           
            public string? ALIGNMENT { get; set; }
            public int? VIEWVISIBLE { get; set; }           
            public int? EDITABLE { get; set; }
            public int? INSERTABLE { get; set; }
            public string COL_REF { get; set; }
            public string COL_REF_DATA { get; set; }
            public int IS_PK { get; set; } = 0;
        }

        public class ParamMetadata : baseColParam, ICloneable
        {
            private string _code = null;
            private string _linkedcolumncode = null;
            private string _linkedparamncode = null;

            [JsonPropertyName("PARAMCODE")]
            public override string CODE { get { return _code; } 
                                         set { _code = value.ToUpper(); }
                                        }
            [JsonPropertyName("PARAMNAME")]
            public override string NAME { get; set; }
            public override string LINKEDCOLUMNCODE
            {
                get { return _linkedcolumncode; }
                set { _linkedcolumncode = value.ToUpper(); }
            }
            public override string? LINKEDPARAMCODE
            {
                get { return _linkedparamncode; }
                set { _linkedparamncode = value.ToUpper(); }
            }

            [JsonPropertyName("PARAMDOMAINCODE")]
            public override string? DOMAINCODE { get; set; }
            public string LINKEDCOLUMNNAME { get; set; }           
            public string? LINKEDPARAMNAME { get; set; }
            public string? DEFAULTVALUE { get; set; }
            public int HIDDENVALUE { get; set; }            
            public int ISSYSTEM { get; set; }

            public object Clone()
            {
                return MemberwiseClone();
            }


        }

        public class SPRMetadata
        {
            public string TYPE_SPR { get; set; }
            public string CODE_SPR { get; set; }
            public string NAME_SPR { get; set; }

        }

      

        public class CfgData
        { 
            public string CODE { get; set; }
            public string NAME { get; set; }
            public string VALUE { get; set; }
        
        }
        public class RuleModel
        {
            [Key]
            public string CODE_GROUP { get; set; }
            public int NUM { get; set; }
            public string TYPE { get; set; }
            public string PAR_GROUP { get; set; }
            public string CODE { get; set; }
            public string TYPE_ATTR { get; set; }
            public string ATTR { get; set; }
            public string OPER_ATTR { get; set; }
            public string TYPE_ATTR_VAL { get; set; }
            public string ATTR_VAL { get; set; }
            public string VAL { get; set; }
            public string OPER_UNION { get; set; }
        }

    }

    public class LogData
    {
        public object? _oldValue { get; set; }
        public object? _newValue { get; set; }
    }
    public class LogInfo
    {
        public string _query { get; set; }  //выполняемый запрос
      public string _dbname { get; set; }//база
      public bool _is_tempory { get; set; } //признак использования временной таблицы
      public string? _name_tmptab { get; set; }//имя временной таблицы
      public Dictionary<string, REP_Models.Rep_Param> _globalFilter { get; set; }//список значение глобального фильтра
      public string? _filter { get; set; }//локальный фильтр
      public string? _orderBy { get; set; }//сортировка
      public int? _skip { get; set; }//пагинация кол-во пропущееных записей
      public int? _take { get; set; }//пагинация кол-во отображаемых записей
    //  public Dictionary<string, object> _seldata { get; set; }

        public Dictionary<string, LogData> _data { get; set; } = new();//словарь старых _oldValue /новых значений _newValue
    }

    public class TabDataPrm
    {
        public string namegrid { get; set; }
        public string codeobj { get; set; }
        public string nameobj { get; set; }
        public Models.TabData parentTab { get; set; }
        public List<REP_Models.ParamMetadata> transitionParams { get; set; }
        public Dictionary<string, string> linkedParam { get; set; }
        public string visibilityFormula { get; set; }
        public string lstpage { get; set; }
        public TabPageKind pagekind { get; set; }
    }
    public class FormParams
    {
        public string _form_code { get; set; }
        public IEnumerable<REP_Models.baseColParam> _metadata { get; set; }
        public Dictionary<string, REP_Models.Rep_Param> _globalFilter { get; set; }
        public Dictionary<string, object> _seldata { get; set; }
        public string _sql { get; set; }
        public bool _is_tempory { get; set; }
        public string _name_tmptab { get; set; }
        public string _query { get; set; }
        public string _dbname { get; set; }
        public List<REP_Models.ParamMetadata> _metadata_param { get; set; }
        public string _action { get; set; }
        public string _butsub_name
        {
            get
            {
                return _action switch
                {
                    "DEL" => "Удалить",
                    "VIEW" => "Закрыть",
                    "ADD" => "Сохранить",
                    "EDIT" => "Сохранить",
                    "GlobalFILTR" => "Применить",
                    _ => "ОК"
                };
            }
        }
        public bool _ISbutsub
        {
            get
            {
                return _action switch
                {
                    "VIEW" => false,
                    _ => true
                };
            }
        }
        public Dictionary<string, IEnumerable<REP_Models.SPRShort>> _SprShortDict { get; set; }
        public Dictionary<string, REP_Models.Rep_SprShortSelDict> _SprSelShortDict { get; set; }
        public Dictionary<string, REP_Models.Rep_SprLongFiltrMeta> _SprLongDict { get; set; }
        public Dictionary<string, REP_Models.Rep_SprLongSelDict> _SprSelLongDict { get; set; }
        public List<REP_Models.CfgData> _cfg_data { get; set; }
    }
    public class MenuRepData
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Type { get; set; }
    }
}
