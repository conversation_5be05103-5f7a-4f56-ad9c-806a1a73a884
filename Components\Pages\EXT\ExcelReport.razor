﻿@rendermode InteractiveServer
@inject NavigationManager Navigation
@inject RepService _repService
@inject EWA.Services.RepService.GET_SPR _getspr
@inject EWA.Services.RepService.MenuGrid MenuGrid
@inject UIService.TabService TabService
@inject DialogService DialogService
@inject ContextMenuService ContextMenuService
@inject IJSRuntime JSRuntime
@inject IExcelService ExcelService
@inject IWebHostEnvironment environment
@inject TooltipService tooltipService
@inject EWA.Services.RepService.LogService _logService
@using static EWA.Models.REP_Models
@implements IDisposable


@using System.Collections.Generic
@using System.Data
@using Microsoft.Extensions.Configuration
@using System.Text.Json
@using Radzen.Blazor
@using Oracle.ManagedDataAccess.Client
@using Radzen
@using EWA.Services
@using EWA.Controllers
@using EWA.Models
@using EWA.Components.Pages
@using System.Net.Http
@using System.Reflection
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Mvc
@using System.Globalization

<!-- <RadzenTabs />  -->
<div class="rz-m-3">
    <RadzenButton ButtonStyle="ButtonStyle.Base" Icon="refresh" Variant="Variant.Text" Click="@LoadDataBegin" MouseEnter="@(args => ShowTooltip(args, "Глобальный фильтр"))" />
    <RadzenText>@statusMessage</RadzenText>
    @if (isLoading)
    {
        <div class="excel-container">
            <div class="excel-loading">
                <div class="excel-loading-content">
                    <i class="rzi rzi-circle-o-notch"></i>
                </div>
            </div>
        </div>
    }
</div>

@code {
    //[Parameter]
    //public string Code { get; set; }
    [Parameter]
    public int ParentTabIndex { get; set; }
    [Parameter]
    public Dictionary<string, object> Params { get; set; }
    public Dictionary<string, object> OldParams { get; set; }
    [Parameter]
    public bool Selected { get; set; }
    [Parameter]
    public int Index { get; set; }
    [Parameter]
    public int? ChildIndex { get; set; }
    [Parameter]
    public TabData Tab { get; set; }

    [Inject] protected EWA.Services.SIBService.SecurityService Security { get; set; }
    private string statusMessage = string.Empty;
    private Dictionary<string, object> parameters;

    //private Dictionary<string, object> globalFilter = new();
    //private Dictionary<string, Rep_Param> globalFilter = new();
    private Dictionary<string, Rep_Param> globalFilter = new(StringComparer.InvariantCultureIgnoreCase);

    string dbname = string.Empty;
    string dbname_query = string.Empty;
    long? ID_OBJECT;
    string query = string.Empty;
    string add_sql = string.Empty;
    string upd_sql = string.Empty;
    string del_sql = string.Empty;
    string query_meta = string.Empty;
    string param_meta = string.Empty;
    string vars_meta = string.Empty;
    string _title = string.Empty;
    string _typeobj = string.Empty;
    string menudata = string.Empty;
    string trans_params = string.Empty;
    bool col_add = false;
    bool col_edit = false;
    bool col_del = false;
    REP_Models.IMetadataObject metaObject = null;
    int count = 0;
    private List<REP_Models.ColumnMetadata> metadata;
    private List<REP_Models.ParamMetadata> metadata_param;
    private List<REP_Models.ParamMetadata> metadata_vars;
    private DBService dbService;
    private Dictionary<string, object> selectedRowData = new Dictionary<string, object>();
    private Dictionary<string, IEnumerable<REP_Models.SPRShort>> SprShortDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprShortSelDict> SprSelShotDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprLongFiltrMeta> SprLongDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprLongSelDict> SprSelLongDict = new();
    public IEnumerable<EWA.Models.REP_Models.GRID_Menu> menuListGrid;
    bool isLoading = false;
    private decimal currentLogId = 0;
    private CancellationTokenSource _reportCts = new CancellationTokenSource();
    private decimal nextValue = 0;
    private DateTime timequery;
    private string timequery_res = string.Empty;
    private bool is_request = false;
    Dictionary<string, object> chng_param;
    string chng_query = string.Empty;
    string name_tmptab = string.Empty;
    string tmp_query = string.Empty;
    private string hint { get; set; }
    private bool isFiltering = false;
    private Dictionary<string, RadzenDropDownDataGrid<IDictionary<string, object>>> gridDict = new();
    private Dictionary<string, IEnumerable<IDictionary<string, object>>> dataDict = new();
    private Dictionary<string, int> SprLongCountDict = new();
    private Dictionary<string, string> SprLongFiltrDict = new();

    //private RepositoryInfo repositoryInfo = new RepositoryInfo();   не используется; в OnInitializedAsync metadata_param - параметры
    private FormParams _PublicParams = new FormParams();
    int h = 0;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (h == 1)
        {
            Tab.is_complete = true;
            h = 3;
            Console.WriteLine("OnAfterRenderAsync" + Tab.CodeObj);
            await OnGlobalFiltrExt();
        }
    }

    private async Task OnGlobalFiltrExt()
    {
        if (metadata_param.Count(x => x.ISMANDATORY == 1 && x.DEFAULTVALUE == null && x.HIDDENVALUE == 0) > 0)
        {
            Console.WriteLine("OnGlobalFiltrExt" + Tab.CodeObj);
            is_request = false;
            await LoadDataBegin();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (Tab.IsSelect)
        {
            if (h == 0)
            {
                h = 1;
            }
        }
        if (!Tab.Show)
        {
            if (_reportCts != null && !_reportCts.IsCancellationRequested)
            {
                _reportCts.Cancel();
            }

            if (currentLogId > 0 && isLoading)
            {
                var logIdToCancel = currentLogId;
                currentLogId = 0;  // для избежания повторных вызовов

                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _logService.LogC(logIdToCancel);
                        Console.WriteLine($"Excel report cancelled due to tab hiding: logId={logIdToCancel}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error cancelling Excel report on tab hiding: {ex.Message}");
                    }
                });
            }
        }
    }

    private async Task<(Dictionary<string, object> formValues, bool formRes)> FiltrRow()
    {
        _PublicParams._metadata = metadata_param;
        _PublicParams._action = "GlobalFILTR";
        _PublicParams._seldata = selectedRowData;
        _PublicParams._SprShortDict = SprShortDict;
        _PublicParams._SprSelShortDict = SprSelShotDict;
        _PublicParams._SprLongDict = SprLongDict;
        _PublicParams._SprSelLongDict = SprSelLongDict;
        _PublicParams._globalFilter = globalFilter;

        var result = await DialogService.OpenAsync<GLOBAL.FormGrid>("Фильтр ",
                            new Dictionary<string, object> { { "InParams", _PublicParams } },
                            new DialogOptions { Draggable = true, Resizable = true }
        );

        var formValues = new Dictionary<string, object>();
        bool formRes = false;
        if (result != null)
        {
            formValues = result.Values as Dictionary<string, object>;
            formRes = result.Result;
        }

        return (formValues, formRes);
    }

    private void CloseModal()
    {
        DialogService.Dispose();
    }

    protected override async Task OnInitializedAsync()
    {
        timequery_res = $"Запрос выполнялся 0 минут или  0 секунд";
        dbname = await _repService.GetConnectionStringAsync("REP");
        //ID_OBJECT = await _repService.GetIdObjectAsync(Code);
        ID_OBJECT = await _repService.GetIdObjectAsync(Tab.CodeObj);

        if (ID_OBJECT.HasValue)
        {
            var infoobject = await _repService.GetInfoObjectAsync(Tab.CodeObj);

            if (infoobject != null)
            {
                _title = infoobject.Name;
                _typeobj = infoobject.TypeObject;
            }

            if (_typeobj == "REP_TABLES" || _typeobj == "SIB_TABLES" || _typeobj == "REP_SPR" || _typeobj == "EDIT_QUERY" || _typeobj == "GET_QUERY" ||
                _typeobj == "DIM" || _typeobj == "GET_PROCEDURE" || _typeobj == "XLSREPORT_TEMPLATE")
            {
                metaObject = await _repService.GetMetaObjectTableAsync(ID_OBJECT.Value);
            }

            if (metaObject != null)
            {
                if (_typeobj == "REP_TABLES" || _typeobj == "SIB_TABLES" || _typeobj == "REP_SPR")
                {
                    dbname_query = await _repService.GetConnectionStringAsync("REP");
                }
                else if (_typeobj == "EDIT_QUERY" || _typeobj == "GET_QUERY" || _typeobj == "DIM" || _typeobj == "GET_PROCEDURE" || _typeobj == "XLSREPORT_TEMPLATE")
                {
                    dbname_query = await _repService.GetConnectionStringAsync(infoobject.AppCode);
                }

                if (metaObject is REP_Models.REP_TABLES repTables)
                {
                    query = repTables.SqlQuery;
                    query_meta = repTables.ColQuery;
                    param_meta = repTables.ParamQuery;
                    add_sql = repTables.InsertSql;
                    upd_sql = repTables.UpdateSql;
                    del_sql = repTables.DeleteSql;
                    col_add = repTables.EnableInsert;
                    col_edit = repTables.EnableUpdate;
                    col_del = repTables.EnableDelete;
                    menudata = repTables.MenuData;
                    vars_meta = repTables.Vars;   // переменные xlsx шаблона
                }
            }

        }
        //metadata = JsonSerializer.Deserialize<List<REP_Models.ColumnMetadata>>(query_meta);
        metadata_param = JsonSerializer.Deserialize<List<REP_Models.ParamMetadata>>(param_meta);
        if (vars_meta is not null)
        {
            metadata_vars = JsonSerializer.Deserialize<List<REP_Models.ParamMetadata>>(vars_meta);
        }
        dbService = new DBService(dbname_query);
        //menuListGrid = await MenuGrid.GetMenuData();
        //menuListGrid = await MenuGrid.GetMenuDataJS(menudata);  // MON  есть ключи KIND, CONFIRMTEXT, VISIBILITYFORMULA
        //nextValue = await Seq.GetNextValueAsync("EWA_ROW_SEQ");
        int i = 0;
        i++;
    }

    private void ShowTooltip(ElementReference elementReference, string mes)
    {
        tooltipService.Open(elementReference, mes);
    }

    private string GetLogJSon(LogInfo lg)
    {
        string jsonString = JsonSerializer.Serialize<LogInfo>(lg, new JsonSerializerOptions()
            {
                WriteIndented = true, // Добавляем отступы для красивого вывода
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

        return jsonString;

    }

    private async Task LoadDataBegin()
    {
        var (formValues, formRes) = await FiltrRow();
        globalFilter.Clear();

        if (formRes)
        {
            parameters = new Dictionary<string, object>();
            foreach (var kvp in formValues)
            {
                string paramName = kvp.Key;
                object paramValue = kvp.Value;

                parameters.Add(paramName, paramValue);
                var meta = metadata_param.First(x => x.CODE == kvp.Key);
                //globalFilter.Add(paramName, paramValue);
                globalFilter.Add(kvp.Key, new Rep_Param(meta.ISMANDATORY, meta.DATATYPE, ParameterDirection.Input, meta.DATALENGTH,
                                                        meta.DATAPRECISION, kvp.Value)
                                );
            }

            // отменяем предыдущий отчет
            _reportCts.Cancel();
            _reportCts = new CancellationTokenSource();

            isLoading = true;
            DialogService.Close(true);
            await JSRuntime.InvokeVoidAsync("Radzen.closeAllPopups");

            is_request = true;
            isFiltering = true;
            statusMessage = "Формирование отчета...";
            StateHasChanged();

            await Task.Run(async () =>
             {
                 try
                 {
                     var timereport_s = DateTime.Now;
                     await GenerateReport(_reportCts.Token);

                     if (statusMessage == "Формирование отчета...")
                     {
                         TimeSpan timereport = DateTime.Now - timereport_s;
                         statusMessage = "Отчет успешно сформирован." + " (" + $"{timereport.TotalSeconds:F2} сек." + ")";
                     }
                 }
                 catch (OperationCanceledException)
                 {
                     statusMessage = "Формирование отчета отменено.";
                 }
                 catch (Exception ex)
                 {
                     statusMessage = "Ошибка при формировании отчета: " + ex.Message;
                 }
                 finally
                 {
                     is_request = false;
                     isLoading = false;
                     await InvokeAsync(StateHasChanged);
                 }
             }, _reportCts.Token);
        }
    }

    private async Task GenerateReport(CancellationToken cancellationToken = default)
    {
        var infoobject = await _repService.GetInfoObjectAsync(Tab.CodeObj);
        bool success = false;
        string errorMessage = "";
        decimal logid = 0;
        try
        {
            // Проверяем отмену в начале
            cancellationToken.ThrowIfCancellationRequested();

            if (ID_OBJECT.HasValue)
            {
                // источники данных
                var dataSources = await _repService.GetDataSourceAsync(ID_OBJECT.Value);

                // Проверяем отмену после получения источников данных
                cancellationToken.ThrowIfCancellationRequested();

                // словарь переменных для замены в шаблоне
                var variables = new Dictionary<string, string>();
                if (metadata_vars != null && metadata_vars.Any())
                {
                    foreach (var varItem in metadata_vars)
                    {
                        if (globalFilter.TryGetValue(varItem.LINKEDPARAMCODE, out Rep_Param paramValue))
                        {
                            string stringValue = paramValue.Val switch
                            {
                                DateTime date => date.ToString("dd.MM.yyyy"),
                                _ => paramValue.Val?.ToString()
                            };
                            variables[varItem.CODE] = stringValue;
                        }
                    }
                }

                // список источников данных
                var dataSourceInfoList = new List<DataSourceInfo>();

                foreach (var dataSource in dataSources)
                {
                    // Получаем информацию об объекте и таблице
                    var obj_code = await _repService.GetCodeObjectAsync(dataSource.IdDataset);
                    var repObject = await _repService.GetInfoObjectAsync(obj_code);
                    var repTable = await _repService.GetMetaObjectTableAsync(dataSource.IdDataset);

                    var paramMappings = JsonSerializer.Deserialize<List<REP_Models.ParamMetadata>>(dataSource.Params);

                    // список параметров Oracle
                    var parameters = new List<OracleParameter>();
                    foreach (var param in paramMappings)
                    {
                        if (globalFilter.TryGetValue(param.LINKEDPARAMCODE, out Rep_Param paramValue))
                        {
                            var oracleParam = new OracleParameter(param.CODE, GetOracleType(param.DATATYPE))
                                {
                                    Value = paramValue.Val
                                };
                            parameters.Add(oracleParam);
                        }
                    }

                    var dataSourceInfo = new DataSourceInfo();
                    dataSourceInfo.TemplateRange = dataSource.Code;

                    if (repObject.TypeObject == "GET_PROCEDURE")
                    {
                        if (repTable is REP_Models.REP_TABLES repTables)
                        {

                            if (repTables.SqlQuery.Contains("p_out=>p_ResultSet"))
                            {
                                // для процедуры с refcurcor
                                dataSourceInfo.CommandText = repTables.SqlQuery.Split('(')[0];  // без параметров
                                                                                                // выходной параметр для курсора
                                parameters.Add(new OracleParameter("p_out", OracleDbType.RefCursor)
                                    {
                                        Direction = ParameterDirection.Output
                                    });
                                dataSourceInfo.SourceType = DataSourceType.StoredProcedure;

                                /* тоже рабочий вариант
                                // cоздаем анонимный PL/SQL блок
                                dataSourceInfo.CommandText = $@"
                                    DECLARE
                                        p_ResultSet SYS_REFCURSOR;
                                    BEGIN
                                        {repTables.SqlQuery};
                                        :result_cursor := p_ResultSet;
                                    END;";
                                // добавляем параметр для результата
                                parameters.Add(new OracleParameter
                                    {
                                        ParameterName = "result_cursor",
                                        OracleDbType = OracleDbType.RefCursor,
                                        Direction = ParameterDirection.Output
                                    });
                                dataSourceInfo.SourceType = DataSourceType.Query;
                                */
                            }
                            else
                            {
                                // для функций оберток с pipelined
                                string procName = repTables.SqlQuery;
                                dataSourceInfo.CommandText = $"SELECT * FROM TABLE({procName})";
                                dataSourceInfo.SourceType = DataSourceType.Query;
                            }
                        }
                    }
                    else
                    {
                        if (repTable is REP_Models.REP_TABLES repTables)
                        {
                            dataSourceInfo.CommandText = repTables.SqlQuery;
                            dataSourceInfo.SourceType = DataSourceType.Query;
                        }
                    }
                    dataSourceInfo.Parameters = parameters;
                    dataSourceInfoList.Add(dataSourceInfo);
                }

                var templateName = query;
                var _templatePath = Path.Combine(environment.ContentRootPath, "Reports");
                var templateFile = Path.Combine(_templatePath, templateName);

                if (!File.Exists(templateFile))
                {
                    statusMessage = "Не найден файл с xlsx-шаблоном отчета.";
                    return;
                    //throw new FileNotFoundException($"Шаблон Excel не найден по пути: {templateFile}");
                }

                // запмись в лог
                LogInfo lg = new LogInfo
                    {
                        _globalFilter = globalFilter,
                        _dbname = dbname_query.Split(';')[0],
                        _query = templateName
                    };

                string jsonString = GetLogJSon(lg);

                (success, errorMessage, logid) = await _logService.LogI(Security.User.CODE, infoobject.AppCode, Tab.Index.ToString(),
                                                                      Tab.PathIndex, "R", jsonString, Tab.CodeObj, infoobject.AppCode, Security.User.SessionId, Security.User.SessionInfo);

                // сохраняем ID лога для отслеживания
                currentLogId = logid;

                // проверка отмены перед генерацией отчета
                cancellationToken.ThrowIfCancellationRequested();

                // генерируем отчет
                var stream = await ExcelService.ExportToExcelMultiSource(templateName, dbname_query, dataSourceInfoList, variables);

                // проверка отмены после генерации Excel (исключить формирование файла и изменение статуса) 
                cancellationToken.ThrowIfCancellationRequested();

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmm");
                var fileName = Path.GetFileNameWithoutExtension(query) + "_" + timestamp + Path.GetExtension(query);

                // создание DotNetStreamReference из потока
                using var streamRef = new DotNetStreamReference(stream: stream);

                await JSRuntime.InvokeVoidAsync("downloadFileFromStream", fileName, streamRef);

                (success, errorMessage) = await _logService.LogS(logid, jsonString);
                currentLogId = 0;
            }
        }
        catch (OperationCanceledException)
        {
            // отчет был отменен, без записи в лог, статус уже "C"
            statusMessage = "Формирование отчета отменено.";
            currentLogId = 0;
            throw; // проброс для обработки в Task.Run
        }
        catch (ExcelTemplateException ex)
        {
            (success, errorMessage) = await _logService.LogE(logid, ex.Message);
            statusMessage = ex.Message;
            currentLogId = 0;
        }
        catch (Exception ex)
        {
            (success, errorMessage) = await _logService.LogE(logid, ex.Message);
            statusMessage = "При формировании отчета произошла ошибка.";
            currentLogId = 0; 
            //throw new Exception("An error occurred while generating report", ex);
        }
    }

    private OracleDbType GetOracleType(string dataType)
    {
        return dataType.ToUpper() switch
        {
            "VARCHAR2" => OracleDbType.Varchar2,
            "NUMBER" => OracleDbType.Decimal,
            "DATE" => OracleDbType.Date,
            _ => OracleDbType.Varchar2   // значение по умолчанию
        };
    }

    public void Dispose()
    {
        if (_reportCts != null && !_reportCts.IsCancellationRequested)
        {
            _reportCts.Cancel();
        }

        if (currentLogId > 0 && isLoading)
        {
            var logIdToCancel = currentLogId;
            currentLogId = 0;

            _ = Task.Run(async () =>
            {
                try
                {
                    await _logService.LogC(logIdToCancel);
                    Console.WriteLine($"Excel report cancelled due to tab closing: logId={logIdToCancel}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error cancelling Excel report on disposal: {ex.Message}");
                }
            });
        }
    }

}