﻿@attribute [Authorize]
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Ra<PERSON>zen
@using Radzen.Blazor
@using EWA.Services
@using EWA.Components.Pages.EWAComp
@using System
@using System.Collections.Generic
@using System.Linq
@using System.Threading.Tasks

<RadzenCard>
    <RadzenRow AlignItems="AlignItems.Center">
        
        <RadzenColumn SizeMD="12">
            <RadzenTemplateForm Method="post" Submit="@FormSubmit" TItem="EWA.Models.SIB_Models.SIB_USERS" Data="@user" Visible="@(user != null)">
                <RadzenAlert Size="AlertSize.Small" Shade="Shade.Light" AlertStyle="AlertStyle.Danger" Variant="Variant.Flat" Title="Пароль не изменен"
                             Visible="@errorVisible">@error</RadzenAlert>
                <RadzenAlert Size="AlertSize.Small" Shade="Shade.Light" AlertStyle="AlertStyle.Success" Variant="Variant.Flat" Visible="@successVisible">
                    Пароль успешно изменен.
                </RadzenAlert>
                <RadzenStack style="margin-bottom: 1rem;">
                    <RadzenFormField Visible="!isTpaswd" Text="Старый пароль" Variant="Variant.Text">
                        <ChildContent>
                            <RadzenPassword @bind-Value="@oldPassword" style="display: block; width: 100%" Name="OldPassword" />
                        </ChildContent>
                        <Helper>
                            <RadzenRequiredValidator Component="OldPassword" Text="Не заполнен текущий пароль" />
                        </Helper>
                    </RadzenFormField>
                    <RadzenFormField Text="Новый пароль" Variant="Variant.Text">
                        <ChildContent>
                            <RadzenPassword @bind-Value="@newPassword" style="display: block; width: 100%" Name="NewPassword" />
                        </ChildContent>
                        <Helper>
                            <RadzenRequiredValidator Component="NewPassword" Text="Не заполнен новый пароль" />
                        </Helper>
                    </RadzenFormField>
                   
                    <RadzenFormField Text="Подтверждение нового пароля" Variant="Variant.Text">
                        <ChildContent>
                            <RadzenPassword @bind-Value="@confirmPassword" style="display: block; width: 100%" Name="ConfirmPassword" />

                        </ChildContent>
                        <Helper>
                            <RadzenRequiredValidator Component="ConfirmPassword" Text="Не заполнено подтверждение пароля" />
                            <RadzenCompareValidator Component="ConfirmPassword" Text="Пароли должны совпадать" Value="@newPassword" />
                        </Helper>
                    </RadzenFormField>
                </RadzenStack>
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End">
                    
                    @if (!disableSub)
                    {
                        <RadzenButton ButtonType="ButtonType.Submit" Text="Сохранить" Variant="Variant.Flat" />
                    }
                    else if (disableSub && !defform)
                    {
                        <RadzenButton Text="@($"Закрыть({countdown})")" ButtonStyle="ButtonStyle.Light" Click="@Close" />
                    }
                </RadzenStack>
            </RadzenTemplateForm>
        </RadzenColumn>
    </RadzenRow>
</RadzenCard>

@code
{
    [Parameter] public EWA.Models.SIB_Models.SIB_USERS user { get; set; }
    [Parameter] public bool isTpaswd { get; set; } = false;
    [Parameter] public bool isNeedChng { get; set; } = false;
    [Inject] protected SIBService.UserService UServ { get; set; }
    [Inject] protected SIBService.SecurityService Security { get; set; }
    [Inject] protected DialogService DialogService { get; set; }

    string oldPassword = "";
    string newPassword = "";
    string confirmPassword = "";

    string error;
    bool errorVisible = false;
    bool successVisible = false;
    int countdown = 5;
    bool disableSub = false;
    bool defform = false;

    protected async Task Close()
    {
        DialogService.Close(new { Result = true });
    }
    protected async Task FormSubmit()
    {
        successVisible = false;
        errorVisible = false;
        bool res = false;
        try
        {
            if (isTpaswd)
            {
                oldPassword = user.TECHN_PASS;
            }
            else
            {
                defform = true;
            }
            await Security.ChangePassword(user,oldPassword, newPassword);
            await UServ.UpdateUserPassHist(user);
            if (user.IStechPSWD)
            { 
                res = await UServ.UpdateUserTech(user);
                await Security.GetInfoUser(user.ID);
            } 
            successVisible = true;
            errorVisible = false;
            if (isTpaswd || isNeedChng)
            {
                disableSub = true;
                await Close();
            }
            disableSub = true;
        }
        catch (Exception ex)
        {
            errorVisible = true;
            successVisible = false;
            error = ex.Message;
        }
    }
}