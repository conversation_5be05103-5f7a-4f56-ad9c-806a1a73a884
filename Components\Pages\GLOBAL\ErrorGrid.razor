﻿@rendermode InteractiveServer
@inject NavigationManager Navigation
@inject RepService _repService
@inject EWA.Services.RepService.GET_SPR _getspr
@inject DialogService DialogService
@inject IJSRuntime JSRuntime
@using System.Collections.Generic
@using System.Data
@using Microsoft.Extensions.Configuration
@using System.Text.Json
@using Radzen.Blazor
@using Oracle.ManagedDataAccess.Client
@using Radzen
@using EWA.Services
@using EWA.Models
@using EWA.Components.Pages


<RadzenStack>   
    <RadzenCard Gap="0.5rem">
        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.End" JustifyContent="JustifyContent.Stretch" Wrap="FlexWrap.Wrap" Gap="0.5rem">
                  <RadzenFormField Variant="Variant.Flat" Style="width: 100%;">
                    <ChildContent>
                       @_errorMessage
                    </ChildContent>
                    
                </RadzenFormField>
           
        </RadzenStack>
    </RadzenCard>

    <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End">
        <RadzenStack Orientation="Orientation.Horizontal">
            <RadzenButton Text="Ok" Style="width: 80px;" ButtonStyle="ButtonStyle.Light" Click="@SaveAsync" />           
        </RadzenStack>
    </RadzenStack>
</RadzenStack>


@code {
    [Parameter] public String _errorMessage { get; set; }

    private async Task SaveAsync()
    {
        DialogService.Close();       
    }

}
