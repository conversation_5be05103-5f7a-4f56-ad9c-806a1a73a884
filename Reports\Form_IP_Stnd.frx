﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" DoublePass="true" ReportInfo.Created="07/30/2025 10:53:07" ReportInfo.Modified="09/01/2025 17:18:39" ReportInfo.CreatorVersion="2025.2.0.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    private void MasterData1_AfterPrint(object sender, EventArgs e)
    {
      DataSourceBase dataset = Report.GetDataSource(&quot;ANK_IP2&quot;);
      if (dataset.RowCount == 0)
      {
        Memo_ANK_IP2.Text = &quot;Нет&quot;;
      }
    }

    private void SuspTransactionsMess_AfterPrint(object sender, EventArgs e)
    {
      DataSourceBase dataset = Report.GetDataSource(&quot;Служебная информация(SuspTransactions)&quot;);
      if (dataset.RowCount == 0)
      {
        Memo_ST.Text = &quot;Нет&quot;;
      }
    }

    private void f_DatainfoClientIdentResults_AfterData(object sender, EventArgs e)
    {
      string datainfoclientidentresults = Report.GetColumnValue(&quot;Служебная информация.DATAINFOCLIENTIDENTRESULTS&quot;).ToString();
      
      if (string.IsNullOrEmpty(datainfoclientidentresults))
      {
        b_DatainfoClientIdentResults.Visible = false;
      }
    }

    private void f_ActiveClientIdentResults_AfterData(object sender, EventArgs e)
    {
      string activeclientidentresults = Report.GetColumnValue(&quot;Служебная информация.ACTIVECLIENTIDENTRESULTS&quot;).ToString();
      
      if (string.IsNullOrEmpty(activeclientidentresults))
      {
        b_ActiveClientIdentResults.Visible = false;
      }
    }

    private void f_NoContactClientIdentResults_AfterData(object sender, EventArgs e)
    {
      string nocontactclientidentresults = Report.GetColumnValue(&quot;Служебная информация.NOCONTACTCLIENTIDENTRESULTS&quot;).ToString();
      
      if (string.IsNullOrEmpty(nocontactclientidentresults))
      {
        b_NoContactClientIdentResults.Visible = false;
      }
    }

    private void f_NoActiveClientIdentResults_AfterData(object sender, EventArgs e)
    {
      string noactiveclientidentresults = Report.GetColumnValue(&quot;Служебная информация.NOACTIVECLIENTIDENTRESULTS&quot;).ToString();
      
      if (string.IsNullOrEmpty(noactiveclientidentresults))
      {
        b_NoActiveClientIdentResults.Visible = false;
      }
    }

    private void f_SpravkaClientIdentResults_AfterData(object sender, EventArgs e)
    {
      string spravkaclientidentresults = Report.GetColumnValue(&quot;Служебная информация.SPRAVKACLIENTIDENTRESULTS&quot;).ToString();
      
      if (string.IsNullOrEmpty(spravkaclientidentresults))
      {
        b_SpravkaClientIdentResults.Visible = false;
      }
    }

    private void f_OtherClientIdentResults_AfterData(object sender, EventArgs e)
    {
      string otherclientidentresults = Report.GetColumnValue(&quot;Служебная информация.OTHERCLIENTIDENTRESULTS&quot;).ToString();
      
      if (string.IsNullOrEmpty(otherclientidentresults))
      {
        b_OtherClientIdentResults.Visible = false;
      }
    }

    private void f_InfoBenefitPriobretatel_AfterData(object sender, EventArgs e)
    {
      DataSourceBase dataset1 = Report.GetDataSource(&quot;ANK_IP10&quot;);
      DataSourceBase dataset2 = Report.GetDataSource(&quot;ANK_IP11&quot;);
      
      string infobenefitpriobretatel = Report.GetColumnValue(&quot;ANK_IP5.INFOBENEFITPRIOBRETATEL&quot;).ToString();
      
      if ((dataset1.RowCount != 0 || dataset2.RowCount != 0) &amp;&amp; 
          infobenefitpriobretatel == &quot;Нет&quot;)
      {
        b_InfoBenefitPriobretatel.Visible = false;
      }
      else
      {
        b_InfoBenefitPriobretatel.Visible = true;
      }
    }

    private void f_11_1_empty_AfterData(object sender, EventArgs e)
    {
      DataSourceBase dataset1 = Report.GetDataSource(&quot;ANK_IP10&quot;);
      DataSourceBase dataset2 = Report.GetDataSource(&quot;ANK_IP11&quot;);
      
      if (dataset1.RowCount == 0 &amp;&amp; dataset2.RowCount != 0)
      {
        b_11_1_empty.Visible = true;
      }
      else
      {
        b_11_1_empty.Visible = false;
      }
    }

    private void f_11_2_empty_AfterData(object sender, EventArgs e)
    {
      DataSourceBase dataset1 = Report.GetDataSource(&quot;ANK_IP10&quot;);
      DataSourceBase dataset2 = Report.GetDataSource(&quot;ANK_IP11&quot;);
      
      if (dataset1.RowCount != 0 &amp;&amp; dataset2.RowCount == 0)
      {
        b_11_2_empty.Visible = true;
      }
      else
      {
        b_11_2_empty.Visible = false;
      }
    }

    private void f_Purpose_AfterData(object sender, EventArgs e)
    {
      DataSourceBase dataset = Report.GetDataSource(&quot;ANK_IP2&quot;);
      
      string purposeOfEstablishingRelations = Report.GetColumnValue(&quot;ANK_IP1.PurposeOfEstablishingRelations&quot;).ToString();
      
      if (purposeOfEstablishingRelations == &quot;Нет данных&quot; &amp;&amp; dataset.RowCount != 0)
      {
        f_Purpose.Text = &quot;Договорные отношения&quot;;
      }
    }
      }
    }
  </ScriptText>
  <Dictionary>
    <OracleDataConnection Name="smdev" ConnectionString="rijcmlqcqSAZRx+/kmWTGGD9Hx6z1ravUjPQ4Y+NahKxl6jwchlnOv2XzTWJBJGc/Dj7cULc0XCc2lGVsd8b+mImXS/qX9rTb9hkn2LTRlDbFb9ync=">
      <ProcedureDataSource Name="ANK_IP1" DataType="System.Int32" Enabled="true" TableName="ANK_IP1" SelectCommand="ANKENP_ANK_BASIC">
        <Column Name="JURIDIC_PERSON_NAME" DataType="System.String"/>
        <Column Name="PREV_SURNAME" DataType="System.String"/>
        <Column Name="CITIZENSHIP" DataType="System.String"/>
        <Column Name="RESIDENCEPLACE" DataType="System.String"/>
        <Column Name="CORRESPONDENCEADDRESS" DataType="System.String"/>
        <Column Name="BIRTHDATE" DataType="System.String"/>
        <Column Name="BIRTHPLACE" DataType="System.String"/>
        <Column Name="ENTRYVISAINFO" DataType="System.String"/>
        <Column Name="DOC" DataType="System.String"/>
        <Column Name="PERSONALNUMBER" DataType="System.String"/>
        <Column Name="REGISTRATIONAUTHORITY" DataType="System.String"/>
        <Column Name="REGISTRATIONINFOLAST" DataType="System.String"/>
        <Column Name="PAYNUMBER" DataType="System.String"/>
        <Column Name="TAXOFFICENAME" DataType="System.String"/>
        <Column Name="ANNUALREVENUE" DataType="System.String"/>
        <Column Name="CONTACTPHONE" DataType="System.String"/>
        <Column Name="EMAIL" DataType="System.String"/>
        <Column Name="WEBSITE" DataType="System.String"/>
        <Column Name="OKVED" DataType="System.String"/>
        <Column Name="OTHERBANKSACCOUNTS" DataType="System.String"/>
        <Column Name="OTHERBANKSACCOUNTSSFM" DataType="System.String"/>
        <Column Name="PURPOSEOFESTABLISHINGRELATIONS" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
        <ProcedureParameter Name="P_USERLOG" DataType="126" Expression="[P_USERLOG]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="ANK_IP10" DataType="System.Int32" Enabled="true" TableName="ANK_IP10" SelectCommand="ANKENP_ANK_INFORM_FL_BP">
        <Column Name="NAME" DataType="System.String"/>
        <Column Name="CITIZENSHIP" DataType="System.String"/>
        <Column Name="BIRTH" DataType="System.String"/>
        <Column Name="REGISTRATIONPLACE" DataType="System.String"/>
        <Column Name="DOC" DataType="System.String"/>
        <Column Name="REGISTRATIONINFO" DataType="System.String"/>
        <Column Name="PAYNUMBER" DataType="System.String"/>
        <Column Name="GROUND" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
        <ProcedureParameter Name="P_USERLOG" DataType="126" Expression="[P_USERLOG]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="ANK_IP11" DataType="System.Int32" Enabled="true" TableName="ANK_IP11" SelectCommand="ANKENP_ANK_FIN_INSTITUT_BP">
        <Column Name="NAME" DataType="System.String"/>
        <Column Name="REGISTRATIONINFO" DataType="System.String"/>
        <Column Name="LOCATION" DataType="System.String"/>
        <Column Name="PAYNUMBER" DataType="System.String"/>
        <Column Name="GROUND" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
        <ProcedureParameter Name="P_USERLOG" DataType="126" Expression="[P_USERLOG]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="ANK_IP2" DataType="System.Int32" Enabled="true" TableName="ANK_IP2" SelectCommand="ANKENP_ANK_BASIC_CONTRACTS">
        <Column Name="CONTRACTCREDITACCOUNTTYPE" DataType="System.String"/>
        <Column Name="CONTRACTSALARYACCOUNTTYPE" DataType="System.String"/>
        <Column Name="CONTRACTDEPOSITACCOUNTTYPE" DataType="System.String"/>
        <Column Name="CONTRACTEMONEYACCOUNTTYPE" DataType="System.String"/>
        <Column Name="CONTRACTACCOUNTTYPE" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="Служебная информация(SuspTransactions)" DataType="System.Int32" Enabled="true" TableName="Служебная информация(SuspTransactions)" SelectCommand="ANKENP_ANK_SERVICEINFO_SUSPTRN">
        <Column Name="CARD_CREATE_DATE" DataType="System.String"/>
        <Column Name="SUM_CURR" DataType="System.String"/>
        <Column Name="OPERATION_FORM_CODE" DataType="System.String"/>
        <Column Name="SUSPICION_CODE" DataType="System.String"/>
        <Column Name="CARD_NUM_DATE" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="ankjp_sol_odl" DataType="System.Int32" Enabled="true" TableName="ankjp_sol_odl" SelectCommand="ANKENP_ANK_SERVICEINFO_SOL_ODL">
        <Column Name="SOL_ODL_BLOCK" DataType="System.String"/>
        <Column Name="SOL_ODL_BUSS" DataType="System.String"/>
        <Column Name="SOL_ODL_UFM" DataType="System.String"/>
        <Column Name="SOL_ODL_DFR" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="Служебная информация" DataType="System.Int32" Enabled="true" TableName="Служебная информация" SelectCommand="ANKENP_ANK_SERVICEINFO">
        <Column Name="CLIENTSERVICEVALUE" DataType="System.String"/>
        <Column Name="CLIENTFURTHERACTIONS" DataType="System.String"/>
        <Column Name="TYPECLIENTFURTHERACTIONS" DataType="System.String"/>
        <Column Name="STANDARDCLIENTFURTHERACTIONS" DataType="System.String"/>
        <Column Name="OTHERCLIENTFURTHERACTIONS" DataType="System.String"/>
        <Column Name="ZAPROSCLIENTACTIONSRISK" DataType="System.String"/>
        <Column Name="FINTRCLIENTACTIONSRISK" DataType="System.String"/>
        <Column Name="OTKAZFINTRANSCLIENTACTIONSRISK" DataType="System.String"/>
        <Column Name="OTKAZDOGCLIENTACTIONSRISK" DataType="System.String"/>
        <Column Name="OTKAZDBOCLIENTACTIONSRISK" DataType="System.String"/>
        <Column Name="OTHERCLIENTACTIONSRISK" DataType="System.String"/>
        <Column Name="CLIENTFURTHERVERIFICATION" DataType="System.String"/>
        <Column Name="STOPOPERPOST" DataType="System.String"/>
        <Column Name="DOVEROPER" DataType="System.String"/>
        <Column Name="NEGATIVGOODWILL" DataType="System.String"/>
        <Column Name="ASSOCIATEDNEGATIVGOODWILL" DataType="System.String"/>
        <Column Name="CLIENTIDENTRESULTS" DataType="System.String"/>
        <Column Name="DATAINFOCLIENTIDENTRESULTS" DataType="System.String"/>
        <Column Name="ACTIVECLIENTIDENTRESULTS" DataType="System.String"/>
        <Column Name="NOCONTACTCLIENTIDENTRESULTS" DataType="System.String"/>
        <Column Name="NOACTIVECLIENTIDENTRESULTS" DataType="System.String"/>
        <Column Name="SPRAVKACLIENTIDENTRESULTS" DataType="System.String"/>
        <Column Name="OTHERCLIENTIDENTRESULTS" DataType="System.String"/>
        <Column Name="CLIENTRESULTS" DataType="System.String"/>
        <Column Name="SIGNFATCA" DataType="System.String"/>
        <Column Name="TERRORPEPSANCTION" DataType="System.String"/>
        <Column Name="CLIENTTOTALRISK" DataType="System.String"/>
        <Column Name="TOTALRISKFACTORS" DataType="System.String"/>
        <Column Name="CLIENTPROFILERISK" DataType="System.String"/>
        <Column Name="PROFILERISKFACTORS" DataType="System.String"/>
        <Column Name="CLIENTOPERRISK" DataType="System.String"/>
        <Column Name="OPERRISKFACTORS" DataType="System.String"/>
        <Column Name="CLIENTGEORISK" DataType="System.String"/>
        <Column Name="GEORISKFACTORS" DataType="System.String"/>
        <Column Name="DT_FILL" DataType="System.String"/>
        <Column Name="FIO" DataType="System.String"/>
        <Column Name="UPDATEDT" DataType="System.String"/>
        <Column Name="USERLOG" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
        <ProcedureParameter Name="P_USERLOG" DataType="126" Expression="[P_USERLOG]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="Служебная информация(FinTransactions)" DataType="System.Int32" Enabled="true" TableName="Служебная информация(FinTransactions)" SelectCommand="ANKENP_ANK_SERVICEINFO_FINTRNS">
        <Column Name="FINTRANSACTIONS" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="ANK_IP5" DataType="System.Int32" Enabled="true" TableName="ANK_IP5" SelectCommand="ANKENP_ANK_RELATED_PERSONS">
        <Column Name="CONTRACTORSJUR" DataType="System.String"/>
        <Column Name="CONNECTEDANDPARTNERORG" DataType="System.String"/>
        <Column Name="INFOREPRESENTATIVE" DataType="System.String"/>
        <Column Name="INFOBENEFITPRIOBRETATEL" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
        <ProcedureParameter Name="P_USERLOG" DataType="126" Expression="[P_USERLOG]" Direction="Input"/>
      </ProcedureDataSource>
    </OracleDataConnection>
    <Parameter Name="p_date" DataType="System.DateTime" AsString=""/>
    <Parameter Name="p_code_subject" DataType="System.String" AsString=""/>
    <Parameter Name="p_userlog" DataType="System.String" AsString=""/>
  </Dictionary>
  <ReportPage Name="Page1" Landscape="true" PaperWidth="297" PaperHeight="210" Watermark.Font="Arial, 60pt">
    <DataBand Name="MasterData1" Top="155.76" Width="1047.06" Height="353.26" CanGrow="true" CanShrink="true" CanBreak="true" DataSource="ANK_IP1">
      <TextObject Name="Memo24" Left="3.78" Top="128.86" Width="1039.37" Height="34.02" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;6. Реквизиты документа, удостоверяющего личность (наименование, серия и номер документа, кем и когда он выдан, срок действия данного документа):&lt;/b&gt;&#13;&#10;[ANK_IP1.DOC]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo32" Left="3.78" Top="220.09" Width="1039.37" Height="34.02" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;8.1 Сведения о регистрации:&lt;/b&gt;&#13;&#10;[ANK_IP1.REGISTRATIONAUTHORITY]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo3" Left="3.78" Top="319.24" Width="1037.89" Height="34.02" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;10. Виды предпринимательской деятельности: &lt;/b&gt;&#13;&#10;[ANK_IP1.OKVED]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo39" Left="3.78" Top="52.91" Width="1043.15" Height="15.12" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;3. Место жительства и (или) место пребывания:&lt;/b&gt; [ANK_IP1.RESIDENCEPLACE]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo5" Left="3.78" Width="1043.15" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;1. Фамилия, Собственное имя, Отчество: &lt;/b&gt; [ANK_IP1.JURIDIC_PERSON_NAME]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo9" Left="3.78" Top="26.46" Width="1043.15" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;2. Гражданство: &lt;/b&gt; [ANK_IP1.CITIZENSHIP]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo12" Left="3.78" Top="102.05" Width="1043.15" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;5. Место рождения: &lt;/b&gt; [ANK_IP1.BIRTHPLACE]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo13" Left="3.78" Top="75.59" Width="1043.15" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;4. Дата рождения: &lt;/b&gt; [ANK_IP1.BIRTHDATE]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo14" Left="3.78" Top="166.3" Width="1043.15" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;7. Идентификационный номер: &lt;/b&gt;[ANK_IP1.PERSONALNUMBER]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo15" Left="3.78" Top="294.8" Width="1043.15" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;9. Учетный номер плательщика: &lt;/b&gt; [ANK_IP1.PAYNUMBER]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo8" Left="3.78" Top="196.54" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;8. Сведения о государственной регистрации (регистрационный номер, дата регистрации, наименование регистрирующего органа):&lt;/b&gt;" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo23" Left="3.78" Top="257.01" Width="1039.37" Height="34.02" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;8.2 Сведения о последней регистрации изменений:&lt;/b&gt;&#13;&#10;[ANK_IP1.REGISTRATIONINFOLAST]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <DataHeaderBand Name="Header2" Width="1047.06" Height="151.76" CanGrow="true">
        <TextObject Name="Memo2" Left="308.02" Top="116.5" Width="578.27" Height="26.46" ShiftMode="WhenOverlapped" Text="АНКЕТА КЛИЕНТА - ИНДИВИДУАЛЬНОГО ПРЕДПРИНИМАТЕЛЯ" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <PictureObject Name="Picture1" Left="389.29" Top="1.05" Width="268.35" Height="105.83" Fill.Color="White" ShiftMode="WhenOverlapped" ImageFormat="Bmp" Image="Qk0WOgEAAAAAADYAAAAoAAAA+AAAAGwAAAABABgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7/AAAADwwPEA4QEA4QEA0QAAAAuri6////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////AAAAAAAAAAAAAAAAAAAAAAAAjIuM////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////GxobAAAABAIEBAIEBAIEAAAAZ2Zn////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////QkBCAAAABAIEBAIEBAIEAAAAQT9B//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7///////////////////////////////////////////////////7/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////Z2dnAAAABAIEBAIEBAIEAAAAGRgZ//////////////////////////////////////////////////////7/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////0M7QhIKEWlhaREJEPTw9Q0JDV1VXb25vkpGSurm67ezt//////7/////////9/X3eHd4PTw9LCssNzU3W1lbjIuMoaChn56fnpyenJucm5mbkI+Q8fDx7OrsZWRlPz0/GxkbAQABAAAAAAAAAgACGRcZS0pLlJOU////////////////wcDBkpGSmZiZmZiZmZiZlpWWo6Gj////////////trW2lpSWmZiZmZiZmZiZlpWWurm6mZeZAAAABAIEBAIEBAIEAQABAAAA////trS2UU9RExITAQABIB4gfXx9////////////////////////////////19fXjIqMZ2dnYV9hcG9wkpGSxcTF////////////////////7u7uiYiJmZiZmZiZmZiZlpSWsrGy////////////rKusl5WXmZiZmZiZmZiZkY+R6Ofo////////6efpkI6QmZiZmZiZmZiZlJSUtLO0////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////n52fpaOlpaOlpaOlpaSlpaOlpaSlpaOlpaOlpaOlpaSlpaOlpaSlpaOlpaOlpaOlpaSlpaOlpaSlpaOlpaOlpaOlpaSlpaOlpaSlpaOlpaOlpaOlpaSlpaOlpaSlpaOlpaOlpaOlpaSlpaOlpaSlpaOlpaOlpaOlpaSlpaOlpaSlpaOlpaOlpaOlpqSmpaOlo6GjysfK////////////393fBAIEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////////////trS2AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAt7e3ysvKAAAAAAAAAAAAAAAAAgACAgACAAAAAAAAAAAAAAAAAAAAeHZ4////////fXx9AAAAAAAAAAAAAAAAAAAAAAAA////////////ZmRmAAAAAAAAAAAAAAAAAAAADgwO0c/RAAAABAIEBAIEBAIEAwEDAgECBwYHAAAAAAAAAAAAAAAAAAAAAAAAAAAA6unq//////////7/////0tHSDgwOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYF9g////////////////AAAAAAAAAAAAAAAAAAAAAAAA////////////AAAAAAAAAAAAAAAAAAAAAAAAODY4////////wcHBAAAAAAAAAAAAAAAAAAAAZmVm////qKWor62vsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wq6mr0c3R//////////////7/////lZKVe3l7fn1+f3x/fnx+f31/fnx+f3x/fn1+f3x/fnx+f3x/fnx+f3x/fn1+f3x/fnx+f3x/fnx+f3x/fn1+f3x/fnx+f3x/fnx+f3x/fn1+f3x/fnx+f3x/fnx+f3x/fn1+f3x/fnx+f3x/fnx+f3x/fn1+f3x/fnx+f3x/fnx+f3x/f31/fnx+fnx+f3x/enh6ko+S////////////AAAAAAAAAwEDBAIEBAIEBAIEAQABAAAAAAAAAAAAAAAAAwEDAAAA1tXW////////AAAAAgECAwEDBAIEBAIEAwEDAAAAAAAAAAAAAwIDBAEEBAIEBAIEAAAAkpKS3NzcAAAAAAAAAAAAAAAAAAAAAAAAAAAABAIEAwEDBAIEAgECAAAAcnFy////sK+wAAAABAIEBAIEBAIEAwEDAAAA////////////mJeYAAAABAIEBAIEBAIEAgACAAAA7u3uAAAABAIEBAIEBAIEBAEEAwEDAQABBAEEBAEEBAEEBAIEBAIEAwEDAAAAAAAA////////////q6mrAAAAAAAAAwEDBAEEBAIEBAIEBAIEBAIEAwEDAgACAAAAAAAA2NbY////////nJucAAAABAIEAwEDBAIEAAAApaSl////////AAAAAwEDBAIEBAIEBAEEAwIDAAAA4N/g////vr2+AAAABAIEBAIEBAIEAAAAoqKi//7/cW9xfnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fXx9fnt+fXx9fnt+fXx9fnt+fXx9fnt+fXx9fnt+fXx9fnt+fXx9fnt+fXx9fnx+fXx9fnt+fXx9fnx+fXx9fnt+fXx9fnx+fXx9fnt+fXx9fnx+fXx9fnt+fXx9fnx+fXx9fnt+fXx9fnx+fXx9fnt+fXx9f3x/bmxu7ezt////////////////////vLm8e3p7hIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGCg4GDeHZ4////////////AAAAAwEDBAEEBAIEBAIEAQABEhASfHx8eHd4TU1NDQwNAAAAAAAAgoGC////zc3NAAAAAwEDBAIEBAIEAwEDAAAAgoKCf39/ERARAAAAAwEDBAIEBAIEAAAAa2pr4+PjAAAADg0OZ2dnq6qr09LT5eTlWlpaAAAAAwEDBAEEAwEDAQABAAAA////1tXWAAAABAIEBAIEBAIEAwEDAAAA6ejp////////vby9AAAABAEEBAIEBAIEAwEDAAAA/v3+AAAAAwEDBAIEBAIEBAIEBAEEBAEEAAAAAAAAAAAAAwEDBAEEBAIEAwEDAAAAWFdY////9vT2AAAAAgACAwEDBAIEBAEEAwEDAAAAAAAAAQABBAEEAwEDAwEDAAAAAAAA6unq////////AAAAAQABBAEEAwEDAAAAJyYn////3NvcAAAABAIEBAIEBAIEBAIEBAEEAAAANzU3////uLi4AAAABAIEBAIEAwEDAAAA1NTU4+Ljend6g4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEf35/kY+R////////////////////////2tnaeXd5g4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDd3V37ezt////////IB4gAAAABAEEBAIEBAIEAAAAOTc5////////////////////n56feHZ4////+vn6AAAAAwEDAwEDBAIEAwEDAAAAube5////m5mbAAAAAwEDBAIEBAIEAAAAQkBC////4uLi////////////////////////NjU2AAAAAwEDBAEEAwEDAAAA////+/v7AAAAAwEDBAIEBAIEBAIEAAAAwsHC////////5eXlAAAAAwEDBAIEBAIEAwEDAAAA7OvsAAAAAQABBAIEBAIEBAIEBAEEAAAAUVBRsrGyBgUGAAAAAwIDBAEEBAEEAgECAAAA////bWttAAAABAIEAwEDBAEEAwIDAAAAZGJklZSVAAAAAQABBAEEAwEDBAIEAAAAFBMU////////fn1+AAAABAEEBAIEAwEDAAAA/Pz8raytAAAABAEEBAIEBAIEBAIEBAEEAwIDAAAA6Ojoube5AAAABAIEBAIEAwEDAAAA////xcTFfHp8hIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDend61NHU/////////////////////////Pv8bmxue3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7cnByx8XH////////0M/QAAAAAQABBAEEBAEEBAEEAAAAAAAAAAAAFxYXWlhatbS1//////////7/////Pz8/AAAAAwEDBAEEAwIDBAMEAAAAAAAALi0uAAAABAEEBAIEBAIEAAAAGBYY////////////////////////////////IB4gAAAABAEEAwEDAAAAEQ8R////////AAAAAgACBAIEBAIEBAIEAAAAnZyd////////////AAAAAwEDBAIEBAIEBAEEAAAAzc3NHx4fAAAABAEEBAIEBAIEAAAABQMF////////+fj5AAAAAwEDAwEDBAEEBAIEAAAA29vbLy0vAAAAAwEDBAIEBAEEAAAALy0v////////4+LjAAAAAwEDBAIEAwEDAwEDAAAAw8HD////////AAAAAgACBAIEAwEDAAAAi4mLhYSFAAAABAIEBAIEBAEEBAEEBAEEAwIDAAAAQT9Bx8bHAAAABAIEBAIEAgACAAAA////oqCidXN1enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7eXh5e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6end6dHN0////////////////////////////////wsDCxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxcPFycfJ8vDy////////////vLu8AAAAAAAAAAAAAgACBAEEAwEDAwEDAAAAAAAAAAAAAAAAp6Sn////////////Hx4fAAAAAAAAAAAAAwEDAwEDAgACAAAABAEEBAIEBAIEBAIEAQABAAAA////////////////////////6unqHRsdAAAAAwEDAwIDAAAARkVG/vz+////////DAsMAAAABAIEBAIEBAIEAAAAaWhp////////////AAAAAQABBAIEBAIEBAEEAAAAo6KjSUdJAAAABAIEBAIEBAIEAAAAOjk6////////////GRgZAAAABAIEBAIEBAEEAAAAs7KzLCosAAAABAIEBAIEBAIEAAAAdXR1////////////HRwdAAAABAIEBAIEBAIEAAAAaGho////////XFtcAAAAAwEDBAIEAAAAGhcaVlVWAAAABAIEBAEEAwIDAAAAAwEDBAEEAwEDAAAAe3l7AAAABAIEBAEEAAAAIB8g////1dPVzszOz83Pzs3Ozs3Oz87Pz87Pzs3Oz83Pzs3Ozs3Oz87Pz87Pzs3Oz83Pzs3Ozs3Oz87Pzs7Ozs3Oz83Pzs3Ozs3Oz87Pz87Pzs3Oz83Pzs3Oz83Pz87Pz87Pzs3Oz87Pzs3Oz83Pz87Pz87Pzs3Oz87Pzs3Oz87Pz87Pz87Pzs3Oz87Pzs3Oz87Pz87Pz87Pzs3O0M7Qzs3Oz87Pz87Pz87Pzs3Oz87Pzs3Oz87Pz87Pz87Pzs3Oz87PzczN397f////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////19fXDw0PkI6Q/fz9////////////jo2OOTc5AAAAAAAAAAAAAAAABAIEAwEDAwIDAQABAAAAi4mL////////////xsbGWFdYEhASAAAAAAAAAAAAAAAAAwEDBAIEBAIEBAIEAwEDAAAA//////////////7/////////////pKOkAAAAAwEDAwEDAQABVFNU////////////NTM1AAAABAIEBAIEBAIEAQABAAAA////////////BAMEAQABBAIEBAIEBAIEAAAAeXd5c3NzAAAABAIEBAIEBAIEAAAAJCEk////////////EhESAAAABAIEBAIEBAIEAAAApKOkX15fAAAABAEEBAIEBAIEAAAAMC4w////////////JSMlAAAABAIEBAIEBAEEAAAAQ0JD////////7+7vAAAABAIEAwEDBAEEAAAAAAAAAwEDBAIEAwEDAAAAaWhpAAAAAwIDAwEDAwEDAAAAAwEDBAIEAwEDAAAAU1JT////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////4+Pj29rb4uHi8vHy////////////////////////////////////////////////////////////////zs3OAAAAAAAAAAAAIiEij42P////////////////+Pf4vb29c3JzAAAAAwEDBAEEAwEDAwADAAAA9/b3////////wb/B////////////9PT0yMfIaGdoAAAAAwEDBAIEBAIEBAIEAAAA5uXmy8nL////////////////////////X15fAAAABAEEAwEDAAAAAQAB////////XFpcAAAABAIEBAIEBAIEBAEEAAAAAAAAjYyNUlFSAAAAAwEDBAIEBAIEBAEEAAAATktOnZ2dAAAABAEEBAIEBAIEAQABAQAB////////iYaJAAAABAIEAwEDBAIEAwEDAAAAt7a3y8rLAAAAAwEDBAIEBAIEAwEDAAAAnJqc////9fP1AAAAAwEDBAIEBAIEBAEEAAAAS0pL////////////Ozo7AAAABAEEBAIEBAEEAwEDBAIEBAIEAgACAAAA////AAAAAQABBAEEAwEDAwEDBAIEBAIEBAEEAAAAZmVmz87PwsHC0dDR9fT1//////////////////////////////////7//////v3+q6mrjIuMioiKoqGizMvM+fn5/////////////f39/Pz8+fj5////9/b3+fj5+vn6+vn6+vn6+vn69/b3/////////////fz99fP1+vn6+vn6+vn6+vn6+Pf4////+vj6+Pf4+vn6+vn6+vn6+vn6+Pf4////////////////////+Pj4+vn6+vn6+vn6+vn6+fj58/Lz////////////////////////////////////////////AAAAAAAAAwEDAwEDAwEDAwEDAwEDAwEDAwEDAwEDAwEDAwADBQMFJCIkdnV2//////////7/////////////////19bXTUpNAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFhQW1dXV////0M7QAAAAAwEDAwEDAwEDAwEDAAAAzszO////////////w8LDAAAABAEEAwEDAAAAAAAAAAAAHx0fcnBysrGy5eTl/v7+29jbAAAAAgACBAIEBAIEAwEDAAAAgH+A////////BQMFAAAAPTs9g4KDuLa41tXWn5+fAAAAAwEDBAIEBAIEBAEEAAAAzs3OKSgpAAAAOTc5d3Z3oqGit7a3srCyDw4PAAAABAIEBAEEAwIDAAAApKKk////gICAAAAABAIEBAIEBAIEBAIEBAEEAQABAAAAAAAAAwIDBAEEBAIEBAIEAwEDAAAAKSgpxsbGAAAABAEEBAIEBAIEAwEDAQABAAAAAAAAAAAAAwIDAwEDBAIEAwEDBAMEAAAA+Pf4////ISAhAAAAAwEDBAEEBAIEAwIDAAAAEA4QAAAAAQABBAIEBAIEBAIEAwEDAAAAkpKS////////////09LTAAAAAwEDBAIEBAIEBAIEBAIEBAEEAAAALSst////d3Z3AAAAAwEDBAIEBAIEBAIEBAIEBAEEAwIDAAAAAAAAAAAAAAAAAAAAAAAAOzk7pqWm//////////7/////////////b25vAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAe3l7a2prAAAAAAAAAAAAAAAAAAAAAAAAycfJ////////////AAAAAAAAAAAAAAAAAAAAAAAAEA4Qz87PAAAAAAAAAAAAAAAAAAAAAAAAdHJ0////////////aWdpAAAAAAAAAAAAAAAAAAAAAAAAODY4////////////////////////////////////////////VVRVAAAAAQABAAAAAQABAAAAAQABAAAAAQABAAAAAQABAAAAAAAAAAAAAAAAAAAAjoyO////////////////aWZpAAAAAAAAAQABAwEDBAEEAwIDBAIEAwEDBAEEAQABAAAATEpM////////bGtsAAAAAQABAAAAAQABAAAACAcI////////////tra2AAAABAIEBAIEBAIEBAEEAwEDAAAAAAAAAAAAAAAAAAAAAAAAAwEDAwEDBAIEBAEEBAIEAAAAXVxd////////amhqAAAAAAAAAAAAAAAAAAAAAAAABQMFAwEDBAIEBAEEAwEDAAAAuLa4kpGSAAAAAAAAAAAAAAAAAAAAAAAAAQABAwEDBAIEBAEEAwIDAAAAm5qb////qKeoAAAABAIEBAIEBAIEAwEDAwEDAwIDBAEEBAEEBAEEBAIEBAIEBAIEBAEEAAAAPz0/6enpAAAABAEEBAIEBAIEBAIEAwEDAwEDAwEDAwEDBAEEBAIEBAEEAwIDAAAARERE////////////AAAAAAAAAwIDAwEDBAEEAwEDAAAAAgACBAIEBAIEBAIEAwEDAAAAAAAA////////////////////HRsdAAAABAEEBAIEBAIEBAIEBAEEAAAAeHd4////////AAAAAgACBAIEBAIEBAIEBAIEBAIEBAEEAwEDBAIEBAIEBAEEBAEEAgECAAAAAAAAAAAAtbS1////////////ubi5AAAAAwEDBAIEBAIEBAIEBAEEAwEDAgACAwIDAwEDBAEEAwEDBAEEAAAAFBQU8vHyAAAAAwEDBAIEAwIDBAIEAAAAaGdo////////////Q0FDAAAABAIEAwEDBAIEAwEDAAAA////AAAAAgACBAIEAwIDBAIEAAAAFhUW//////7/////LSstAAAABAEEAwIDBAIEAwIDAAAAtLS0////////////////////////////////////////////uLe4AAAAAwEDBAIEAwIDBAIEAwIDBAIEAwIDBAIEAwIDBAIEAwIDBAEEAwEDAwADAAAAXVxd////////sbGxAAAAAwEDBAEEAwEDBAIEAwEDBAEEAAAAAAAAAAAAAwEDAgECAAAA////////////Dw8PAAAABAEEBAIEAwEDAAAAhYSF////////rKysAAAABAIEBAIEBAIEBAEEAwEDAgACAwEDBAEEBAEEAwEDAwEDAwEDBAEEBAEEAwEDAAAAAAAA4eDh////////raytAAAAAAAAAAAAAgACAwEDAwEDAwEDBAEEAwEDAAAAAAAAAAAA////2NfYAAAAAAAAAAAAAgACAwEDAwEDAwEDBAEEAwEDAQABAAAAAAAA////////zs3OAAAABAEEBAEEAwEDBAEEBQQFAAAAAAAAAwEDBAEEBAEEBAIEAwEDAgACAAAAv76/////AAAAAAAAAQABAwEDBAEEBAEEBAIEBAIEAwEDAwEDAQABAAAAAAAALS0t////////////////+vn6CgoKAAAAAQABAwEDBAEEBAIEBAIEBAEEBAEEAwEDAAAAAAAA2NfY////////////////////tLO0AAAAAwIDBAEEBAEEBAEEAwEDAAAAwL/A////////VlVWAAAAAwEDAwEDBAIEBAIEBAIEBAIEBAEEBAEEAwEDAwEDAwEDBAEEBAEEAwEDAAAAAAAAXFtc////////bWxtAAAABAIEBAIEBAIEBAIEAwEDAAAAAAAAAAAAAwEDAwEDBAIEAwIDBAEEAAAA////AAAAAgACAwIDBAIEAwIDAAAABwYH////////////oqKiAAAAAwEDBAIEAwIDBAIEAAAArKusTUxNAAAAAwIDBAIEAwIDBAEEAAAA+vn6////////AAAAAwADAwIDBAIEAwIDAwEDAAAA////////////////////////////////////////////////////AAAAAwADAwIDBAIEAwIDBAIEAwIDAwEDAAAAAAAAAwEDBAIEAwIDBAIEAwEDBQIFAAAAnZud////R0ZHAAAABAEEAwIDBAIEAwEDBQQFAAAACAYIIiAiDgsOAAAAAAAAAAAAh4eH////////7u3uAAAAAgACBAIEAwEDAgACAAAA/vz+////oqCiAAAABAIEBAIEBAEEAwIDAgACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMDAw5OPk////////////+vn6X15fOjk6EhASAAAAAAAAAAAAAAAAAAAAAAAAFhUWfHp8////////////aWlpQUBBFxUXAAAAAAAAAAAAAAAAAAAAAAAAAAAATEtM8fDx////////8/LzAAAAAAAAAAAAAAAAAAAAAAAAz87PGhkaAAAAAAAAAAAAAAAAAAAAAAAAmpia////////NjQ2HhweAgACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAASEdIwL/A////////////////////////////i4qLAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALy0v8vHy//////7/////////////////////AAAAAAAAAAAAAAAAAAAAAAAAAAAA////////////7OvsAAAAAAAAAAAAAwEDBAIEBAIEBAEEBAIEAwIDAAAAAAAAAAAAAQABBAEEBAEEAwIDAwIDAAAAVFJU////oaChAAAAAwEDBAIEBAIEBAEEAAAAwL/A////sbCxAAAAAwIDAwEDBAIEAwEDAAAApqWmX15fAAAABAEEAwIDBAIEAwEDAAAA6Obo////////////AAAABAIEAwEDBAIEAwIDAAAAQUBBurm6AAAABAEEAwIDBAIEAwEDAAAAl5aX////397fAAAAAwIDBAIEAwIDBAEEAAAAODc4////////////////////////////////////////////////////ODc4AAAABAIEAwIDBAIEAwIDAwEDAgECMTAxFhUWAAAAAAAABAIEAwIDBAIEAwEDAgACAAAA////Ojk6AAAAAwIDBAIEAwIDAwEDAAAA1NLU////////////////4uLilZOVVlVW////////////i4qLAAAABAEEAwIDAwEDAAAAPDo8////lZWVAAAABAIEBAIEBAEEAAAADw4P////////1tXWt7W3oZ+hlpWWnZydrKusz87P////////////////////////////////////////////////+Pf48PDw+/r7////////////////////////////////////////////+vr68fDx9/f3////////////////////////////////////////////////////////////8e/xpqamhYOFhYWFsrGy////////////////////////////////////8PDw4+Pj4ODg6+vr////////////////////////////////////////////////////0tLSn56fenp6amlqdHN0nZyd9fT1////////////////////////////////////////////////////////////////////////////////////////EhESAAAABAIEBAIEBAIEAwEDAAAAhIOE////raytAAAAAQABBAIEBAEEBAEEAwIDAAAApqWm////AAAAAAAAAwEDBAIEBAIEAQABAAAAeXh58vHyIiAiAAAABAEEAwIDBAEEAAAAOTc5ysnKAAAAAwEDBAIEAwIDBAEEAAAAjoyO////////////MTAxAAAABAIEAwIDBAIEAgACAAAA////AAAAAwEDBAEEAwIDBAIEAAAAODY4////mZiZAAAABAEEAwIDBAIEAwEDAAAAmZiZ////////////////////////////////////////////////////mJeYAAAAAwEDBAIEAwIDBAIEAwEDAAAA////////+/n7EA4QAAAABAIEAwIDBAIEAwEDAAAAnZydbm1uAAAABAEEAwIDBAIEAQABDAoM4eHhyMfIx8fHyMfIyMjIz87P1dXV2djZw8LD7+7v////////IyMjAAAAAwEDBAEEAwIDAAAA0tDSlZOVAAAABAIEBAIEBAEEAAAAKCYo////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////ExETAAAABAIEBAIEBAEEAwEDAAAA////////////29nbAAAAAwEDBAIEBAIEBAEEAAAAAAAA////1dLVAAAAAAAAAwEDBAIEBAIEAQABAAAAAAAAAAAAAwEDAwIDBAIEAwIDAwEDAAAA////AAAAAwEDAwEDBAIEAwIDAwEDAAAAAAAAAAAAAAAAAwIDAwEDAwEDBAIEAwIDBAEEAAAA0c/RLCssAAAAAwIDBAIEAwIDAwADAAAA////VlVWAAAAAwEDBAIEAwIDBAEEAAAA/fz9////////////////////////////////////////////////////+vn6AAAABAIEAwEDBAIEAwIDBAEEAAAArayt////////oJ+gAAAAAwEDBAIEAwIDBAEEAAAAQT9BxMTEAAAAAwIDBAIEAwIDBAEEAQABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAATkxO////////////AAAAAgACAwEDBAIEAAAAEA4QlZSVAAAABAIEBAIEBAEEAAAARkVG////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////VlRWAAAABAIEBAIEBAIEAwEDAAAA1tXW//////7/////MC4wAAAABAIEBAIEBAIEAwEDAAAApKOk////5uTmFxcXAAAAAAAAAAAAAAAAAgACBAEEAwEDAwIDBAIEAwIDBAIEAwEDAAAAx8bHPDs8AAAABAEEAwIDBAIEAwIDAwEDAQABAgACAQABAwIDAwEDBAIEAwIDBAIEAwEDAAAAZWVllJSUAAAABAIEAwIDBAIEAwEDAAAA0c/REA8QAAAABAEEAwIDBAIEAAAAJSQl////////////////////////////////////////////////////////////FxYXAAAABAEEAwIDBAIEAwEDAAAALS4txsXGv7+/Dw4PAAAABAEEAwIDBAIEAwIDAAAAFhQW////AAAAAgACAwIDBAIEAwIDBAEEAAAAAAAAAAAAAAAAAgECBAEEAwIDBAIEAQABAAAA////////////qaipAAAAAwEDBAEEBAEEAAAABwYHAgACBAIEBAIEBAEEAAAAYmJi////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////tbW1AAAABAEEBAIEBAIEBAEEAAAAQD9A////////////dXR1AAAABAEEBAIEBAIEBAEEAAAAQkJC////////////8vHyjo6OTUtNGRcZAAAAAAAAAAAAAAAAAwIDBAIEAwIDBAIEAAAAXVxdpaWlAAAAAwEDBAIEAwIDBAIEAwEDAwEDAAAAAQABAAAAAgACAwEDBAIEAwIDBAEEAQABAAAA////AAAAAwEDBAIEAwIDBAIEAAAAbmxuQ0NDAAAAAwEDBAIEAwIDAAAAFxYX////////////////////////////////////////////////////////////eXl5AAAAAwEDBAIEAwIDBAIEAwEDAAAAAAAAAAAAAAAABAEEAwIDBAIEAwIDBAEEAAAALy8v////ZWVlAAAABAEEAwEDBAIEAAAAMC4w////////////AAAAAgECBAEEAwIDBAEEAAAA9fT1//////7/////PTs9AAAAAwIDBAEEBAIEAgACBAEEBAIEBAIEBAEEAAAAgYCB////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////AAAAAgECBAEEBAIEBAEEAwIDAAAAn52f////////S0lLAAAAAwEDBAIEBAIEBAEEAAAAEA0Q////////+/v7jIuM////////////////////ysjKERARAQABAwIDBAIEAwEDAgACAAAA////AAAABAEEAwIDBAIEAwEDAwEDBwYHBQIFAAAAAgACAAAAAgACAwEDBAIEAwEDBAIEAAAA9fT1CAcIAQABAwEDBAIEAwIDAQABAAAA////AwIDAAAAAwEDBAIEAwEDAAAAHBsc////////////////////////////////////////////////////////2dnZAAAABAEEAwIDBAIEAwIDBAIEAwIDBAIEAwEDBAEEAwIDBAIEAwIDBAEEAwEDAAAAo6Gj////////AAAAAAAABAIEAwEDBAEEAAAASUZJ8fDxtrO2AAAABAEEAwEDBAIEAwEDAAAA+/v7////////////////AAAAAQABAwEDBAEEBAEEBAIEBAIEBAIEBAIEAAAAn56f////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////NjQ2AAAABAEEBAIEBAIEAwEDAwMDAAAAKykrWFdYAAAAAwEDBAEEBAIEBAIEAwEDAAAAGhga//////7/////Hx4fAAAACwkLVFNUi4mLra2ttbO1HRsdAAAABAIEAwIDBAIEAwEDAAAA6OjoGhkaAAAABAIEAwIDBAEEAQABAQAB////////////4uHiAAAABAEEAwIDBAIEAwEDAAAAiIeIdHF0AAAABAEEAwIDBAIEAwEDAAAA3t/e/fv9AAAAAgACAwEDBAEEBAMEAAAALy0v////////////////////////////////////////////////////////AAAAAAAABAIEAwIDBAIEAwIDBAIEAwEDAAAAAAAAAQABAAAAAAAAAAAAAAAAUFBQ////////////0tDSAAAAAAAABAEEAwEDBAEEAAAAAAAAAAAABAEEAwEDBAIEAwEDAAAAEA4Q////////////////////w8LDAAAAAwEDBAIEBAIEBAIEBAIEBAIEBAIEAAAAvLy8////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////l5SXAAAABAEEBAIEBAIEAwEDAwEDAwEDAAAAAAAAAwEDBAEEBAIEBAIEBAIEBAEEAAAAUE5Q////////////ubm5AAAAAAAAAAAAAAAAAAAAAAAAAAAABAIEAwIDBAIEAwEDBAEEAAAAmJeYfn1+AAAAAwIDBAIEAwEDBAIEAAAA09LT////////////AAAAAAAABAIEAwIDBAIEAAAAHRwd3t3eAAAAAwEDBAIEAwIDBAIEAAAAeXh5////wcDBAAAABAIEAwEDBAEEAwEDAAAARkVG////////////////////////////////////////////////////WVdZAAAAAwEDBAIEAwIDBAIEAwEDAAAAFRQVCAYIAgACAAAAEhASSUdJw8LD/////v7+//////7/////7u3uEhESAAAAAAAAAwIDBAEEAwEDBAEEAwIDBAIEAwEDAAAAAAAA19bX////////////////////////WFZYAAAAAwEDBAIEBAIEBAIEBAIEBAEEAAAA2djZ//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////9vX2AAAAAwEDBAIEAwEDAQABAAAAAwADBAIEBAIEBAIEBAIEBAIEBAIEBAEEAwEDAAAA1NTU////////////////AAAAAAAAAAAAAwEDBAEEBAIEBAIEAwIDBAIEAwEDAwEDAAAAAAAA397f2NfYAAAABAEEAwIDBAEEAwIDAAAAb29v////////////VlZWAAAAAwIDBAIEAwIDBAEEAAAA////AAAAAwADAwIDBAIEAwIDAAAAFBQU////////f39/AAAABAIEAwEDBAEEAwIDAAAAZWNl////////////////////////////////////////////////uLi4AAAABAEEAwIDBAIEAwIDBAEEAAAA////////////////////////////////////////////////////////srKyKyorAAAAAAAAAAAAAAAAAAAAAAAAAAAAGRgZ3t3e/////v7+////////////////////////AQABAAAAAAAAAAAAAAAAAAAAAAAAAAAA9vb2////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////9vX27evt4uHi393f29rb3Nvc393f4+Hj6+nr8vLy/fz9/////////////////////////////////////////////////////////////////////////Pv8+Pf4+vn6////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////GBYYAAAABAEEAQABGxsbQ0JDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAjo2O////////////////////pqWmMzMzCwkLAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKygry8rL////////AAAAAAAAAAAAAAAAAAAAAAAAAAAA////////////tbS1AAAAAAAAAAAAAAAAAAAAAAAAmZeZUE5QAAAABAIEAwIDBAIEAwEDAAAA+Pf4////////Ojk6AAAAAAAAAAAAAAAAAAAAAAAAaGZo////////////////////////////////////////////////AAAAAgACBAIEAwIDBAIEAwEDAgACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZmRm////////////////////////////////wsHClJOUgoCChoaGoJ+g6Ojo////////////////////////////////////////5OLkmpqaoaGhoqGioqGioqGioqGilZWV////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////2tjatrW2mZeZfnt+c3FzdnR2d3V3d3Z3eHZ4eHZ4eHZ4eXd5eHZ4d3V3dnR2dXN1c3JzfHp8i4mLmpmarKqsvr2++/n7////////wL7At7W3rKqsnJqckI2QgX+BdnR2dXN1dXN1dnR2dnN2dXN1dXN1dHJ0dnR2hYKFl5WXrqyuyMXI5ePl////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////mZeZAAAAAwIDAgECAAAAycjJ////5uXmoqKic3JzU1JTRENEVFNUgoKC9/b3////////////////////////////////////////+/v74ODgysrKw8PDysnK4eHh////////////////////7Ovs4uLi4+Pj4+Pj4+Pj4+Pj29rb////////////////39/f4+Tj4+Pj4+Tj4+Pj4uHi9PP0paOlAAAAAwIDBAIEAwEDBAIEAAAAl5aX////////////39/f5OPk5OPk5OPk5OPk5eTl0M7Q////////////////////////////////////////////////OTc5AAAAAwIDBAIEAwIDBAIEAwEDAwADAQABAgACAQABAgACAQABAgACAAAABgQG//////////////////////////7///////////////////////////////////////////////////////////////////7/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////2dfZm5mbdHJ0enh6fHl8fXt9fXx9fXt9fHp8enh6enh6eHZ4f3x/g4GDhIKEhIKEhIKEhIKEhIGEgoGCg4CDgH6AgH2Afnx+eXd5c3Jzk5CTenh6e3h7fXp9fXx9f31/gH6AgoCChIKEg4GDhIKEg4GDg4GDfXt9enh6e3h7enh6end6eXd5eHZ4eXd5eHZ4dHJ0j42Puri66unq////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////CAYIAAAABAIEAwIDAAAABgUGeHd4x8XH+vr6////////////////+vr6vby9x8XH//////////////////////////////////////////////////////////////7///////////7///////////////////////////////////////7/////////////////////////////////////AAAABAEEAwIDBAIEAwEDAAAAODU4//////////////////////////////////////////7/////////////////////////////////////////////mZiZAAAABAIEAwIDBAIEAwIDBAIEAwEDBAIEAwIDBAIEAwIDBAIEAwIDBAEEAAAA5+bn////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////19XXgH6Ad3V3fXt9e3l7dnV2dnN2jImMoqCisrGywL/AzcrN1NPU5ubmube5fn1+hIKEg4GDhIGEhIKEg4GDg4GDg4GDg4GDg4GDfHp8dnR2lZOVoZ6hlJGUdHN0f31/g4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDgH6Az83P4uHi0dDRy8nLvby9r66vnpyehYOFdXJ1d3V3eXd5enh6c3JzhYKF5OLk//////7/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7OvsAAAAAAAAAwEDAwEDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAJiUm////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////IiAiAAAABAIEAwIDBAIEAgACAAAA////////////////////////////////////////////////////////////////////////////////////////+vn6AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAf31/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////uba5cnByfHt8dnR2kY+RxcPF7evt////////////////////////////sK6we3h7g4GDg4GDgoCCfHp8e3l7g4GDg4GDhIKEgn+Cc3FztbK1/////////////////v7+oZ6hdnR2goCChIKEgn+CfHl8fHl8goCCg4GDg4CDenh6ysjK////////////////////////////4+PjvLq8hYOFd3R3fnx+cnByura6////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7+3vCAgIAAAAAAAAAwEDBAEEBAEEAwEDAwIDAwEDAwEDAwEDAwIDAwEDAAAA////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////fn1+AAAAAAAAAAAAAAAAAAAAAAAArq2u////////////////////////////////////////////////////////////////////////////////////////RURFMzEzNTQ1NTQ1NTQ1NTQ1NTQ1NTQ1NTQ1NTQ1NTQ1NTQ1NTQ1NTQ1Ly4vWFdY////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////9vX2c3Jzd3V3n5yf9vX2////////////////////////////////8vLyf31/fHp8g4KDfXt9dnN2ioeKwL/A29nbfnx+g4GDgX+Bend66+nr////////////////////////////ycfJdXJ1gn+CgoCC4d7huLe4hIGEd3V3f3x/goCCdnR2kY+R////////////////////////////////////5uXmjImMe3p7c3Fzy8nL////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////k5GTAAAAAAAAAAAAAAAAAAAAAgACAwIDBAEEAwEDBAEEBAEEAAAArq2u////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////6enpbGtsd3Z3d3d3d3Z3d3d3cXBxqqmq////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////vLq8c3Jzvbq9////////////////////////////////////1tPWc3BzgX+Bf31/c3FzqKWo7+7v////////kI2QgH6AgoCCe3h79/b3////////////////////////////////////wb/BeXd5fXx9op+i////////5+XnnZudc3Jzgn+CfXx9e3l78e/x////////////////////////////////////npuefnx+gH6A////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////ysnKd3Z3PDs8Dw4PAAAAAAAAAAAAAAAAAAAAAAAAAAAAPTs9////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////rautcm9y8O7w////////////////////////////////ycbJcnBygoCCdnN2mZeZ+vn6////////////zcvNd3V3g4CDdXJ18vHy////////////////////////////////////////////lpSWf3x/dXN14t/i////////////8O7wkI6QeXZ5gX+BdHJ05uXm////////////////////////////////zcvNenh6eXV5////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////9vX25ubm2dnZzMzMvLu8n52f////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////1NLUcnFyiIWI5uPm////////////////////////xMDEdHJ0gX+BeHZ42NbY////////////////////g4GDgX+BdnR21dLV////////////////////////////////////////////////+ff5dXN1f31/kpCS////////////////////yMfIdHJ0gX+Bc3Bz5OLk////////////////////////09HTfnt+e3l7nJqc////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////o6Cjc3JzdXJ1fnt+o6Gjuri6ysjK1dPVube5dnN2fHt8h4SH//3/////////////////////3tzed3V3fnx+n52f////////////////////////////////////////////////////////q6irfXx9dXN18fHx////////////////////9PP0f31/fnx+dXN1zcvN09HTx8THtbO1nJqceHZ4eXZ5eXh5g4CD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////09DTjImMc3FzdXN1dHJ0dHJ0eXh5fXt9e3p7kY6R////////////////////////////p6WnfXt9eHV48/Lz////////////////////////////////////////////////////////8/Lzd3V3fHp8u7m7////////////////////////////hYOFf31/enh6eHZ4dXN1d3V3eHZ4eHZ4d3V3tLK0//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7/4eDh09HTxcLFe3l7fnx+jouO////////////////////////////////f31/gX+BdnV2////////////////////////////////////////////////////////////////e3l7gH6AkI2Q////////////////////////////////gn+CgH6Ai4iLwsDCs7Czv76/3Nvc//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7/////////////tbO1fXt9fXt9////////////////////////////////8/LzdXN1gX+Bi4iL////////////////////////////////////////////////////////////////lJKUf31/eHZ4////////////////////////////////+ff5d3V3eHZ42NXY////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////dHJ0dnN23t3e////////////////////////////////1NLUend6fXt9srCy//////////////////////////////////////////////////7/////////////vLq8fHp8eHV45uXm////////////////////////////////y8jLd3V3gH6A////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////4eDhdXN1k5CT////////////////////////////////////uri6fHt8end63Nvc////zMnMjouO////////////////////////////////////////xsPG6+nr////5eTleXd5fHl8zMrM////////////////////////////////////g4GDdHJ05eTl////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////0tDScm9yz8zP////////////////////////////////////o6Gjf3x/eXd58O3wjIqMcG1woJ2g////////////////////////////////////////kI2QcnBynpue3Nrce3l7fXp9t7S3////////////////////////////////////vLm8c3Fzw8HD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////0tDSb21v5uXm////////////////////////////////////kY+RgH6Ag4GDfHl8c3FzkY+R/////////////////////////////////////////////Pz8gX6BeXh5fXp9hIOEf31/paSl////////////////////////////////////0tHSc3JzuLW4////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7OrsbGps0s/S////////////////////////////////////gX6BgoCCgX+BjIqM1NPU////////////////////////////////////////////////////////ko6Sf31/g4KDgX6BkY+R////////////////////////////////////vLq8dHJ0x8bH////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////iYaJd3V38fDx/////////////////////fz9yMbIfnx+fHt8hIKEfnx+rKqs////////////////////////////////////////////////////////////////d3R3g4KDhYOFenh6h4SH0M7Q////////////////////////4+HjeXd5cnBy9/b3/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////v3+ioiKZmRmiIaIp6SnqqeqmZeZfHl8Z2VncG5wmZaZnpyef31/goCCiIaI////////////////////////////////////////////////////////////6urqeHV4g4GDgH6AnZudf31/bmxubmxuhoOGo6CjsK+wq6mriYeJc3FzcG5wwsDC////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////6OboxMLEtLO0trS2xMLE3Nvc+Pf4////////////eXZ5goCCdnR28vHy////////////////////////////////////////////////////////vby9fHp8g4GDdnR29/b3////3dzduri6mJaYfnx+cG1wb21vfnx+oJ2g5+Xn////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////r6yvfXt9fXt9rqyu////////////////////////////////////////////////////////ioiKgH6AgH6AjouO////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////9fT1dXN1g4GDdnN2/Pv8////////////////////////////////////////////////6unqdXR1hIKEeHV40tHS////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////npyef31/fnx+mpia////////////////////////////////////////////////npuef31/gn+ChIKE////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7uzudHF0hIGEdnN20M7Q////////////////////////////////////////6ejpdXJ1goCCdHF04+Hj////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////qqeqe3h7g4GDdXN18fDx////////////////////////////////////goCCgX+BeXh5sK6w////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////goCCf31/goCCe3d7+fj5////////////////////////////m5ibfHp8e3l7mZaZ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////+vj6enh6fnx+gX+BeXZ56efp////////////////////l5WXeXh5d3V3oZ6h//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////3/jouOeHZ4fn1+dHF0rqyu7+7v7Ovsv76/d3V3dnR2d3R3zcvN////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////1dTVlJKUdHJ0cG5wbGlsa2lrbGtsi4mLxMLE//////////7/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7evt4+Hj7Ovs////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////"/>
        <TextObject Name="Memo1" Left="435.19" Top="80.7" Width="222.99" Height="26.46" ShiftMode="WhenOverlapped" Text="ОАО &quot;БЕЛГАЗПРОМБАНК&quot;" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="b_InfoBenefitPriobretatel" Top="544.61" Width="1047.06" Height="26.67" CanGrow="true" CanShrink="true" CanBreak="true" DataSource="ANK_IP5">
      <TextObject Name="f_InfoBenefitPriobretatel" Left="3.78" Top="3.99" Width="1031.49" Height="18.9" CanGrow="true" GrowToBottom="true" AfterDataEvent="f_InfoBenefitPriobretatel_AfterData" Text="[ANK_IP5.INFOBENEFITPRIOBRETATEL]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header13" Top="513.02" Width="1047.06" Height="27.59" CanGrow="true">
        <TextObject Name="Memo126" Left="3.78" Top="3.78" Width="1030.81" Height="20.03" ShiftMode="WhenOverlapped" Text="11. Сведения о выгодоприобретателях:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData20" Top="719.57" Width="1047.06" Height="18.9" CanGrow="true" CanShrink="true" DataSource="ANK_IP10">
      <TextObject Name="Memo168" Left="737.01" Width="151.18" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP10.PAYNUMBER]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo162" Width="151.18" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP10.NAME]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo164" Left="151.18" Width="97.49" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP10.CITIZENSHIP]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo165" Left="248.67" Width="155.74" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP10.BIRTH]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo166" Left="404.41" Width="128.5" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP10.REGISTRATIONPLACE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo167" Left="532.91" Width="204.09" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP10.DOC]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo31" Left="888.19" Width="158.74" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP10.GROUND]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header14" Top="575.28" Width="1047.06" Height="140.29" CanGrow="true" CanShrink="true" CanBreak="true">
        <TextObject Name="Memo127" Left="3.78" Top="6.56" Width="1031.81" Height="18.9" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="11.1. Сведения о физических лицах/индивидуальных предпринимателях:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo128" Top="30.24" Width="151.18" Height="110.05" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Ф.И.О." Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo129" Left="151.18" Top="30.24" Width="97.49" Height="110.05" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Гражданство" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo130" Left="248.67" Top="30.24" Width="155.74" Height="110.05" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Дата и место рождения" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo146" Left="404.41" Top="30.24" Width="128.5" Height="110.05" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Место жительства (регистрация)" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo148" Left="532.91" Top="30.24" Width="204.09" Height="110.05" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Реквизиты документа, удостоверяющего личность: наименование, серия и номер документа, кем и когда выдан, идентификационный номер (при наличии)" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo152" Left="737.01" Top="30.24" Width="151.18" Height="110.05" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="УНП" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo4" Left="888.19" Top="30.24" Width="158.74" Height="110.05" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Иные сведения" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="b_11_1_empty" Top="742.47" Width="1047.06" Height="37.8" CanGrow="true">
      <TextObject Name="f_11_1_emppty" Left="3.78" Top="3.78" Width="771.02" Height="34.02" CanGrow="true" CanShrink="true" AfterDataEvent="f_11_1_empty_AfterData" Text="&lt;b&gt;11.1. Сведения о физических лицах/индивидуальных предпринимателях:&lt;/b&gt;&#13;&#10;Клиент сведений не предоставил" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
    </DataBand>
    <DataBand Name="MasterData2" Top="860.08" Width="1047.06" Height="22.68" CanGrow="true" CanShrink="true" DataSource="ANK_IP11">
      <TextObject Name="Memo158" Width="154.96" Height="22.68" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP11.NAME]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo159" Left="154.96" Width="360.32" Height="22.68" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP11.REGISTRATIONINFO]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo160" Left="516.28" Width="237.35" Height="22.68" Border.Lines="Left, Top, Bottom" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP11.LOCATION]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo110" Left="754.39" Width="113.76" Height="22.68" Border.Lines="Left, Top, Bottom" CanGrow="true" GrowToBottom="true" Text="[ANK_IP11.PAYNUMBER]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo7" Left="868.91" Width="177.02" Height="22.68" Border.Lines="All" CanGrow="true" GrowToBottom="true" Text="[ANK_IP11.GROUND]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header15" Top="784.27" Width="1047.06" Height="71.81" CanGrow="true" CanShrink="true" CanBreak="true">
        <TextObject Name="Memo157" Top="5.41" Width="1031.81" Height="18.9" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="11.2. Сведения  об организациях/финансовых институтах:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo155" Top="26.46" Width="154.96" Height="45.35" Border.Lines="All" GrowToBottom="true" Text="Наименование" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo20" Left="154.96" Top="26.46" Width="360.49" Height="45.35" Border.Lines="All" Text="Регистрационный номер и дата регистрации организации, наименование регистрирующего органа" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo21" Left="754.39" Top="26.46" Width="113.87" Height="45.35" Border.Lines="Top, Bottom" Text="УНП" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo27" Left="516.28" Top="26.46" Width="237.5" Height="45.35" Border.Lines="Right, Top, Bottom" Text="Место нахождения (юридический адрес)" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo60" Left="869.29" Top="26.46" Width="177.12" Height="45.35" Border.Lines="All" Text="Иные сведения" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="b_11_2_empty" Top="886.76" Width="1047.06" Height="37.8" CanGrow="true">
      <TextObject Name="f_11_2_empty" Left="3.78" Top="3.78" Width="771.02" Height="34.02" CanGrow="true" CanShrink="true" AfterDataEvent="f_11_2_empty_AfterData" Text="&lt;b&gt;11.2. Сведения  об организациях/финансовых институтах:&lt;/b&gt;&#13;&#10;Клиент сведений не предоставил" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
    </DataBand>
    <DataBand Name="MasterData13" Top="928.56" Width="1047.06" Height="41.58" CanGrow="true" CanShrink="true">
      <TextObject Name="Memo10" Left="3.78" Top="3.78" Width="975.12" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="12. Вид договорных отношений с ОАО «Белгазпромбанк» (наименование договора, тип и номер счета):" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo_ANK_IP2" Left="3.78" Top="22.68" Width="1005.33" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" BeforePrintEvent="MasterData1_AfterPrint" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
    </DataBand>
    <DataBand Name="MasterData5" Top="1015.94" Width="1047.06" Height="18.9" CanGrow="true" CanShrink="true" DataSource="ANK_IP2">
      <TextObject Name="Memo69" Width="207.87" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP2.CONTRACTCREDITACCOUNTTYPE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo70" Left="207.87" Width="196.54" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP2.CONTRACTSALARYACCOUNTTYPE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo71" Left="404.41" Width="185.2" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP2.CONTRACTDEPOSITACCOUNTTYPE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo72" Left="589.61" Width="200.32" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP2.CONTRACTEMONEYACCOUNTTYPE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo73" Left="789.92" Width="257.01" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP2.CONTRACTACCOUNTTYPE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header8" Top="974.14" Width="1047.06" Height="37.8" CanGrow="true">
        <TextObject Name="Memo135" Left="789.92" Width="257.01" Height="37.8" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Прочие договора" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo137" Left="404.41" Width="185.2" Height="37.8" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Текущие/вкладные счета клиента" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo138" Left="589.61" Width="200.32" Height="37.8" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Договора электронные деньги" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo139" Width="207.87" Height="37.8" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Кредиты клиента" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo140" Left="207.87" Width="196.54" Height="37.8" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Зарплатный проект" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData3" Top="1038.84" Width="1047.06" Height="41.57" CanGrow="true" CanShrink="true" DataSource="ANK_IP1">
      <TextObject Name="Memo74" Left="3.78" Width="975.12" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="13. Цели установления и предполагаемый характер отношений с Банком:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="f_Purpose" Left="3.78" Top="18.9" Width="1005.33" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" AfterDataEvent="f_Purpose_AfterData" Text="[ANK_IP1.PurposeOfEstablishingRelations]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
    </DataBand>
  </ReportPage>
  <ReportPage Name="service" Landscape="true" PaperWidth="297" PaperHeight="210" Watermark.Font="Arial, 60pt">
    <DataBand Name="MasterData8" Width="1047.06" Height="411.63" CanGrow="true" CanShrink="true" CanBreak="true" DataSource="Служебная информация">
      <TextObject Name="Memo16" Width="1039.37" Height="26.46" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Служебная информация:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo25" Left="3.78" Top="30.24" Width="1043.15" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Категория клиента :" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo19" Left="3.78" Top="52.91" Width="1043.15" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[Служебная информация.CLIENTSERVICEVALUE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo97" Left="3.78" Top="79.37" Width="1043.15" Height="37.8" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Решение ответственного по банку о дальнейших действиях банка в отношении клиента, предусмотренных законодательством, и сроках их выполнения:  &lt;/b&gt; &#13;&#10;[Служебная информация.CLIENTFURTHERACTIONS]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo17" Left="3.78" Top="120.94" Width="1043.15" Height="37.8" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Решение о переводе в однотипные финансовые операции:  &lt;/b&gt; &#13;&#10;[Служебная информация.TYPECLIENTFURTHERACTIONS]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo33" Left="3.78" Top="162.52" Width="1043.15" Height="30.24" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Решение о переводе в стандартные финансовые операции: &lt;/b&gt; &#13;&#10;[Служебная информация.STANDARDCLIENTFURTHERACTIONS]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo34" Left="3.78" Top="197.87" Width="1043.15" Height="34.02" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Иные меры: &lt;/b&gt; &#13;&#10;[Служебная информация.OTHERCLIENTFURTHERACTIONS]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo40" Left="3.78" Top="263.43" Width="1043.15" Height="30.24" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Решение ответственного по банку о дальнейших действиях банка в отношении клиента, направленные на ограничение (снижение) рисков и сроки его выполнения: " Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo41" Left="3.78" Top="294.8" Width="1043.15" Height="34.02" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Запрос дополнительных сведений и документов, их анализ:&lt;/b&gt; &#13;&#10;[Служебная информация.ZAPROSCLIENTACTIONSRISK]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo42" Left="3.78" Top="330.71" Width="1043.15" Height="37.8" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Принятие решения о необходимости особого пристального внимания к проведению клиентом финансовых операций:&lt;/b&gt; &#13;&#10;[Служебная информация.FINTRCLIENTACTIONSRISK]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo43" Left="3.78" Top="370.06" Width="1043.15" Height="37.8" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Приостановление  и (или) отказ в осуществлении финансовой операции:&lt;/b&gt; &#13;&#10;[Служебная информация.OTKAZFINTRANSCLIENTACTIONSRISK]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo59" Left="7.56" Top="241.89" Width="1035.59" Height="11.34" CanGrow="true" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
    </DataBand>
    <DataBand Name="MasterData9" Top="415.63" Width="1047.06" Height="691.62" CanGrow="true" CanShrink="true" CanBreak="true" DataSource="Служебная информация">
      <TextObject Name="Memo114" Left="3.78" Top="0.66" Width="1043.15" Height="37.8" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Отказ клиенту в заключении или в исполнении договора на осуществление финансовых операций в письменной форме: &lt;/b&gt; &#13;&#10;[Служебная информация.OTKAZDOGCLIENTACTIONSRISK]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo115" Left="3.78" Top="43.23" Width="1043.15" Height="34.02" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Отказ клиенту в подключении к системе ДБО, прекращение или приостановление оказания услуг посредством этой системы: &lt;/b&gt; &#13;&#10;[Служебная информация.OTKAZDBOCLIENTACTIONSRISK]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo116" Left="3.78" Top="79.14" Width="1043.15" Height="37.8" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Иные меры:&lt;/b&gt; &#13;&#10;[Служебная информация.OTHERCLIENTACTIONSRISK]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo119" Left="3.78" Top="383.06" Width="1043.15" Height="37.8" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Решение ответственного должностного лица о верификации и сроки его выполнения: &lt;/b&gt; &#13;&#10;[Служебная информация.CLIENTFURTHERVERIFICATION]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo117" Left="3.78" Top="650.71" Width="1036.29" Height="37.13" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Результаты дополнительных мероприятий проведенных банком при идентификации:&lt;/b&gt;" Padding="2, 2, 2, 2" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo65" Left="3.78" Top="151.18" Width="1039.37" Height="37.8" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Решение ОДЛ о замораживании средств и/или блокировании финансовой операции в отношении лиц, причастных к террористической деятельности: &lt;/b&gt; &#13;&#10;[ankjp_sol_odl.SOL_ODL_BLOCK]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo11" Left="3.78" Top="188.98" Width="1039.37" Height="49.13" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Комментарии Бизнес подразделений о замораживании средств и/или блокировании финансовой операции в отношении лиц, причастных к террористической деятельности:&lt;/b&gt;&#13;&#10;[ankjp_sol_odl.SOL_ODL_BUSS]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo18" Left="3.78" Top="241.89" Width="1039.37" Height="49.13" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Комментарии Управления финансового мониторинга Банка о замораживании средств и/или блокировании финансовой операции в отношении лиц, причастных к террористической деятельности:&lt;/b&gt;&#13;&#10;[ankjp_sol_odl.SOL_ODL_UFM]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo22" Left="3.78" Top="294.8" Width="1039.37" Height="52.91" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Комментарии о замораживании средств и/или блокировании финансовой операции в отношении лиц, причастных к террористической деятельности, полученные от Департамента финансовых расследований Комитета государственного контроля Республики Беларусь:&lt;/b&gt;&#13;&#10;[ankjp_sol_odl.SOL_ODL_DFR]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo26" Left="3.78" Top="451.09" Width="1039.37" Height="51.81" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Приостановление осуществления финансовых операций по счету по постановлению (решению) государственных органов или наложения ареста на денежные средства на счете:&lt;/b&gt;&#13;&#10;[Служебная информация.STOPOPERPOST]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo28" Left="3.78" Top="502.9" Width="1039.37" Height="36.69" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Открытие клиенту банковского счета по доверенности лицом, не являющимся работником клиента:&lt;/b&gt;&#13;&#10;[Служебная информация.DOVEROPER]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo29" Left="3.78" Top="540.44" Width="1039.37" Height="36.69" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;В отношении клиента установлена негативная деловая репутация:&lt;/b&gt;&#13;&#10;[Служебная информация.NEGATIVGOODWILL]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo30" Left="3.78" Top="577.39" Width="1039.37" Height="36.69" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;Индивидуальный предприниматель был связан с клиентами банка, имеющими негативную деловую репутацию:&lt;/b&gt;&#13;&#10;[Служебная информация.ASSOCIATEDNEGATIVGOODWILL]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo36" Left="3.78" Top="627.4" Width="1035.59" Height="11.34" CanGrow="true" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo37" Left="3.78" Top="359.06" Width="1035.59" Height="11.34" CanGrow="true" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo38" Left="3.78" Top="427.09" Width="1035.59" Height="11.34" CanGrow="true" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo58" Left="7.56" Top="128.5" Width="1035.59" Height="11.34" CanGrow="true" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
    </DataBand>
    <DataBand Name="b_DatainfoClientIdentResults" Top="1111.25" Width="1047.06" Height="40.7" CanGrow="true" DataSource="Служебная информация">
      <TextObject Name="f_DatainfoClientIdentResults" Left="3.78" Top="2.9" Width="1035.59" Height="37.8" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" AfterDataEvent="f_DatainfoClientIdentResults_AfterData" Text="&lt;b&gt;Дата информирования клиента о необходимости прохождения повторного анкетирования:&lt;/b&gt; &#13;&#10;[Служебная информация.DATAINFOCLIENTIDENTRESULTS]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
    </DataBand>
    <DataBand Name="b_ActiveClientIdentResults" Top="1155.95" Width="1047.06" Height="42.14" CanGrow="true" DataSource="Служебная информация">
      <TextObject Name="f_ActiveClientIdentResults" Left="3.78" Top="4.34" Width="1039.37" Height="37.8" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" AfterDataEvent="f_ActiveClientIdentResults_AfterData" Text="&lt;b&gt;Клиент активен. Вопросник не предоставлен:&lt;/b&gt; &#13;&#10;[Служебная информация.ACTIVECLIENTIDENTRESULTS]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
    </DataBand>
    <DataBand Name="b_NoContactClientIdentResults" Top="1202.09" Width="1047.06" Height="36.13" CanGrow="true" DataSource="Служебная информация">
      <TextObject Name="f_NoContactClientIdentResults" Left="3.78" Top="2.11" Width="1039.37" Height="34.02" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" AfterDataEvent="f_NoContactClientIdentResults_AfterData" Text="&lt;b&gt;Невозможно установить контакт с клиентом. Операции не осуществляются на основании его платежных поручений на протяжении 12 месяцев:&lt;/b&gt; &#13;&#10;[Служебная информация.NOCONTACTCLIENTIDENTRESULTS]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
    </DataBand>
    <DataBand Name="b_NoActiveClientIdentResults" Top="1242.22" Width="1047.06" Height="37.02" CanGrow="true" DataSource="Служебная информация">
      <TextObject Name="f_NoActiveClientIdentResults" Left="3.78" Top="3" Width="1039.37" Height="34.02" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" AfterDataEvent="f_NoActiveClientIdentResults_AfterData" Text="&lt;b&gt;Вопросник клиентом предоставлен. Операции не осуществляются на основании его платежных поручений на протяжении 12 месяцев:&lt;/b&gt; &#13;&#10;[Служебная информация.NOACTIVECLIENTIDENTRESULTS]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
    </DataBand>
    <DataBand Name="b_SpravkaClientIdentResults" Top="1283.24" Width="1047.06" Height="36.82" CanGrow="true" DataSource="Служебная информация">
      <TextObject Name="f_SpravkaClientIdentResults" Left="3.78" Top="2.8" Width="1039.37" Height="34.02" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" AfterDataEvent="f_SpravkaClientIdentResults_AfterData" Text="&lt;b&gt;Сведения о справках последующего контроля:&lt;/b&gt; &#13;&#10;[Служебная информация.SPRAVKACLIENTIDENTRESULTS]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
    </DataBand>
    <DataBand Name="b_OtherClientIdentResults" Top="1324.06" Width="1047.06" Height="66.05" CanGrow="true" DataSource="Служебная информация">
      <TextObject Name="f_OtherClientIdentResults" Left="3.78" Top="1.8" Width="1039.37" Height="34.02" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" AfterDataEvent="f_OtherClientIdentResults_AfterData" Text="&lt;b&gt;Прочая информация:&lt;/b&gt; &#13;&#10;[Служебная информация.OTHERCLIENTIDENTRESULTS]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo35" Left="3.78" Top="49.13" Width="1035.59" Height="11.34" CanGrow="true" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
    </DataBand>
    <DataBand Name="MasterData4" Top="1394.11" Width="1047.06" Height="317.57" CanGrow="true" CanShrink="true" CanBreak="true" DataSource="Служебная информация">
      <TextObject Name="Memo62" Left="3.78" Top="5.02" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Информация о результатах обработки идентификационных сведений клиента:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo64" Left="3.78" Top="25.7" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[Служебная информация.CLIENTRESULTS]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo68" Left="3.78" Top="46.35" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Наличие американских признаков: " Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo75" Left="3.78" Top="67.35" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[Служебная информация.SIGNFATCA]" Padding="2, 1, 2, 1" Format="Boolean" Format.TrueText="True" Format.FalseText="False" Font="Arial, 10pt"/>
      <TextObject Name="Memo63" Left="3.78" Top="87.72" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Принадлежность клиента к спискам лиц, причастных к террористической деятельности, ПЗЛ и санкций:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo66" Left="3.78" Top="114.39" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[Служебная информация.TERRORPEPSANCTION]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo45" Left="3.78" Top="139.13" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Значение итоговой степени риска работы с клиентом:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo44" Left="3.78" Top="161.8" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[Служебная информация.CLIENTTOTALRISK]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo61" Left="3.78" Top="183.76" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Обоснование итоговой степени риска:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo67" Left="3.78" Top="206.44" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[Служебная информация.TOTALRISKFACTORS]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo47" Left="3.78" Top="229.12" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Значения степени частного риска, выявляемого по профилю клиента:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo46" Left="3.78" Top="251.8" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[Служебная информация.CLIENTPROFILERISK]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo49" Left="3.78" Top="274.47" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Наименования выбранных факторов при расчете степени частного  риска, выявляемого по профилю клиента: " Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo48" Left="3.78" Top="297.15" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[Служебная информация.PROFILERISKFACTORS]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
    </DataBand>
    <DataBand Name="MasterData6" Top="1715.68" Width="1047.06" Height="296.61" CanGrow="true" CanShrink="true" CanBreak="true" DataSource="Служебная информация">
      <TextObject Name="Memo51" Left="3.78" Top="0.3" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Значения степени частного риска, выявляемого по видам операций: " Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo50" Left="3.78" Top="24.2" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[Служебная информация.CLIENTOPERRISK]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo53" Left="3.78" Top="49.59" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Наименования выбранных факторов при расчете степени частного  риска, выявляемого по видам операций: " Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo52" Left="3.78" Top="72.27" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[Служебная информация.OPERRISKFACTORS]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo55" Left="3.78" Top="94.94" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Значения степени частного риска, выявляемого по географическому признаку: " Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo54" Left="3.78" Top="118.62" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[Служебная информация.CLIENTGEORISK]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo57" Left="3.78" Top="143.58" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Наименования выбранных факторов при расчете степени частного риска, выявляемого по географическому  признаку: " Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo56" Left="3.78" Top="168.26" Width="1039.37" Height="21.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[Служебная информация.GEORISKFACTORS]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo81" Left="3.78" Top="272.13" Width="997.8" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[Служебная информация(FinTransactions).FINTRANSACTIONS]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo76" Left="3.78" Top="230.55" Width="997.8" Height="37.8" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Сведения обо всех финансовых операциях, сумма которых равна или превышает 1000 базовых величин (кроме операций с электронными деньгами), клиентов, осуществляющих разовые операции:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo6" Left="3.78" Top="204.09" Width="1035.59" Height="11.34" CanGrow="true" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
    </DataBand>
    <DataBand Name="MasterData11" Top="2016.29" Width="1047.06" Height="26.46" CanGrow="true" CanShrink="true">
      <TextObject Name="Memo83" Left="3.78" Top="3.78" Width="997.8" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Сведения обо всех подозрительных финансовых операциях, совершенных клиентом:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
    </DataBand>
    <DataBand Name="MasterData12" Top="2046.75" Width="1047.06" Height="26.46" CanGrow="true" CanShrink="true">
      <TextObject Name="Memo_ST" Left="3.78" Top="3.78" Width="1024.69" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" BeforePrintEvent="SuspTransactionsMess_AfterPrint" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
    </DataBand>
    <DataBand Name="MasterData14" Top="2149.24" Width="1047.06" Height="18.9" CanGrow="true" CanShrink="true" CanBreak="true" DataSource="Служебная информация(SuspTransactions)">
      <TextObject Name="Memo102" Width="230.55" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[Служебная информация(SuspTransactions).CARD_CREATE_DATE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo103" Left="230.55" Width="181.42" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[Служебная информация(SuspTransactions).SUM_CURR]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo104" Left="411.97" Width="188.98" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[Служебная информация(SuspTransactions).OPERATION_FORM_CODE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo105" Left="600.95" Width="177.64" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[Служебная информация(SuspTransactions).SUSPICION_CODE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo106" Left="778.58" Width="268.35" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[Служебная информация(SuspTransactions).CARD_NUM_DATE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header4" Top="2077.21" Width="1047.06" Height="68.03" CanGrow="true">
        <TextObject Name="Memo85" Left="778.58" Width="268.35" Height="68.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Номер и дата составленного специального формуляра" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo86" Left="411.97" Width="188.98" Height="68.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Вид финансовой операции" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo87" Left="600.95" Width="177.64" Height="68.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Критерий выявления или признак подозрительной финансовой операции" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo88" Width="230.55" Height="68.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Дата и место осуществления финансовой операции" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo89" Left="230.55" Width="181.42" Height="68.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Сумма и валюта" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData15" Top="2172.14" Width="1047.06" Height="94.37" CanGrow="true" CanShrink="true" DataSource="Служебная информация">
      <TextObject Name="Memo107" Left="11.34" Top="45.35" Width="249.45" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Ф.И.О. лица, составившего анкету:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo109" Left="566.15" Top="45.35" Width="232.99" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Дата обновления, актуализации:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo111" Left="805.58" Top="45.35" Width="237.91" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[Служебная информация.UPDATEDT]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo112" Left="11.34" Top="17.47" Width="128.94" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="Дата заполнения:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo397" Left="147.09" Top="17.47" Width="250.73" Height="18.9" CanGrow="true" CanShrink="true" Text="[Служебная информация.DT_FILL]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo369" Left="10.22" Top="67.91" Width="509.88" Height="18.9" CanGrow="true" CanShrink="true" Text="[Служебная информация.FIO]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
    </DataBand>
  </ReportPage>
</Report>
