﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using static EWA.Models.REP_Models;

namespace EWA.Models
{
	public class WFL_Models
    {
        public class WFL_GRID_DATA
        {
            public long Id { get; set; }
            public string Code { get; set; }
            public string Name { get; set; }
            public string TypeT { get; set; }
            public string TypeTName { get; set; }
        }

        public class WFL_GRID_PARAM
        {
            public string Code { get; set; }
            public string Name { get; set; }
            public string Type { get; set; }
            public string Value { get; set; }
            public List<string> AvailableValues { get; set; } = new();
        }

        public class WFLRepData
		{
			public string Alias { get; set; }
			public string Code { get; set; }
			public List<string> Methods { get; set; }
		}

        public class WFL_Param
        {
            public string Code { get; set; }
            public string Name { get; set; }
            public string DataType { get; set; }
            public Dictionary<string, string>? Options { get; set; }
        }

        public class WFLOBJ
        {
            public string Code { get; set; }
            public string Type { get; set; }
            public double PosX { get; set; }
            public double PosY { get; set; }
            public List<WFLOBJ_Ports> Ports { get; set; } = new List<WFLOBJ_Ports>();

        }
        public class WFLOBJ_Ports
        {
            public string Code { get; set; }
            public double PosX { get; set; }
            public double PosY { get; set; }
        }
        public class WFLOBJ_Link
        {
            public string SrcObj { get; set; }
            public WFLOBJ_Ports SrcPort { get; set; }
            public string TrgObj { get; set; }
            public WFLOBJ_Ports TrgPort { get; set; }
        }
        public class WFLPARAM
        {
            public string Code { get; set; }
            public string Name { get; set; }
            public string Type { get; set; }
            public string Value { get; set; }
            public List<string> AvailableValues { get; set; } = new();
        }

        public class W_OBJ_META
        { 
            public decimal Id { get; set; }
            public string Code { get; set; }
            public string Name { get; set; }
            public string TypeObj { get; set; }
            public string CodeObj { get; set; }
            public string ActionSql { get; set; }
        }

        public class W_WKL_META
        {
            public decimal Id { get; set; }
            public string Code { get; set; }
            public string Name { get; set; }
            public string TypeObj { get; set; }
            public string CodeObj { get; set; }
            public List<WFLOBJ> ListObj { get; set; } = new List<WFLOBJ>();
            public List<WFLOBJ_Link> LinkObj { get; set; } = new List<WFLOBJ_Link>();

        }
        public class W_WKF_META
        {
            public decimal Id { get; set; }
            public string Code { get; set; }
            public string Name { get; set; }
            public List<WFLOBJ> ListObj { get; set; } = new List<WFLOBJ>();
            public List<WFLOBJ_Link> LinkObj { get; set; } = new List<WFLOBJ_Link>();
            public List<WFLPARAM> StartParam { get; set; } = new List<WFLPARAM>();
        }


        public class WFLInfo
        {
            public decimal IdActiv { get; set; }
            public int Id { get; set; }
            public string Code { get; set; }
            public string Name { get; set; }
            public List<WFLOBJ> ListObj { get; set; } = new List<WFLOBJ>();
            public List<WFLOBJ_Link> LinkObj { get; set; } = new List<WFLOBJ_Link>();
            public List<WFLPARAM> StartParam { get; set; } = new List<WFLPARAM>();
            public string StartParamStr { get; set; }

        }
        public class WRKLInfo
        {
            public decimal IdActiv { get; set; }
            public decimal IdActivW { get; set; }
            public int Id { get; set; }
            public string Code { get; set; }
            public string Name { get; set; }
            public string TYPE_OBJ { get; set; }
            public string CODE_OBJ { get; set; }
            public List<WFLOBJ> ListObj { get; set; } = new List<WFLOBJ>();
            public List<WFLOBJ_Link> LinkObj { get; set; } = new List<WFLOBJ_Link>();

        }

        public class WTaskInfo
        {
            public decimal IdActiv { get; set; }
            public decimal IdActivW { get; set; }
            public decimal IdActivM { get; set; }
            public int Id { get; set; }
            public string Code { get; set; }
            public string Name { get; set; }
            public string TYPE_OBJ { get; set; }
            public string CODE_OBJ { get; set; }
            public string SQLAction { get; set; }
            public string SQLParam { get; set; }

        }

        public class WRFL_WORKFLOW_ACTIVATION
        {
            public decimal Id { get; set; }
            public decimal IdWrfl { get; set; }
            public string CodeWrfl { get; set; }
            public DateTime? Dts { get; set; }
            public DateTime? Dte { get; set; }
            public string Status { get; set; }
            public decimal StatusID { get; set; }
            public string Err { get; set; }
        }

        
        public enum WorkflowObjectType
        {
            WFL, // воркфлоу
            WLT, // ворклет
            TSK  // задание
        }

        public class WorkflowGraphNode
        {
            public decimal IdActv { get; set; }
            public decimal IdActvW { get; set; }
            public decimal IdActvT { get; set; }
            public decimal Id { get; set; }
            public string Code { get; set; }
            public WorkflowObjectType Type { get; set; }

            public decimal StatusObj { get; set; }
            public string? SqlQuery { get; set; } // только для Task
            public List<WorkflowGraphNode> AllChildren { get; set; } = new();
            public List<WorkflowGraphNode> DependsOn { get; set; } = new();
            public List<WorkflowGraphNode> Children { get; set; } = new();
            public bool IsProcessing { get; set; } = false;
        }


    }
}
