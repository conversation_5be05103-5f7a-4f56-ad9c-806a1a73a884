﻿@attribute [Authorize]
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Ra<PERSON>zen
@using Radzen.Blazor
@using EWA.Services
@using System
@using System.Collections.Generic
@using System.Linq
@using System.Threading.Tasks

<RadzenCard class=" rz-pt-0" Variant="Variant.Text">
    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" AlignItems="AlignItems.Center" class="rz-p-4;">
        <RadzenImage Path="@UserPhoto" Style="width: 100px; height: 100px; border-radius: 50%;" />
        <RadzenStack Gap="0">
            <RadzenFormField Text="ФИО" Variant="Variant.Text" Style="flex: 1;">
                <RadzenTextBox Value="@user.NAME" Disabled="true"/>
            </RadzenFormField>
            <RadzenFormField Text="Должность" Variant="Variant.Text" Style="flex: 1;">
                <RadzenTextBox Value="@user.POSITION" Disabled="true"/>
            </RadzenFormField>
            <RadzenFormField Text="Логин" Variant="Variant.Text" Style="flex: 1;">
                <RadzenTextBox Value="@user.CODE" Disabled="true"/>
            </RadzenFormField>
            <RadzenFormField>
                <RadzenFileInput Accept="image/*" ChooseText="Обновить фото профиля" TValue="string" @key="_fileKey" Change=@(args => OnFileChange(args)) />
            </RadzenFormField>
        </RadzenStack>
    </RadzenStack>
</RadzenCard>


@code{
    [Inject]
    protected DialogService DialogService { get; set; }
    [Inject]
    protected SIBService.SecurityService Security { get; set; }
    [Inject]
    protected SIBService.UserService UsrServ { get; set; }
    [Parameter] public EWA.Models.SIB_Models.SIB_USERS user { get; set; }
    private string UserPhoto;
    protected override async Task OnInitializedAsync()
    {
        var prfPhoto = user.PROFILE_PHOTO;
        if (!string.IsNullOrEmpty(prfPhoto))
        {
            UserPhoto = await UsrServ.GetUserPhotoBase64Async(user.PROFILE_PHOTO);
        }
        if (UserPhoto == null)
        {
            UserPhoto = "images/person.svg";
        }
    }
    private Guid _fileKey = Guid.NewGuid();
    async Task OnFileChange(string val)
    {
        if (!string.IsNullOrEmpty(val) && val.StartsWith("data:image/"))
        {
            user.DT_CHANGE = DateTime.Now;
            user.USER_CHANGE = user.CODE;
            user.PROFILE_PHOTO = val;
            UserPhoto = await UsrServ.GetUserPhotoBase64Async(val);
            var res = await UsrServ.UpdatePhotoProfile(user);
        }
        _fileKey = Guid.NewGuid();
    }
}