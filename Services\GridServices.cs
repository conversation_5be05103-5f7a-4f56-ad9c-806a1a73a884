﻿using EWA.Models;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.OData.Edm;
using NCalc;
using Oracle.ManagedDataAccess.Types;
using <PERSON><PERSON><PERSON>;
using System.Text.RegularExpressions;
using static EWA.Models.REP_Models;

namespace EWA.Services
{
    public class GridServices
    {
        private static Dictionary<string, SibFormInfo> SibInfo = new Dictionary<string, SibFormInfo> {
                { "SUSERS",
                new SibFormInfo {AddForm=typeof(EWA.Components.Pages.SIB.SIBUserAdd), AddTitle="Добавление нового пользователя",
                                 UpdForm=typeof(EWA.Components.Pages.SIB.SIBUserEdit), UpdTitle="Редактирование пользователя",
                                 DelForm=typeof(EWA.Components.Pages.GLOBAL.FormGrid), DelTitle="Удаление пользователя"}
                },
                { "SRULES",
                new SibFormInfo {AddForm=typeof(EWA.Components.Pages.SIB.SIBRuleAdd), AddTitle="Добавление правила доступа",
                                 UpdForm=typeof(EWA.Components.Pages.SIB.SIBRuleEdit), UpdTitle="Редактировать правило доступа",
                                 DelForm=typeof(EWA.Components.Pages.GLOBAL.FormGrid), DelTitle="Удаление правила доступа"}
                }/*,
                { "SUSERSATTR",
                new SibFormInfo {AddForm=typeof(EWA.Components.Pages.SIB.SIBUserAttrAdd), AddTitle="Добавление атрибута пользователя",
                                 UpdForm=typeof(EWA.Components.Pages.GLOBAL.FormGrid), UpdTitle="Редактирование атрибута пользователя",
                                 DelForm=typeof(EWA.Components.Pages.GLOBAL.FormGrid), DelTitle="Удаление атрибута пользователя"}
                },
                { "SOBJECTATTR",
                new SibFormInfo {AddForm=typeof(EWA.Components.Pages.GLOBAL.FormGrid), AddTitle="Добавление атрибута объекта",
                                 UpdForm=typeof(EWA.Components.Pages.GLOBAL.FormGrid), UpdTitle="Редактирование атрибута объекта",
                                 DelForm=typeof(EWA.Components.Pages.GLOBAL.FormGrid), DelTitle="Удаление атрибута объекта"}
                },
                { "SROLES",
                new SibFormInfo {AddForm=typeof(EWA.Components.Pages.GLOBAL.FormGrid), AddTitle="Добавление модуля",
                                 UpdForm=typeof(EWA.Components.Pages.GLOBAL.FormGrid), UpdTitle="Редактирование модуля",
                                 DelForm=typeof(EWA.Components.Pages.GLOBAL.FormGrid), DelTitle="Удаление модуля"}
                },
                { "SATTRSPR",
                new SibFormInfo {AddForm=typeof(EWA.Components.Pages.GLOBAL.FormGrid), AddTitle="Добавление атрибута",
                                 UpdForm=typeof(EWA.Components.Pages.GLOBAL.FormGrid), UpdTitle="Редактировать атрибут",
                                 DelForm=typeof(EWA.Components.Pages.GLOBAL.FormGrid), DelTitle="Удаление атрибута"}
                }*/
                };

        public (Type, string) getSibFormInfo(string oper, string code, string title)
        {
            if (SibInfo.ContainsKey(code))
            {
                switch (oper)
                {
                    case "ADD":
                        return (SibInfo[code].AddForm, SibInfo[code].AddTitle);
                    case "EDIT":
                        return (SibInfo[code].UpdForm, SibInfo[code].UpdTitle);
                    case "DEL":
                        return (SibInfo[code].DelForm, SibInfo[code].DelTitle);
                }
            }

            return (typeof(EWA.Components.Pages.GLOBAL.FormGrid), title);
        }
        public bool MenyCheck(string _originalCondition, Dictionary<string, object> _rowData, Dictionary<string, Rep_Param> _paramData)
        {
            if (String.IsNullOrEmpty(_originalCondition))
                return true;

            string pattern101 = @"(\:columnvalue|\:paramvalue)\(([^)]*)\)";
            string replacement = "$2";
            string output = Regex.Replace(_originalCondition, pattern101, replacement);


            string patternIsNull = @"\bis\s+null\b";
            string patternIsNotNull = @"\bis\s+not\s+null\b";
            output = Regex.Replace(output, patternIsNull, "=null");
            output = Regex.Replace(output, patternIsNotNull, "!=null");



            Expression e = new Expression(output, ExpressionOptions.AllowNullParameter);
            String pattern = @"(:\w+)\(([^)]+)\)";
            MatchCollection matches = Regex.Matches(_originalCondition, pattern);
            foreach (Match match in matches)
            {

                string paramName = match.Groups[2].Value;

                if (e.Parameters.ContainsKey(paramName))
                    continue;

                if (_paramData.ContainsKey(paramName))
                {
                    e.Parameters.Add(paramName, _paramData[paramName].Val);
                }
                else
                if (_rowData.ContainsKey(paramName))
                {
                    e.Parameters.Add(paramName, _rowData[paramName]);

                }
                else
                {
                    e.Parameters.Add(paramName, null);
                }
            }


            var result = e.Evaluate();

            return (bool)result;
        }


        public decimal? GetValueNum(object _values)
        {
            if (_values == null || _values == DBNull.Value)
                return null;
            
            return Convert.ToDecimal(_values);

        }
        public Type GetColumnType(string typeName)
        {
            return typeName.ToUpper() switch
            {
                "NUMBER" => typeof(decimal), // or typeof(decimal)
                "FLOAT" => typeof(decimal),
                "INT" => typeof(decimal),//typeof(long),
                "VARCHAR2" => typeof(string),
                "CHAR" => typeof(string),
                "CLOB" => typeof(string),
                "BOOL" => typeof(bool),
                "DATE" => typeof(DateTime),
                "TIMESTAMP(6)" => typeof(DateTime),
                _ => typeof(string),
            };


        }
        /*
         switch ((meta.DATATYPE, meta.DATALENGTH, meta.DATAPRECISION))
{
    case ("VARCHAR2", int datalength, _) when datalength > 0 && (meta.DATAPRECISION is null or not null):
        type = $"VARCHAR2({datalength})";
        break;
    case ("NUMBER", int datalength, int precision) when datalength > 0 && precision > 0:
        type = $"NUMBER({datalength}, {precision})";
        break;
};*/

        public Type GetColumnType(ColumnMetadata column)
        {
            return (column.DATATYPE, column.DATALENGTH, column.DATAPRECISION)  switch
            {
                ("NUMBER",>0, >0) => typeof(decimal), // or typeof(decimal)
                ("NUMBER", > 0, _) => typeof(long),
                ("FLOAT", _, _) => typeof(decimal),
                ("INT", _, _) => typeof(long),//typeof(long),
                ("VARCHAR2", _, _) => typeof(string),
                ("CHAR", _, _) => typeof(string),
                ("CLOB", _, _)  => typeof(string),
                ("BOOL", _, _)  => typeof(bool),
                ("DATE", _, _)  => typeof(DateTime),
                ("TIMESTAMP(6)", _, _) => typeof(DateTime),
                _ => typeof(string),
            };


        }

        public bool GetColumnSort(string typeName, bool is_load)
        {
            if (!is_load)
            {
                return false;
            }
            return typeName.ToUpper() switch
            {
                
                "CLOB" => false,                
                _ => true,
            };


        }

        public string GetColumnPropertyExpression(string name, Type type)
        {
            var expression = $@"it[""{name}""].ToString()";

            if (type == typeof(int))
            {
                return $"int.Parse({expression})";
            }
            else if (type == typeof(DateTime))
            {
                return $"DateTime.Parse({expression})";
            }
            else if (type.IsEnum)
            {
                return $@"Int32(Enum.Parse(it[""{name}""].GetType(), {expression}))";
            }

            return expression;
        }

        public string GetPropertyName(string colCode)
        {
            if (colCode == "some_special_code")
            {
                return "special_property";
            }

            return colCode;
        }

        public bool AreDictionariesEqual(Dictionary<string, object> dict1, Dictionary<string, object> dict2)
        {
            if (dict1 == null | dict2 == null)
            {
                return false;
            }
            if (dict1.Count != dict2.Count)
                return false;

            foreach (var kvp in dict1)
            {
                if (!dict2.TryGetValue(kvp.Key, out var value) || !Equals(kvp.Value, value))
                    return false;
            }

            return true;
        }

        public bool AreRowEqual(IDictionary<string, object> dict1, IDictionary<string, object> dict2, List<ColumnMetadata> column)
        {
            if (dict1 == null | dict2 == null)
            {
                return false;
            }
         
            foreach (var kvp in column)
            {
                if (!dict2.TryGetValue(kvp.CODE, out var value1)) 
                    return false;

                if (!dict1.TryGetValue(kvp.CODE, out var value2))
                    return false;

                if (kvp.DATATYPE=="NUMBER")
                {
                    if (value1 == DBNull.Value && value2 == DBNull.Value)
                        continue;

                    if (value1==DBNull.Value && value2!=DBNull.Value)
                        return false;

                    if (value2 == DBNull.Value && value1 != DBNull.Value)
                        return false;
                    /* if (Convert.ToDecimal(value1)!= Convert.ToDecimal(value2))
                        return false; */
                    // учет OracleDecimal
                    decimal d1 = value1 is OracleDecimal od1 ? od1.Value
                                    : Convert.ToDecimal(value1);

                    decimal d2 = value2 is OracleDecimal od2 ? od2.Value
                                  : Convert.ToDecimal(value2);
                    if (d1 != d2)
                        return false;

                }
                else if  (!Equals(value1, value2))
                    return false;
                    
                    
            }

            return true;
        }

        public bool AreDictionariesEqual1(IDictionary<string, object> dict1, IDictionary<string, object> dict2)
        {
            if (dict1 == null | dict2 == null)
            {
                return false;
            }
            if (dict1.Count != dict2.Count)
                return false;

            foreach (var kvp in dict1)
            {
                if (!dict2.TryGetValue(kvp.Key, out var value) || !Equals(kvp.Value, value))
                    return false;
            }

            return true;
        }

        public void CopyDictionaries(Dictionary<string, object> OldParams, Dictionary<string, object> dict1)
        {
            if (dict1 == null)
            {
                return;
            }

            if (OldParams == null)
            {
                OldParams = new Dictionary<string, object>();
            }

            OldParams.Clear();

            foreach (var kvp in dict1.Select(x => x.Key))
            {
                OldParams.Add(kvp, dict1[kvp]);
            }

            return;
        }

        public async Task<(Dictionary<string, Rep_Param> _filterprm, string _filterSql_w, string _filterSql)> ConvertFilterDescriptorToSql(IEnumerable<Radzen.FilterDescriptor> filters, List<REP_Models.ColumnMetadata> metadata)
        {
            var filterConditions = new List<string>();
            var filterConditions_w = new List<string>();
            Dictionary<string, Rep_Param> _filterprm = new Dictionary<string, Rep_Param>();
            string _filterSql_w= string.Empty;
            string _filterSql= string.Empty;
            if (filters == null || !filters.Any())
            {
                return (_filterprm, _filterSql_w, _filterSql);
            }


            foreach (var filter in filters)
            {
                var column = filter.Property;
                var value = filter.FilterValue?.ToString();
                var operation = filter.FilterOperator;

                string dtoper = "!";


                switch (operation)
                {
                    case FilterOperator.Equals:
                        dtoper = "=";
                        break;
                    case FilterOperator.NotEquals:
                        dtoper = "!=";
                        break;
                    case FilterOperator.LessThan:
                        dtoper = "<";
                        break;
                    case FilterOperator.LessThanOrEquals:
                        dtoper = "<=";
                        break;
                    case FilterOperator.GreaterThan:
                        dtoper = ">";
                        break;
                    case FilterOperator.GreaterThanOrEquals:
                        dtoper = ">=";
                        break;
                    

                };

                foreach (var col in metadata.Where(x => x.CODE == column))
                {
                    string sqlCondition = "";
                    Type t = GetColumnType(col);
                    switch (operation, t)
                    {
                        case (FilterOperator.IsNull, _) :
                        case (FilterOperator.IsEmpty, _) :
                            sqlCondition = $"({column} is null)";
                            filterConditions.Add(sqlCondition);
                            filterConditions_w.Add(sqlCondition);
                            break;

                        case (FilterOperator.IsNotNull, _) :
                        case (FilterOperator.IsNotEmpty, _) :
                            sqlCondition = $"({column} is not null)";
                            filterConditions.Add(sqlCondition);
                            filterConditions_w.Add(sqlCondition);
                            break;

                        case (FilterOperator.Equals, _) when col.DOMAINCODE != "Bool":
                        case (FilterOperator.NotEquals, _):                     
                        case (FilterOperator.LessThan, _):
                        case (FilterOperator.LessThanOrEquals, _):
                        case (FilterOperator.GreaterThan, _):
                        case (FilterOperator.GreaterThanOrEquals, _):
                            sqlCondition = $"{column}{dtoper}{value}";
                            filterConditions.Add(sqlCondition);
                            if (t == typeof(DateTime))
                            {
                                sqlCondition = $"trunc({column}) {dtoper} trunc(:f_{column})";
                            }
                            else if (t != typeof(string))
                            {
                                sqlCondition = $"{column} {dtoper}:f_{column}";
                            }
                            else
                            {
                                sqlCondition = $"lower({column}){dtoper}lower(:f_{column})";
                            }
                            filterConditions_w.Add(sqlCondition);
                            Rep_Param prm = new Rep_Param(1, col.DATATYPE, System.Data.ParameterDirection.Input, col.DATALENGTH, col.DATAPRECISION, filter.FilterValue);
                            _filterprm.Add($"f_{column}", prm);
                            break;

                        
                        case (FilterOperator.Contains, _):
                            sqlCondition = $"lower({column}) like lower('%{value}%')";
                            filterConditions.Add(sqlCondition);
                            filterConditions_w.Add(sqlCondition);
                            break;
                        case (FilterOperator.DoesNotContain, _):
                            sqlCondition = $"lower({column}) not like lower('%{value}%')";
                            filterConditions.Add(sqlCondition);
                            filterConditions_w.Add(sqlCondition);
                            break;
                        case (FilterOperator.StartsWith, _):
                            sqlCondition = $"lower({column}) like lower('{value}%')";
                            filterConditions.Add(sqlCondition);
                            filterConditions_w.Add(sqlCondition);
                            break;
                        case (FilterOperator.EndsWith, _):
                            sqlCondition = $"lower({column}) like lower('%{value}')";
                            filterConditions.Add(sqlCondition);
                            filterConditions_w.Add(sqlCondition);
                            break;
                    };
                    
                  


                    if (col.DOMAINCODE == "Bool")
                    {

                        value = bool.Parse(value) == true ? "1" : "0";
                        sqlCondition = $"({column} ={value})";
                        filterConditions.Add(sqlCondition);
                        filterConditions_w.Add(sqlCondition);
                        continue;
                    }

                   

                }


            }

            return (_filterprm, string.Join(" AND ", filterConditions_w), string.Join(" AND ", filterConditions));
        }
    }
}
