﻿@attribute [Authorize]

@using System
@using System.Collections.Generic
@using System.Linq
@using System.Text
@using System.Text.Json
@using System.Threading.Tasks
@using System.Text.RegularExpressions
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Radzen
@using Radzen.Blazor
@using Microsoft.AspNetCore.Identity
@using EWA.Models
@using EWA.Services
@inject TooltipService tooltipService
<style>
    .rz-selectbutton .rz-button.rz-button-sm {
        display: inline-block;
        background-color: var(--rz-grid-header-background-color);
        color: var(--rz-input-placeholder-color);
        border: var(--rz-border-normal);
    }

        .rz-selectbutton .rz-button.rz-button-sm.rz-state-active {
            background-color: var(--rz-primary-light);
            color: var(--rz-selectbar-selected-color);
            border: var(--rz-selectbar-border);
        }

    :root {
        --rz-input-line-height: 1rem;
        --rz-input-height: 2rem;
    }
</style>
<RadzenStack>
    <RadzenFieldset Text="Редактор правил" Collapsible="true" Style="width: 100%; height:100%;">
        <RadzenDataGrid @ref="grid"
                        TItem="REP_Models.RuleModel"
                        Data="@rules_h"
                        AllowSorting="false"
                        Density="Density.Compact"
                        RowRender="@RowRender"
                        LoadChildData="@LoadChildData"
                        RowCollapse="@(args => grid.ColumnsCollection.ToList().ForEach(c => c.ClearFilters()))"
                        EditMode="DataGridEditMode.Single"
                        RowUpdate="@OnUpdateRow"
                        RowCreate="@OnCreateRow"
                        AllowPaging="true"
                        PagerPosition="PagerPosition.Bottom"
                        AllowColumnResize="true"
                        ColumnWidth="100px"
                        Style="width:100%; height:420px;"
                        @bind-Value=@selectedItems
                        SelectionMode="DataGridSelectionMode.Single"
                        PagerHorizontalAlign="HorizontalAlign.Center"
                        ShowPagingSummary="true"
                        PageSizeText="записей на странице"
                        EmptyText="Нет записей для отображения"
                        PagingSummaryFormat="@pagingSummaryFormat"
                        PageSize="@pageDefault"
                        PageSizeOptions="@pageSizeOptions"
                        >
            <HeaderTemplate>
                <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween">
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="2rem">
                        <RadzenColumn>
                            <RadzenButton Icon="add"
                                          Click="@InsertCond"
                                          Disabled="@(RowToInsert.Count() > 0)"
                                          Variant="Variant.Text"
                                          ButtonStyle="ButtonStyle.Base"
                                          MouseLeave="TooltipService.Close"
                                          MouseEnter="@(args => ShowTooltip(args, "Добавить условие", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                            <RadzenButton Icon="library_add"
                                          Click="@InsertGroup"
                                          Disabled="@(RowToInsert.Count() > 0)"
                                          Variant="Variant.Text"
                                          ButtonStyle="ButtonStyle.Base"
                                          MouseLeave="TooltipService.Close"
                                          MouseEnter="@(args => ShowTooltip(args,"Добавить группу", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                            <RadzenButton Icon="content_copy"
                                          Variant="Variant.Text"
                                          ButtonStyle="ButtonStyle.Base"
                                          Click="@CopySelectedRecord"
                                          Disabled="@(selectedItems.Count == 0 || selectedItems.Any(item => item.CODE == "GR_MAIN"))"
                                          MouseLeave="TooltipService.Close"
                                          MouseEnter="@(args => ShowTooltip(args,"Создать копию строки", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                        </RadzenColumn>
                        <RadzenColumn>
                            <RadzenButton Icon="edit"
                                          Click="@EditSelectedRow"
                                          Disabled="@(selectedItems.Count == 0 || selectedItems.Any(item => item.CODE == "GR_MAIN"))"
                                          Variant="Variant.Text"
                                          ButtonStyle="ButtonStyle.Base"
                                          MouseLeave="TooltipService.Close"
                                          MouseEnter="@(args => ShowTooltip(args,"Редактировать строку", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                            <RadzenButton Icon="delete"
                                          Click="@RemoveRow"
                                          Disabled="@(selectedItems.Count == 0 || selectedItems.Any(item => item.CODE == "GR_MAIN"))"
                                          Variant="Variant.Text"
                                          ButtonStyle="ButtonStyle.Base"
                                          MouseLeave="TooltipService.Close"
                                          MouseEnter="@(args => ShowTooltip(args, "Удалить строку", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                        </RadzenColumn>
                        <RadzenColumn>
                            <RadzenToggleButton Icon="preview" ToggleIcon="preview_off"
                                                Click=@(() => FieldVisible = !FieldVisible)
                                                Variant="Variant.Text"
                                                @bind-Value="isActive"
                                                ButtonStyle="ButtonStyle.Base"
                                                MouseLeave="TooltipService.Close"
                                                MouseEnter="@(args => ShowTooltip(args, "Видимость полей", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                            <RadzenButton Icon="expand_circle_down"
                                          Variant="Variant.Text"
                                          ButtonStyle="ButtonStyle.Base"
                                          Click="@(args => ToggleRowsExpand(true))"
                                          MouseLeave="TooltipService.Close"
                                          MouseEnter="@(args => ShowTooltip(args, "Развернуть все", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                            <RadzenButton Icon="expand_circle_up" Variant="Variant.Text" ButtonStyle="ButtonStyle.Base"
                                          MouseLeave="TooltipService.Close"
                                          MouseEnter="@(args => ShowTooltip(args, "Свернуть все", new TooltipOptions(){ Position = TooltipPosition.Top }))"
                                          Click="@(args => ToggleRowsExpand(false))" />

                        </RadzenColumn>
                    </RadzenStack>

                    <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                        <RadzenButton Icon="deployed_code_update"
                                      Click="@CheckRule"
                                      Variant="Variant.Text"
                                      ButtonStyle="ButtonStyle.Base"
                                      MouseLeave="TooltipService.Close"
                                      MouseEnter="@(args => ShowTooltip(args,"Создать правило", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                        <RadzenButton Icon="reset_wrench"
                                      Click="@RestRules"
                                      Variant="Variant.Text"
                                      ButtonStyle="ButtonStyle.Base"
                                      MouseLeave="TooltipService.Close"
                                      MouseEnter="@(args => ShowTooltip(args, "Откатить", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                        <RadzenButton Icon="clear_all"
                                      Click="@ClearAllRules"
                                      Variant="Variant.Text"
                                      ButtonStyle="ButtonStyle.Base"
                                      MouseLeave="TooltipService.Close"
                                      MouseEnter="@(args => ShowTooltip(args, "Очистить", new TooltipOptions(){ Position = TooltipPosition.Top }))" />

                    </RadzenStack>
                </RadzenStack>
            </HeaderTemplate>
            <Columns>

                <RadzenDataGridColumn Title="Код Группы" Property="CODE_GROUP" MinWidth="50px" Width="150px" Frozen="true">
                    <Template Context="data">
                        @{
                            var groupName = data.CODE;
                            string displayName = GetDisplayName(groupName);
                        }
                        @if (data.TYPE == "GROUP")
                        {
                            <strong>
                                @displayName
                            </strong>
                        }
                        else
                        {
                            <span>
                                @displayName
                            </span>
                        }
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Код родительской группы" Property="PAR_GROUP" MinWidth="25px" Width="110px">
                    <Template Context="data">
                        @{
                            bool showWarning = data.TYPE == "COND" && data.PAR_GROUP == "GR_MAIN" && rules.Any(r => r.TYPE == "GROUP" && r.CODE_GROUP != "GR_MAIN");
                            string displayName;
                            if (data.PAR_GROUP == null)
                            {
                                displayName = "";
                            }
                            else
                            {
                                displayName = GetDisplayName(data.PAR_GROUP);
                            }
                        }
                        <span style="@(showWarning ? "color: var(--rz-danger)" : "")" title="@(showWarning ? "Необходимо сменить родительскую группу!" :  data.PAR_GROUP)">
                            @(displayName)
                        </span>
                    </Template>

                    <EditTemplate Context="rule">
                        @{
                            var displayNames = groupCodes
                            /*.Where(code => code != rule.CODE_GROUP) */
                            .Select(code => new
                            {
                                Value = code,
                                Text = GetDisplayName(code)
                            })
                            .ToList();
                        }
                        <RadzenDropDown @bind-Value="rule.PAR_GROUP"
                                        Data="@displayNames"
                                        Style="width:100%; "
                                        Placeholder="Выберите группу"
                                        TextProperty="Text"
                                        ValueProperty="Value"
                                        TValue="string"
                                        Change="@(value => OnParGroupChange(value, rule))"
                                        Disabled="@(rule.CODE == "GR_MAIN")" />

                    </EditTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="№" Property="NUM" MinWidth="15px" Visible="@FieldVisible" />
                <RadzenDataGridColumn Title="Код" Property="CODE" MinWidth="25px" Visible="@FieldVisible" />
                <RadzenDataGridColumn Title="Тип" Property="TYPE" MinWidth="25px" Width="70px" Visible="@FieldVisible">
                    <Template Context="rule">
                        @(typeGroup.FirstOrDefault(x => x.Value == rule.TYPE).Key)
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Title="Объединение" Property="OPER_UNION" Width="105px" MinWidth="70px">
                    <Template Context="rule">
                        @(logicConditions.FirstOrDefault(x => x.Value == rule.OPER_UNION).Key)
                    </Template>
                    <EditTemplate Context="rule">
                        <RadzenSelectBar @bind-Value="rule.OPER_UNION"
                                         Data="@logicConditions"
                                         TextProperty="Key"
                                         ValueProperty="Value"
                                         
                                         Style="width:110px;"
                                         Disabled="@(!CheckCntRow(rule.PAR_GROUP) || rule.CODE == "GR_MAIN")"
                                         TValue="string"
                                         Size="ButtonSize.Small" />
                    </EditTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Метрика" Property="TYPE_ATTR" Width="70px" MinWidth="40px">
                    <Template Context="rule">
                        <span title="@(type_attr.FirstOrDefault(x => x.Value == rule.TYPE_ATTR).Key)">
                            @(type_attr.FirstOrDefault(x => x.Value == rule.TYPE_ATTR).Key)
                        </span>
                    </Template>
                    <EditTemplate Context="rule">
                        <RadzenDropDown Name="METRICA"
                                        @bind-Value="rule.TYPE_ATTR"
                                        Data="@type_attr"
                                        TextProperty="Key"
                                        Change="onChooseTypeAttr"
                                        ValueProperty="Value"
                                        Style="width:100%; display:block;"
                                        Placeholder="Выберите метрику"
                                        TValue="string"
                                        Disabled="@(rule.TYPE == "GROUP")" />
                        <RadzenRequiredValidator Component="METRICA" Text="Обязательное поле!" Popup="true" Visible="@(rule.TYPE == "COND")" />
                    </EditTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Значение метрики" Property="ATTR" Width="120px" MinWidth="40px">
                    <Template Context="rule">
                        <span title="@(Attrs_Dic.FirstOrDefault(x => x.Key == rule.ATTR)?.Value)">
                            @(Attrs_Dic.FirstOrDefault(x => x.Key == rule.ATTR)?.Value)
                        </span>
                    </Template>
                    <EditTemplate Context="rule">
                        <RadzenDropDown Name="ATTR"
                                        @bind-Value="rule.ATTR"
                                        Data="@Attrs"
                                        TextProperty="Value"
                                        ValueProperty="Key"
                                        Style="width:100%; display:block;"
                                        Placeholder="Выберите значение метрики"
                                        TValue="string"
                                        Disabled="@(rule.TYPE == "GROUP" || rule.TYPE_ATTR == null)"
                                        AllowFiltering="true"
                                        FilterCaseSensitivity=FilterCaseSensitivity.CaseInsensitive />
                        <RadzenRequiredValidator Component="ATTR" Text="Обязательное поле!" Popup="true" Visible="@(rule.TYPE == "COND")" />
                    </EditTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Оператор" Property="OPER_ATTR" Width="100px" MinWidth="40px">
                    <Template Context="rule">
                        <span title=" @(conditions.FirstOrDefault(x => x.Value == rule.OPER_ATTR).Key)">
                            @(conditions.FirstOrDefault(x => x.Value == rule.OPER_ATTR).Key)
                        </span>
                    </Template>
                    <EditTemplate Context="rule">
                        <RadzenDropDown Name="OPER_ATTR"
                                        @bind-Value="rule.OPER_ATTR"
                                        Data="@conditions"
                                        TextProperty="Key"
                                        ValueProperty="Value"
                                        Style="width:100%; display:block;"
                                        Placeholder="Выберите оператор"
                                        TValue="string"
                                        Disabled="@(rule.TYPE == "GROUP")" />
                        <RadzenRequiredValidator Component="OPER_ATTR" Text="Обязательное поле!" Popup="true" Visible="@(rule.TYPE == "COND")" />
                    </EditTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Тип значения" Property="TYPE_ATTR_VAL" Width="70px" MinWidth="40px">
                    <Template Context="rule">
                        <span title="@(type_attr_val.FirstOrDefault(x => x.Value == rule.TYPE_ATTR_VAL).Key)">
                            @(type_attr_val.FirstOrDefault(x => x.Value == rule.TYPE_ATTR_VAL).Key)
                        </span>
                    </Template>
                    <EditTemplate Context="rule">
                        <RadzenDropDown @bind-Value="rule.TYPE_ATTR_VAL"
                                        Data="@type_attr_val"
                                        TextProperty="Key"
                                        Name="TYPE_VALUE"
                                        Change="onChooseTypeAttrVal"
                                        ValueProperty="Value"
                                        Style="width:100%; display:block;"
                                        Placeholder="Выберите атрибут"
                                        TValue="string"
                                        Disabled="@(rule.TYPE == "GROUP")" />
                        <RadzenRequiredValidator Component="TYPE_VALUE" Text="Обязательное поле!" Popup="true" Visible="@(rule.TYPE == "COND")" />

                    </EditTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Значение (справочник)" Property="ATTR_VAL" Width="120px" MinWidth="40px">
                    <Template Context="rule">
                        <span title=" @(Attrs_Dic.FirstOrDefault(x => x.Key == rule.ATTR_VAL)?.Value)">
                            @(Attrs_Dic.FirstOrDefault(x => x.Key == rule.ATTR_VAL)?.Value)
                        </span>
                    </Template>
                    <EditTemplate Context="rule">
                        <RadzenDropDown @bind-Value="rule.ATTR_VAL"
                                        Data="@AttrsVal"
                                        TextProperty="Value"
                                        ValueProperty="Key"
                                        Style="width:100%; display:block;"
                                        Name="ATTR_VAL"
                                        Placeholder="Выберите атрибут"
                                        TValue="string"
                                        Disabled="@(rule.TYPE == "GROUP" || rule.TYPE_ATTR_VAL == "V" || rule.TYPE_ATTR == null)"
                                        AllowFiltering="true"
                                        FilterCaseSensitivity=FilterCaseSensitivity.CaseInsensitive />
                        <RadzenRequiredValidator Component="ATTR_VAL" Text="Обязательное поле!" Popup="true" Visible="@(rule.TYPE == "COND" && rule.TYPE_ATTR_VAL != "V" && !string.IsNullOrEmpty(rule.TYPE_ATTR_VAL))" />
                    </EditTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Значение (ручной ввод)" Property="VAL">
                    <EditTemplate Context="rule">
                        <RadzenTextBox Name="VAL_H" @bind-Value="rule.VAL" Style="width:100%; display:block;" Disabled="@(rule.TYPE == "GROUP" || rule.TYPE_ATTR_VAL !="V" )" />
                        <RadzenRequiredValidator Component="VAL_H" Text="Обязательное поле!" Popup="true" Visible="@(rule.TYPE == "COND" && rule.TYPE_ATTR_VAL == "V")" />
                    </EditTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Действие" Frozen="true" FrozenPosition="FrozenColumnPosition.Right" MinWidth="120px">
                    <Template Context="rule">
                        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween">
                            <RadzenColumn>
                                <RadzenButton Icon="add"
                                              Click="@InsertCond"
                                              Disabled="@(RowToInsert.Count() > 0)"
                                              Variant="Variant.Text"
                                              ButtonStyle="ButtonStyle.Base"
                                              Size="ButtonSize.ExtraSmall"
                                              MouseLeave="TooltipService.Close"
                                              MouseEnter="@(args => ShowTooltip(args, "Добавить условие", new TooltipOptions(){ Position = TooltipPosition.Top, Delay = 1000 }))" />
                                <RadzenButton Icon="library_add"
                                              Click="@InsertGroup"
                                              Disabled="@(RowToInsert.Count() > 0)"
                                              Variant="Variant.Text"
                                              ButtonStyle="ButtonStyle.Base"
                                              Size="ButtonSize.ExtraSmall"
                                              MouseLeave="TooltipService.Close"
                                              MouseEnter="@(args => ShowTooltip(args,"Добавить группу", new TooltipOptions(){ Position = TooltipPosition.Top, Delay = 1000 }))" />
                             </RadzenColumn>
                            <RadzenColumn>
                                <RadzenButton Icon="edit"
                                              Click="@EditSelectedRow"
                                              Disabled="@(rule.CODE == "GR_MAIN")"
                                              Variant="Variant.Text"
                                              ButtonStyle="ButtonStyle.Base"
                                              Size="ButtonSize.ExtraSmall"
                                              MouseLeave="TooltipService.Close"
                                              MouseEnter="@(args => ShowTooltip(args,"Редактировать строку", new TooltipOptions(){ Position = TooltipPosition.Top, Delay = 1000 }))" />
                                <RadzenButton Icon="delete"
                                              Click="@RemoveRow"
                                              Disabled="@(rule.CODE == "GR_MAIN")"
                                              Variant="Variant.Text"
                                              ButtonStyle="ButtonStyle.Base"
                                              Size="ButtonSize.ExtraSmall"
                                              MouseLeave="TooltipService.Close"
                                              MouseEnter="@(args => ShowTooltip(args, "Удалить строку", new TooltipOptions(){ Position = TooltipPosition.Top, Delay = 1000 }))" />
                            </RadzenColumn>

                        </RadzenStack>
                    </Template>
                    <EditTemplate Context="rule">
                        <RadzenButton Size="ButtonSize.ExtraSmall"
                                      Icon="check"
                                      Click="@(args => grid.UpdateRow(rule))" />
                        <RadzenButton Size="ButtonSize.ExtraSmall"
                                      Icon="close"
                                      Click="@((args) => CancelEdit(rule))"
                                      Style="margin-left: 5px;" />
                    </EditTemplate>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </RadzenFieldset>

    <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" Style="margin-top: 1rem;">
        <RadzenFieldset Text="Результат" Style="width: 100%">
            <RadzenTextArea Value="@rule_STR" Style="width: 100%; height: 200px; resize:vertical" ReadOnly="true" />
        </RadzenFieldset>
    </RadzenStack>

    <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End" Gap="0.5rem" Style="margin-top: 1rem;">
        <RadzenButton ButtonType="ButtonType.Submit" Icon="save" Text="Сохранить" Click="@SaveClick" Variant="Variant.Flat" />
        <RadzenButton ButtonStyle="ButtonStyle.Light" Text="Отмена" Click="@CancelClick" Variant="Variant.Flat" />
    </RadzenStack>
</RadzenStack>

@code {
    [Inject]
    protected IJSRuntime JSRuntime { get; set; }
    [Inject]
    protected NavigationManager NavigationManager { get; set; }
    [Inject]
    protected DialogService DialogService { get; set; }
    [Inject]
    protected TooltipService TooltipService { get; set; }
    [Inject]
    protected ContextMenuService ContextMenuService { get; set; }
    [Inject]
    protected NotificationService NotificationService { get; set; }
    [Inject]
    protected EWA.Services.SIBService.UserService UServ { get; set; }
    [Inject]
    protected EWA.Services.FormRuleService FRServ { get; set; }
    [Parameter]
    public string Code { get; set; }
    [Parameter]
    public string DBname { get; set; }
    [Parameter]
    public Dictionary<string, string> metaALG { get; set; }
    [Parameter]
    public Dictionary<string, object> metaALGData { get; set; }
    public Dictionary<string, object> metaALGData_OUT = new Dictionary<string, object>();
    string pagingSummaryFormat = "Страница {0} из {1} <b>(всего {2} записей)</b>";
    IEnumerable<int> pageSizeOptions = new int[] { 10, 25, 50 };
    int pageDefault = 10;
    bool FieldVisible = false;
    private bool isActive = true;
    private bool allRowsExpanded = false; // По умолчанию установите значение на false
    async Task ToggleRowsExpand(bool expand)
    {
        allRowsExpanded = expand;

        if (expand)
        {
            await grid.ExpandRows(rules);
        }
        else
        {
            await grid.CollapseRows(grid.View);
        }
    }
    RadzenDataGrid<REP_Models.RuleModel> grid;
    private List<REP_Models.RuleModel> rules = new();
    IEnumerable<REP_Models.RuleModel> rules_h = Enumerable.Empty<REP_Models.RuleModel>();
    private REP_Models.RuleModel selectedRow;
    private IList<REP_Models.RuleModel> selectedItems = new List<REP_Models.RuleModel>();
    private List<REP_Models.RuleModel> RowToInsert = new List<REP_Models.RuleModel>();
    private List<REP_Models.RuleModel> RowToUpdate = new List<REP_Models.RuleModel>();
    private List<string> groupCodes = new List<string> { "GR_MAIN" };
    private string defgroup = "GR_MAIN";
    private REP_Models.RuleModel currentRule = new REP_Models.RuleModel();
    private int ruleCounter = 1;
    private string rule_JSN = string.Empty;
    private string rule_STR = string.Empty;
    private List<DropDownAttr> Attrs_Dic = new List<DropDownAttr>();
    private List<DropDownAttr> Attrs = new List<DropDownAttr>();
    private List<DropDownAttr> AttrsVal = new List<DropDownAttr>();
    private string list_ind = string.Empty;
    private string list_attr = string.Empty;
    private Dictionary<string, (string Value, string tattr)> AttSpr = new Dictionary<string, (string Value, string tattr)>();

    private Dictionary<string, string> conditions = new Dictionary<string, string>
    {
        { "Равно", "=" },
        { "Больше", ">" },
        { "Меньше", "<" },
        { "Меньше либо равно", "<=" },
        { "Больше либо равно", ">=" },
        { "Не равно", "!=" },
        { "Входит в", "IN" },
        { "Не входит в", "NOT IN" },
        { "Содержит", "LIKE" },
        { "Не содержит", "NOT LIKE" },
        { "Между", "BETWEEN" }
    };

    private Dictionary<string, string> type_attr = new Dictionary<string, string>
    {
        { "Атрибут", "A" },
        { "Показатель", "I" },
    };

    private Dictionary<string, string> type_attr_val = new Dictionary<string, string>
    {
        { "Атрибут", "A" },
        { "Показатель", "I" },
        { "Значение", "V"}
    };

    private Dictionary<string, string> logicConditions = new Dictionary<string, string>
    {
        { "И", "AND" },
        { "ИЛИ", "OR" }
    };

    private Dictionary<string, string> typeGroup = new Dictionary<string, string>
    {
        { "Группа", "GROUP"},
        { "Условие", "COND"}
    };

    private void onChooseTypeAttr(object value)
    {
        Attrs = Attrs_Dic
                        .Where(x => x.Tattr == value.ToString())
                        .ToList();
    }

    private void onChooseTypeAttrVal(object value)
    {
        if (value.ToString() != "V")
        {
            AttrsVal = Attrs_Dic
                        .Where(x => x.Tattr == value.ToString())
                        .ToList();
        }
    }
    //преобразование кода группы и условия в текст
    private string GetDisplayName(string groupName)
    {
        var formats = new Dictionary<string, string>
        {
            { @"GR_(\d+)", "Группа {0}" },
            { @"CND_(\d+)", "Условие {0}" },
            { @"GR_MAIN", "Основная группа" }
        };
        foreach (var format in formats)
        {
            var match = Regex.Match(groupName, format.Key);
            if (match.Success)
            {
                return string.Format(format.Value, match.Groups[1].Value);
            }
        }
        return groupName;
    }

    void ShowTooltip(ElementReference elementReference, string buttomName, TooltipOptions options = null) => tooltipService.Open(elementReference, buttomName, options);

    void Reset()
    {
        RowToInsert.Clear();
        RowToUpdate.Clear();
    }

    void Reset(REP_Models.RuleModel row)
    {
        RowToInsert.Remove(row);
        RowToUpdate.Remove(row);
    }

    private void ClearData()
    {
        Reset();
        rules.Clear();
        ruleCounter = 1;
        currentRule = new REP_Models.RuleModel();
        rule_STR = string.Empty;
        groupCodes = new List<string> { "GR_MAIN" };
        rules.Add(new REP_Models.RuleModel
            {
                NUM = ruleCounter,
                CODE_GROUP = defgroup,
                CODE = defgroup,
                TYPE = "GROUP"
            });
        rules_h = rules.Where(r => r.PAR_GROUP == null);
    }

    private async Task ClearAllRules()
    {
        var res = await DialogService.Confirm("Очистить правило?", "Очистка", new ConfirmOptions() { OkButtonText = "Да", CancelButtonText = "Нет" });
        if (res == true)
        {
            ClearData();
            await grid.Reload();
            StateHasChanged();
        }
    }

    private async Task RestRules()
    {
        var res = await DialogService.Confirm("Восстановить правило?", "Откат", new ConfirmOptions() { OkButtonText = "Да", CancelButtonText = "Нет" });
        if (res == true)
        {
            ClearData();
            foreach (var kvp in metaALGData)
            {
                if (kvp.Key == "ALG")
                {
                    rule_STR = kvp.Value.ToString();
                }
                else if (kvp.Key == "ALG_JSN")
                {
                    rule_JSN = kvp.Value.ToString();
                }
                else if (kvp.Key == "I_LST")
                {
                    list_ind = kvp.Value.ToString();
                }
                else if (kvp.Key == "A_LST")
                {
                    list_attr = kvp.Value.ToString();
                }

            }
            if (!string.IsNullOrEmpty(rule_JSN))
            {
                rules = JsonSerializer.Deserialize<List<REP_Models.RuleModel>>(rule_JSN);
                groupCodes.Clear();
                groupCodes.AddRange(rules.Where(r => r.TYPE == "GROUP")
                                        .Select(r => r.CODE_GROUP)
                                        .Distinct());
            }
            else
            {
                if (!rules.Any(r => r.CODE_GROUP == defgroup))
                {
                    rules.Add(new REP_Models.RuleModel
                        {
                            NUM = ruleCounter,
                            CODE_GROUP = defgroup,
                            CODE = defgroup,
                            TYPE = "GROUP"
                        });
                }
            }
            ruleCounter = rules.Any() ? rules.Max(r => r.NUM) + 1 : 0;
            rules_h = rules.Where(r => r.PAR_GROUP == null);
            await grid.Reload();
            StateHasChanged();
        }
    }

    void CancelEdit(REP_Models.RuleModel rule)
    {
        Reset();
        grid.CancelEditRow(rule);
    }

    void InsertGroup()
    {
        Reset();
        string newGroupCode = $"GR_{groupCodes.Count}";
        string parentGroup = defgroup;
        if (selectedItems != null && selectedItems.Count > 0)
        {
            selectedRow = selectedItems[0];
            if (selectedRow.TYPE == "GROUP")
            {
                parentGroup = selectedRow.CODE_GROUP;
            }
            else if (selectedRow.TYPE == "COND")
            {
                parentGroup = selectedRow.PAR_GROUP;
            }
        }
        else
        {
            parentGroup = defgroup;
        }

        int rule_int = ruleCounter++;
        var row = new REP_Models.RuleModel
            {
                NUM = rule_int,
                CODE_GROUP = newGroupCode,
                CODE = newGroupCode,
                TYPE = "GROUP",
                PAR_GROUP = parentGroup,
            };
        RowToInsert.Add(row);
        grid.InsertRow(row);
    }

    void InsertCond()
    {
        Reset();
        string codeGroup = defgroup;
        if (selectedItems != null && selectedItems.Count > 0)
        {
            selectedRow = selectedItems[0];
            codeGroup = string.IsNullOrEmpty(selectedRow.CODE_GROUP) ? defgroup : selectedRow.CODE_GROUP;
        }
        int rul_num = ruleCounter++;
        var row = new REP_Models.RuleModel
            {
                NUM = rul_num,
                CODE_GROUP = codeGroup,
                CODE = $"CND_{rul_num}",
                TYPE = "COND",
                PAR_GROUP = codeGroup,
            };
        RowToInsert.Add(row);
        grid.InsertRow(row);
    }

    private async Task RemoveRow()
    {
        if (selectedItems != null && selectedItems.Count > 0)
        {
            var res = await DialogService.Confirm("Удалить строку?", "Удаление", new ConfirmOptions() { OkButtonText = "Да", CancelButtonText = "Нет" });
            if (res == true)
            {
                selectedRow = selectedItems[0];
                if (selectedRow.TYPE == "GROUP")
                {
                    groupCodes.Remove(selectedRow.CODE_GROUP);
                }
                rules.Remove(selectedRow);
                await grid.Reload();
                StateHasChanged();
            }
        }
    }

    void EditSelectedRow()
    {
        if (selectedItems != null && selectedItems.Count > 0)
        {
            selectedRow = selectedItems[0];
            if (selectedRow.TYPE == "COND")
            {
                onChooseTypeAttr(selectedRow.TYPE_ATTR);
                onChooseTypeAttrVal(selectedRow.TYPE_ATTR_VAL);
            }
            grid.EditRow(selectedRow);
        }
    }

    void OnUpdateRow(REP_Models.RuleModel row)
    {
        if (row.TYPE_ATTR_VAL == null || row.TYPE_ATTR_VAL.ToString() == string.Empty)
        {
            row.ATTR_VAL = null;
            row.VAL = null;
        }
        else if (row.TYPE_ATTR_VAL == "V")
        {
            row.ATTR_VAL = null;
        }
        else if (row.TYPE_ATTR_VAL != "V")
        {
            row.VAL = null;
        }
        Reset(row);
        grid.Reload();
        StateHasChanged();
    }

    void OnCreateRow(REP_Models.RuleModel row)
    {
        if (row.TYPE_ATTR_VAL == null || row.TYPE_ATTR_VAL.ToString() == string.Empty)
        {
            row.ATTR_VAL = null;
            row.VAL = null;
        }
        else if (row.TYPE_ATTR_VAL == "V")
        {
            row.ATTR_VAL = null;
        }
        else if (row.TYPE_ATTR_VAL != "V")
        {
            row.VAL = null;
        }
        rules.Add(row);
        if (row.TYPE == "GROUP")
        {
            groupCodes.Add(row.CODE);
        }
        RowToInsert.Clear();
        grid.Reload();
        StateHasChanged();
    }

    private void CopySelectedRecord()
    {
        Reset();
        int rul_num = ruleCounter++;
        string CodeGroup;
        if (selectedItems != null && selectedItems.Count > 0)
        {
            selectedRow = selectedItems[0];
        }
        if (selectedRow.TYPE == "GROUP")
        {
            CodeGroup = $"GR_{groupCodes.Count}";
        }
        else
        {
            CodeGroup = $"CND_{rul_num}";
        }
        if (selectedRow.TYPE == "COND")
        {
            onChooseTypeAttr(selectedRow.TYPE_ATTR);
            onChooseTypeAttrVal(selectedRow.TYPE_ATTR_VAL);
        }
        var row = new REP_Models.RuleModel
            {
                NUM = rul_num,
                CODE_GROUP = CodeGroup,
                CODE = CodeGroup,
                TYPE = selectedRow.TYPE ?? string.Empty,
                PAR_GROUP = selectedRow.PAR_GROUP ?? string.Empty,
                TYPE_ATTR = selectedRow.TYPE_ATTR ?? string.Empty,
                ATTR = selectedRow.ATTR ?? string.Empty,
                OPER_ATTR = selectedRow.OPER_ATTR ?? string.Empty,
                TYPE_ATTR_VAL = selectedRow.TYPE_ATTR_VAL ?? string.Empty,
                ATTR_VAL = selectedRow.ATTR_VAL ?? string.Empty,
                VAL = selectedRow.VAL ?? string.Empty,
                OPER_UNION = selectedRow.OPER_UNION ?? string.Empty
            };
        RowToInsert.Add(row);
        grid.InsertRow(row);
    }

    private bool is_start = false;
    private void OnParGroupChange(object value, REP_Models.RuleModel rule)
    {
        var selectedGroup = value as string;
        rule.PAR_GROUP = selectedGroup;
        if (rule.TYPE == "COND")
        {
            rule.CODE_GROUP = selectedGroup;
        }
    }

    void RowRender(RowRenderEventArgs<REP_Models.RuleModel> args)
    {
        args.Expandable = rules.Where(e => e.PAR_GROUP == args.Data.CODE).Any();
    }
    void LoadChildData(DataGridLoadChildDataEventArgs<REP_Models.RuleModel> args)
    {
        args.Data = rules.Where(e => e.PAR_GROUP == args.Item.CODE);
    }
    protected override bool ShouldRender()
    {
        return is_start;
    }
    private void CheckRule()
    {
        rule_STR = BuildRuleString(rules);
    }

    public string BuildRuleString(List<REP_Models.RuleModel> rules)
    {
        var ruleDict = rules
                     .Where(r => r.PAR_GROUP != null)
                     .GroupBy(r => r.PAR_GROUP)
                     .ToDictionary(g => g.Key, g => g.ToList());
        return BuildGroup(ruleDict, "GR_MAIN");
    }

    private string BuildGroup(Dictionary<string, List<REP_Models.RuleModel>> ruleDict, string groupCode)
    {
        if (!ruleDict.ContainsKey(groupCode))
            return string.Empty;
        var conditions = new List<string>();
        foreach (var rule in ruleDict[groupCode])
        {
            if (rule.TYPE == "GROUP")
            {
                var nestedGroup = BuildGroup(ruleDict, rule.CODE);
                if (!string.IsNullOrEmpty(nestedGroup))
                {
                    conditions.Add(nestedGroup);
                }
            }
            else if (rule.TYPE == "COND")
            {
                var condition = $"{rule.ATTR} {rule.OPER_ATTR} {rule.VAL} {rule.ATTR_VAL}";
                conditions.Add(condition);
            }
        }
        var groupString = string.Join($" {ruleDict[groupCode].Last().OPER_UNION} ", conditions);
        return conditions.Count > 1 ? $"({groupString})" : groupString;
    }

    private bool CheckCntRow(string codeGroup)
    {
        string groupToCheck = string.IsNullOrEmpty(codeGroup) ? defgroup : codeGroup;
        var conditionCount = rules.Count(rule => rule.TYPE == "COND" && rule.CODE_GROUP == groupToCheck);
        var groupCount = rules.Count(rule => rule.TYPE == "GROUP" && rule.PAR_GROUP == groupToCheck);
        return conditionCount >= 1 || groupCount >= 1;
    }

    protected override async Task OnInitializedAsync()
    {
        AttSpr = await FRServ.GetAttrAsync(Code, DBname);
        Attrs_Dic = AttSpr.Select(kv => new DropDownAttr
            {
                Key = kv.Key,
                Value = kv.Value.Value,
                Tattr = kv.Value.tattr
            }).ToList();
        foreach (var kvp in metaALGData)
        {
            if (kvp.Key == "ALG")
            {
                rule_STR = kvp.Value.ToString();
            }
            else if (kvp.Key == "ALG_JSN")
            {
                rule_JSN = kvp.Value.ToString();
            }
            else if (kvp.Key == "I_LST")
            {
                list_ind = kvp.Value.ToString();
            }
            else if (kvp.Key == "A_LST")
            {
                list_attr = kvp.Value.ToString();
            }

        }
        if (!string.IsNullOrEmpty(rule_JSN))
        {
            rules = JsonSerializer.Deserialize<List<REP_Models.RuleModel>>(rule_JSN);
            groupCodes.Clear();
            groupCodes.AddRange(rules.Where(r => r.TYPE == "GROUP")
                                    .Select(r => r.CODE_GROUP)
                                    .Distinct());
            ruleCounter = rules.Any() ? rules.Max(r => r.NUM) + 1 : 0;
        }
        else
        {
            rules.Add(new REP_Models.RuleModel
                {
                    NUM = ruleCounter,
                    CODE_GROUP = defgroup,
                    CODE = defgroup,
                    TYPE = "GROUP"
                });
        }
        rules_h = rules.Where(r => r.PAR_GROUP == null);
        await ToggleRowsExpand(true);
        is_start = true;
    }
    private void SaveClick()
    {
        rule_JSN = JsonSerializer.Serialize(rules, new JsonSerializerOptions { WriteIndented = true });
        metaALGData_OUT.Clear();
        foreach (var kvp in metaALG)
        {
            if (kvp.Key == "ALG")
            {
                metaALGData_OUT.Add(kvp.Key, rule_STR);
            }
            else if (kvp.Key == "ALG_JSON")
            {
                metaALGData_OUT.Add(kvp.Key, rule_JSN);
            }
            else if (kvp.Key == "IND_LIST")
            {
                metaALGData_OUT.Add(kvp.Key, string.Join(", ", rules
                                                                    .Where(r => r.TYPE_ATTR == "I")
                                                                    .Select(r => r.ATTR)
                                                                    .Distinct()
                                                                    .Union(
                                                                            rules.Where(r => r.TYPE_ATTR_VAL == "I")
                                                                                 .Select(r => r.ATTR_VAL)
                                                                                 .Distinct()
                                                                            )
                                                        )
                                    );
            }
            else if (kvp.Key == "ATTR_LIST")
            {

                metaALGData_OUT.Add(kvp.Key, string.Join(", ", rules
                                                                    .Where(r => r.TYPE_ATTR == "A")
                                                                    .Select(r => r.ATTR)
                                                                    .Distinct()
                                                                    .Union(
                                                                            rules.Where(r => r.TYPE_ATTR_VAL == "A")
                                                                                 .Select(r => r.ATTR_VAL)
                                                                                 .Distinct()
                                                                            )
                                                        )
                                    );
            }
        }
        DialogService.Close(new { status = 1, r_metaData = metaALGData_OUT });
    }
    private void CancelClick()
    {
        metaALGData_OUT.Clear();
        foreach (var kvp in metaALG)
        {
            metaALGData_OUT.Add(kvp.Key, null);
        }
        DialogService.Close(new { status = 0, r_metaData = metaALGData_OUT });
    }

    public class DropDownAttr
    {
        public string Key { get; set; }
        public string Value { get; set; }
        public string Tattr { get; set; }

    }
}