﻿@inject EWA.Services.SIBService.AesEncryptionService AesService
@inject DialogService _dialogService


<RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.SpaceBetween" Gap="0" Style="height:100%" @onkeydown="HandleKeyDown" @onkeyup="HandleKeyUp">
    <RadzenLabel Text="Строка для шифрования" />    
    <RadzenTextArea @bind-Value="PlainText" Placeholder="Введите текст для шифрования" Style="width: 100%; height: 100px;" />
</RadzenStack>
<RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.SpaceBetween" Gap="0" Style="height:100%">
    <RadzenButton Text="Зашифровать" Click="ManEncrypt" Style="margin-top: 10px;" />
</RadzenStack>

<RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.SpaceBetween" Gap="0" Style="height:100%">
    <RadzenAlert Shade="Radzen.Shade.Lighter" Variant="Radzen.Variant.Flat" Size="Radzen.AlertSize.Small" AlertStyle="Radzen.AlertStyle.Info" Visible="@isErr">@EncryptedError</RadzenAlert>

    @if (!isErr)
    {
        @if (!string.IsNullOrEmpty(PlainText) && !string.IsNullOrEmpty(EncryptedText))
        {
            <RadzenLabel Text="Результат" />
            <RadzenTextArea @bind-Value="EncryptedText" Style="width: 100%; height: 100px;" ReadOnly="true" />
        }
    }
</RadzenStack>

@code {
    
    private bool isErr { get; set; } = false;
    private string PlainText { get; set; } = string.Empty;
    private string EncryptedError { get; set; } = string.Empty;
    private string EncryptedText { get; set; } = string.Empty;
    private string DecryptedText { get; set; } = string.Empty;

     private void ManEncrypt()
    {
        try
        {
            if (!string.IsNullOrEmpty(PlainText))
            {
                EncryptedText = AesService.Encrypt(PlainText);
                DecryptedText = string.Empty;
                isErr = false;
            }
            else
            {
                EncryptedError = $"Ошибка: Не введен текст для шифрования";
                isErr = true;
            }
        }
        catch (Exception ex)
        {
            EncryptedError = $"Ошибка: {ex.Message}";
            isErr = true;
        }
    }
    
    private HashSet<string> pressedKeys = new();

    private async Task HandleKeyDown(KeyboardEventArgs e)
    {
        if (string.IsNullOrWhiteSpace(e?.Key))
            return;

        pressedKeys.Add(e.Key.ToLower());

        if (pressedKeys.Contains("alt") && pressedKeys.Contains("g"))
        {
            if (pressedKeys.Contains("1"))
            {
                var parameters = new Dictionary<string, object>();

                var options = new DialogOptions
                    {
                        Draggable = true,
                        Resizable = true
                    };
                await _dialogService.OpenAsync<FreeTime>("перерывчик",parameters, options);
                pressedKeys.Clear();
            }
        }
    }

    private void HandleKeyUp(KeyboardEventArgs e)
    {
        pressedKeys.Remove(e.Key.ToLower());
    }
}