﻿@attribute [Authorize]
@using System
@using System.Collections.Generic
@using System.Linq
@using System.Threading.Tasks
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@using Microsoft.AspNetCore.Identity
@using EWA.Models
@using EWA.Components.Pages.GLOBAL;

<RadzenRow>
    <RadzenColumn SizeMD="12">
        <RadzenTemplateForm TItem="EWA.Models.SIB_Models.SIB_RULES" Data="@data" Visible="@(data != null)" Submit="@FormSubmit">
            <RadzenAlert Title="Cannot create role" Shade="Shade.Lighter" Variant="Variant.Flat" Size="AlertSize.Small" AlertStyle="AlertStyle.Danger" Visible="@errorVisible">@error</RadzenAlert>
            <RadzenStack style="margin-bottom: 1rem;">
                <RadzenFieldset Text="Общее" Collapsible="true" Style="width: 100%;">

                    <RadzenStack Orientation="Orientation.Vertical">
                        <RadzenFormField Text="Код правила" Variant="Variant.Flat">
                            <ChildContent>
                                <RadzenTextBox style="width: 100%" @bind-Value="@(data.CODE)" Name="RULE_CODE" />
                            </ChildContent>
                        </RadzenFormField>
                        <RadzenFormField Text="Описание" Variant="Variant.Flat">
                            <ChildContent>
                                <RadzenTextBox style="width: 100%" @bind-Value="@(data.DESCRIPTION)" Name="DESCRIPTION" />
                            </ChildContent>
                        </RadzenFormField>
                        <RadzenFormField Text="Доступ" Variant="Variant.Flat">
                            <ChildContent>
                                <RadzenDropDown @bind-Value="data.ACCESS_TYPE" Data="@options" TextProperty="Text" ValueProperty="Value" Placeholder="Выберите тип доступа..." Style="width: 100%;" />
                            </ChildContent>
                        </RadzenFormField>
                        <RadzenRow Gap="0.2rem">
                            <RadzenCheckBox Value="@(data.IS_ACTIVE == 1)" ValueChanged="@((bool val) => data.IS_ACTIVE = val ? 1 : 0)" Name="IS_ACTIVE" />
                            <RadzenLabel Text="Признак активности правила" Component="IS_ACTIVE" class="rz-ms-2" />
                        </RadzenRow>
                    </RadzenStack>
                </RadzenFieldset>
                <RadzenFieldset Text="Действия с объектом" Collapsible="true" Style="width: 100%;">
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="4rem">
                        <RadzenStack Orientation="Orientation.Vertical" Gap="0.5rem">
                            <RadzenRow Gap="0.2rem">
                                <RadzenCheckBox Value="@(data.ACTION_USE == 1)" ValueChanged="@((bool val) => data.ACTION_USE = val ? 1 : 0)" Name="ACTION_USE" />
                                <RadzenLabel Text="Просмотр" Component="ACTION_USE" class="rz-ms-2" />
                            </RadzenRow>
                            <RadzenRow Gap="0.2rem">
                                <RadzenCheckBox Value="@(data.ACTION_EDIT == 1)" ValueChanged="@((bool val) => data.ACTION_EDIT = val ? 1 : 0)" Name="ACTION_EDIT" />
                                <RadzenLabel Text="Редактирование" Component="ACTION_EDIT" class="rz-ms-2" />
                            </RadzenRow>
                        </RadzenStack>
                        <RadzenStack Orientation="Orientation.Vertical" Gap="0.5rem">
                            <RadzenRow Gap="0.2rem">
                                <RadzenCheckBox Value="@(data.ACTION_ADD == 1)" ValueChanged="@((bool val) => data.ACTION_ADD = val ? 1 : 0)" Name="ACTION_ADD" />
                                <RadzenLabel Text="Добавление" Component="ACTION_ADD" class="rz-ms-2" />
                            </RadzenRow>
                            <RadzenRow Gap="0.2rem">
                                <RadzenCheckBox Value="@(data.ACTION_DEL == 1)" ValueChanged="@((bool val) => data.ACTION_DEL = val ? 1 : 0)" Name="ACTION_DEL" />
                                <RadzenLabel Text="Удаление" Component="ACTION_DEL" class="rz-ms-2" />
                            </RadzenRow>
                        </RadzenStack>
                    </RadzenStack>
                </RadzenFieldset>
                <RadzenFieldset Text="Настройки правила" Collapsible="true" Style="width: 100%;">
                        <ChildContent>
                    <RadzenStack Orientation="Orientation.Vertical" AlignItems="AlignItems.Start">
                            <RadzenButton Variant="Variant.Outlined" Shade="Shade.Dark" Icon="construction" Text="Конструктор правила" ButtonStyle="ButtonStyle.Base" Click="@OpenAddRuleDialog" />

            <RadzenTabs  Style="width:100%">
                <Tabs>
                    <RadzenTabsItem Text="Алгоритм правила" >
                            <RadzenTextArea Style="width: 100%; height: 150px; resize:vertical" ReadOnly="true" Disabled="true" Value="@(data.RULE)" />
                    </RadzenTabsItem>
                    <RadzenTabsItem Text="Алгоритм правила JSON"> 
                            <RadzenTextArea Style="width: 100%; height: 150px;  resize:vertical" ReadOnly="true" Disabled="true" Value="@(data.RULE_J)" />
                    </RadzenTabsItem>
                </Tabs>

             </RadzenTabs>

                    </RadzenStack>
                        </ChildContent>
                </RadzenFieldset>
            </RadzenStack>
            <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End" Gap="0.5rem">
                <RadzenButton ButtonType="ButtonType.Submit"  Text="@InParams._butsub_name" Variant="Variant.Flat" />
                <RadzenButton ButtonStyle="ButtonStyle.Light" Text="Отмена" Click="@CancelClick" Variant="Variant.Flat" />
            </RadzenStack>
        </RadzenTemplateForm>
    </RadzenColumn>
</RadzenRow>

@code {

    [Parameter] public FormParams InParams { get; set; }
    [Inject] protected IJSRuntime JSRuntime { get; set; }
    [Inject] protected NavigationManager NavigationManager { get; set; }
    [Inject] protected DialogService DialogService { get; set; }
    [Inject] protected TooltipService TooltipService { get; set; }
    [Inject] protected ContextMenuService ContextMenuService { get; set; }
    [Inject] protected NotificationService NotificationService { get; set; }
    [Inject] protected EWA.Services.SIBService.SecurityService Security { get; set; }
    [Inject] protected EWA.Services.SIBService.UserService UServ { get; set; }
    [Inject] protected EWA.Services.SIBService.RulesService RServ { get; set; }
    [Inject] public EWA.Services.RepService.ISequenceService Seq { get; set; }
    protected EWA.Models.SIB_Models.SIB_RULES data;
    protected string error;
    protected bool errorVisible;

    bool form_res = false;
    private Dictionary<string, object> _formValues = new Dictionary<string, object>();

    private List<DropDownOption> options = new List<DropDownOption>
    {
        new DropDownOption { Value = "DENY", Text = "Запрещено" },
        new DropDownOption { Value = "PERMIT", Text = "Разрешено" }
    };

    public class DropDownOption
    {
        public string Value { get; set; }
        public string Text { get; set; }
    }

    protected override async Task OnInitializedAsync()
    {
        data = new EWA.Models.SIB_Models.SIB_RULES();
        data.ACCESS_TYPE = "DENY";
    }
    private async Task OpenAddRuleDialog()
    {
        var options = new DialogOptions()
            {
                Width = "80vw",
                Height = "auto",
                Draggable = true, 
                Resizable = true
            };
        var parameters = new Dictionary<string, object>
        {
            { "SRule", data.RULE },
            { "FRule", data.RULE_J },
            { "UserAttrs", data.USER_ATTRS },
            { "ObjectAttrs", data.OBJECT_ATTRS }
        };
        var result = await DialogService.OpenAsync<SIBRuleForm>("Добавить новое правило", parameters, options);

        if (result != null)
        {
            var status = result.status;
            var rResult = result.rResult;
            var rResultS = result.rResultS;
            var uattr = result.uattr;
            var oattr = result.oattr;

            if (status == 1)
            {
                data.RULE = rResultS;
                data.RULE_J = rResult;
                data.USER_ATTRS = uattr;
                data.OBJECT_ATTRS = oattr;
            }
        }
        StateHasChanged();
    }
    protected async Task<(bool form_res, string errorMessage)> FormSubmit()
    {
        var formValues = new Dictionary<string, object>(_formValues);
        try
        {
            var nextValue = await Seq.GetNextValueAsync("EWA_ROW_SEQ");
            data.ID = nextValue;
            data.DT_CREATE = DateTime.Now;
            data.DT_CHANGE = DateTime.Now;
            data.USER_CHANGE = @Security.User.CODE;
            data.USER_CREATE = @Security.User.CODE;

            formValues["ID"] = data.ID;
            formValues["CODE"] = data.CODE;
            formValues["RULE"] = data.RULE;
            formValues["RULE_J"] = data.RULE_J;
            formValues["ACCESS_TYPE"] = data.ACCESS_TYPE;
            formValues["DT_CREATE"] = data.DT_CREATE;
            formValues["DT_CHANGE"] = data.DT_CHANGE;
            formValues["USER_CREATE"] = data.USER_CREATE;
            formValues["USER_CHANGE"] = data.USER_CHANGE;
            formValues["IS_ACTIVE"] = data.IS_ACTIVE;
            formValues["DESCRIPTION"] = data.DESCRIPTION;
            formValues["USER_ATTRS"] = data.USER_ATTRS;
            formValues["OBJECT_ATTRS"] = data.OBJECT_ATTRS;
            formValues["ACTION_USE"] = data.ACTION_USE;
            formValues["ACTION_ADD"] = data.ACTION_ADD;
            formValues["ACTION_EDIT"] = data.ACTION_EDIT;
            formValues["ACTION_DEL"] = data.ACTION_DEL;
        }
        catch (Exception ex)
        {
            errorVisible = true;
            error = ex.Message;
            form_res = false;
            return (form_res, error);
        }
        if (1 == 1)
        {
            #pragma warning disable CS4014
            InvokeAsync(async () =>
            {
                await Task.Delay(1000);
                (form_res, error) = await UServ.CreateUpdateRule(data, InParams._sql);
                DialogService.Close();
            });
            #pragma warning restore CS4014
            await BusyDialog();
            if (!form_res)
            {
                string errorTitle = "Ошибка добавления";
                await DialogService.OpenAsync<ErrorGrid>(errorTitle,
                              new Dictionary<string, object> { { "_errorMessage", error } },
                                  new DialogOptions { Draggable = true, Resizable = true }
                );
                return (form_res, error);
            }
        }
        DialogService.Close(new { Values = formValues, Result = form_res, errorMessage = error });
        return (form_res, error);
    }
    async Task BusyDialog()
    {
        await DialogService.OpenAsync("", ds =>
            @<RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.End" JustifyContent="JustifyContent.Stretch" Wrap="FlexWrap.Wrap" Gap="0.5rem">
                <RadzenProgressBarCircular ProgressBarStyle="ProgressBarStyle.Primary" Value="100" ShowValue="false" Mode="ProgressBarMode.Indeterminate" Size="ProgressBarCircularSize.Small" />
            </RadzenStack>
            , new DialogOptions()
            {
                ShowTitle = false,
                Style = "min-height:auto;min-width:auto;width:auto;background-color:transparent",
                CloseDialogOnEsc = false,
                Draggable = true,
                Resizable = true
            });
    }
    protected async Task CancelClick()
    {
        var formValues = new Dictionary<string, object>(_formValues);
        form_res = false;
        DialogService.Close(new { Values = formValues, Result = form_res, errorMessage = error });
    }


}