using ClosedXML.Excel;
using ClosedXML.Report;
using EWA.Models;
using Microsoft.Extensions.Configuration;
using Oracle.ManagedDataAccess.Client;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.Runtime.ConstrainedExecution;
using System.Text;
using System.Text.RegularExpressions;


namespace EWA.Services
{
    public enum DataSourceType
    {
        Query,
        StoredProcedure
    }

    public class DataSourceInfo
    {
        public string TemplateRange { get; set; }
        public DataSourceType SourceType { get; set; }
        public string CommandText { get; set; }
        public List<OracleParameter> Parameters { get; set; } = new List<OracleParameter>();
    }

    public class ExcelTemplateException : Exception
    {
        public ExcelTemplateException(string message) : base(message) { }
        public ExcelTemplateException(string message, Exception innerException) : base(message, innerException) { }
    }

    public interface IExcelService
    {
        Task<MemoryStream> ExportToExcelMultiSource(string templateName, string connectionString, List<DataSourceInfo> dataSources, Dictionary<string, string> variables = null);
        Task<Stream> ExportGridToExcel(
            IEnumerable<REP_Models.ColumnMetadata> metadata,
            string connectionString,
            string baseSql,
            int totalRows,
            Dictionary<string, REP_Models.Rep_Param> parameters = null,
            string filter = null,
            string orderBy = null);
        Task<Stream> ExportGridToCSV(
            IEnumerable<REP_Models.ColumnMetadata> metadata,
            string connectionString,
            string baseSql,
            int totalRows,
            Dictionary<string, REP_Models.Rep_Param> parameters = null,
            string filter = null,
            string orderBy = null);

        Task<Stream> ExportDataToExcel(
            IEnumerable<REP_Models.ColumnMetadata> metadata,
            IEnumerable<IDictionary<string, object>> data);

        Task<Stream> ExportDataToCSV(
            IEnumerable<REP_Models.ColumnMetadata> metadata,
            IEnumerable<IDictionary<string, object>> data);
    }


    public class ExcelService : IExcelService
    {
        private readonly IConfiguration _configuration;
        private readonly string _templatePath;
        private readonly RepService _repService;

        public ExcelService(IConfiguration configuration, IWebHostEnvironment environment, RepService repService)
        {
            _configuration = configuration;
            _templatePath = Path.Combine(environment.ContentRootPath, "Reports");
            _repService = repService;
        }

        // метод для получения списка полей, используемых в шаблоне
        private HashSet<string> GetRequiredFields(IXLNamedRange namedRange)
        {
            // все ячейки именованного диапазона
            var cells = namedRange.Ranges.SelectMany(r => r.CellsUsed());
            var expectedFieldNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            // теги вида {{item.FieldName}} в ячейках
            foreach (var cell in cells)
            {
                if (cell.Value.ToString() is string cellValue)
                {
                    // все вхождения {{item.FieldName}}
                    var matches = Regex.Matches(cellValue, @"\{\{([^}]+)\}\}");
                    foreach (Match match in matches)
                    {
                        if (match.Groups.Count > 1)
                        {
                            string expression = match.Groups[1].Value.Trim();

                            if (expression.StartsWith("item.", StringComparison.OrdinalIgnoreCase))
                            {
                                string fieldName = expression.Substring(5);  // без "item."
                                expectedFieldNames.Add(fieldName);
                            }
                        }
                    }
                }
            }
            return expectedFieldNames;
        }

        public async Task<MemoryStream> ExportToExcelMultiSource(string templateName, string connectionString, List<DataSourceInfo> dataSources, Dictionary<string, string> variables = null)
        {
            try
            {
                //string connectionString = await _repService.GetConnectionStringAsync("FR");
                var templateFile = Path.Combine(_templatePath, templateName);

                if (!File.Exists(templateFile))
                {
                    throw new FileNotFoundException($"Шаблон Excel не найден по пути: {templateFile}");
                }

                // загрузка шаблона для предварительного анализа полей
                using var templateWorkbook = new XLWorkbook(templateFile);

                // проверка наличия всех необходимых именованных диапазонов
                // и получение списка полей в шаблоне
                var requiredFieldsByRange = new Dictionary<string, HashSet<string>>();

                foreach (var source in dataSources)
                {
                    if (templateWorkbook.NamedRange(source.TemplateRange) == null)
                    {
                        Console.WriteLine($"Шаблон не содержит именованного диапазона '{source.TemplateRange}'.");
                        throw new InvalidOperationException($"Шаблон не содержит именованного диапазона '{source.TemplateRange}'.");
                    }

                    // список полей, используемых в шаблоне для этого диапазона
                    var requiredFields = GetRequiredFields(templateWorkbook.NamedRange(source.TemplateRange));
                    requiredFieldsByRange[source.TemplateRange] = requiredFields;
                    //Console.WriteLine($"Поля в шаблоне из диапазона {source.TemplateRange}: {string.Join(", ", requiredFields)}");
                }

                // проверка соответствия полей шаблона до выполнения запросов
                using (var connection = new OracleConnection(connectionString))
                {
                    await connection.OpenAsync();

                    foreach (var source in dataSources)
                    {
                        using var cmd = connection.CreateCommand();
                        cmd.CommandText = source.CommandText;
                        cmd.CommandType = source.SourceType == DataSourceType.StoredProcedure
                            ? CommandType.StoredProcedure
                            : CommandType.Text;

                        // параметры, если они есть
                        if (source.Parameters?.Any() == true)
                        {
                            foreach (var param in source.Parameters)
                            {
                                if (param.OracleDbType == OracleDbType.Date && param.Value != null && param.Value != DBNull.Value)
                                {
                                    if (TryParseDate(param.Value.ToString(), out DateTime result, dateformats))
                                    {
                                        param.Value = result;
                                    }
                                }   
                                cmd.Parameters.Add(param);
                            }
                        }

                        using var adapter = new OracleDataAdapter(cmd);
                        var dataTable = new DataTable();

                        // схема данных без выполнения запроса
                        OracleCommandBuilder builder = new OracleCommandBuilder(adapter);
                        adapter.FillSchema(dataTable, SchemaType.Source);

                        // проверка соответствия полей
                        var requiredFields = requiredFieldsByRange[source.TemplateRange];
                        var dataTableColumns = new HashSet<string>(
                            dataTable.Columns.Cast<DataColumn>().Select(c => c.ColumnName),
                            StringComparer.OrdinalIgnoreCase
                        );

                        //Console.WriteLine($"Поля в dataset для диапазона {source.TemplateRange}: {string.Join(", ", dataTableColumns)}");
                        var missingFields = requiredFields.Except(dataTableColumns, StringComparer.OrdinalIgnoreCase).ToList();
                        if (missingFields.Any())
                        {
                            string errorMessage = $"Для поля(ей):  {string.Join(", ", missingFields)}  в диапазоне {source.TemplateRange} шаблона нет соответствия в datasource";
                            Console.WriteLine(errorMessage);
                            //throw new InvalidOperationException(errorMessage);
                            throw new ExcelTemplateException(errorMessage);
                        }
                    }
                }

                // выполнение запросов и заполнение данных
                var results = new Dictionary<string, DataTable>();

                using (var connection = new OracleConnection(connectionString))
                {
                    await connection.OpenAsync();

                    foreach (var source in dataSources)
                    {
                        using var cmd = connection.CreateCommand();
                        cmd.CommandText = source.CommandText;
                        cmd.CommandType = source.SourceType == DataSourceType.StoredProcedure
                            ? CommandType.StoredProcedure
                            : CommandType.Text;

                        // параметры, если они есть
                        if (source.Parameters?.Any() == true)
                        {
                            foreach (var param in source.Parameters)
                            {
                                // новый экземпляр параметра с теми же значениями
                                var newParam = new OracleParameter
                                {
                                    ParameterName = param.ParameterName,
                                    OracleDbType = param.OracleDbType,
                                    Value = param.Value,
                                    Direction = param.Direction,
                                    Size = param.Size,
                                    Precision = param.Precision,
                                    Scale = param.Scale
                                };
                                if (param.OracleDbType == OracleDbType.Date && param.Value != null && param.Value != DBNull.Value)
                                {
                                    if (TryParseDate(param.Value.ToString(), out DateTime result, dateformats))
                                    {
                                        newParam.Value = result;
                                    }
                                }
                                cmd.Parameters.Add(newParam);
                            }
                        }

                        using var adapter = new OracleDataAdapter(cmd);
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        if (dataTable.Rows.Count == 0 && dataSources.Count == 1)
                        {
                            string errorMessage = "Нет данных для построения отчета.";
                            Console.WriteLine(errorMessage);
                            throw new ExcelTemplateException(errorMessage);
                        }
                        results.Add(source.TemplateRange, dataTable);
                    }
                }

                // новый экземпляр workbook для генерации отчета
                using var workbook = new XLWorkbook(templateFile);

                // создание XLTemplate
                using var template = new XLTemplate(workbook);

                // добавление диапазонов в шаблон 
                foreach (var result in results)
                {
                    template.AddVariable(result.Key, result.Value);
                }

                // дополнительные переменные, если они есть
                if (variables != null)
                {
                    foreach (var variable in variables)
                    {
                        template.AddVariable(variable.Key, variable.Value);
                    }
                }

                // генерация отчета Excel
                try
                {
                    template.Generate();
                }
                catch (Exception genEx)
                {
                    Console.WriteLine($"Error during template generation: {genEx.Message}");
                    Console.WriteLine($"Stack trace: {genEx.StackTrace}");
                    throw;
                }

                // сохранение в поток
                var stream = new MemoryStream();
                template.SaveAs(stream);
                stream.Position = 0;
                return stream;
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Ошибка при экспорте в Excel: {ex.Message}");
                throw;
            }
        }

        // OracleDataAdapter, datatable, пагинацмя по max кол-ву строк на листе
        public async Task<Stream> ExportGridToExcel(
            IEnumerable<REP_Models.ColumnMetadata> metadata,
            string connectionString,
            string baseSql,
            int totalRows,
            Dictionary<string, REP_Models.Rep_Param> parameters = null,
            string filter = null,
            string orderBy = null)
        {
            using var workbook = new XLWorkbook();
            const int maxRowsPerSheet = 1048575; // max - 1  для заголовка
            var visibleColumns = metadata.Where(x => x.VIEWVISIBLE == 1).ToList();

            // список видимых колонок для SQL
            var columnsList = string.Join(", ", visibleColumns.Select(c => $"r.{c.CODE}"));
            var columnsListWithoutAlias = string.Join(", ", visibleColumns.Select(c => c.CODE));

            using (var connection = new OracleConnection(connectionString))
            {
                await connection.OpenAsync();
                var totalSheets = (int)Math.Ceiling((double)totalRows / maxRowsPerSheet);

                for (int i = 0; i < totalSheets; i++)
                {
                    var skip = i * maxRowsPerSheet;
                    var take = Math.Min(maxRowsPerSheet, totalRows - skip);

                    var paginatedSql = $@"
                        SELECT {columnsListWithoutAlias} FROM (
                            SELECT a.{columnsListWithoutAlias}, ROWNUM rnum FROM (
                                SELECT {columnsList} FROM ({baseSql}) r
                                {(string.IsNullOrEmpty(filter) ? "" : "WHERE " + filter)}
                                {(string.IsNullOrEmpty(orderBy) ? "" : "ORDER BY " + orderBy)}
                            ) a
                            WHERE ROWNUM <= :take + :skip
                        ) t
                        WHERE t.rnum > :skip";

                    using var command = new OracleCommand(paginatedSql, connection);
                    command.BindByName = true;

                    command.Parameters.Add(new OracleParameter("skip", OracleDbType.Int32) { Value = skip });
                    command.Parameters.Add(new OracleParameter("take", OracleDbType.Int32) { Value = take });
                    AddParameters(command, parameters);

                    var worksheet = workbook.Worksheets.Add($"Sheet{i + 1}");

                    // заголовки
                    int col = 1;
                    foreach (var column in visibleColumns)
                    {
                        worksheet.Cell(1, col).Value = column.NAME;
                        worksheet.Cell(1, col).Style.Font.Bold = true;
                        col++;
                    }

                    using var adapter = new OracleDataAdapter(command);
                    var dataTable = new DataTable();
                    await Task.Run(() => adapter.Fill(dataTable));

                    if (dataTable.Rows.Count > 0)
                    {
                        worksheet.Cell(2, 1).InsertData(dataTable.AsEnumerable());
                    }
                    // автоподбор ширины колонок
                    worksheet.Columns().AdjustToContents();
                }
            }

            var stream = new MemoryStream();
            workbook.SaveAs(stream);
            stream.Position = 0;
            return stream;
        }
  
        private readonly string[] dateformats = { "dd.MM.yyyy", "dd/MM/yyyy", "MM/dd/yyyy", "yyyy-MM-dd", "MM.dd.yy" };

        private static bool TryParseDate(string input, out DateTime dateResult, string[] dateformats)
        {
            return DateTime.TryParseExact(
                input,
                dateformats,
                CultureInfo.InvariantCulture,
                DateTimeStyles.None,
                out dateResult
            );
        }
        private OracleDbType GetColumnType(string typeName)
        {
            return typeName.ToUpper() switch
            {
                "NUMBER" => OracleDbType.Decimal, // or typeof(decimal)
                "FLOAT" => OracleDbType.Decimal,
                "INT" => OracleDbType.Long,
                "VARCHAR2" => OracleDbType.Varchar2,
                "CHAR" => OracleDbType.Char,
                "CLOB" => OracleDbType.Clob,
                "BOOL" => OracleDbType.Boolean,
                "DATE" => OracleDbType.Date,
                "TIMESTAMP(6)" => OracleDbType.TimeStamp,
                _ => OracleDbType.Varchar2,
            };

        }

        private void AddParameters(OracleCommand command, Dictionary<string, REP_Models.Rep_Param> parameters)
        {
            if (parameters == null) return;

            foreach (var param in parameters)
            {
                if (command.Parameters.Contains(param.Key)) continue;

                var parameter = new OracleParameter
                {
                    ParameterName = param.Key,
                    OracleDbType = GetColumnType(param.Value.DATATYPE),
                    Value = param.Value.Val ?? DBNull.Value,
                    Direction = param.Value.Direct,
                };
                if (param.Value.DATATYPE == "DATE")
                {
                    if (TryParseDate(parameter.Value.ToString(), out DateTime result, dateformats))
                    {
                        parameter.Value = result;
                    }

                }
                /*
                                if (param.Value.DATAPRECISION.HasValue)
                                {
                                    parameter.Precision = (byte)param.Value.DATAPRECISION.Value;
                                }

                                if (param.Value.DATALENGTH.HasValue)
                                {
                                    parameter.Scale = (byte)param.Value.DATALENGTH.Value;
                                }
                */
                command.Parameters.Add(parameter);
            }
        }

        public async Task<Stream> ExportGridToCSV(
            IEnumerable<REP_Models.ColumnMetadata> metadata,
            string connectionString,
            string baseSql,
            int totalRows,
            Dictionary<string, REP_Models.Rep_Param> parameters = null,
            string filter = null,
            string orderBy = null)
        {
            const int maxRowsPerSheet = 1048575;  // max - 1 для заголовков
            var visibleColumns = metadata.Where(x => x.VIEWVISIBLE == 1).ToList();

            // список видимых полей для sql-запроса
            var columnsList = string.Join(", ", visibleColumns.Select(c => $"r.{c.CODE}"));
            var columnsListWithoutAlias = string.Join(", ", visibleColumns.Select(c => c.CODE));

            var totalSheets = (int)Math.Ceiling((double)totalRows / maxRowsPerSheet);

            MemoryStream resultStream;
            if (totalSheets == 1)
            {
                resultStream = new MemoryStream();
                //using var writer = new StreamWriter(resultStream, leaveOpen: true);
                using var writer = new StreamWriter(resultStream, new UTF8Encoding(true), leaveOpen: true);

                using var connection = new OracleConnection(connectionString);
                await connection.OpenAsync();

                var paginatedSql = $@"
                    SELECT {columnsList} FROM ({baseSql}) r
                    {(string.IsNullOrEmpty(filter) ? "" : "WHERE " + filter)}
                    {(string.IsNullOrEmpty(orderBy) ? "" : "ORDER BY " + orderBy)}";

                using var command = new OracleCommand(paginatedSql, connection);
                command.BindByName = true;
                AddParameters(command, parameters);

                using var adapter = new OracleDataAdapter(command);
                var dataTable = new DataTable();
                await Task.Run(() => adapter.Fill(dataTable));

                // заголовки
                writer.WriteLine(string.Join(";", visibleColumns.Select(c => c.NAME)));
                // данные
                foreach (DataRow row in dataTable.Rows)
                {
                    var values = new List<string>();
                    foreach (var column in visibleColumns)
                    {
                        var value = row[column.CODE];
                        values.Add(FormatValueForCSV(value, column));
                    }
                    writer.WriteLine(string.Join(";", values));
                }
                writer.Flush();
            }
            else
            {
                resultStream = new MemoryStream();
                using var archive = new System.IO.Compression.ZipArchive(resultStream, System.IO.Compression.ZipArchiveMode.Create, true);
                using var connection = new OracleConnection(connectionString);
                await connection.OpenAsync();

                for (int i = 0; i < totalSheets; i++)
                {
                    var skip = i * maxRowsPerSheet;
                    var take = Math.Min(maxRowsPerSheet, totalRows - skip);

                    var paginatedSql = $@"
                        SELECT {columnsListWithoutAlias} FROM (
                            SELECT a.{columnsListWithoutAlias}, ROWNUM rnum FROM (
                                SELECT {columnsList} FROM ({baseSql}) r
                                {(string.IsNullOrEmpty(filter) ? "" : "WHERE " + filter)}
                                {(string.IsNullOrEmpty(orderBy) ? "" : "ORDER BY " + orderBy)}
                            ) a
                            WHERE ROWNUM <= :take + :skip
                        ) t
                        WHERE t.rnum > :skip";

                    using var command = new OracleCommand(paginatedSql, connection);
                    command.BindByName = true;
                    command.Parameters.Add(new OracleParameter("skip", OracleDbType.Int32) { Value = skip });
                    command.Parameters.Add(new OracleParameter("take", OracleDbType.Int32) { Value = take });
                    AddParameters(command, parameters);

                    using var adapter = new OracleDataAdapter(command);
                    var dataTable = new DataTable();
                    await Task.Run(() => adapter.Fill(dataTable));

                    var entry = archive.CreateEntry($"Sheet{i + 1}.csv");
                    using var entryStream = entry.Open();
                    //using var writer = new StreamWriter(entryStream);
                    using var writer = new StreamWriter(entryStream, new UTF8Encoding(true));

                    // заголовки и данные
                    writer.WriteLine(string.Join(";", visibleColumns.Select(c => c.NAME)));
                    foreach (DataRow row in dataTable.Rows)
                    {
                        var values = new List<string>();
                        foreach (var column in visibleColumns)
                        {
                            var value = row[column.CODE];
                            values.Add(FormatValueForCSV(value, column));
                        }
                        writer.WriteLine(string.Join(";", values));
                    }
                }
            }

            resultStream.Position = 0;
            return resultStream;
        }

        public async Task<Stream> ExportDataToExcel(
            IEnumerable<REP_Models.ColumnMetadata> metadata,
            IEnumerable<IDictionary<string, object>> data)
        {
            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("Sheet1");
            var visibleColumns = metadata.Where(x => x.VIEWVISIBLE == 1).ToList();

            // заголовки
            for (int col = 0; col < visibleColumns.Count; col++)
            {
                worksheet.Cell(1, col + 1).SetValue(visibleColumns[col].NAME);
            }

            // данные
            int row = 2;
            foreach (var dataRow in data)
            {
                int col = 1;
                foreach (var column in visibleColumns)
                {
                    var value = dataRow[column.CODE];
                    if (value != DBNull.Value && value != null)
                    {
                        switch (column.DATATYPE)
                        {
                            case "DATE":
                                if (value is DateTime dateValue)
                                {
                                    worksheet.Cell(row, col).SetValue(dateValue);
                                    string format = column.DOMAINCODE == "ExactDate" ? "dd.mm.yyyy" : "dd.mm.yyyy hh:mm:ss";
                                    worksheet.Cell(row, col).Style.NumberFormat.Format = format;
                                }
                                break;
                            case "NUMBER":
                                if (column.DATAPRECISION > 0)
                                    worksheet.Cell(row, col).SetValue(Convert.ToDouble(value));
                                else
                                    worksheet.Cell(row, col).SetValue(Convert.ToInt64(value));
                                break;
                            default:
                                worksheet.Cell(row, col).SetValue(value.ToString());
                                break;
                        }
                    }
                    col++;
                }
                row++;
            }
            // автоподбор ширины колонок
            worksheet.Columns().AdjustToContents();

            var memoryStream = new MemoryStream();
            await Task.Run(() => workbook.SaveAs(memoryStream));
            memoryStream.Position = 0;
            return memoryStream;
        }

        public async Task<Stream> ExportDataToCSV(
            IEnumerable<REP_Models.ColumnMetadata> metadata,
            IEnumerable<IDictionary<string, object>> data)
        {
            var resultStream = new MemoryStream();
            using var writer = new StreamWriter(resultStream, new UTF8Encoding(true), leaveOpen: true);
            var visibleColumns = metadata.Where(x => x.VIEWVISIBLE == 1).ToList();

            // заголовки
            await writer.WriteLineAsync(string.Join(";", visibleColumns.Select(c => c.NAME)));

            // данные
            foreach (var dataRow in data)
            {
                var values = new List<string>();
                foreach (var column in visibleColumns)
                {
                    var value = dataRow[column.CODE];
                    values.Add(FormatValueForCSV(value, column));
                }
                await writer.WriteLineAsync(string.Join(";", values));
            }

            await writer.FlushAsync();
            resultStream.Position = 0;
            return resultStream;
        }

        private string FormatValueForCSV(object value, REP_Models.ColumnMetadata column)
        {
            if (value == DBNull.Value || value == null)
                return string.Empty;

            switch (column.DATATYPE)
            {
                case "DATE":
                    if (value is DateTime date)
                    {
                        string format = column.DOMAINCODE == "ExactDate" ? "dd.MM.yyyy" : "dd.MM.yyyy HH:mm:ss";
                        return date.ToString(format);
                    }
                    return string.Empty;

                case "NUMBER":
                    if (column.DATAPRECISION > 0)
                    {
                        return Convert.ToDouble(value).ToString(CultureInfo.InvariantCulture);
                    }
                    else
                    {
                        if (value is decimal decValue)
                            return Convert.ToInt64(decValue).ToString();
                        return Convert.ToInt64(value).ToString();
                    }

                default:
                    string stringValue = value.ToString();
                    if (stringValue.Contains("\"") || stringValue.Contains(";"))
                    {
                        // экранирование внутренних кавычек и обрамление всей строки в кавычки
                        return $"\"{stringValue.Replace("\"", "\"\"")}\"";
                    }
                    return stringValue;
            }
        }

    }
}
