﻿@using Ra<PERSON>zen

<RadzenPassword @bind-Value="@Value" style="@($"{Style}")" Type="@InputType" Name="@Name" />
<RadzenIcon Icon="@EyeIcon"
            Style="position: absolute; top: 8px; right: 10px; cursor: pointer;" @onclick="@TogglePasswordVisibility" />

@code {
    [Parameter] public string Value { get; set; }
    [Parameter] public EventCallback<string> ValueChanged { get; set; }

    [Parameter] public string Name { get; set; }
    [Parameter] public string Placeholder { get; set; }
    [Parameter] public string Style { get; set; }

    [Parameter(CaptureUnmatchedValues = true)]
    public Dictionary<string, object> AdditionalAttributes { get; set; }

    private bool isPasswordVisible = false;
    private string InputType => isPasswordVisible ? "text" : "password";
    private string EyeIcon => isPasswordVisible ? "visibility_off" : "visibility";

    private void TogglePasswordVisibility()
    {
        isPasswordVisible = !isPasswordVisible;
    }
}
