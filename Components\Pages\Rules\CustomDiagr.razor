﻿@using System.Text.Json
@inject DialogService DialogService

<svg width="1000" height="600" style="border:1px solid black;"
     @onmousemove="OnMouseMove" @onmouseup="OnMouseUp">

    @foreach (var node in Nodes)
    {
        <circle cx="@node.X" cy="@node.Y" r="30" fill="lightblue"
                @onmousedown="() => OnMouseDown(node)" />
        <text>@node.Label</text>
    }

    @foreach (var link in Links)
    {
        <line x1="@link.From.X" y1="@link.From.Y" x2="@link.To.X" y2="@link.To.Y"
              stroke="black" stroke-width="2" marker-end="url(#arrow)" />
        <text>@link.Condition</text>
    }
</svg>

<RadzenButton Text="Добавить узел" Click="AddNode" />
<RadzenButton Text="Сохранить" Click="SaveDiagram" />

@code {
    List<Node> Nodes = new();
    List<Link> Links = new();
    Node? SelectedNode = null;
    bool IsDragging = false;

    void AddNode()
    {
        var newNode = new Node { X = 100, Y = 100, Label = "New Node" };
        Nodes.Add(newNode);
    }

    void OnMouseDown(Node node)
    {
        SelectedNode = node;
        IsDragging = true;
    }

    void OnMouseMove(MouseEventArgs e)
    {
        if (IsDragging && SelectedNode != null)
        {
            SelectedNode.X = (int)e.ClientX;
            SelectedNode.Y = (int)e.ClientY;
        }
    }

    void OnMouseUp()
    {
        IsDragging = false;
        SelectedNode = null;
    }

    void SaveDiagram()
    {
        var json = JsonSerializer.Serialize(new { Nodes, Links });
        Console.WriteLine(json);
    }

    class Node
    {
        public int X { get; set; }
        public int Y { get; set; }
        public string Label { get; set; }
    }

    class Link
    {
        public Node From { get; set; }
        public Node To { get; set; }
        public string Condition { get; set; } = "";
        public int MidX => (From.X + To.X) / 2;
        public int MidY => (From.Y + To.Y) / 2;
    }
}
