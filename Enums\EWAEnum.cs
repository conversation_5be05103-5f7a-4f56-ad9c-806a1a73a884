﻿using System.ComponentModel;

namespace EWA.Enums
{
    public enum TabPageKind
    {
        /// <summary>
        /// Режим модального окна
        /// </summary>
        Modal,
        /// <summary>
        /// Режим CntPage
        /// </summary>
        CntPage,
        /// <summary>
        /// Режим автооткрытия
        /// </summary>
        AutoMode,
        /// <summary>
        /// Режим стандартный
        /// </summary>
        Standart
    }
    public enum LoginStatus
    {
        [Description("Пользователь {0} не зарегистрирован в приложении")]
        ADNotPermit,

        [Description("Неверный логин или пароль")]
        InvalidLP,

        [Description("Совершено {0} неуспешных входов. Пользователь временно заблокирован. Попробуйте через {1} минут.")]
        IsTmpBlock,

        [Description("Пользователь {0} заблокирован. Обратитесь к администратору")]
        IsBlock,

        [Description("Пользователь заблокирован. Обратитесь к администратору")]
        Blck
    }
    public enum ExportType
    {
        XLSX,
        CSV,
        None_Type
    }

    // все данные или данные текущей страницы
    public enum ExportScope
    {
        All,
        CurrentPage,
        None_Type
    }
    public enum ExportGrid
    { 
        XLS_All,
        XLS_Cur,
        CSV_All,
        CSV_Cur,
        NODE_Choise
    }

    public enum BlockUser
    { 
        Manual,
        Temp,
        Auto
    }
    public enum UserRole1
    {
        Administrator,
        Editor,
        Guest
    }
    enum SeriveMenuAction
    {
        Encrpt,
        WFLServ
    }
    enum ProfileMenuActions
    {
        Logout,
        ChangePassword,
        UserCard
    }
}
