﻿@rendermode InteractiveServer
@inject NavigationManager Navigation
@inject RepService _repService
@inject EWA.Services.RepService.GET_SPR _getspr
@inject DialogService DialogService
@inject FormServices _formService
@inject IJSRuntime JSRuntime
@inject EWA.Services.AMLService _amlserv
@inject TooltipService tooltipService
@inject NotificationService NotificationService
@using System.Collections.Generic
@using System.Data
@using Microsoft.Extensions.Configuration
@using System.Text.Json
@using Radzen.Blazor
@using Oracle.ManagedDataAccess.Client
@using Ra<PERSON>zen
@using EWA.Services
@using EWA.Models
@using EWA.Components.Pages
@using System.Globalization
@using static EWA.Models.REP_Models



<RadzenStack>
    @if (InParams._action == "DEL")
    {
        <RadzenText>Вы действительно хотите удалить следующие данные?</RadzenText>
    }
    <RadzenCard Gap="0.5rem" @onkeypress= "SearchInputKeyPressed" >

        <RadzenStack  Orientation="Orientation.Horizontal" AlignItems="AlignItems.End" JustifyContent="JustifyContent.Stretch" Wrap="FlexWrap.Wrap" Gap="0.5rem">
            @if (InParams._action == "ADD" || InParams._action == "EDIT")
            {
                @if (InParams._cfg_data != null && InParams._cfg_data.Any(c => c.CODE == "USE_FORM" && c.VALUE == "RuleForm"))
                {
                    <RadzenButton Variant="Variant.Outlined" Shade="Shade.Dark" Icon="construction" Text="Конструктор показателя" ButtonStyle="ButtonStyle.Base" Click="@OpenAddRuleDialog" />
                }
                @if (InParams._cfg_data != null && InParams._cfg_data.Any(c => c.CODE == "USE_FORM" && c.VALUE == "WFLForm"))
                {
                    <RadzenButton Variant="Variant.Outlined" Shade="Shade.Dark" Icon="construction" Text="Конструктор объекта" ButtonStyle="ButtonStyle.Base" Click="@OpenAddWRFLData" />
                }
                @if (InParams._cfg_data != null && InParams._cfg_data.Any(c => c.CODE == "USE_FORM" && c.VALUE == "WFLParam"))
                {
                    <RadzenButton Variant="Variant.Outlined" Shade="Shade.Dark" Icon="construction" Text="Параметры воркфлоу" ButtonStyle="ButtonStyle.Base" Click="@OpenAddWRFLParam" />
                }
                @*
                после  всхех изменений доработать парсер запросов для метода
                @if (_cfg_data != null && _cfg_data.Any(c => c.CODE == "USE_FORM" && c.VALUE == "ParseSQL"))
                {
                    <RadzenButton Variant="Variant.Outlined" Shade="Shade.Dark" Icon="developer_mode" Text="Разбор SQL" ButtonStyle="ButtonStyle.Base" Click="@ParseSQL" />
                }
                *@
                @if (InParams._cfg_data != null && InParams._cfg_data.Any(c => c.CODE == "USE_FORM" && c.VALUE == "MethodForm"))
                {
                    <RadzenButton Disabled="@GetCodeMethodCol(InParams._metadata.OfType<REP_Models.ColumnMetadata>()
                                                                            .FirstOrDefault(b => b.IS_SYSTEM == 1 && b.TYPE_SYSTEM == "MTD_B_ID")?.CODE)"
                Variant="Variant.Outlined" Shade="Shade.Dark" Icon="construction" Text="Конструктор метода" ButtonStyle="ButtonStyle.Base" Click="@OpenAddMethodDialog" />
                }            
                @if (InParams._cfg_data != null && InParams._cfg_data.Any(c => c.CODE == "USE_FORM" && c.VALUE == "DiagramForm"))
                {
                    <RadzenButton Variant="Variant.Outlined" Shade="Shade.Dark" Icon="construction" Text="Конструктор зависимостей" ButtonStyle="ButtonStyle.Base" Click="@OpenDiagramDialog" />
                }
            }
            @foreach (var meta in InParams._metadata.Where(x => IsWhere(x)))
                    {
            string DomCode = meta?.DOMAINCODE?.ToUpperInvariant();
            string ColName = (DomCode == "BOOL") ? "" : meta?.NAME;

                <RadzenFormField Text="@ColName" Variant="Variant.Flat"
                                 Style="width: 100%;" Visible=@IsHide(@meta)>
               
                   <ChildContent>
                            @if (DomCode == "BOOL")
                            {

                            <RadzenCheckBox TriState="true" TValue="bool?"
                                            Style="max-width: 5%;"
                                            @ref="checkBoxList[meta.CODE]"
                                            Value="@_formService.GetValueBool(meta.CODE, _formValues)"
                                            ValueChanged="@((value) => {
                                                  _formService.SetValueBool(meta.CODE, value, _formValues);
                                                  ShowRadzenCheckBoxTooltip(meta.CODE,_formService.GetValueBool(meta.CODE, _formValues));

                                                })"
                                            Disabled=@IsDisable(@meta)
                                            MouseEnter="@(args => ShowRadzenCheckBoxTooltip(meta.CODE,_formService.GetValueBool(meta.CODE, _formValues)) )" />
                            <RadzenLabel Text=@meta.NAME />


                            }
                            else
                            @if (InParams._SprSelShortDict.ContainsKey(meta.CODE))
                        {
                                <RadzenDropDown Disabled=@IsDisable(@meta)
                                                @bind-Value="@_SprSelShortDict1[@meta.CODE].ValPrm"
                                                TextProperty="Name"
                                                Change="@(arg => OnChange(arg, meta.CODE))"
                                            ValueProperty="@(meta.DOMAINCODE?.ToUpper() == "CODE" ? "Code" : "ID")" 
                                            Style="width:100%;"
                                            AllowFiltering="true"
                                            FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                                            Data="@InParams._SprShortDict[@InParams._SprSelShortDict[@meta.CODE].KeySprShortDict]"
                                            Name="@meta.CODE"
                                            AllowClear="true"
                                            />
                        }
                        else
                        @if (InParams._SprSelLongDict.ContainsKey(meta.CODE))
                        {
                                <RadzenDropDownDataGrid Disabled=@IsDisable(@meta)
                                                    @ref=gridDict[meta.CODE] Data="@dataDict[meta.CODE]" ColumnWidth="200px" 
                                                    LoadData="@(arg => LoadData(arg, meta.CODE, ""))"
                                                    @bind-Value=@_SprSelLongDict1[meta.CODE].ValPrm
                                                    Count=@SprLongCountDict[meta.CODE]
                                                    TValue="IDictionary<string, object>"
                                                    OpenOnFocus=gridFocus[meta.CODE]
                                                    Change="@(arg => OnChange(arg, meta.CODE))"
                                                    PageSize="10"
                                                    ShowPagingSummary="true"
                                                    AllowFiltering="true"
                                                    AllowClear="true"
                                                    AllowSorting="true"
                                                    TextProperty="@(_formService.GetColumnPropertyExpression("LastName", typeof(string)))">
                                <HeaderTemplate>
                                    <RadzenButton Icon="filter_list" ButtonStyle="ButtonStyle.Light" Size="ButtonSize.Small" @onclick:stopPropagation Click="@(args => OpenFilterDialog(meta.CODE))"></RadzenButton>
                                    <RadzenTextBox @bind-Value="@SprLongFiltrDict[meta.CODE]" />
                                    <RadzenButton Icon="find_in_page" ButtonStyle="ButtonStyle.Light" Size="ButtonSize.Small" @onclick:stopPropagation Click="@(args => Find(meta.CODE))"></RadzenButton>
                                </HeaderTemplate>
                                <ValueTemplate>
                                    @if (context != null)
                                        @if (context.Count != 0)
                                        {
                                            {
                                                    @string.Join(", ", InParams._SprLongDict[InParams._SprSelLongDict[meta.CODE].KeySprLongDict].Column.Where(c => c.DATATYPE == "VARCHAR2").Take(gridDict[meta.CODE].MaxSelectedLabels).Where(d => d.CODE != "dd" && @context.ContainsKey("dd") == false).Select(c => context[c.CODE]))

                                            }
                                        }

                                </ValueTemplate>
                                <Columns>
                                        @foreach (var column in InParams._SprLongDict[InParams._SprSelLongDict[meta.CODE].KeySprLongDict].Column)
                                    {
                                        <RadzenDropDownDataGridColumn @key=@column.CODE Title="@column.NAME" Type="@_formService.GetColumnType(column.DATATYPE)"
                                                                      Property="@column.CODE" >
                                            <Template>
                                                @context[@column.CODE]
                                            </Template>

                                        </RadzenDropDownDataGridColumn>
                                    }
                                </Columns>
                            </RadzenDropDownDataGrid>

                        }
                        else if (meta.DATATYPE == "VARCHAR2" )
                        {
                                <RadzenTextBox Value="@_formService.GetValue(meta.CODE, _formValues)" TValue="string"
                                               Name="@meta.CODE"
                                               MaxLength="@meta.DATALENGTH"                                               
                                               @oninput="@(args =>
                                                  {
                                                      if (InParams._metadata.Count(x => x.Parent == meta.CODE) > 0)
                                                          _formService.SetValue(meta.CODE, args.Value.ToString(), _formValues, InParams._metadata);
                                                      else 
                                                         _formService.SetValue(meta.CODE, args.Value.ToString(), _formValues);
                                                  })"
                                               
                                               Disabled="@IsDisable(meta)" />
                            }
                          

            else if (meta.DATATYPE == "NUMBER" && InParams._SprSelLongDict.ContainsKey(meta.CODE) == false )
                        {
                            <RadzenNumeric TValue="decimal?" ShowUpDown="false" Culture="@culture"                                          
                                           Value="GetValueNum(meta.CODE, _formValues)"
                                           @oninput="@(args =>SetValueNum(meta.CODE, args.Value, meta))"
                                               Name="@meta.CODE"
                                               Disabled="@IsDisable(meta)" />

                                
                            }
             
            else if (meta.DATATYPE == "DATE")
                        {
                                <RadzenDatePicker Disabled=@IsDisable(@meta)
                                                  DateFormat="dd.MM.yyyy"
                                                  TValue="DateTime?"
                                              Value=@_formService.GetValueDT(meta.CODE, _formValues)
                                              @oninput="@(args =>_formService.SetValueDT(meta.CODE, args.Value, _formValues, InParams._metadata))"
                                              ValueChanged="@((valueDT) =>
_formService.SetValueDT(meta.CODE, valueDT, _formValues, InParams._metadata))"
                                              Name="@meta.CODE"
                                              />

                             
                        }
                        else if (meta.DATATYPE == "CLOB")
                        {

                                <RadzenTextArea Disabled=@IsDisable(@meta)
                                 Value="@_formService.GetValue(meta.CODE, _formValues)" Cols="21"
                                 ValueChanged="@((value) => _formService.SetValue(meta.CODE, value, _formValues))" />
                        }
                        <!-- добавить для разных типов-->
                    </ChildContent>
                    <Helper>
                        @if (meta.ISMANDATORY == 1 && (InParams._action != "DEL" || InParams._action != "VIEW") && _validator.ContainsKey(meta.CODE))
                        {
                            <RadzenText TextStyle="TextStyle.Caption" Style=@($"color:var({@_validator[meta.CODE].color})")>@_validator[meta.CODE].message</RadzenText>
                        }
                        @if (_validatorFormat.ContainsKey(meta.CODE))
                        {
                                <RadzenText TextStyle="TextStyle.Caption" Style=@($"color:var({@_validatorFormat[meta.CODE].color})")>@_validatorFormat[meta.CODE].message</RadzenText>
                        }
                    </Helper>
                </RadzenFormField>
               
            }
        </RadzenStack>
    </RadzenCard>

    <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End">
        <RadzenStack Orientation="Orientation.Horizontal">           
            @if (!InParams._ISbutsub)
            {
                <RadzenButton Text="@InParams._butsub_name" ButtonStyle="ButtonStyle.Light"  Click="@CloseAsync" />
            }
            else
            {
                <RadzenButton Text="@InParams._butsub_name" Style="width: auto;" Click="@SaveAsync" @ref="ref_SaveAsync" />
                <RadzenButton Text="Отмена" ButtonStyle="ButtonStyle.Light" Click="@CloseAsync"  />
            }

        </RadzenStack>
    </RadzenStack>
</RadzenStack>

@code {
    private RadzenButton ref_SaveAsync;
    protected async Task SearchInputKeyPressed(KeyboardEventArgs args)
    {
        if (args.Key == "Enter" )
        {
            await ref_SaveAsync.Element.FocusAsync();
            await SaveAsync();
        }

    }

    protected async Task SearchInputKeyPressed1(KeyboardEventArgs args)
    {
        if (args.Key == "Enter")
        {
            await ref_SaveAsync.Element.FocusAsync();
            await SaveAsync();
        }

    }

    [Inject]
    protected EWA.Services.SIBService.SecurityService Security { get; set; }
    [Parameter] public FormParams InParams { get; set; }


    private CultureInfo culture = Thread.CurrentThread.CurrentCulture;
    private string _NumberDecimalSeparator = Thread.CurrentThread.CurrentCulture.NumberFormat.NumberDecimalSeparator;
    private Dictionary<string, string> _metaALG = new();
    private Dictionary<string, object> _metaAlGData = new();

    private Dictionary<string, EWA.Models.REP_Models.Rep_SprShortSelDict> _SprSelShortDict1 = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprLongSelDict> _SprSelLongDict1 = new();
    private Dictionary<string, RadzenDropDownDataGrid<IDictionary<string, object>>> gridDict = new();
    private Dictionary<string, IEnumerable<IDictionary<string, object>>> dataDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_Validator> _validator = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_Validator> _validatorFormat = new();
    private Dictionary<string, object> _formValues = new Dictionary<string, object>();
    private Dictionary<string, object> param = new();

    private Dictionary<string, RadzenCheckBox<bool?>> checkBoxList=new();//=new RadzenCheckBox<bool?>();
    bool form_res = false;
    private DBService dbService;
    private int M = 999999999;
    bool f = false;
    private TooltipOptions toolTipOpt = new TooltipOptions() { Delay = 100, Duration = 1000 };
   
    private void ShowRadzenCheckBoxTooltip(string code, bool? val)
    {

        string result = val switch
        {
            null => "null",
            true => "true",
            false => "false"
        };

        tooltipService.Open(checkBoxList[code].Element, result, toolTipOpt);
    }
    /*
    private void ShowRadzenCheckBoxTooltip(ElementReference elementReference, bool? val, TooltipOptions options = null)
        {
        ElementReference el = checkBox.Element;
            string result = val switch
        {
        null => "null",
        true => "true",
        false => "false"
        };
        tooltipService.Open(elementReference, result, options);
    }
    */
    private string CheckValue(string key, string valStr, baseColParam meta)
    {
        if (meta.DATATYPE == "NUMBER")
        {
            bool hasDecimal = valStr.Contains(_NumberDecimalSeparator);
            int integerPartLength = hasDecimal ? valStr.IndexOf(_NumberDecimalSeparator) : valStr.Length;
            int decimalPartLength = hasDecimal ? valStr.Length - integerPartLength - 1 : 0;

            // Общие ограничения по длине числового типа
            if (!(meta.DATALENGTH>0) &&
            (integerPartLength + decimalPartLength > 38))
            {
                return $"Превышена длина типа NUMBER(38), текущая {(integerPartLength + decimalPartLength)}";
            }

            // Ограничение только целочисленного формата (без дробной части)
            if (meta.DATALENGTH.HasValue && !meta.DATAPRECISION.HasValue)
            {
                if (decimalPartLength > 0)
                    return $"Тип NUMBER({meta.DATALENGTH}) не допускает дробную часть.";

                if (meta.DATALENGTH.Value < integerPartLength)
                    return $"Превышена длина целого типа NUMBER({meta.DATALENGTH}), текущая {integerPartLength}";
            }

            // Полностью заданный формат (целое и дробное)
            if (meta.DATALENGTH.HasValue && meta.DATAPRECISION.HasValue)
            {
                if (meta.DATALENGTH.Value < integerPartLength ||
                meta.DATAPRECISION.Value < decimalPartLength)
                {
                    return $"Превышена длина типа NUMBER({meta.DATALENGTH},{meta.DATAPRECISION}). Текущие значения ({integerPartLength}, {decimalPartLength})";
                }
            }
        }
        return null;
    }
    private decimal? GetValueNum(string key, Dictionary
            <string, object> _formValues)
    {

        if (!_formValues.ContainsKey(key))
            return null;
        if (_formValues[key] == DBNull.Value || _formValues[key] == null)
            return null;



        try
        {

            return Convert.ToDecimal(_formValues[key]);


        }
        catch (Exception ex)
        {

            return null;
        }



    }


    private async Task SetValueNum(string key, object value, baseColParam _meta)
    {

        //1
        if (value==null)
        {
            if (_formValues.ContainsKey(key))
            {
                _formValues[key] = value;
            }
            else
            {
                _formValues.Add(key, value);
            }

            foreach (var prm in InParams._metadata.Where(x => x.Parent == key))
            {


                _formValues[prm.CODE] = value;

            }
            return;
        }
        decimal result=0;
        string valstr = value.ToString().Replace(".", _NumberDecimalSeparator).Replace(",", _NumberDecimalSeparator);
        string error = CheckValue(key, valstr, _meta);
        _validatorFormat.Remove(key);

        if (error == null)
        {
            try
            {
                result = Convert.ToDecimal(valstr);


            }
            catch (Exception ex)
            {

                error = $"Ошибка преобразования значения {valstr}";

            }
        }
        if (error == null)
        {
            if (_formValues.ContainsKey(key))
            {
                _formValues[key] = result;
            }
            else
            {
                _formValues.Add(key, result);
            }
            var customFormat = new NumberFormatInfo
                {
                    NumberDecimalSeparator = ","
                };
            foreach (var prm in InParams._metadata.Where(x => x.Parent == key))
            {
                if (prm.DATALENGTH > 0 && !(prm.DATAPRECISION > 0))
                {
                    int result1 = Convert.ToInt32(result);
                    _formValues[prm.CODE] = result1.ToString(prm.Pattern);
                }
                else
                {
                    customFormat.NumberDecimalSeparator = prm.DecimalSeparator;
                    _formValues[prm.CODE] = result.ToString(prm.Pattern, customFormat);
                }

            }
        }
        else
        {
            EWA.Models.REP_Models.Rep_Validator vv = new() { color = "--rz-warning", message = error };
            _validatorFormat.Add(key, vv);
            await JSRuntime.InvokeVoidAsync("eval", $@"document.getElementById(""{key}"").blur()");
        }
    }

    /*для парсера запроса*/
    private bool GetCodeMethodCol(string cCode)
    {
        bool out_val = true;
        if (string.IsNullOrEmpty(cCode))
        {
            return out_val;
        }
        //var res_val = _formValues.TryGetValue(cCode, out var val) ? val : null;
        var res_dic = _SprSelShortDict1.TryGetValue(cCode, out var dval) ? dval?.ValPrm : null;
        if (res_dic != null)
        {
            out_val = false;
        }

        return out_val;
    }
    private async Task ParseSQL()
    {
        foreach (REP_Models.ColumnMetadata kvp in InParams._metadata)
        {
            if (kvp.IS_SYSTEM == 1 && (kvp.TYPE_SYSTEM == "MTDT_SQL" || kvp.TYPE_SYSTEM == "MTDT_MTAB" || kvp.TYPE_SYSTEM == "MTDT_JTB"
        || kvp.TYPE_SYSTEM == "MTDT_FLTR" || kvp.TYPE_SYSTEM == "MTDT_SLCT" || kvp.TYPE_SYSTEM == "MTDT_PARAMS"))
            {
                if (!_metaALG.Any(k => k.Key == kvp.CODE))
                {
                    _metaALG.Add(kvp.CODE, kvp.TYPE_SYSTEM);
                }
            }
        }

        string t_sql = string.Empty;
        foreach (var kvp in _metaALG)
        {
            if (_formValues.ContainsKey(kvp.Key) && kvp.Value == "MTDT_SQL")
            {
                t_sql = _formValues[kvp.Key].ToString();
            }
        }
        if (t_sql != null)
        {
            var res_parse = _amlserv.ParseQuery(t_sql);

            if (!res_parse.Equals(default((int, Dictionary<string, object>))))
            {
                if(res_parse.Code == 0)
                {
                    foreach (var rval in res_parse.JsonResult)
                    {
                        string key = rval.Key;
                        object value = rval.Value;
                        foreach (var meta in _metaALG)
                        {
                            if (meta.Value == key)
                            {
                                if (_formValues.ContainsKey(meta.Key))
                                {
                                    _formValues[meta.Key] = value;
                                }
                                else
                                {
                                    _formValues.Add(meta.Key, value);
                                }
                                break;
                            }
                        }
                    }
                }

            }
        }
        StateHasChanged();

    }
    private async Task OpenAddMethodDialog()
    {
        string colCode = string.Empty;
        string colDomC = string.Empty;
        decimal colValID = 0;
        string colValCode = string.Empty;
        foreach (REP_Models.ColumnMetadata kvp in InParams._metadata)
        {
            if (kvp.IS_SYSTEM == 1 && (kvp.TYPE_SYSTEM == "MTD_SLCT" || kvp.TYPE_SYSTEM == "MTD_PARAMS" || kvp.TYPE_SYSTEM == "MTD_B_ID" || kvp.TYPE_SYSTEM == "MTD_INDF"))
            {
                if (!_metaALG.Any(k => k.Key == kvp.CODE))
                {
                    _metaALG.Add(kvp.CODE, kvp.TYPE_SYSTEM);
                }
            }
        }
        _metaAlGData.Clear();
        foreach (var kvp in _metaALG)
        {
            if (_formValues.ContainsKey(kvp.Key))
            {
                _metaAlGData.Add(kvp.Value, _formValues[kvp.Key]);
            }
        }
        var metadataItem = InParams._metadata.FirstOrDefault(b => ((REP_Models.ColumnMetadata)b).IS_SYSTEM == 1 && ((REP_Models.ColumnMetadata)b).TYPE_SYSTEM == "MTD_B_ID");
        if (metadataItem != null)
        {
            colCode = metadataItem.CODE;
            colDomC = metadataItem.DOMAINCODE;

            var rval = _SprSelShortDict1.TryGetValue(colCode, out var ival) ? ival?.ValPrm : null;


            if (rval is decimal)
            {
                colValID = Convert.ToDecimal(rval);
            }
            else if (rval is string)
            {
                colValCode = rval.ToString();
            }

            if (InParams._SprSelShortDict.TryGetValue(colCode, out var selValue))
            {
                var key = selValue?.KeySprShortDict;  
                if (key != null && InParams._SprShortDict.TryGetValue(key, out var dictValue))
                {
                    if (colValCode != "")
                    {
                        colValID = (int)dictValue.FirstOrDefault(v => v.Code == colValCode)?.ID;
                    }
                    else if(colValID > 0)
                    {
                        colValCode = dictValue.FirstOrDefault(v => (decimal)v.ID == colValID)?.Code;
                    }
                }
            }

        }
        var parameters = new Dictionary<string, object>
            {
                {"MethodID",colValID},
                {"MethodCode", colValCode },
                { "metaALG" , _metaALG},
                { "metaALGData", _metaAlGData}
            };

        var options = new DialogOptions()
                {
                    Width = "80%",
                    Height = "auto",
                    Draggable = true, 
                    Resizable = true
                };
        var result = await DialogService.OpenAsync<Rules.MethodForm>("Настройка метода", parameters, options);
        if (result != null)
        {
            if (result.status == 1)
            {
                foreach (var kvp in result.out_Data)
                {
                    if (_formValues.ContainsKey(kvp.Key))
                    {
                        _formValues[kvp.Key] = kvp.Value;
                    }
                    else
                    {
                        _formValues.Add(kvp.Key, kvp.Value);
                    }
                }
            }

        }

    }

    private async Task OpenAddWRFLData()
    {
        foreach (REP_Models.ColumnMetadata kvp in InParams._metadata)
        {
            if (kvp.IS_SYSTEM == 1 && (kvp.TYPE_SYSTEM == "WFL_W_LIST" || kvp.TYPE_SYSTEM == "WFL_W_LNK"))
            {
                if (!_metaALG.Any(k => k.Key == kvp.CODE))
                {
                    _metaALG.Add(kvp.CODE, kvp.TYPE_SYSTEM);
                }
            }
        }
        _metaAlGData.Clear();
        foreach (var kvp in _metaALG)
        {
            if (_formValues.ContainsKey(kvp.Key))
            {
                _metaAlGData.Add(kvp.Value, _formValues[kvp.Key]);
            }
        }
        var options = new DialogOptions()
                {
                    Width = "80%",
                    Height = "auto",
                    Draggable = true,
                    Resizable = true
                };
        var parameters = new Dictionary<string, object>
            {
            { "_DBname",InParams._dbname},
            { "_Code", InParams._form_code },
            { "metaALG" , _metaALG},
            { "metaALGData", _metaAlGData}
            };
        var result = await DialogService.OpenAsync<WRFL.WRFL_DIAG>("Добавить новый показатель", parameters, options);

        if (result != null)
        {
            var status = result.status;
            var r_metaData = result.r_metaData;
            if (status == 1)
            {
                foreach (var kvp in r_metaData)
                {
                    if (_formValues.ContainsKey(kvp.Key))
                    {
                        _formValues[kvp.Key] = kvp.Value;
                    }
                    else
                    {
                        _formValues.Add(kvp.Key, kvp.Value);
                    }
                }
            }
        }
        StateHasChanged();
    }
    private async Task OpenAddWRFLParam()
    {
        foreach (REP_Models.ColumnMetadata kvp in InParams._metadata)
        {
            if (kvp.IS_SYSTEM == 1 && kvp.TYPE_SYSTEM == "WFL_W_PARAM")
            {
                if (!_metaALG.Any(k => k.Key == kvp.CODE))
                {
                    _metaALG.Add(kvp.CODE, kvp.TYPE_SYSTEM);
                }
            }
        }
        _metaAlGData.Clear();
        foreach (var kvp in _metaALG)
        {
            if (_formValues.ContainsKey(kvp.Key))
            {
                _metaAlGData.Add(kvp.Value, _formValues[kvp.Key]);
            }
        }
        var options = new DialogOptions()
                {
                    Width = "80%",
                    Height = "auto",
                    Draggable = true,
                    Resizable = true
                };
        var parameters = new Dictionary<string, object>
                {
            { "_DBname",InParams._dbname},
            { "_Code", InParams._form_code },
            { "metaALG" , _metaALG},
            { "metaALGData", _metaAlGData}
                };
        var result = await DialogService.OpenAsync<WRFL.WRFL_PARAM>("Параметры воркфлоу", parameters, options);

        if (result != null)
        {
            var status = result.status;
            var r_metaData = result.r_metaData;
            if (status == 1)
            {
                foreach (var kvp in r_metaData)
                {
                    if (_formValues.ContainsKey(kvp.Key))
                    {
                        _formValues[kvp.Key] = kvp.Value;
                    }
                    else
                    {
                        _formValues.Add(kvp.Key, kvp.Value);
                    }
                }
            }
        }
        StateHasChanged();
    }

    private async Task OpenAddRuleDialog()
    {
        foreach (REP_Models.ColumnMetadata kvp in InParams._metadata)
        {
            if (kvp.IS_SYSTEM == 1 && (kvp.TYPE_SYSTEM == "ALG" || kvp.TYPE_SYSTEM == "ALG_JSN" || kvp.TYPE_SYSTEM == "I_LST" || kvp.TYPE_SYSTEM == "M_LST" || kvp.TYPE_SYSTEM == "A_LST"))
            {
                if (!_metaALG.Any(k => k.Key == kvp.CODE))
                {
                    _metaALG.Add(kvp.CODE, kvp.TYPE_SYSTEM);
                }
            }
        }
        _metaAlGData.Clear();
        foreach (var kvp in _metaALG)
        {
            if (_formValues.ContainsKey(kvp.Key))
            {
                _metaAlGData.Add(kvp.Value, _formValues[kvp.Key]);
            }
        }
        var options = new DialogOptions()
                {
                    Width = "80%",
                    Height = "auto",
                    Draggable = true, 
                    Resizable = true
                };
        var parameters = new Dictionary<string, object>
        {
            {"DBname",InParams._dbname},
            { "Code", InParams._form_code },
            { "metaALG" , _metaALG},
            { "metaALGData", _metaAlGData}
        };
        var result = await DialogService.OpenAsync<Rules.RuleForm>("Добавить новый показатель", parameters, options);
        //var status = result.status;

        if (result != null)
        {        
            var status = result.status;
            var r_metaData = result.r_metaData;
            if (status == 1)
            {
                foreach (var kvp in r_metaData)
                {
                    if (_formValues.ContainsKey(kvp.Key))
                    {
                        _formValues[kvp.Key] = kvp.Value;
                    }
                    else
                    {
                        _formValues.Add(kvp.Key, kvp.Value);
                    }
                }
            }
        }
        StateHasChanged();
    }
    private async Task OpenDiagramDialog()
    {
        foreach (REP_Models.ColumnMetadata kvp in InParams._metadata)
        {
            if (kvp.IS_SYSTEM == 1 && (kvp.TYPE_SYSTEM == "WFL_NODES" || kvp.TYPE_SYSTEM == "WFL_NODES_LNK" || kvp.TYPE_SYSTEM == "WFL_NODE_STR"))
            {
                if (!_metaALG.Any(k => k.Key == kvp.CODE))
                {
                    _metaALG.Add(kvp.CODE, kvp.TYPE_SYSTEM);
                }
            }
        }
        _metaAlGData.Clear();
        foreach (var kvp in _metaALG)
        {
            if (_formValues.ContainsKey(kvp.Key))
            {
                _metaAlGData.Add(kvp.Value, _formValues[kvp.Key]);
            }
        }

        var options = new DialogOptions()
                {
                    Width = "80%",
                    Height = "auto",
                    Draggable = true, 
                    Resizable = true
                };
        var parameters = new Dictionary<string, object>
            {

            {"_DBname",InParams._dbname},
            { "_Code", InParams._form_code },
            { "metaALG" , _metaALG},
            { "metaALGData", _metaAlGData}

            };
        var result = await DialogService.OpenAsync<Rules.DiagramForm>("Добавить диаграмму", parameters, options);
        if (result != null)
        {
            var status = result.status;
            var r_metaData = result.r_metaData;
            if (status == 1)
            {
                foreach (var kvp in r_metaData)
                {
                    if (_formValues.ContainsKey(kvp.Key))
                    {
                        _formValues[kvp.Key] = kvp.Value;
                    }
                    else
                    {
                        _formValues.Add(kvp.Key, kvp.Value);
                    }
                }
            }
        }
        StateHasChanged();
    }

    private bool IsHide(object _meta)
    {
        if (_meta is REP_Models.ParamMetadata var)
        {
            if (var.HIDDENVALUE == 1)
                return false;
        }
        return true;
    }

    private bool IsDisable(baseColParam _meta)
    {
        if (InParams._action == "DEL" || InParams._action == "VIEW" || _meta.Parent!=null)
        {
            return true;
        }

        return false;
    }

    private bool IsWhere(object _meta)
    {
        if (_meta is REP_Models.ColumnMetadata var)
        {
            if ((var.INSERTABLE == 1 && InParams._action == "ADD") && var.VIEWVISIBLE == 1 ||
            (var.EDITABLE == 1 && InParams._action == "EDIT") && var.VIEWVISIBLE == 1 ||
            (var.VIEWVISIBLE == 1 && InParams._action == "DEL") ||
            (var.VIEWVISIBLE == 1 && InParams._action == "VIEW")
            )
            {
                return true;
            }
            return false;
        }

        if (_meta is REP_Models.ParamMetadata varp)
        {
            if (varp.CODE != "p_NameFilter")
            {
                return true;
            }
            return false;
        }


        return false;
    }

    private string ErrorMessage = String.Empty;    

    private async Task<(Dictionary<string, object> formValues, bool form_res, string error_message)> SaveAsync()
    {
        Console.WriteLine("SaveAsync ФИЛЬТР OK ");

        ErrorMessage = string.Empty;
        string errorMessage = string.Empty;
        var formValues = new Dictionary<string, object>();
        formValues = _formValues;
        bool form_res = true;

        bool isValidator = true;

        try
        {
            foreach (var meta in InParams._metadata)
            {
                if (_SprSelShortDict1.ContainsKey(meta.CODE))
                {
                    if (InParams._action == "ADD" && _formValues.ContainsKey(meta.CODE) == false)
                    {

                        _formValues.Add(meta.CODE, _SprSelShortDict1[meta.CODE].ValPrm);
                    }
                    else
                    {
                        _formValues[meta.CODE] = _SprSelShortDict1[meta.CODE].ValPrm;
                    }

                    if (InParams._action == "GlobalFILTR")
                    {
                        InParams._SprSelShortDict[meta.CODE].ValPrm = _SprSelShortDict1[meta.CODE].ValPrm;
                    }

                }

                if (_SprSelLongDict1.ContainsKey(meta.CODE))
                {
                    if (InParams._action == "GlobalFILTR")
                    {
                        InParams._SprSelLongDict[meta.CODE].ValPrm = _SprSelLongDict1[meta.CODE].ValPrm;
                    }
                    if ((_SprSelLongDict1[meta.CODE].ValPrm?.Count ?? 0) > 0)

                    {
                        if (InParams._action == "ADD" && _formValues.ContainsKey(meta.CODE) == false)
                        {
                            _formValues.Add(meta.CODE, _SprSelLongDict1[meta.CODE].ValPrm.First().Value);
                        }
                        else
                        {
                            _formValues[meta.CODE] = _SprSelLongDict1[meta.CODE].ValPrm.First().Value;
                        }
                    }
                    else
                    {
                        if (_formValues.ContainsKey(meta.CODE) == false)
                        {
                            _formValues.Add(meta.CODE, null);
                        }
                        else
                        {
                            _formValues[meta.CODE] = null;
                        }
                    }
                }
                if (!_formValues.ContainsKey(meta.CODE))
                {
                    _formValues[meta.CODE] = null;
                }
            }



            if (InParams._action != "DEL")
            {
                isValidator = _formService.CheckValidator(InParams._metadata, _validator, formValues);
            }
            if (isValidator == false)
            {
                return (formValues, false, "");
            }
        }
        catch (Exception ex)
        {
            DialogService.Close(new { Values = formValues, Result = form_res });
            errorMessage = $"General Error: {ex.Message}";
            return (formValues, false, errorMessage);
        }


        if (InParams._action == "GlobalFILTR" || InParams._action == "FILTR")
        {
            DialogService.Close(new { Values = formValues, Result = form_res });
            return (formValues, form_res, "");
        }

        

        bool success=false;

        Dictionary<string, object> outPrm = new Dictionary<string, object>();
        Dictionary<string, Rep_Param> parameters = new Dictionary<string, Rep_Param>();
        string tdelsql = string.Empty;
        string tinsertsql = string.Empty;
        var tdelparameters = new Dictionary<string, Rep_Param>();
        var tinsertparameters = new Dictionary<string, Rep_Param>();
        Dictionary<string, Rep_Param> singleparameters = new Dictionary<string, Rep_Param>();
        string pkcolumns= string.Empty;
        Console.WriteLine("SaveAsync 1 ФИЛЬТР OK ");

      
            bool flag = false;

#pragma warning disable CS4014
            InvokeAsync(async () => {                
                await Task.Delay(1000);

                (parameters, success, errorMessage) = await _formService.SetParam(formValues, InParams._metadata, InParams._globalFilter, InParams._seldata);
                if (success && InParams._is_tempory)
                {
                    (success, errorMessage, tdelsql, tinsertsql) = await TempTable(tdelparameters, tinsertparameters, formValues);

                }
                if (success)
                {
                    (success, errorMessage, singleparameters, pkcolumns) = await SingleRow();
                }
                if (success)
                {
                    (success, errorMessage) = await dbService.RowAction(InParams._sql, parameters, formValues, tdelparameters, tdelsql, 
                                                                        tinsertparameters, tinsertsql, InParams._is_tempory, 
                                                                        InParams._action, InParams._query, singleparameters, pkcolumns);
                }
                while (!flag)
                {
                    await Task.Delay(1000);
                }
                DialogService.Close();
            }
                 );
#pragma warning restore CS4014

            flag = true;
            await BusyDialog();           

            if (!success)
            {
                string errorTitle = "Ошибка ";
                switch (InParams._action)
                {
                    case "ADD":
                        errorTitle = errorTitle + "добавления";
                        break;
                    case "EDIT":
                        errorTitle = errorTitle + "редактирования";
                        break;
                    case "DEL":
                        errorTitle = errorTitle + "удаления";
                        break;
                }
                if (InParams._form_code == "SATTRSPR" && errorMessage.Contains("ORA-00001:"))
                    errorMessage = "Код атрибута должен быть уникальным.";
                await DialogService.OpenAsync<ErrorGrid>(errorTitle,
                                  new Dictionary<string, object> { { "_errorMessage", errorMessage } },
                                  new DialogOptions { Draggable = true, Resizable = true }
                );
                ErrorMessage = errorMessage;
                return (formValues, false, ErrorMessage);
            }
            
       
        DialogService.Close(new { Values = formValues, Result = form_res, errorMessage = errorMessage });
        return (formValues, form_res, errorMessage);
    }
    private async Task<(bool res, string errorMessage,  Dictionary<string, Rep_Param> singleparameters, string pkcolumns)> SingleRow()
    {
        bool res = true;
        string errorMessage = string.Empty;
        var singleparameters = new Dictionary<string, Rep_Param>();
        string pkcolumns = "";
        try
        {
            foreach (var prm in InParams._metadata_param)
            {

                if (InParams._globalFilter.ContainsKey(prm.CODE))
                {
                    singleparameters.Add(prm.CODE, InParams._globalFilter[prm.CODE]);
                }
                else
                {
                    singleparameters.Add(prm.CODE, new Rep_Param(prm.ISMANDATORY, prm.DATATYPE, ParameterDirection.Input, prm.DATALENGTH,
                                                       prm.DATAPRECISION, null));
                }

            }

            pkcolumns = string.Join(" and ", InParams._metadata.Where(x => ((REP_Models.ColumnMetadata)x).IS_PK == 1).Select(i =>
            {
                if (i.DATATYPE == "DATE")
                {
                return $"trunc({i.CODE})=trunc(:IN_{i.CODE})";
                }
                else
                {
                    return $"{i.CODE}=:IN_{i.CODE}";
                }
            }));

            foreach (var prm in InParams._metadata.Where(x => ((REP_Models.ColumnMetadata)x).IS_PK == 1))
            {
                singleparameters.Add("IN_" + prm.CODE, new Rep_Param(prm.ISMANDATORY, prm.DATATYPE, ParameterDirection.Input, prm.DATALENGTH,
                                                           prm.DATAPRECISION, "IS_PK"/*data[prm.CODE]*/));
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"General Error: {ex.Message}";
            res = false;
        }
        return (res, errorMessage, singleparameters, pkcolumns);
        // var (success, errorMessage) = await dbService.GetDataSingle1(InParams._query, data, singleparameters, pkcolumns);
    }
    private async Task<(bool res, string errorMessage, string tdelsql, string tinsertsql)> TempTable(Dictionary<string, Rep_Param> tdelparameters, 
                                                                                                     Dictionary<string, Rep_Param> tinsertparameters, 
                                                                                                     Dictionary<string, object> formValues)
    {
     bool res=true;
     string errorMessage=string.Empty;
     string tdelsql=string.Empty;
     string tinsertsql = string.Empty;

     try
     {
            if (InParams._action == "DEL" || InParams._action == "EDIT")
            {
                tdelsql = "delete " + InParams._name_tmptab + "\r\n" +
                           "where " + string.Join(" and ", InParams._metadata.Where(x => ((REP_Models.ColumnMetadata)x).IS_PK == 1).Select(i => i.CODE + "=:IN_" + i.CODE));
              

                foreach (var prm in InParams._metadata.Where(x => ((REP_Models.ColumnMetadata)x).IS_PK == 1))
                {
                    tdelparameters.Add("IN_" + prm.CODE, new Rep_Param(prm.ISMANDATORY, prm.DATATYPE, ParameterDirection.Input, prm.DATALENGTH,
                                        prm.DATAPRECISION, formValues[prm.CODE])
                                       );
                }               
            }
            if (InParams._action == "ADD" || InParams._action == "EDIT")
            {
                foreach (var prm in InParams._metadata)                {
                    tinsertparameters.Add("IN_" + prm.CODE, new Rep_Param(prm.ISMANDATORY, prm.DATATYPE, ParameterDirection.Input, prm.DATALENGTH,
                                        prm.DATAPRECISION, formValues[prm.CODE])
                                       );
                } 

                tinsertsql = "insert into " + InParams._name_tmptab + "(" +
            string.Join(" , ", InParams._metadata.Select(i => i.CODE)) + ")\r\n" +
            "values(" + string.Join(" , ", InParams._metadata.Select(i => ":IN_" + i.CODE)) + ")";
              
            }

     }
     catch (Exception ex)
     {
         errorMessage = $"General Error: {ex.Message}";
         res = false;
     }
        return (res, errorMessage, tdelsql, tinsertsql);

    }
   
    async Task BusyDialog()
    {
        await DialogService.OpenAsync("", ds =>
            @<RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.End" JustifyContent="JustifyContent.Stretch" Wrap="FlexWrap.Wrap" Gap="0.5rem">
                <RadzenProgressBarCircular ProgressBarStyle="ProgressBarStyle.Primary" Value="100" ShowValue="false" Mode="ProgressBarMode.Indeterminate" Size="ProgressBarCircularSize.Small" />
             </RadzenStack>
    , new DialogOptions()
                {
                    ShowTitle = false,
                    Style = "min-height:auto;min-width:auto;width:auto;background-color:transparent",
                    CloseDialogOnEsc = false, Draggable = true, Resizable = true 
                });
    }

    private async Task<(Dictionary<string, object> formValues, bool form_res, string errormessage)> CloseAsync()
    {
        var formValues = new Dictionary<string, object>(_formValues);
        form_res = false;
        DialogService.Close(new { Values = formValues, Result = form_res, errorMessage = ErrorMessage });
        return (formValues, form_res, ErrorMessage);
    }
    /*
    private async Task CloseAsync()
    {
    var formValues = new Dictionary<string, object>(_formValues);
    form_res = false;
    DialogService.Close();
       // return null;
    }
    */
    private async Task ReadSingleRow(IDictionary<string, object> data)
    {

        var parameters = new Dictionary<string, Rep_Param>();
        string pkcolumns = "";
        foreach (var prm in InParams._metadata_param)
        {

            if (InParams._globalFilter.ContainsKey(prm.CODE))
            {  
                parameters.Add(prm.CODE, InParams._globalFilter[prm.CODE]);
            }
            else
            {
                parameters.Add(prm.CODE, new Rep_Param(prm.ISMANDATORY, prm.DATATYPE, ParameterDirection.Input, prm.DATALENGTH,
                                                       prm.DATAPRECISION, null));
            }

        }

        pkcolumns = string.Join(" and ", InParams._metadata.Where(x => ((REP_Models.ColumnMetadata)x).IS_PK == 1).Select(i => i.CODE + "=:IN_" + i.CODE));

        foreach (var prm in InParams._metadata.Where(x => ((REP_Models.ColumnMetadata)x).IS_PK == 1))
        {   
            parameters.Add("IN_" + prm.CODE, new Rep_Param(prm.ISMANDATORY, prm.DATATYPE, ParameterDirection.Input, prm.DATALENGTH,
                                                               prm.DATAPRECISION, data[prm.CODE]));
        }
        var (success, errorMessage) = await dbService.GetDataSingle1(InParams._query, data, parameters, pkcolumns);
    }

    //для списка
    private Dictionary<string, IEnumerable<REP_Models.SPRMetadata>> _referenceData = new();

    private Dictionary<string, IEnumerable<REP_Models.SPRShort>> SprShortDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprShortSelDict> SprSelShotDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprLongFiltrMeta> SprLongDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprLongSelDict> SprSelLongDict = new();
    private Dictionary<string, int> SprLongCountDict = new();
    private Dictionary<string, string> SprLongFiltrDict = new();
    private Dictionary<string, bool> gridFocus = new();
    private bool flag = false;

    private async Task Find(string PARAMCODE)
    {
        string filter = "";
        if (String.IsNullOrEmpty(@SprLongFiltrDict[PARAMCODE]) == false)
        {


            filter = string.Join(" or ",
                        InParams._SprLongDict[InParams._SprSelLongDict[PARAMCODE].KeySprLongDict].Column.Where(x => x.DATATYPE == "VARCHAR2").Select(x => "upper(" + x.CODE + ")" +
                    " like upper('%" + @SprLongFiltrDict[PARAMCODE] + "%')")
                        );
        }
        await LoadData(new LoadDataArgs { Skip = 0, Top = 10 }, PARAMCODE, filter);
    }
    private async Task OpenFilterDialog(string PARAMCODE)
    {
        SprShortDict.Clear();
        SprSelShotDict.Clear();
        SprLongDict.Clear();
        SprSelLongDict.Clear();
        gridFocus[PARAMCODE] = true;
        gridDict[PARAMCODE].OpenOnFocus = true;
        await gridDict[PARAMCODE].FocusAsync();
        foreach ((string dd, string param, string datatype ) in InParams._SprLongDict[InParams._SprSelLongDict[PARAMCODE].KeySprLongDict].Param.Where(x => x.DIMCODE != null).Select(x => (x.DIMCODE, x.CODE, x.DATATYPE)))
        {

            bool flag = true;
            if (SprShortDict.ContainsKey(dd) == false && SprLongDict.ContainsKey(dd) == false)
            {
                var (_sprMetadata, _isShortDim, _SprLongFiltrMeta) = await _getspr.GetSPRDataAsync1(dd, InParams._dbname, datatype);
                if (_isShortDim)
                {
                    SprShortDict.Add(dd, _sprMetadata);
                }
                else
                {
                    SprLongDict.Add(dd, _SprLongFiltrMeta);

                }
            }

            if (SprLongDict.ContainsKey(dd))
            {
                if (SprSelLongDict.ContainsKey(param) == false)
                {
                    Dictionary<string, object> ValPrm = new();
                    SprSelLongDict.Add(param, new REP_Models.Rep_SprLongSelDict { KeySprLongDict = dd, ValPrm = ValPrm });
                }
            }
            else
            {
                if (SprSelShotDict.ContainsKey(param) == false)
                {
                    SprSelShotDict.Add(param, new EWA.Models.REP_Models.Rep_SprShortSelDict { KeySprShortDict = dd, ValPrm = null });
                }
            }
            int j = 0;
        }
        FormParams _filtrParams = new FormParams
            {
                _metadata = InParams._SprLongDict[InParams._SprSelLongDict[PARAMCODE].KeySprLongDict].Param ,
                _action = "FILTR",
                _SprShortDict = SprShortDict,
                _SprSelShortDict = SprSelShotDict,
                _SprLongDict = SprLongDict,
                _SprSelLongDict = SprSelLongDict,
                _dbname = InParams._dbname
            };
        var result = await DialogService.OpenAsync<FormGrid>("Дополнительные фильтры",
                        new Dictionary<string, object> {{ "InParams", _filtrParams}},
                        new DialogOptions { Draggable = true, Resizable = true }
                     );
         flag = false;
        // Если диалог был подтверждён, обновляем значения фильтров
        if (result.Result)
        {
           //
            bool fl = false;

#pragma warning disable CS4014
            InvokeAsync(async () =>
            {
                await Task.Delay(1000);
                param.Clear();
                var formValues = new Dictionary<string, object>();
                formValues = result.Values as Dictionary<string, object>;
                foreach (var kvp in formValues.Where(x => x.Key != "p_NameFilter"))
                {

                    string paramName = kvp.Key;
                    object paramValue = kvp.Value;

                    param.Add(paramName, paramValue);
                }
                flag = true;

                await LoadData(new LoadDataArgs { Skip = 0, Top = 10 }, PARAMCODE, "");
                
                while (!fl)
                {
                    await Task.Delay(1000);
                }
                DialogService.Close();
            }
                 );
#pragma warning restore CS4014

            fl = true;
            await BusyDialog();
           //
           

        }



        await gridDict[PARAMCODE].FocusAsync();

    }

    async Task LoadData(LoadDataArgs args, string PARAMCODE, string filter)
    {
        if (flag == false)
        {
            return;
        }
        int skip = args.Skip ?? 0;
        int take = args.Top ?? 10;

        //_SprLongDict[_SprSelLongDict[PARAMCODE].KeySprLongDict]
        // dataDict[PARAMCODE]
        var result = await dbService.GetDataPagination(
        baseSql: InParams._SprLongDict[InParams._SprSelLongDict[PARAMCODE].KeySprLongDict].sqlQuery,
        skip: skip,
        take: take,
        parameters: param,
        filter: filter,
        orderBy: args.OrderBy);

        if (string.IsNullOrEmpty(result.ErrorMessage))
        {
            dataDict[PARAMCODE] = result.Items;
            SprLongCountDict[PARAMCODE] = result.count;

        }
        else
        {
            //надо будет в модальном окне показать
        }

    }
    async void OnChange(object value, string PARAMCODE)
    {
        //_SprSelLongDict[PARAMCODE].ValPrm = (IDictionary<string, object>)value;
        baseColParam var = InParams._metadata.FirstOrDefault(x => x.Parent == PARAMCODE);

        if (var != null && gridFocus.ContainsKey(PARAMCODE))
        {
            ((IDictionary<string, object>)value).TryGetValue(var.Pattern, out var val);
            _formValues[var.CODE] = val;
        }
        if (var != null && !gridFocus.ContainsKey(PARAMCODE))
        {
            baseColParam par = InParams._metadata.FirstOrDefault(x => x.CODE == PARAMCODE);


            object val = InParams._SprShortDict[par.DIMCODE].Where(x => x.ID == value && par.DOMAINCODE.ToUpper()!= "CODE" ||
                                                                        x.Code == value.ToString() && par.DOMAINCODE.ToUpper()== "CODE")

                                                             .Select(x =>
                    {
                        if (var.Pattern.ToUpper() == "CODE") return x.Code;
                        if (var.Pattern.ToUpper() == "ID") return x.ID;
                        else return x.Name;
                    })
                .FirstOrDefault();

            _formValues[var.CODE] = val;
        }
        if (gridFocus.ContainsKey(PARAMCODE))
        {
            gridFocus[PARAMCODE] = false;
            gridDict[PARAMCODE].OpenOnFocus = false;
            //  selectedItem = (IDictionary<string, object>)value;
            //?await JSRuntime.InvokeVoidAsync("Radzen.closeAllPopups");
        }

    }

    private void InitializeFormValues()
    {
        _formService.InitValidatorColor(InParams._metadata, _validator);
        //GlobalFILTR
        foreach (string key in InParams._SprSelLongDict.Keys)
        {           

            gridDict.Add(key, new RadzenDropDownDataGrid<IDictionary<string, object>>());
            IEnumerable<IDictionary<string, object>> data;             
            dataDict.Add(key, null);
            SprLongCountDict.Add(key, 1);
            SprLongFiltrDict.Add(key, "");
            gridFocus.Add(key, false);
        }

        if (InParams._action == "GlobalFILTR")
        {
            foreach (var keyValuePair in InParams._globalFilter)//.Where(x => x.Value.Val != null))
            {
                _formValues[keyValuePair.Key] = keyValuePair.Value.Val;
                if (InParams._SprSelShortDict.ContainsKey(keyValuePair.Key))
                {
                    _SprSelShortDict1.Add(keyValuePair.Key, 
                                        new EWA.Models.REP_Models.Rep_SprShortSelDict 
                                         { KeySprShortDict = keyValuePair.Key, 
                                           ValPrm = keyValuePair.Value.Val
                                         });
                }

                if (InParams._SprSelLongDict.ContainsKey(keyValuePair.Key))
                {
                    var items = new List<IDictionary<string, object>>();
                    var item = new Dictionary<string, object>();
                    _SprSelLongDict1.Add(keyValuePair.Key, new EWA.Models.REP_Models.Rep_SprLongSelDict { });
                    _SprSelLongDict1[keyValuePair.Key].ValPrm = InParams._SprSelLongDict[keyValuePair.Key].ValPrm;
                    if (InParams._SprSelLongDict[keyValuePair.Key].ValPrm != null)
                    {
                        foreach (var column in InParams._SprSelLongDict[keyValuePair.Key].ValPrm)
                        {
                            item[column.Key] = column.Value;
                        }
                        items.Add(item);
                        dataDict[keyValuePair.Key] = items;
                    }
                }
            }

            return;
        }

        if (InParams._action == "ADD" || InParams._action == "FILTR")
        {
            foreach (var gg in InParams._SprSelLongDict)
            {
                _SprSelLongDict1.Add(gg.Key,
                                     new REP_Models.Rep_SprLongSelDict
                                         {
                                             KeySprLongDict = gg.Value.KeySprLongDict,
                                             ValPrm = new Dictionary<string, object>()//gg.Value.ValPrm
                                         }
                );
            }
        }

        if (InParams._action == "EDIT" || InParams._action == "DEL" || InParams._action =="VIEW")
        {

            foreach (var keyValuePair in InParams._seldata)
            {
                _formValues[keyValuePair.Key] = keyValuePair.Value;
                if (InParams._SprSelShortDict.ContainsKey(keyValuePair.Key))
                {
                    object val;

                    if (keyValuePair.Value == null || keyValuePair.Value is DBNull)
                    {
                        val = null;
                    }
                    else if (keyValuePair.Value is string stringValue)
                    {
                        val = stringValue;
                    }
                    else if (Decimal.TryParse(keyValuePair.Value.ToString(), out decimal number))
                    {
                        val = number;

                    }                     
                    else
                    {
                        throw new InvalidOperationException($"Unsupported type for key {keyValuePair.Key}: {keyValuePair.Value.GetType()}");
                    }

                    _SprSelShortDict1.Add(keyValuePair.Key, 
                                         new EWA.Models.REP_Models.Rep_SprShortSelDict 
                                         { KeySprShortDict = keyValuePair.Key, 
                                            ValPrm = val
                                         });


                }

                if (InParams._SprSelLongDict.ContainsKey(keyValuePair.Key))
                {
                    int i = 0;
                    var items = new List<IDictionary<string, object>>();
                    var item = new Dictionary<string, object>();
                    _SprSelLongDict1.Add(keyValuePair.Key, new EWA.Models.REP_Models.Rep_SprLongSelDict { });
                    _SprSelLongDict1[keyValuePair.Key].ValPrm = new Dictionary<string, object>();

                    foreach (var column in InParams._SprLongDict[InParams._SprSelLongDict[keyValuePair.Key].KeySprLongDict].Column)
                    {                      

                        if (i == 0)
                        {
                            _SprSelLongDict1[keyValuePair.Key].ValPrm.Add(column.CODE, keyValuePair.Value);
                            item[column.CODE] = keyValuePair.Value;
                        }

                        if (i == 1 && column.DATATYPE=="VARCHAR2")
                        {
                            baseColParam var= InParams._metadata.FirstOrDefault(x => x.Parent == keyValuePair.Key);

                            if (var==null)
                            {
                                _SprSelLongDict1[keyValuePair.Key].ValPrm.Add(column.CODE, InParams._seldata[keyValuePair.Key + "#N"]);
                                item[column.CODE] = InParams._seldata[keyValuePair.Key + "#N"];
                            }
                            else
                            {
                                _SprSelLongDict1[keyValuePair.Key].ValPrm.Add(column.CODE, InParams._seldata[var.CODE]);
                                item[column.CODE] = InParams._seldata[var.CODE];
                            }
                        }

                        if (i == 1 && column.DATATYPE!="VARCHAR2")
                        {
                            _SprSelLongDict1[keyValuePair.Key].ValPrm.Add(column.CODE, keyValuePair.Value);
                            item[column.CODE] = String.Empty;
                        }

                        if (i>1)
                        {
                            _SprSelLongDict1[keyValuePair.Key].ValPrm.Add(column.CODE, String.Empty);
                            item[column.CODE] = String.Empty;
                        }
                        if (i != 1)
                        {
                            i++;
                        }
                        if (i == 1 && column.DATATYPE == "VARCHAR2")
                        {
                            i++; 
                        }
                    }
                    items.Add(item);

                    dataDict[keyValuePair.Key] = items;
                }
            }
        }
        else
        {
            foreach (var meta in InParams._metadata)
            {
                if (InParams._SprSelShortDict.ContainsKey(meta.CODE))
                {
                    _SprSelShortDict1.Add(meta.CODE,
                                         new EWA.Models.REP_Models.Rep_SprShortSelDict
                                             {
                                                 KeySprShortDict = meta.CODE,
                                                 ValPrm = null
                                             });
                }
            }

        }

    }

    private bool _isInitialized = false;
    protected override async Task OnParametersSetAsync()
    {
        if (!_isInitialized)
        {
            dbService = new DBService(InParams._dbname, Security);
            InitializeFormValues();
            foreach(var prm in InParams._metadata)
            {
                prm.RegExPattern = GetRegex(prm);
            }
            _isInitialized = true;
        }
    }
    protected override async Task OnInitializedAsync()
    {
        /*
        foreach (var meta in InParams._metadata.Where(x => IsWhere(x)))
        {
            if (meta.DOMAINCODE == "Bool")
            {
                RadzenCheckBox<bool?> meta.CODE; // Создание экземпляра
                checkBoxList.Add(meta.CODE, checkbox);
            }
        }
        */
        //dbService = new DBService(InParams._dbname);
        //InitializeFormValues();
        base.OnInitialized();
        // culture.NumberFormat.NumberDecimalSeparator = ",";
    }

    private string GetRegex(baseColParam meta)
    {/*
        return (meta.DATATYPE, meta.DATALENGTH, meta.DATAPRECISION) switch
        {
            ("NUMBER", > 0, > 0) => $"^\\d{{1,{meta.DATALENGTH - meta.DATAPRECISION}}}(?:\\.\\d{{1,{meta.DATAPRECISION}}})?$",
            ("NUMBER", > 0, _) => "^[-]?\d{0,38}(\.\d{1,38})?$|^[-]?\d{1,38}$",
            ("NUMBER", _, _) => @"^\d{1,38}(\.\d{1,38})?$",
          ("FLOAT", _, _) => typeof(decimal),
            ("INT", _, _) => typeof(long),//typeof(long),
            ("VARCHAR2", _, _) => typeof(string),
            ("CHAR", _, _) => typeof(string),
            ("CLOB", _, _) => typeof(string),
            ("BOOL", _, _) => typeof(bool),
            ("DATE", _, _) => typeof(DateTime),
            ("TIMESTAMP(6)", _, _) => typeof(DateTime),
            _ => typeof(string),
        };
        "^[-]?\d{0,38}(\.\d{1,38})?$|^[-]?\d{1,38}$"
        */
        if (meta.DATALENGTH > 0 && meta.DATAPRECISION >= 0)
        {
            // Оба параметра установлены
            return $"^\\d{{1,{meta.DATALENGTH - meta.DATAPRECISION}}}(?:\\.\\d{{1,{meta.DATAPRECISION}}})?$";
        }
        else if (meta.DATALENGTH > 0)
        {
            // Только длина установлена
            return $"^\\d{{1,{meta.DATALENGTH}}}(\\..*)?$";
        }
        else
        {
            // Нет ограничений, возвращаем null
            //return null;
            return $"^\\d{{1,{2}}}(\\..*)?$";
            int gg = 0;
        }
    }
    private RenderFragment GetTextBoxComponent(baseColParam meta) => __builder =>
    {
       
        /*        retrn builder =>
                {  
                    builder.OpenComponent<RadzenTextBox>(0);

                    builder.AddAttribute(1, "Value", _formService.GetValue(meta.CODE, _formValues));
                    builder.AddAttribute(2, "TValue", typeof(string));
                    if (InParams._metadata.Count(x => x.Parent == meta.CODE) > 0)
                    {
                        builder.AddAttribute(3, "@oninput", EventCallback.Factory.Create(this, (ChangeEventArgs args) => _formService.SetValue1(meta.CODE, args.Value?.ToString(), _formValues, InParams._metadata)));
                    }
                    else
                    {
                        builder.AddAttribute(3, "ValueChanged", EventCallback.Factory.Create<string>(this, (value) => _formService.SetValue(meta.CODE, value, _formValues)));
                    }
                        builder.AddAttribute(4, "Name", meta.CODE);
                        builder.AddAttribute(5, "Disabled", IsDisable(meta));
                    builder.CloseComponent();


                };*/


    };

}
