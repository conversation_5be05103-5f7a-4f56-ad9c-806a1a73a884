﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Oracle.ManagedDataAccess.Client;
using System.Configuration;
using System.Data;
using System.Text.Json;
using EWA.Data;
using EWA.Models;
using static EWA.Models.REP_Models;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using static EWA.Services.SIBModule;
using static EWA.Services.SIBService;
using Microsoft.JSInterop;
using System.CodeDom.Compiler;
using System;
using Parlot.Fluent;
using Microsoft.AspNetCore.Components;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using static EWA.Models.SIB_Models;


namespace EWA.Services
{
    public class RepService
    {
        private readonly string _connectionString;
        string dbname = string.Empty;
        private DBService dbService;
        private readonly DBContext _context;
        private readonly ConnectionStringProvider _ConService;
        public RepService(DBContext context, ConnectionStringProvider ConService)
        {
            _context = context;
            _ConService = ConService;
        }

        public async Task<REP_Models.ActionAccess> GetActionAccess(SIB_Models.SIB_USERS user, decimal object_id)
        {
            REP_Models.ActionAccess result = new();
            string query = "SELECT ID_USER, ID_OBJECT, ACTION_ADD, ACTION_EDIT, ACTION_DEL FROM ( " + Environment.NewLine
                          + "  with main_data as (SELECT id_user, id_object, decode(access_type, 'PERMIT', 1, 0) as access_type, action_get,action_add,action_edit,action_del " + Environment.NewLine
                         + $"                       FROM SIB_POLICY where id_user = {user.ID} and id_object = {object_id}) " + Environment.NewLine
                          + ", act_add as ( select id_user, id_object, min(access_type)act from main_data where action_add = 1 GROUP BY id_user, id_object) " + Environment.NewLine
                          + ", act_edit as (select id_user, id_object, min(access_type)act from main_data where action_edit = 1 GROUP BY id_user, id_object) " + Environment.NewLine
                          + ", act_del as (select id_user, id_object, min(access_type)act from main_data where action_del = 1 GROUP BY id_user, id_object) " + Environment.NewLine
                         + $" select {user.ID} as id_user, {object_id} as id_object, nvl(a.act, 0) as action_add , nvl(e.act, 0) action_edit, nvl(d.act, 0) action_del " + Environment.NewLine
                         + "  from  dual left join act_add a on 1 = 1 left join act_edit e on 1 = 1 left join act_del d on 1 = 1 ) ";
            dbname = await GetConnectionStringAsync("REP");
            dbService = new DBService(dbname);
            var parameters = new Dictionary<string, object>();
            try
            {
                var (items, errorMessage) = await dbService.GetDataSimple(query, parameters);
                var item = items.FirstOrDefault();
                if (item != null)
                {
                    result = new REP_Models.ActionAccess
                    {
                        ID_USER = item.ContainsKey(nameof(REP_Models.ActionAccess.ID_USER)) && item[nameof(REP_Models.ActionAccess.ID_USER)] != null ? Convert.ToDecimal(item[nameof(REP_Models.ActionAccess.ID_USER)]) : 0,
                        ID_OBJECT = item.ContainsKey(nameof(REP_Models.ActionAccess.ID_OBJECT)) && item[nameof(REP_Models.ActionAccess.ID_OBJECT)] != null ? Convert.ToDecimal(item[nameof(REP_Models.ActionAccess.ID_OBJECT)]) : 0,
                        ACTION_ADD = item.ContainsKey(nameof(REP_Models.ActionAccess.ACTION_ADD)) && item[nameof(REP_Models.ActionAccess.ACTION_ADD)] != null ? Convert.ToDecimal(item[nameof(REP_Models.ActionAccess.ACTION_ADD)]) : 0,
                        ACTION_EDIT = item.ContainsKey(nameof(REP_Models.ActionAccess.ACTION_EDIT)) && item[nameof(REP_Models.ActionAccess.ACTION_EDIT)] != null ? Convert.ToDecimal(item[nameof(REP_Models.ActionAccess.ACTION_EDIT)]) : 0,
                        ACTION_DEL = item.ContainsKey(nameof(REP_Models.ActionAccess.ACTION_DEL)) && item[nameof(REP_Models.ActionAccess.ACTION_DEL)] != null ? Convert.ToDecimal(item[nameof(REP_Models.ActionAccess.ACTION_DEL)]) : 0
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Ошибка GetActionAccess.", ex);
            }


            return result;
        }

        public async Task<string> GetCfgValueAsync(string cfgGroup, string cfgCode, string cfgParam)
        {
            var config = await _context.GLB_CONFIG
                .FirstOrDefaultAsync(c => c.CFG_GROUP == cfgGroup && c.CFG_CODE == cfgCode && c.CFG_PARAM == cfgParam);

            return config?.CFG_VALUE;
        }
        public async Task<string> GetConnectionStringAsync(string cfgCode)
        {
            string errmess = string.Empty;
            string connectionString = string.Empty;
            string appCode = await GetCfgValueAsync("CON_STR", cfgCode, "appCode");
            string tmpConString = _ConService.GetConnectionString(appCode);

            if (string.IsNullOrEmpty(tmpConString) )
            {
                return null;
                //throw new InvalidOperationException("Не удалось получить необходимые параметры для строки подключения.");
            }
            connectionString = tmpConString;
            return connectionString;
        }
        public async Task<string> GenSqrt(string code, RepositoryInfo rep)
        {
            string sqrt = @"DECLARE
                            p_ResultSet SYS_REFCURSOR;";
            string into = "into ";
            string col = "(";
            string val = "values(";
            string us = "using ";

               
            foreach (var meta in rep.Column.Where(x=>x.CODE!= "ERRORINFO"))
            {
                
                string type = string.Empty;

                switch ((meta.DATATYPE, meta.DATALENGTH, meta.DATAPRECISION))
                {
                    // Проверка типов VARCHAR2 или CHAR с ненулевой длиной
                    case var (_, datalength, _) when datalength > 0 && (meta.DATATYPE == "VARCHAR2" || meta.DATATYPE == "CHAR"):
                        type = $"{meta.DATATYPE}({datalength})";
                        break;

                    // Проверка типа NUMBER с заданной точностью и длиной
                    case var (_, datalength, precision) when datalength > 0 && precision > 0 && meta.DATATYPE == "NUMBER":
                        type = $"{meta.DATATYPE}({datalength}, {precision})";
                        break;
                    // Проверка типа NUMBER с заданной точностью 
                    case var (_, datalength, precision) when datalength > 0 && meta.DATATYPE == "NUMBER":
                        type = $"{meta.DATATYPE}({datalength})";
                        break;
                    default:
                        type = $"{meta.DATATYPE}";
                        break;
                };
                string codePart = meta.CODE.Length > 28 ? meta.CODE.Substring(0, 28) : meta.CODE;

                sqrt = sqrt + Environment.NewLine + $" l_{codePart} {type};";
                into = into + $" l_{codePart},";
                col = col + $" {meta.CODE},";
                val = val +$" :v_{codePart},";
                us= us + $" l_{codePart},";
            }
          
            sqrt = sqrt + Environment.NewLine +
                   "BEGIN" + Environment.NewLine +
                   rep.Query + Environment.NewLine +
                   "LOOP" + Environment.NewLine +
                   "FETCH p_ResultSet " + into.Remove(into.Length - 1) + ";" + Environment.NewLine +
                   "EXIT WHEN p_ResultSet%NOTFOUND;" + Environment.NewLine +
                   "EXECUTE IMMEDIATE" + Environment.NewLine +
                   $"'INSERT INTO ' || :p_Table_name || ' {col.Remove(col.Length - 1)}) {val.Remove(val.Length - 1)})'" + Environment.NewLine +
                   $"{us.Remove(us.Length - 1)};" + Environment.NewLine +
                   "END LOOP;" + Environment.NewLine +
                   "CLOSE p_ResultSet;" + Environment.NewLine +
                   "COMMIT;" + Environment.NewLine +
                   "EXCEPTION" + Environment.NewLine +
                   "WHEN OTHERS THEN" + Environment.NewLine +
                   "ROLLBACK;" + Environment.NewLine +
                   "RAISE;" + Environment.NewLine +
                   "END; ";

            return sqrt;
        }
        public async Task<RepositoryInfo> GetRepositoryInfo(SIB_Models.SIB_USERS user, string code)
        {
            RepositoryInfo rep = new RepositoryInfo();
            long? id_object = await GetIdObjectAsync(code);
            if (id_object != null) 
            {
                var infoobject = await GetInfoObjectAsync(code);
                if (infoobject != null)
                {
                    rep.Title = infoobject.Name;
                    rep.TypeObject= infoobject.TypeObject;
                    rep.AppCode = infoobject.AppCode;
                }
            }

            REP_Models.IMetadataObject metaObject = null;
            if (rep.TypeObject == "REP_TABLES" || rep.TypeObject == "SIB_TABLES" || rep.TypeObject == "REP_SPR" || rep.TypeObject == "EDIT_QUERY" || 
                rep.TypeObject == "GET_QUERY" || rep.TypeObject == "DIM" || rep.TypeObject == "GET_PROCEDURE" || rep.TypeObject == "REPORT_TEMPLATE")

            {
                metaObject = await GetMetaObjectTableAsync(id_object.Value);
            }

            if (metaObject != null)
            {
                if (rep.TypeObject == "REP_TABLES" || rep.TypeObject == "SIB_TABLES" || rep.TypeObject == "REP_SPR")
                {
                    rep.Dbname_Query = await GetConnectionStringAsync("REP");
                }
                else
                {
                    rep.Dbname_Query = await GetConnectionStringAsync(rep.AppCode);
                }
                if (metaObject is REP_Models.REP_TABLES repTables)
                {

                    rep.Query = repTables.SqlQuery;
                    //query_meta = repTables.ColQuery;
                    //param_meta = repTables.ParamQuery;
                    rep.AddSql = repTables.InsertSql;
                    rep.UpdSql = repTables.UpdateSql;
                    rep.DelSql = repTables.DeleteSql;
                    rep.ColAdd = repTables.EnableInsert;
                    rep.ColUpd = repTables.EnableUpdate;
                    rep.ColDel = repTables.EnableDelete;
                    //menudata = repTables.MenuData;
                    //cfg_data = repTables.CfgData;

                    rep.Column = JsonSerializer.Deserialize<List<REP_Models.ColumnMetadata>>(repTables.ColQuery);
                    rep.Column.Add(new ColumnMetadata { CODE = "ERRORINFO", DATATYPE = "VARCHAR2", DATALENGTH = 4000, NAME = "Предупреждение", VIEWVISIBLE = 1 });
                    rep.Param = JsonSerializer.Deserialize<List<REP_Models.ParamMetadata>>(repTables.ParamQuery);

                    if (repTables.CfgData != null)
                    {
                        rep.Cfg = JsonSerializer.Deserialize<List<REP_Models.CfgData>>(repTables.CfgData);
                    }
                    //доступ к кнопкам по правилам
                    if(!user.ISadmin)
                    {
                        decimal id_obj = (decimal)id_object.Value;
                        var res_Action = await GetActionAccess(user, id_obj);
                        rep.ColAdd = rep.ColAdd && res_Action.ACTION_ADD == 1;
                        rep.ColUpd = rep.ColUpd && res_Action.ACTION_EDIT == 1;
                        rep.ColDel = rep.ColDel && res_Action.ACTION_DEL == 1;
                    }
                }
            }
                return rep;
        }
        public async Task<long?> GetIdObjectAsync(string code)
        {
            
            return await _context.REP_OBJECT
                .Where(o => o.Code.ToUpper() == code.ToUpper())
                .Select(o => (long?)o.Id)
                .FirstOrDefaultAsync();
        }

        public async Task<string?> GetCodeObjectAsync(long id)
        {
            return await _context.REP_OBJECT
                .Where(o => o.Id == id)
                .Select(o => (string?)o.Code)
                .FirstOrDefaultAsync();
        }
        public async Task<REP_Models.REP_OBJECT> GetInfoObjectAsync(string code)
        {
            try
            {
                return await _context.REP_OBJECT
                .Where(o => o.Code.ToUpper() == code.ToUpper())
                .Select(o => new REP_Models.REP_OBJECT
                {
                    Id = o.Id,
                    Code = o.Code,
                    Name = o.Name,
                    TypeObject = o.TypeObject,
                    AppCode = o.AppCode
                })
                .FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                // Логируем ошибку
                Console.WriteLine($"Error in GetInfoObjectAsync: {ex.Message}");
                throw; // Пробрасываем исключение дальше
            }

        }

        public async Task<IMetadataObject> GetMetaObjectTableAsync(long id)
        {
            var result = await _context.REP_TABLES
                .Where(q => q.IdRepTables == id)
                .Select(q => new REP_Models.REP_TABLES
                {
                    IdRepTables = q.IdRepTables,
                    ColQuery = q.ColQuery,
                    ParamQuery = q.ParamQuery,
                    SqlQuery = q.SqlQuery,
                    InsertSql = q.InsertSql,
                    DeleteSql = q.DeleteSql,
                    UpdateSql = q.UpdateSql,
                    EnableInsert = q.EnableInsert,
                    EnableUpdate = q.EnableUpdate,
                    EnableDelete = q.EnableDelete,
                    MenuData = q.MenuData,
                    IsShortDim = q.IsShortDim,
                    Vars = q.Vars,
                    CfgData = q.CfgData

                })
                .FirstOrDefaultAsync();

            if (result != null && string.IsNullOrEmpty(result.ParamQuery))
            {
                result.ParamQuery = @"[{
                              ""COL_CODE"": ""PNAME"",
                              ""COL_NAME"": ""Наименование"",
                              ""COL_TYPE"":  ""VARCHAR2""}]";
            }

            return result;

        }

        public async Task<string> GetTransitionParamsAsync(long idRel)
        {
            var result = await _context.REP_OBJECT_REL
                .Where(o => o.IdRel == idRel)
                .Select(o => o.Params)
                .FirstOrDefaultAsync();
            return result;
        }

        public async Task<IEnumerable<REP_DATA_SRC>> GetDataSourceAsync(long idObject)
        {
            try
            {
                var dataSources = await _context.REP_DATA_SRC
                    .Where(ds => ds.IdObject == idObject)
                    .ToListAsync();

                return dataSources;
            }
            catch (Exception ex)
            {
                throw new Exception("An error occurred while getting data sources", ex);
            }
        }

        /// <summary>
        public class LogService
        {
            private DBService dbService;
            private string dbname = string.Empty;
            private Dictionary<string, Rep_Param> parameters = new Dictionary<string, Rep_Param>();

            private readonly RepService _repService;
            private string sub_session_id = string.Empty;

            // Событие для уведомления о завершении логирования
            public static event Action<decimal> OnLogCompleted;
            public LogService(RepService repService)
            {
                _repService = repService;
            }

            private async Task EnsureInitializedAsync()
            {
                if (dbService == null)
                {
                    dbname = await _repService.GetConnectionStringAsync("REP");
                    dbService = new DBService(dbname);
                    sub_session_id = Guid.NewGuid().ToString();
                }
            }

            public async Task<(bool res, string errorMessage, decimal logid)> LogI(string user_code, string app_code, string id_tab, string object_path,
                                        string type_oper, string log_info, string object_code, string main_app_code, string session_id, string session_info)
            {
                await EnsureInitializedAsync();
                decimal id_out = 0;
                bool success = false;
                string errorMessage = "";
                Dictionary<string, object> outPrm = new Dictionary<string, object>();

                parameters.Clear();
                parameters.Add("OUT_p_id", new Rep_Param ( 1, "NUMBER", ParameterDirection.Output, null, null, null));
                parameters.Add("p_user_code", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100, null, user_code));
                parameters.Add("p_app_code", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100, null, app_code));
                parameters.Add("p_main_app_code", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100, null, main_app_code));
                parameters.Add("p_id_tab", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100, null, object_code+"_"+id_tab));
                parameters.Add("p_object_path", new Rep_Param(1, "CLOB", ParameterDirection.Input, null, null, object_path));
                parameters.Add("p_type_oper", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100, null, type_oper));
                parameters.Add("p_session_id", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100, null, session_id));
                parameters.Add("p_sub_session_id", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100, null, sub_session_id));
                parameters.Add("p_session_info", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100, null, session_info));
                parameters.Add("p_log_info", new Rep_Param(1, "CLOB", ParameterDirection.Input, null, null, log_info));
                parameters.Add("p_object_code", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 100, null, object_code));


                string query = $@"
                    begin  
                     :OUT_p_id:=EWA_Log_SEQ.NextVal;

                    insert into exo_ewa_rep.LOG_OPERS(id, dts_begin, user_code, app_code, id_tab,
                                                      object_path, type_oper, session_id, status, sub_session_id, session_info,
                                                      log_info, object_code, main_app_code)
                    values(:OUT_p_id, sysdate, :p_user_code, :p_app_code, :p_id_tab,
                           :p_object_path, :p_type_oper, :p_session_id, 'W', :p_sub_session_id, :p_session_info,
                           :p_log_info, :p_object_code, :p_main_app_code
                           ); 
                    end;";

                (success, errorMessage, outPrm) = await dbService.ExecuteDataWithOut(query, parameters);
                
                decimal.TryParse(outPrm["OUT_p_id"].ToString(), out id_out);
                return (success, errorMessage, id_out);
            }

            public async Task<(bool res, string errorMessage)> LogS(decimal logid, string log_info)
            {
                await EnsureInitializedAsync();
                bool success = false;
                string errorMessage = "";
                Dictionary<string, object> outPrm = new Dictionary<string, object>();

                parameters.Clear();
                parameters.Add("p_id", new Rep_Param(1, "NUMBER", ParameterDirection.Input, null, null, logid));               
                parameters.Add("p_log_info", new Rep_Param(1, "CLOB", ParameterDirection.Input, null, null, log_info));


                string query = $@"
                    begin  
                     
                     update exo_ewa_rep.LOG_OPERS
                     set dts_end=sysdate,
                         status='S',
                         log_info=:p_log_info
                     where id=:p_id;
                    end;";

                (success, errorMessage) = await dbService.ExecuteData1(query, parameters);
                return (success, errorMessage);
            }

            public async Task<(bool res, string errorMessage)> LogS(decimal logid)
            {
                await EnsureInitializedAsync();
                bool success = false;
                string errorMessage = "";
                Dictionary<string, object> outPrm = new Dictionary<string, object>();

                parameters.Clear();
                parameters.Add("p_id", new Rep_Param(1, "NUMBER", ParameterDirection.Input, null, null, logid));

                string query = $@"
                    begin

                     update exo_ewa_rep.LOG_OPERS
                     set dts_end=sysdate,
                         status='S'
                     where id=:p_id;
                    end;";

                (success, errorMessage) = await dbService.ExecuteData1(query, parameters);

                // Уведомляем о завершении логирования
                if (success)
                {
                    OnLogCompleted?.Invoke(logid);
                }

                return (success, errorMessage);
            }

            public async Task<(bool res, string errorMessage)> LogE(decimal logid, string error_info)
            {
                await EnsureInitializedAsync();
                bool success = false;
                string errorMessage = "";
                Dictionary<string, object> outPrm = new Dictionary<string, object>();

                parameters.Clear();
                parameters.Add("p_id", new Rep_Param(1, "NUMBER", ParameterDirection.Input, null, null, logid));
                parameters.Add("p_error_info", new Rep_Param(1, "VARCHAR2", ParameterDirection.Input, 4000, null, error_info));


                string query = $@"
                    begin
                     update exo_ewa_rep.LOG_OPERS
                     set dts_end=sysdate,
                         status='E',
                         error_info=:p_error_info
                     where id=:p_id;
                    end;";

                (success, errorMessage) = await dbService.ExecuteData1(query, parameters);
                return (success, errorMessage);
            }

            public async Task<(bool res, string errorMessage)> LogC(decimal logid)
            {
                await EnsureInitializedAsync();
                bool success = false;
                string errorMessage = "";             

                parameters.Clear();
                parameters.Add("p_id", new Rep_Param(1, "NUMBER", ParameterDirection.Input, null, null, logid));
               

                string query = $@"
                    begin
                     update exo_ewa_rep.LOG_OPERS
                     set dts_end=sysdate,
                         status='C'
                     where id=:p_id;
                    end;";

                (success, errorMessage) = await dbService.ExecuteData1(query, parameters);
                return (success, errorMessage);
            }

            public async Task<string> GetLogStatus(decimal logid)
            {
                await EnsureInitializedAsync();

                parameters.Clear();
                parameters.Add("p_id", new Rep_Param(1, "NUMBER", ParameterDirection.Input, null, null, logid));

                string query = "SELECT status FROM exo_ewa_rep.LOG_OPERS";
                string pkcolumns = "id = :p_id";

                try
                {
                    var data = new Dictionary<string, object>();
                    var (success, errorMessage) = await dbService.GetDataSingle1(query, data, parameters, pkcolumns);

                    if (success && data.ContainsKey("STATUS"))
                    {
                        return data["STATUS"]?.ToString();
                    }
                    return null; // Запись не найдена или ошибка
                }
                catch
                {
                    return null; // В случае ошибки возвращаем null
                }
            }

        }
        /// </summary>
        public class MenuService
        {
            private DBService dbService;
            string dbname = string.Empty;

            private readonly RepService _repService;
            public MenuService(RepService repService)
            {
                _repService = repService;
            }
            public static readonly List<MenuRepData> RepRegistory = new()
            {
                //new MenuRepData {Id = 0,Code = "XSL", Type = "REP"},
                new MenuRepData {Id = 1,Code = "DEV", Type = "REP"},
                new MenuRepData {Id = 2,Code = "AML", Type = "REP"},
                new MenuRepData {Id = 4,Code = "SIB", Type = "SIB"},
                new MenuRepData {Id = 5,Code = "DWH", Type = "REP"},
                new MenuRepData {Id = 6,Code = "GC", Type = "REP"},
                new MenuRepData {Id = 7,Code = "WFL", Type = "WFL"},
                new MenuRepData {Id = 3, Code = "LOG", Type = "REP"}
            };   
            public string GetIconForType(string type)
            {
                return type switch
                {
                    "FOLDER" => "folder_data",
                    "EDIT_QUERY" => "edit_document",
                    "GET_QUERY" => "draft",
                    "REP_TABLES" => "manufacturing",
                    "STables" => "id_card",
                    "DIM" => "list_alt",
                    "WFLTables" => "view_timeline",
                    _ => "other_admission"
                };
            }
            public async Task<IEnumerable<MenuInfo>> GetLeftMenu(SIB_Models.SIB_USERS user, int type_menu)
            {
                IEnumerable<MenuInfo> menuInfos = Enumerable.Empty<MenuInfo>();
                string app_code = string.Empty;
                string type_code = string.Empty;
                
                app_code = RepRegistory.FirstOrDefault(x => x.Id == type_menu)?.Code;
                type_code = RepRegistory.FirstOrDefault(x => x.Id == type_menu)?.Type;

                if (!string.IsNullOrEmpty(app_code))
                {
                    if (type_code == "REP")
                    {
                        menuInfos = await GetMenuData(user, app_code);
                    }
                    else if (type_code == "SIB")
                    {
                        menuInfos = await SIBModule.GetMenuData();
                    }
                    else if (type_code == "WFL")
                    {
                        menuInfos = await WFLModule.GetMenuData();
                    }
                }
                return menuInfos;
            }
            public async Task<IEnumerable<MenuInfo>> GetMenuData(SIB_Models.SIB_USERS user, string app_code = null)
            {
                IEnumerable<MenuInfo> menuInfos;
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);

                string query = "with shortcuts_permit AS ( "+ Environment.NewLine
                              +" SELECT DISTINCT o.id AS shortcut_id "+ Environment.NewLine
                              +"   FROM REP_OBJECT_HIERARCHY h "+ Environment.NewLine
                              +"   join REP_OBJECT o ON o.id = h.id_child "+ Environment.NewLine
                              +"   join( select id_object from( select id_object, min(atype) atype from( "+ Environment.NewLine
                              +"                select p.id_object, decode(p.access_type, 'PERMIT', 1, 0) as atype "+ Environment.NewLine
                             +$"                  from SIB_POLICY p where p.id_user = {user.ID} and action_get = 1) "+ Environment.NewLine
                              +"                 group by id_object ) where atype = 1) p ON  p.id_object = o.id "+ Environment.NewLine
                             +$"  WHERE {user.IS_ADMIN} = 0 AND o.type_object != 'FOLDER' AND upper(o.app_code) = upper('{app_code}') "+ Environment.NewLine
                             + " union all SELECT DISTINCT o.id AS shortcut_id " + Environment.NewLine
                              + "   FROM REP_OBJECT_HIERARCHY h " + Environment.NewLine
                              + "   join REP_OBJECT o ON o.id = h.id_child " + Environment.NewLine
                             +$"  WHERE o.type_object != 'FOLDER' AND upper(o.app_code) = upper('{app_code}') " + Environment.NewLine
                             + $"    AND {user.IS_ADMIN} = 1 ), " + Environment.NewLine
                              + " menu AS ( "+ Environment.NewLine
                              +" SELECT h.id_parent,h.id_child,NVL(h.name_interface, o.name) AS name, "+ Environment.NewLine
                              +"        o.code AS code_object,o.type_object,h.ordernum "+ Environment.NewLine
                              +"   FROM REP_OBJECT_HIERARCHY h "+ Environment.NewLine
                              +"   join REP_OBJECT o ON o.id = h.id_child "+ Environment.NewLine
                             +$"  WHERE upper(o.app_code) = upper('{app_code}') "+ Environment.NewLine
                              +"  START WITH o.id IN (SELECT shortcut_id FROM shortcuts_permit) "+ Environment.NewLine
                              +" CONNECT BY NOCYCLE PRIOR h.id_parent = h.id_child  ) "+ Environment.NewLine
                              +"SELECT DISTINCT m.code_object AS CODE_CHILD,NVL(op.code, 'MAIN') AS CODE_PARENT,m.code_object AS CODE_OBJECT, "+ Environment.NewLine
                              +"       m.name AS NAME_OBJECT,m.type_object AS TYPE_OBJECT,m.ordernum "+ Environment.NewLine
                              +"  FROM menu m "+ Environment.NewLine
                              +"  LEFT join REP_OBJECT op ON op.id = m.id_parent "+ Environment.NewLine
                              +" order by ordernum";
                try
                {
                    var (items, errorMessage) = await dbService.GetDataSimple(query);
                    menuInfos = items.Select(item => new MenuInfo
                    {
                        CODE_CHILD = item.ContainsKey(nameof(MenuInfo.CODE_CHILD)) ? item[nameof(MenuInfo.CODE_CHILD)].ToString() : null,
                        CODE_PARENT = item.ContainsKey(nameof(MenuInfo.CODE_PARENT)) ? item[nameof(MenuInfo.CODE_PARENT)].ToString() : null,
                        CODE_OBJECT = item.ContainsKey(nameof(MenuInfo.CODE_OBJECT)) ? item[nameof(MenuInfo.CODE_OBJECT)].ToString() : null,
                        NAME_OBJECT = item.ContainsKey(nameof(MenuInfo.NAME_OBJECT)) ? item[nameof(MenuInfo.NAME_OBJECT)].ToString() : null,
                        TYPE_OBJECT = item.ContainsKey(nameof(MenuInfo.TYPE_OBJECT)) ? item[nameof(MenuInfo.TYPE_OBJECT)].ToString() : null
                    }).ToList();
                }
                catch (Exception ex)
                {
                    //throw new Exception("An error occurred while fetching menu data", ex);
                    menuInfos = new List<MenuInfo>();
                }
                return menuInfos;
            }
        }
        
        public class MenuGrid
        {
            private DBService dbService;
            string dbname = string.Empty;

            private readonly RepService _repService;
            public MenuGrid(RepService repService)
            {
                _repService = repService;
            }
            public string GetIconForType(string type)
            {
                return type switch
                {
                    "FOLDER" => "folder",
                    "EDIT_QUERY" => "list",
                    "GET_QUERY" => "pageview",
                    "REP_TABLES" => "hd",
                    "SIB_TABLES" => "shield",
                    "STables" => "id_card",
                    "WFLTables" => "view_timeline",
                    _ => "help"
                };
            }

            public async Task<IEnumerable<GRID_Menu>> GetMenuDataJS(string menudata)
            {
                IEnumerable<GRID_Menu> gridMenu;
                if (menudata != null)
                {
                    try
                    {
                        gridMenu = JsonSerializer.Deserialize<IEnumerable<GRID_Menu>>(menudata);
                        if (gridMenu == null)
                        {
                            //throw new Exception("Failed to deserialize menu data from JSON.");
                            gridMenu = Enumerable.Empty<GRID_Menu>();
                        }
                    }
                    catch (Exception ex)
                    {
                        //throw new Exception("An error occurred while processing menu data", ex);
                        gridMenu = Enumerable.Empty<GRID_Menu>();
                    }
                }
                else
                {
                    gridMenu = Enumerable.Empty<GRID_Menu>();
                }
                return gridMenu;
            }

            public async Task<IEnumerable<GRID_Menu>> GetMenuData(SIB_Models.SIB_USERS user, string code_obj)
            {
                IEnumerable<GRID_Menu> gridMenu= Enumerable.Empty<EWA.Models.REP_Models.GRID_Menu>();
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);

                string query = "select ID_REL, CODE_CHILD,CODE_PARENT,CODE_OBJECT,NAME_OBJECT,TYPE_OBJECT,KIND,CONFIRMTEXT,VISIBILITYFORMULA, AUTO_MODE from (  "+Environment.NewLine
                              +" with main_data as ( SELECT h.ID_REL,  "+Environment.NewLine
                              +"                            CASE WHEN h.shortcutnote IS NULL THEN 'MAIN'  "+Environment.NewLine
                              +"                                 ELSE 'LVL_' || ((LENGTH(h.shortcutnote) - LENGTH(REPLACE(h.shortcutnote, '\\', ''))) + 1) || '_' ||   "+Environment.NewLine
                              +"                                 REPLACE(SUBSTR(UPPER(h.shortcutnote), INSTR(h.shortcutnote, '\\', -1) + 1), ' ', '_')  END AS CODE_PARENT,  "+Environment.NewLine
                              +"                            CH.CODE as CODE_CHILD, ch.code as CODE_OBJECT, h.name as NAME_OBJECT, ch.type_object as TYPE_OBJECT, h.kind as KIND, "+Environment.NewLine 
                              +"                            h.confirmtext as CONFIRMTEXT, h.visibilityformula as VISIBILITYFORMULA, h.ordernum as lvl, h.AUTO_MODE, pr.code as CODE_P ,  "+Environment.NewLine
                              +"                            h.shortcutnote as path_column "+Environment.NewLine
                              +"                       from rep_object_rel h  "+Environment.NewLine
                              +"                       join rep_object  ch on ch.id = h.id_object_chi  "+Environment.NewLine
                              +"                       join rep_object pr on pr.id = h.id_object_par  "+Environment.NewLine
                              +"                       join( select id_object from( select id_object, min(atype) atype from(  "+Environment.NewLine
                              +"                               select p.id_object, decode(p.access_type, 'PERMIT', 1, 0) as atype  "+Environment.NewLine
                             +$"                                 from SIB_POLICY p where p.id_user = {user.ID} and action_get = 1 ) group by id_object )  "+Environment.NewLine
                              +"                              where atype = 1) p ON p.id_object = ch.id  "+Environment.NewLine
                             +$"                       where upper(pr.code) = upper('{code_obj}') and {user.IS_ADMIN} = 0  " +Environment.NewLine
                              +"                       union all SELECT h.ID_REL,  " + Environment.NewLine
                              +"                            CASE WHEN h.shortcutnote IS NULL THEN 'MAIN'  " + Environment.NewLine
                              +"                                 ELSE 'LVL_' || ((LENGTH(h.shortcutnote) - LENGTH(REPLACE(h.shortcutnote, '\\', ''))) + 1) || '_' ||   " + Environment.NewLine
                              +"                                 REPLACE(SUBSTR(UPPER(h.shortcutnote), INSTR(h.shortcutnote, '\\', -1) + 1), ' ', '_')  END AS CODE_PARENT,  " + Environment.NewLine
                              +"                            CH.CODE as CODE_CHILD, ch.code as CODE_OBJECT, h.name as NAME_OBJECT, ch.type_object as TYPE_OBJECT, h.kind as KIND, " + Environment.NewLine
                              +"                            h.confirmtext as CONFIRMTEXT, h.visibilityformula as VISIBILITYFORMULA, h.ordernum as lvl, h.AUTO_MODE, pr.code as CODE_P ,  " + Environment.NewLine
                              +"                            h.shortcutnote as path_column " + Environment.NewLine
                              +"                       from rep_object_rel h  " + Environment.NewLine
                              +"                       join rep_object  ch on ch.id = h.id_object_chi  " + Environment.NewLine
                              +"                       join rep_object pr on pr.id = h.id_object_par  " + Environment.NewLine
                             +$"                       where upper(pr.code) = upper('{code_obj}') and {user.IS_ADMIN} = 1 )" + Environment.NewLine
                              + ",virt_folders AS ( select DISTINCT CODE_P, path_column from main_data )  "+Environment.NewLine
                              +",parsed_data (parent, child, remaining_path, lvl) AS (SELECT 'MAIN' AS parent,  "+Environment.NewLine
                              +"                                                             CASE WHEN INSTR(path_column, '\\') > 0 THEN SUBSTR(path_column, 1, INSTR(path_column, '\\') - 1) ELSE path_column END AS child,  "+Environment.NewLine
                              +"                                                             CASE WHEN INSTR(path_column, '\\') > 0 THEN SUBSTR(path_column, INSTR(path_column, '\\') + 1) ELSE NULL END AS remaining_path,  "+Environment.NewLine
                              +"                                                             1 AS lvl  "+Environment.NewLine
                              +"                                                        FROM virt_folders  "+Environment.NewLine
                              +"                                                       WHERE path_column IS NOT NULL UNION ALL  "+Environment.NewLine
                              +"                                                      SELECT pd.child AS parent,  "+Environment.NewLine
                              +"                                                             CASE WHEN INSTR(pd.remaining_path, '\\') > 0 THEN SUBSTR(pd.remaining_path, 1, INSTR(pd.remaining_path, '\\') - 1) ELSE pd.remaining_path END AS child,  "+Environment.NewLine
                              +"                                                             CASE WHEN INSTR(pd.remaining_path, '\\') > 0 THEN SUBSTR(pd.remaining_path, INSTR(pd.remaining_path, '\\') + 1) ELSE NULL END AS remaining_path,  "+Environment.NewLine
                              +"                                                             pd.lvl + 1 AS lvl  "+Environment.NewLine
                              +"                                                        FROM parsed_data pd   "+Environment.NewLine
                              +"                                                       WHERE pd.remaining_path IS NOT NULL )  "+Environment.NewLine
                              +"-- ВИРТУАЛЬНЫЕ ПАПКИ  "+Environment.NewLine
                              +" SELECT DISTINCT 0 AS ID_REL,  "+Environment.NewLine
                              +"        CASE WHEN parent = 'MAIN' THEN 'MAIN' ELSE 'LVL_' || (lvl - 1) || '_' || REPLACE(UPPER(parent), ' ', '_') END AS CODE_PARENT, "+Environment.NewLine
                              +"        'LVL_' || lvl || '_' || REPLACE(UPPER(child), ' ', '_') AS CODE_CHILD,  "+Environment.NewLine
                              +"        'LVL_' || lvl || '_' || REPLACE(UPPER(child), ' ', '_') AS CODE_OBJECT, "+Environment.NewLine
                              +"        child AS NAME_OBJECT,  'FOLDER' AS TYPE_OBJECT,-1 AS KIND,NULL AS CONFIRMTEXT,NULL AS VISIBILITYFORMULA,lvl,0 AS AUTO_MODE  "+Environment.NewLine
                              +"   FROM parsed_data UNION ALL  "+Environment.NewLine
                              +"-- ОБЪЕКТЫ  "+Environment.NewLine
                              +" SELECT ID_REL,CODE_PARENT,CODE_CHILD,CODE_OBJECT,NAME_OBJECT,TYPE_OBJECT,KIND,CONFIRMTEXT, VISIBILITYFORMULA, lvl, AUTO_MODE  "+Environment.NewLine
                              +"   FROM main_data ) order by lvl, decode(type_object, 'FOLDER', 0, 1), name_object";
                try
                {
                    //gridMenu = await dbService.QueryAsync<GRID_Menu>(query);
                    var (items, errorMessage) = await dbService.GetDataSimple(query);
                    gridMenu = items.Select(item => new GRID_Menu
                    {

                        ID = item.ContainsKey("ID_REL") && item["ID_REL"] != null ? Convert.ToInt64(item["ID_REL"]) : 0,
                        CODE_CHILD = item.ContainsKey(nameof(GRID_Menu.CODE_CHILD)) ? item[nameof(GRID_Menu.CODE_CHILD)].ToString() : null,
                        CODE_PARENT = item.ContainsKey(nameof(GRID_Menu.CODE_PARENT)) ? item[nameof(GRID_Menu.CODE_PARENT)].ToString() : null,
                        CODE_OBJECT = item.ContainsKey(nameof(GRID_Menu.CODE_OBJECT)) ? item[nameof(GRID_Menu.CODE_OBJECT)].ToString() : null,
                        NAME_OBJECT = item.ContainsKey(nameof(GRID_Menu.NAME_OBJECT)) ? item[nameof(GRID_Menu.NAME_OBJECT)].ToString() : null,
                        TYPE_OBJECT = item.ContainsKey(nameof(GRID_Menu.TYPE_OBJECT)) ? item[nameof(GRID_Menu.TYPE_OBJECT)].ToString() : null,
                        KIND= item.ContainsKey("KIND") && item["KIND"] != null ? Convert.ToInt16(item["KIND"]) : 0,
                        VISIBILITYFORMULA = item.ContainsKey(nameof(GRID_Menu.VISIBILITYFORMULA)) ? item[nameof(GRID_Menu.VISIBILITYFORMULA)].ToString() : null,
                        AUTO_MODE= item.ContainsKey("AUTO_MODE") && item["AUTO_MODE"] != null ? Convert.ToInt16(item["AUTO_MODE"]) : 0
                    }).ToList();
                }
                catch (Exception ex)
                {
                    throw new Exception("An error occurred while fetching menu data", ex);
                }
                return gridMenu;
            }
        }

        public class GET_SPR
        {
            private DBService dbService;
            string dbname = string.Empty;
            long? ID_OBJECT;
            REP_Models.IMetadataObject metaObject = null;
            string _title = string.Empty;
            string _typeobj = string.Empty;
            string query = string.Empty;
            string query_meta = string.Empty;
            string param_meta = string.Empty;
            private List<REP_Models.ColumnMetadata> metadata;
            private List<REP_Models.ColumnMetadata> metadata_param;

            private readonly RepService _repService;
            public GET_SPR(RepService repService)
            {
                _repService = repService;
            }
            public string GetIconForType(string type)
            {
                return type switch
                {
                    "FOLDER" => "folder",
                    "EDIT_QUERY" => "list",
                    "GET_QUERY" => "pageview",
                    "REP_TABLES" => "hd",
                    "SIB_TABLES" => "shield",
                    "STables" => "id_card",
                    _ => "help"
                };
            }

            public async Task<(IEnumerable<SPRMetadata> sprMetadata, bool isShortDim,
                               List<ParamMetadata> metadata_param, string query)> GetSPRDataAsync(string code_spr)
            {
                IEnumerable<SPRMetadata> _sprMetadata;
                bool isShortDim = true;
                _sprMetadata = Enumerable.Empty<SPRMetadata>();
                List<REP_Models.ParamMetadata> _metadata_param = new List<ParamMetadata>();
                Enumerable.Empty<GRID_Menu>();
                dbname = await _repService.GetConnectionStringAsync("REP");
                dbService = new DBService(dbname);


                ID_OBJECT = await _repService.GetIdObjectAsync(code_spr);

                if (ID_OBJECT.HasValue)
                {
                    var infoobject = await _repService.GetInfoObjectAsync(code_spr);
                    if (infoobject != null)
                    {
                        _typeobj = infoobject.TypeObject;

                        if (_typeobj == "EDIT_QUERY" || _typeobj == "GET_QUERY" || _typeobj == "REP_TABLES" || _typeobj == "SIB_TABLES" || _typeobj == "DIM")
                        {
                            metaObject = await _repService.GetMetaObjectTableAsync(ID_OBJECT.Value);
                        }

                        if (metaObject != null)
                        {

                            if (metaObject is REP_Models.REP_TABLES repTables)
                            {

                                query = repTables.SqlQuery;
                                param_meta = repTables.ParamQuery;
                                _metadata_param = JsonSerializer.Deserialize<List<REP_Models.ParamMetadata>>(param_meta);
                                if (repTables.IsShortDim != 1)
                                {
                                    return (_sprMetadata, false, _metadata_param, query);

                                }
                            }


                            var parameters = new Dictionary<string, object>();
                            foreach (var kvp in _metadata_param.Where(x => x.CODE != "p_NameFilter"))
                            {
                                string paramName = kvp.CODE;
                                object paramValue = null;

                                parameters.Add(paramName, paramValue);
                            }
                            //_sprMetadata = await dbService.QueryAsync<SPRMetadata>(query, parameters);
                            var (items, errorMessage) = await dbService.GetDataSimple(query, parameters);

                            int i = 0;
                            _sprMetadata = items.Select(item => new SPRMetadata
                            {
                                TYPE_SPR = item.ContainsKey(nameof(SPRMetadata.TYPE_SPR)) ? item[nameof(SPRMetadata.TYPE_SPR)].ToString() : null,
                                CODE_SPR = item.ContainsKey(nameof(SPRMetadata.CODE_SPR)) ? item[nameof(SPRMetadata.CODE_SPR)].ToString() : null,
                                NAME_SPR = item.ContainsKey(nameof(SPRMetadata.NAME_SPR)) ? item[nameof(SPRMetadata.NAME_SPR)].ToString() : null
                            }).ToList();
                        }
                    }
                }
                return (_sprMetadata, true, _metadata_param, query);
            }

            public async Task<(IEnumerable<SPRShort> sprMetadata, bool isShortDim,
                                  Rep_SprLongFiltrMeta rep)> GetSPRDataAsync1(string code_spr, string dbname, string datatype)
            {
                IEnumerable<SPRShort> _sprMetadata;
                bool isShortDim = true;
                _sprMetadata = Enumerable.Empty<SPRShort>();
                List<REP_Models.ParamMetadata> _metadata_param = new List<ParamMetadata>();
                List<REP_Models.ColumnMetadata> _metadata_column = new List<ColumnMetadata>();

                string dbnameRepo = await _repService.GetConnectionStringAsync("REP");
                string dbnameData = dbname;

                DBService dbService = new DBService(dbnameRepo);


                string column_meta = "";

                ID_OBJECT = await _repService.GetIdObjectAsync(code_spr);

                if (ID_OBJECT.HasValue)
                {
                    var infoobject = await _repService.GetInfoObjectAsync(code_spr);
                    if (infoobject != null)
                    {
                        _typeobj = infoobject.TypeObject;

                        if (_typeobj == "EDIT_QUERY" || _typeobj == "GET_QUERY" || _typeobj == "REP_TABLES" || _typeobj == "SIB_TABLES" || _typeobj == "DIM")
                        {
                            metaObject = await _repService.GetMetaObjectTableAsync(ID_OBJECT.Value);
                        }

                        if (metaObject != null)
                        {

                            if (metaObject is REP_Models.REP_TABLES repTables)
                            {

                                query = repTables.SqlQuery;
                                param_meta = repTables.ParamQuery;
                                column_meta = repTables.ColQuery;
                                _metadata_param = JsonSerializer.Deserialize<List<REP_Models.ParamMetadata>>(param_meta);
                                _metadata_column = JsonSerializer.Deserialize<List<REP_Models.ColumnMetadata>>(column_meta);
                                if (repTables.IsShortDim != 1)
                                {
                                    Rep_SprLongFiltrMeta rep = new Rep_SprLongFiltrMeta { Column = _metadata_column, Param = _metadata_param, sqlQuery = query };
                                    return (_sprMetadata, false, rep);

                                }
                            }


                            var parameters = new Dictionary<string, object>();
                            foreach (var kvp in _metadata_param.Where(x => x.CODE != "p_NameFilter"))
                            {
                                string paramName = kvp.CODE;
                                object paramValue = null;

                                parameters.Add(paramName, paramValue);
                            }
                            dbService = new DBService(dbnameData);
                            var (items, errorMessage) = await dbService.GetDataSPRShort(query, parameters, datatype, _metadata_column);
                         

                            _sprMetadata = items;
                            int i = 0;

                        }
                    }
                }
                return (_sprMetadata, true, null);
            }

            public async Task<IEnumerable<SPRShort>> GetSPRDataShort(string code_spr, string db_name)
            {
                IEnumerable<SPRShort> sprMetadata = Enumerable.Empty<SPRShort>();
                List<REP_Models.ParamMetadata> metadataParams = new List<REP_Models.ParamMetadata>();
                List<REP_Models.ColumnMetadata> metadataColumns = new List<REP_Models.ColumnMetadata>();
                try
                {
                    string dbnameRepo = await _repService.GetConnectionStringAsync("REP");
                    string dbnameData = db_name;

                    DBService dbService = new DBService(dbnameRepo);
                    var idObject = await _repService.GetIdObjectAsync(code_spr);
                    if (!idObject.HasValue)
                        return sprMetadata;
                    var infoObject = await _repService.GetInfoObjectAsync(code_spr);
                    if (infoObject == null)
                        return sprMetadata;

                    string typeObject = infoObject.TypeObject;
                    REP_Models.REP_TABLES metaObject = null;
                    if (typeObject == "DIM")
                    {
                        metaObject = await _repService.GetMetaObjectTableAsync(idObject.Value) as REP_Models.REP_TABLES;
                    }

                    if (metaObject == null)
                        return sprMetadata;

                    string query = metaObject.SqlQuery;
                    string paramMetaJson = metaObject.ParamQuery;
                    string columnMetaJson = metaObject.ColQuery;

                    metadataParams = JsonSerializer.Deserialize<List<REP_Models.ParamMetadata>>(paramMetaJson);
                    metadataColumns = JsonSerializer.Deserialize<List<REP_Models.ColumnMetadata>>(columnMetaJson);

                    var parameters = new Dictionary<string, object>();
                    foreach (var param in metadataParams.Where(p => p.CODE != "p_NameFilter"))
                    {
                        parameters.Add(param.CODE, null);
                    }

                    dbService = new DBService(dbnameData);
                    var (items, errorMessage) = await dbService.GetDataDIMShort(query);

                    if (!string.IsNullOrEmpty(errorMessage))
                    {
                        Console.WriteLine($"Error: {errorMessage}");
                    }
                    else
                    {
                        sprMetadata = items;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Exception occurred: {ex.Message}");
                }

                return sprMetadata;
            }

        }



        

        public interface ISequenceService
        {
            Task<decimal> GetNextValueAsync(string sequenceName);
        }
        public class SequenceService : ISequenceService
        {
            private readonly DBContext _context;

            public SequenceService(DBContext context)
            {
                _context = context;
            }

            public async Task<decimal> GetNextValueAsync(string sequenceName)
            {
                var query = $"SELECT {sequenceName}.NEXTVAL FROM dual";

                // Получаем соединение из контекста
                var connection = _context.Database.GetDbConnection();

                // Создаем команду
                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.CommandType = System.Data.CommandType.Text;

                // Открываем соединение, если оно закрыто
                if (connection.State != System.Data.ConnectionState.Open)
                {
                    await connection.OpenAsync();
                }

                // Выполняем запрос и получаем результат
                var nextValue = await command.ExecuteScalarAsync();

                return Convert.ToDecimal(nextValue);
            }
        }
        public class ClipboardService
        {
            private readonly IJSRuntime _jsRuntime;

            public ClipboardService(IJSRuntime jsRuntime)
            {
                _jsRuntime = jsRuntime;
            }

            public async Task WriteTextAsync(string text)
            {
                await _jsRuntime.InvokeVoidAsync("navigator.clipboard.writeText", text);
            }
        }
    }
}
