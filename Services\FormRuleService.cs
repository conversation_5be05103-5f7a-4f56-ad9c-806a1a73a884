﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Oracle.ManagedDataAccess.Client;
using System.Configuration;
using System.Data;
using System.Text.Json;
using EWA.Data;
using EWA.Models;
using static EWA.Models.REP_Models;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using static EWA.Services.SIBModule;
using System.Security.Cryptography.Xml;
using DocumentFormat.OpenXml.EMMA;

namespace EWA.Services
{
    public class FormRuleService
    {
        private readonly DBContext _context;

        public FormRuleService(DBContext context)
        {
            _context = context;
        }

        public async Task<Dictionary<string, (string Value, string tattr)>> GetAttrAsync(string code_spr, string dbname)
        {
            var result = new Dictionary<string, (string Value, string tattr)>();
            string base_sql = string.Empty;
            switch (code_spr)
            {
                case "EQ_AML_CFG_IND":
                    base_sql = @"select code as CODE, name as VALUE ,'I' as TATTR, 'IND' as GATTR from aml.cfg_indicator union ALL
                                 select CODE as CODE, NAME as VALUE, 'A'  as TATTR, sql_table GATTR from aml.cfg_attribute union ALL
								 select CODE as CODE, NAME as VALUE, 'F'  as TATTR, 'FUNC' as GATTR from aml.cfg_method
								 ";
                    break;
                case "SUSERSATTR":
                    base_sql = "";
                    break;
                default:
                    base_sql = "select null as CODE, null as VALUE, null as TATTR, null as GATTR form dual";
                    break;
            }

            DBService dbService = new DBService(dbname);
            var (items, errorMessage) = await dbService.GetDataSimple(base_sql);

            if (!string.IsNullOrEmpty(errorMessage))
            {
                Console.WriteLine($"Error: {errorMessage}");
            }
            else
            {
                foreach (var item in items)
                {
                    var code = item.ContainsKey("CODE") ? item["CODE"]?.ToString() : null;
                    var value = item.ContainsKey("VALUE") ? item["VALUE"]?.ToString() : null;
                    var tattr = item.ContainsKey("TATTR") ? item["TATTR"]?.ToString() : null;

                    if (code != null && value != null && tattr != null)
                    {
                        result[code] = (value, tattr);
                    }
                }
            }
            return result;
        }

    }
}
