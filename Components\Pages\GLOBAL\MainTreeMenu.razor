﻿@attribute [Authorize]
@inherits LayoutComponentBase
@inject EWA.Services.UIService.TabService TabService
@inject EWA.Services.RepService.MenuService MenuService
@inject EWA.Services.SIBService.SecurityService Security
@inject EWA.Services.RepService AllRepService
@using EWA.Services
@using EWA.Components.Pages.SIB.UserCard

<style>
    .rz-treenode-content .rz-treenode-label {
        flex: 1;
    }
</style>

<RadzenTree @ref="tree"
            Expand="@OnExpand"
            Collapse="@OnCollapse"
            Change="@OnChange"
            Style="width: 100%; overflow: auto;"
            ItemContextMenu="@ShowContextMenu">

    @if (TreeType > 0)
    {
        if (TreeType == 1 || TreeType == 2 || TreeType == 5 || TreeType == 6)
        {
            @foreach (var root in menuList.Where(m => m.CODE_PARENT == "MAIN").SelectMany(m => menuList.Where(m2 => m2.CODE_PARENT == m.CODE_CHILD)))
            {
                @BuildTreeItem(root)
            }
        }
        if (TreeType == 4 || TreeType == 7 || TreeType == 3)
        {
            @foreach (var root in menuList.Where(m => m.CODE_PARENT == "MAIN"))
            {
                @BuildTreeItem(root)
            }
        }
    }
</RadzenTree>

@code {
    [Parameter] public int TreeType { get; set; }

    [Inject]
    protected ContextMenuService ContextMenuService { get; set; }

    //08.04.2025 Sheshko
    protected EWA.Models.SIB_Models.SIB_USERS user_Tech;
    string Val_Tech = string.Empty;
    IEnumerable<EWA.Models.REP_Models.MenuInfo> menuList = new List<EWA.Models.REP_Models.MenuInfo>();
    private Dictionary<string, bool> expandedState = new Dictionary<string, bool>();

    private string main_app_Code = string.Empty;

    void OnExpand(TreeExpandEventArgs args)
    {
        if (args.Value is EWA.Models.REP_Models.MenuInfo singleMenu)
        {
            var id = $"{singleMenu.CODE_OBJECT}#{singleMenu.CODE_PARENT}";
            expandedState[id] = true;
        }
    }
    void OnCollapse(TreeEventArgs args)
    {
        if (args.Value is EWA.Models.REP_Models.MenuInfo singleMenu)
        {
            var id = $"{singleMenu.CODE_OBJECT}#{singleMenu.CODE_PARENT}";
            expandedState[id] = false;
        }
    }
    private RadzenTree tree;

    void OnChange(TreeEventArgs args)
    {
        var selectedItems = args.Value as EWA.Models.REP_Models.MenuInfo;

        if (selectedItems != null)
        {
            var typeObject = selectedItems.TYPE_OBJECT;
            if (typeObject != "FOLDER")
            {
                if (typeObject == "XLSREPORT_TEMPLATE")
                {
                    TabService.AddTopTab("ExcelReport", selectedItems.CODE_OBJECT, selectedItems.NAME_OBJECT, "main_app_Code");
                }
                else
                {
                    if (TreeType == 1 || TreeType == 2 || TreeType == 4 || TreeType == 5 || TreeType == 6 || TreeType == 7 || TreeType == 3)
                    { TabService.AddTopTab("EditGrid", selectedItems.CODE_OBJECT, selectedItems.NAME_OBJECT, main_app_Code); }
                }
            }
            else
            {
                var id = $"{selectedItems.CODE_OBJECT}#{selectedItems.CODE_PARENT}";
                if (!expandedState.TryGetValue(id, out var current))
                {
                    current = false;
                }
                expandedState[id] = !current;
                
            }
            selectedItems = null;
            tree.ClearSelection();
            StateHasChanged();
        }
    }
    void ShowContextMenu(TreeItemContextMenuEventArgs args)
    {
        ContextMenuService.Open(args, new List<ContextMenuItem>
        {
            new ContextMenuItem { Text = "Развернуть все", Value = "ExpandAll" },
            new ContextMenuItem { Text = "Свернуть все", Value = "CollapseAll" }
        }, OnMenuItemClick);
    }

    void OnMenuItemClick(MenuItemEventArgs args)
    {
        if (args.Value.ToString() == "ExpandAll")
        {
            SetAllNodesExpanded(true);
        }
        else if (args.Value.ToString() == "CollapseAll")
        {
            SetAllNodesExpanded(false);
        }
        ContextMenuService.Close();
        StateHasChanged();
    }
    void SetAllNodesExpanded(bool expanded)
    {
        foreach (var item in menuList)
        {
            var id = $"{item.CODE_OBJECT}#{item.CODE_PARENT}";
            expandedState[id] = expanded;
        }
    }

    RenderFragment BuildTreeItem(EWA.Models.REP_Models.MenuInfo item) => __builder =>
    {
        var children = menuList.Where(m => m.CODE_PARENT == item.CODE_CHILD).ToList();
        bool isExpanded = expandedState.ContainsKey($"{item.CODE_OBJECT}#{item.CODE_PARENT}") && expandedState[$"{item.CODE_OBJECT}#{item.CODE_PARENT}"];
        if (children.Any())
        {
            <RadzenTreeItem Text="@item.NAME_OBJECT" Value="@item" Expanded="@isExpanded">
                <Template>
                    <RadzenIcon Icon="@MenuService.GetIconForType(item.TYPE_OBJECT)"
                                IconColor="@(item.TYPE_OBJECT == "FOLDER" ? "var(--rz-primary-dark)" : "var(--rz-primary-light)")" style="width: 20px; margin-right: 6px;" />
                    <b>@context.Text</b>
                </Template>
                <ChildContent>
                    @foreach (var child in children)
                    {
                        @BuildTreeItem(child)
                    }
                </ChildContent>
            </RadzenTreeItem>
        }
        else
        {
            <RadzenTreeItem Text="@item.NAME_OBJECT" Value="@item" >
                <Template>
                    <RadzenIcon Icon="@MenuService.GetIconForType(item.TYPE_OBJECT)"
                                IconColor="@(item.TYPE_OBJECT == "FOLDER" ? "var(--rz-primary-dark)" : "var(--rz-primary-light)")" style="width: 20px; margin-right: 6px;" />
                    @context.Text
                </Template>
            </RadzenTreeItem>
        }
    };

    protected override async Task OnParametersSetAsync()
    {
        main_app_Code = RepService.MenuService.RepRegistory.FirstOrDefault(x => x.Id == TreeType)?.Code;

        //08.04.2025 Sheshko
        user_Tech = await Security.GetUserById($"{Security.User.ID}");
        Val_Tech = await AllRepService.GetCfgValueAsync("APP", "PSWD", "CHEK_TECHN_PASS");

        if (Val_Tech == "1") 
        {
            if (!user_Tech.IStechPSWD)
            {
                await LoadMenuData();
            }
        } 
        else
        {
            await LoadMenuData();
        }
    }
    private async Task LoadMenuData()
    {
        menuList = await MenuService.GetLeftMenu(Security.User,TreeType);
        StateHasChanged();
    }
}