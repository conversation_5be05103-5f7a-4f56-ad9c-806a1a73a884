﻿namespace EWA.Models
{
    public class AML_Models
    {
        public class SelectField
        {
            public string Field { get; set; }
            public string Alias { get; set; }
        }

        public class MainTab
        {
            public string Tab { get; set; }
            public int? IsMain { get; set; }
            public string Alias { get; set; }
        }

        public class Join
        {
            public string Tab { get; set; }
            public string Alias { get; set; }
            public string DependsOn { get; set; }
            public string AliasDep { get; set; }
            public string Condition { get; set; }
            public string JoinType { get; set; }
        }

        public class Filter
        {
            public string Field { get; set; }
            public string Operator { get; set; }
            public List<object> Values { get; set; } = new List<object>();
            public string Format { get; set; }
            public string Parameter { get; set; }
        }
        public class Params
        {
            public string? Value { get; set; }
            public string? TypeVal { get; set; }
            public string Parameter { get; set; }
        }
        public class QueryStructure
        {
            public List<SelectField> SelectFields { get; set; }
            public List<MainTab> MainTab { get; set; }
            public List<Join> Joins { get; set; }
            public List<Filter> Filters { get; set; }
            public List<Params> Parameters { get; set; }
        }
        public class MethodMetaResult
        {
            public List<SelectField> SelectTab { get; set; }
            public List<Filter> FilterTabs { get; set; }
            public List<Params> Params { get; set; }
            public string TypeMethod { get; set; }
        }
        public class Node
        {
            public string Code { get; set; }
            public double PosX { get; set; }
            public double PosY { get; set; }
            public List<Node_Ports> Ports { get; set; } = new List<Node_Ports>();
        }

        public class Node_Ports
        {
            public string Code { get; set; }
            public double PosX { get; set; }
            public double PosY { get; set; }
        }
        public class Link_NODE
        {
            public string SrcNode { get; set; }
            public Node_Ports SrcPort { get; set; }
            public string TrgNode { get; set; }
            public Node_Ports TrgPort { get; set; }
        }
        public class AML_ATTR
        {
            public string CodeAttr { get; set; }
            public string AliasTab { get; set; }
            public string SQLTab { get; set; }
            public string SQLTablColumn { get; set; }
            public string AliasTabLnk { get; set; }
            public string SQLTabLnk { get; set; }
            public string SQLTablColumnLmk { get; set; }


        }

    }
}
