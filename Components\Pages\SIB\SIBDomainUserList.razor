﻿@attribute [Authorize]
@inject TooltipService tooltipService
@using EWA.Models
@using System
@using System.Collections.Generic
@using System.Linq
@using System.Threading.Tasks
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Ra<PERSON>zen
@using Radzen.Blazor
@using Microsoft.AspNetCore.Identity
@using Oracle.ManagedDataAccess.Client
@using EWA.Components.Pages.GLOBAL;
@using System.Data;

@inject EWA.Services.SIBService.ConnectionStringProvider _ConService;

<RadzenRow>
    <RadzenDataGrid @ref="grid"
                    AllowColumnResize="true" 
                    TItem="SIB_Models.SIBDomainUserInfo"
                    Density="Density.Compact"
                    Data="@objects"
                    AllowPaging="true"
                    PageSize="10"
                    ColumnWidth="100px"
                    Style="width:100%; height:420px;"
                    AllowFiltering="true"
                    AllowSorting="true"
                    EmptyText=@EmptyText
                    @bind-Value=@selectedItems>
        <HeaderTemplate>
            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween">
                <RadzenStack Orientation="Orientation.Horizontal" Gap="2rem">
                    <RadzenColumn>
                        <RadzenButton Icon="refresh"
                                      Click="@LoadDataGrid"
                                      Variant="Variant.Text"
                                      ButtonStyle="ButtonStyle.Base" 
                                      MouseEnter="@(args => ShowTooltip(args, "Глобальный фильтр"))" />
                    </RadzenColumn>
                </RadzenStack>
            </RadzenStack>
        </HeaderTemplate>
        <Columns>
            <RadzenDataGridColumn Frozen="true" Title="Действия" FrozenPosition="FrozenColumnPosition.Right" Width="30px">
                <Template Context="item">
                            <RadzenCheckBox @bind-Value="item.IsNew"
                                            TValue="bool"
                                            Change="@(args => OnIsAddChanged(item, args))"
                                            Disabled="item.IsAdd"/>
                </Template>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="Username" Title="Логин пользователя" />
            <RadzenDataGridColumn Property="FullUserName" Title="Логин пользователя (полный)" />
            <RadzenDataGridColumn Property="DisplayName" Title="ФИО пользователя" />
            <RadzenDataGridColumn Property="Email" Title="Email" />
            <RadzenDataGridColumn Property="Department" Title="Отдел"/>
            <RadzenDataGridColumn Title="Группы AD" Property="StrGroup" />
            <RadzenDataGridColumn Property="IsAdd" Title="Добавлен" Frozen="true" >
                <Template Context="item">
                            <RadzenCheckBox ReadOnly=true @bind-Value="item.IsAdd"/>
                </Template>
                </RadzenDataGridColumn>
        </Columns>
    </RadzenDataGrid>
    <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End" Gap="0.5rem" Style="margin-top: 1rem;">
        <RadzenButton ButtonType="ButtonType.Submit" Icon="save" Text="Добавить" Click="@SaveClick" Variant="Variant.Flat" />
        <RadzenButton ButtonStyle="ButtonStyle.Light" Text="Отмена" Click="@CancelClick" Variant="Variant.Flat" />
    </RadzenStack>
</RadzenRow>


@code {
    RadzenDataGrid<SIB_Models.SIBDomainUserInfo> grid = new();
    string USER_CODE = string.Empty;
    string USER_NAME = string.Empty;
    bool ISNADD = true;
    private FormParams _PublicParams = new FormParams();
    private EWA.Models.REP_Models.RepositoryInfo repositoryInfo = new REP_Models.RepositoryInfo();

    string EmptyText = "Нет записей для отображения";
    protected SIB_Models.SIB_USERS user;
    protected SIB_Models.SIBDomainUserInfo domuser = new();
    protected IEnumerable<SIB_Models.SIB_ROLES> roles;
    protected IEnumerable<SIB_Models.SIB_MODULES> modules;
    protected IEnumerable<string> userRoles = Enumerable.Empty<string>();
    protected IEnumerable<SIB_Models.SIBDomainGroupAppGroup> approles;
    private IList<SIB_Models.SIBDomainUserInfo> objects = new List<SIB_Models.SIBDomainUserInfo>();
    private IList<SIB_Models.SIBDomainUserInfo> selectedItems = new List<SIB_Models.SIBDomainUserInfo>();
    private IList<SIB_Models.SIBDomainUserInfo> AddsUsers = new List<SIB_Models.SIBDomainUserInfo>();
    private IList<SIB_Models.SIBDomainUserInfo> UpdUsers = new List<SIB_Models.SIBDomainUserInfo>();


    private SIB_Models.SIBDomainUserInfo selectedRow;


    [Inject] protected EWA.Services.SIBService.DomainUserService DUServ { get; set; }
    [Inject] public EWA.Services.RepService.ISequenceService Seq { get; set; }
    [Inject] protected EWA.Services.SIBService.UserService UServ { get; set; }


    protected string error;
    protected bool errorVisible;
    bool form_res = false;
    private Dictionary<string, object> _formValues = new Dictionary<string, object>();



    [Parameter] public FormParams InParams { get; set; }
    [Inject] protected IJSRuntime JSRuntime { get; set; }
    [Inject] protected NavigationManager NavigationManager { get; set; }
    [Inject] protected DialogService DialogService { get; set; }
    [Inject] protected TooltipService TooltipService { get; set; }
    [Inject] protected ContextMenuService ContextMenuService { get; set; }
    [Inject] protected NotificationService NotificationService { get; set; }
    [Inject] protected EWA.Services.SIBService.SecurityService Security { get; set; }

    [Inject] protected EWA.Services.SIBService.RulesService RServ { get; set; }

    List<REP_Models.ColumnMetadata> _metadata = new List<REP_Models.ColumnMetadata>
    {
       new REP_Models.ColumnMetadata { CODE = "ID", NAME = "Уникальный идентификатор клиента", DATATYPE = "NUMBER", DATALENGTH = 32, IS_PK = 1,VIEWVISIBLE = 0 },
                    new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "Код пользователя", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1, ISMANDATORY = 1, INSERTABLE = 1 },
                    new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ФИО пользователя", DATATYPE = "VARCHAR2", DATALENGTH = 1000, EDITABLE = 1, VIEWVISIBLE =1, ISMANDATORY = 1, INSERTABLE = 1 },
                    new REP_Models.ColumnMetadata { CODE = "EXT_LOGIN", NAME = "Внешний логин пользователя", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1, ISMANDATORY = 0},
                    new REP_Models.ColumnMetadata { CODE = "EXT_DOMAIN", NAME = "Имя домена пользователя", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1, ISMANDATORY = 0},
                    new REP_Models.ColumnMetadata { CODE = "IS_ADMIN", NAME = "Признак суперадмина", DATATYPE = "NUMBER", DATALENGTH = 1, EDITABLE =0, VIEWVISIBLE =1,  DOMAINCODE ="Bool",INSERTABLE = 0 },
                    new REP_Models.ColumnMetadata { CODE = "PROFILE_PHOTO", NAME = "Фото профиля", DATATYPE = "CLOB", VIEWVISIBLE =1, DOMAINCODE ="CLob" },
                    new REP_Models.ColumnMetadata { CODE = "POSITION", NAME = "Должность", DATATYPE = "VARCHAR2", DATALENGTH = 250, EDITABLE =1, VIEWVISIBLE =1, INSERTABLE = 1 },
                    new REP_Models.ColumnMetadata { CODE = "DEPARTMENT", NAME = "Подразделение", DATATYPE = "VARCHAR2", DATALENGTH = 250, EDITABLE =1, VIEWVISIBLE =1, INSERTABLE = 1  },
                    new REP_Models.ColumnMetadata { CODE = "PHONE", NAME = "Контактный телефон", DATATYPE = "VARCHAR2", DATALENGTH = 50, EDITABLE =1, VIEWVISIBLE =1, INSERTABLE = 1  },
                    new REP_Models.ColumnMetadata { CODE = "EMAIL", NAME = "Адрес электронной почты", DATATYPE = "VARCHAR2", DATALENGTH = 100, EDITABLE =1, VIEWVISIBLE =1, INSERTABLE = 1 },
                    new REP_Models.ColumnMetadata { CODE = "DT_LAST_LOGIN", NAME = "Дата и время последнего входа в приложение", DATATYPE = "DATE", DATALENGTH = null, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "DT_CREATE", NAME = "Дата создания", DATATYPE = "DATE", DATALENGTH = null, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "DT_CHANGE", NAME = "Дата изменения", DATATYPE = "DATE", DATALENGTH = null, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "USER_CREATE", NAME = "Кем создан", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "USER_CHANGE", NAME = "Кем изменен", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "IS_ACTIVE", NAME = "Признак активного пользователя ", DATATYPE = "NUMBER", DATALENGTH = 1, EDITABLE =0, VIEWVISIBLE =1, DOMAINCODE ="Bool" },
                    new REP_Models.ColumnMetadata { CODE = "IS_BLOCK", NAME = "Признак блокировки", DATATYPE = "NUMBER", DATALENGTH = 1, VIEWVISIBLE =1, DOMAINCODE ="Bool" },
                    new REP_Models.ColumnMetadata { CODE = "DT_BLOCK", NAME = "Дата блокировки пользователя", DATATYPE = "DATE", DATALENGTH = null, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "TYPE_BLOCK", NAME = "Тип блокировки", DATATYPE = "VARCHAR2", DATALENGTH = 10, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "LOGIN_FAIL_COUNT", NAME = "Кол-во неудачных попыток входа0", DATATYPE = "NUMBER", DATALENGTH = 10, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "IS_TECHN_PASS", NAME = "Признак использования технического пароля", DATATYPE = "NUMBER", DATALENGTH = 1, EDITABLE =0, VIEWVISIBLE =1, DOMAINCODE ="Bool" },
                    new REP_Models.ColumnMetadata { CODE = "TECHN_PASS", NAME = "Значение технического пароля", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "CN", NAME = "", DATATYPE = "DECIMAL", DATALENGTH = 100, VIEWVISIBLE =0 }
    };


    List<REP_Models.ParamMetadata> _metadata_param = new List<REP_Models.ParamMetadata>
    {
        new REP_Models.ParamMetadata
        {CODE = "USER_LOGIN",NAME = "Логин пользователя",DOMAINCODE = "CODE",DATATYPE = "VARCHAR2",ISMANDATORY = 0,DATALENGTH = 30,HIDDENVALUE = 0,ISSYSTEM = 0
        },
        new REP_Models.ParamMetadata
        {CODE = "USER_NAME",NAME = "ФИО пользователя",DOMAINCODE = "NAME",DATATYPE = "VARCHAR2",ISMANDATORY = 0,DATALENGTH = 300,HIDDENVALUE = 0,ISSYSTEM = 0
        },
        new REP_Models.ParamMetadata
        {CODE = "ISNADD",NAME = "Только новые",DOMAINCODE = "BOOL",DATATYPE = "NUMBER",ISMANDATORY = 0,DATALENGTH = 300,HIDDENVALUE = 0,ISSYSTEM = 0, DEFAULTVALUE = "1"
        }
    };

    private void ShowTooltip(ElementReference elementReference, string  mes)
    {
        tooltipService.Open(elementReference, mes);
    }

    protected override async Task OnInitializedAsync()
    {
        EmptyText = "Введите параметры";
        AddsUsers.Clear();
        UpdUsers.Clear();
        roles = await UServ.GetAllRoles();
        modules = await UServ.GetAllModules();
        approles = await DUServ.GetGroupRepAss();
        await LoadDataGrid();
    }

    private void OnIsAddChanged(SIB_Models.SIBDomainUserInfo item, bool isChecked)
    {
        item.IsNew = isChecked;

        if (isChecked)
        {
            if (!AddsUsers.Any(u => u.Username == item.Username))
            {
                AddsUsers.Add(item);
            }
        }
        else
        {
            var existing = AddsUsers.FirstOrDefault(u => u.Username == item.Username);
            if (existing != null)
            {
                AddsUsers.Remove(existing);
            }
        }
    }

    private async Task<(Dictionary<string, object> formValues, bool formRes)> FiltrRow()
    {
        string code_form = "FormGrid";
        Type componentType = AppDomain.CurrentDomain.GetAssemblies()
                                        .SelectMany(assembly => assembly.GetTypes())
                                        .FirstOrDefault(type => type.Name == code_form);

        Dictionary<string, IEnumerable<REP_Models.SPRShort>> _SprShortDict = new();
        Dictionary<string, EWA.Models.REP_Models.Rep_SprLongFiltrMeta> _SprLongDict = new();
        Dictionary<string, EWA.Models.REP_Models.Rep_SprShortSelDict> _SprSelShotDict = new();
        Dictionary<string, EWA.Models.REP_Models.Rep_SprLongSelDict> _SprSelLongDict = new();
        Dictionary<string, EWA.Models.REP_Models.Rep_Param> globalFilter = new(StringComparer.InvariantCultureIgnoreCase);


        globalFilter["USER_LOGIN"] = new EWA.Models.REP_Models.Rep_Param(_ismandatory: 0,_datatype: "VARCHAR",_direct: ParameterDirection.Input,_datalength: 30,_dataprecision: null, val: null);
        globalFilter["USER_NAME"] = new EWA.Models.REP_Models.Rep_Param(_ismandatory: 0, _datatype: "VARCHAR", _direct: ParameterDirection.Input, _datalength: 300, _dataprecision: null, val: null);
        globalFilter["ISNADD"] = new EWA.Models.REP_Models.Rep_Param(_ismandatory: 0, _datatype: "NUMBER", _direct: ParameterDirection.Input, _datalength: 1, _dataprecision: null, val: "1");

        _PublicParams._action = "GlobalFILTR";
        _PublicParams._is_tempory = false;
        _PublicParams._metadata_param = _metadata_param;
        _PublicParams._dbname = _ConService.GetConnectionString("EWAREP");
        _PublicParams._metadata = _metadata_param;
        _PublicParams._SprLongDict = _SprLongDict;
        _PublicParams._SprShortDict = _SprShortDict;
        _PublicParams._SprSelShortDict = _SprSelShotDict;
        _PublicParams._SprSelLongDict = _SprSelLongDict;
        _PublicParams._globalFilter = globalFilter;

        var result = await DialogService.OpenAsync("Фильтр пользователей домена",
                            ds => (RenderFragment)(builder =>
                                {
                                    builder.OpenComponent(0, Type.GetType(componentType.FullName));
                                    builder.AddComponentParameter(1, "InParams", _PublicParams);
                                    builder.CloseComponent();
                                }),
                            new DialogOptions { Draggable = true, Resizable = true }
                     );

        var formValues = new Dictionary<string, object>();
        bool formRes = false;
        if (result != null)
        {
            formValues = result.Values as Dictionary<string, object>;
            formRes = result.Result;
        }

        return (formValues, formRes);
    }



    protected async Task LoadDataGrid()
    {
        var (formValues, formRes) = await FiltrRow();
        if (formRes)
        {
            foreach (var item in formValues)
            {
                if (item.Key == "USER_LOGIN")
                { USER_CODE = item.Value?.ToString(); }
                if (item.Key == "USER_NAME")
                { USER_NAME = item.Value?.ToString(); }
                if (item.Key == "ISNADD")
                { ISNADD = Convert.ToDecimal(item.Value) == 1; }
            }

            var resultGrp = await DUServ.GetGroupRep();
            var resultAD = await DUServ.GetAllUsersAD(USER_CODE, USER_NAME);
            var usrSib = await DUServ.GetUserSib();
            if (resultAD != null && resultGrp != null)
            {

                var validGroupCodes = resultGrp
                    .Where(g => !string.IsNullOrWhiteSpace(g.CodeGroup) ) 
                    .Select(g => g.CodeGroupNormal);

                objects = resultAD
                    .Where(kvp => kvp.LstGroup.Any(g => validGroupCodes.Contains(g.CodeGroupNormal)))
                    .Select(kvp => new SIB_Models.SIBDomainUserInfo
                        {
                            Username = kvp.Username,
                            Domain = kvp.Domain,
                            DisplayName = kvp.DisplayName,
                            Email = kvp.Email,
                            Department = kvp.Department,
                            Title = kvp.Title,
                            LstGroup = kvp.LstGroup
                                          .Where(g => validGroupCodes.Contains(g.CodeGroupNormal))
                                          .ToList(),
                            IsAdd = usrSib.Contains(kvp.UsernameNorm?.ToUpperInvariant()) ? true : false
                        }).Where(obj => !(ISNADD && obj.IsAdd))
                        .ToList();
            }
        }
        await grid.Reload();
        StateHasChanged();
    }

    protected async Task<(bool form_res, string errorMessage)> SaveClick()
    {
        string errorMessage = null;
        bool form_res = true;
        var usersList = new List<Dictionary<string, object>>();
        /*добавляем новых*/

        foreach(var dusr in AddsUsers)
        {
            var nextValue = Seq.GetNextValueAsync("EWA_USER_SEQ").GetAwaiter().GetResult();
            var userGroups = dusr.LstGroup.Select(g => g.CodeGroup);
            var userRoles = new List<string>();
            var userModules = new List<string>();
            //userRoles = Enumerable.Empty<string>(); 
            user = new SIB_Models.SIB_USERS();

            foreach (var groupCode in userGroups)
            {
                var matchedData = approles
                    .Where(ar => ar.CodeGroup.ToLower() == groupCode.ToLower() && ar.TypeSIB == "Role")
                    .Select(ar => ar.CodeSIB.ToUpper());
                userRoles.AddRange(matchedData);
            }
            foreach (var groupCode in userGroups)
            {
                var matchedData = approles
                    .Where(ar => ar.CodeGroup.ToLower() == groupCode.ToLower() && ar.TypeSIB == "Module")
                    .Select(ar => ar.CodeSIB.ToUpper());
                userModules.AddRange(matchedData);
            }

            user.ID = nextValue;
            user.DT_CREATE = DateTime.Now;
            user.DT_CHANGE = DateTime.Now;
            user.USER_CREATE = Security.User.CODE;
            user.USER_CHANGE = Security.User.CODE;
            user.IS_ACTIVE = 0;
            user.IS_TECHN_PASS = 0;
            user.IS_BLOCK = 0;
            user.CODE = dusr.Username;
            user.NAME = dusr.DisplayName;
            user.POSITION = null;
            user.DEPARTMENT = dusr.Department;
            user.PHONE = null;
            user.EMAIL = dusr.Email;
            user.EXT_LOGIN = dusr.Username;
            user.EXT_DOMAIN = dusr.Domain;

            var assignedRoles = roles.Where(data => userRoles.Contains(data.NormCode)).ToList();
            var newRoles = assignedRoles.Select(data => new EWA.Models.SIB_Models.SIB_USERROLES_LNK
                {
                    ID_USER = user.ID,
                    ID_ROLE = data.ID,
                    DT_CHANGE = user.DT_CHANGE,
                    USER_CHANGE = user.USER_CHANGE,
                    Roles = data
                }).ToList();
            user.UserRoles = newRoles;

            var assignedModules = modules.Where(data => userRoles.Contains(data.NormCode)).ToList();
            var newModules = assignedModules.Select(data => new EWA.Models.SIB_Models.SIB_USERMODULES_LNK
                {
                    ID_USER = user.ID,
                    ID_MODULE = data.ID,
                    DT_CHANGE = user.DT_CHANGE,
                    USER_CHANGE = user.USER_CHANGE,
                    Modules = data
                }).ToList();

            user.UserModules = newModules;

            (form_res, errorMessage) = await DUServ.CreateUserAD(user);

            var userDict = new Dictionary<string, object>();
            foreach (var meta in _metadata)
            {
                var prop = typeof(SIB_Models.SIB_USERS).GetProperty(meta.CODE);
                if (prop != null)
                {
                    var value = prop.GetValue(user);
                    userDict[meta.CODE] = value;
                }
                else if (meta.CODE == "CN")
                {
                    userDict[meta.CODE] = 1;
                    
                }
                else
                {
                    userDict[meta.CODE] = null;
                }
            }
            usersList.Add(userDict);
        }

            /*обновляем существующих*/
            /*
            foreach (var dusr in UpdUsers)
            {
            var userGroups = dusr.LstGroup.Select(g => g.CodeGroup);
            var userRoles = new List<string>();
            //userRoles = Enumerable.Empty<string>();
            user = new SIB_Models.SIB_USERS();
                user = await DUServ.GetUserByCodeSib(dusr.Username);

                    foreach (var groupCode in userGroups)
                    {
                    var matchedRoles = approles
                .Where(ar => ar.CodeGroup.ToLower() == groupCode.ToLower())
            .Select(ar => ar.CodeSIB);
            userRoles.AddRange(matchedRoles);
            }

            user.DT_CHANGE = DateTime.Now;
            user.USER_CHANGE = Security.User.CODE;
            user.IS_ACTIVE = 0;
            user.IS_TECHN_PASS = 0;
            user.NAME = dusr.DisplayName;
            //user.POSITION = null;
            //user.PHONE = null;
            user.DEPARTMENT = dusr.Department;
            user.EMAIL = dusr.Email;
            user.EXT_LOGIN = dusr.FullUserName;

            var assignedRoles = roles.Where(role => userRoles.Contains(role.NormCode)).ToList();
            var newRoles = assignedRoles.Select(role => new EWA.Models.SIB_Models.SIB_USERROLES_LNK
                    {
                    ID_USER = user.ID,
                    ID_ROLE = role.ID,
                Roles = role
            }).ToList();

            user.UserRoles = newRoles;

            (form_res, errorMessage) = await DUServ.UpdateUserAD(user);
            if (form_res)
                {
                var updRoles = Enumerable.Empty<decimal>();
                updRoles = newRoles.Select(r => r.ID_ROLE).ToList();

                await UServ.UpdateUserRolesAsync(user.ID, updRoles);
        }
        }*/
        if (!form_res)
        {
            return (form_res, errorMessage);
        }
        
        DialogService.Close(new { Values = usersList, Result = form_res, errorMessage = errorMessage });
        return (form_res, errorMessage);
    }

    protected async Task CancelClick()
    {
        string errorMessage = null;
        var usersList = new List<Dictionary<string, object>>();
        DialogService.Close(new { Values = usersList, Result = form_res, errorMessage = errorMessage });
    }
}