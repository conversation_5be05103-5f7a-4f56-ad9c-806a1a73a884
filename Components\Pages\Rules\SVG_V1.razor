﻿@page "/svg_1"


<style>
    .diagram-container {
        width: 100%;
        height: @(gridHeight)px;
        border: 1px solid black;
    }
    
    .svg-container {
        width: 100%;
        height: 80vh;
        border: 1px solid black;
        overflow: hidden;
        cursor: grab;
    }

     .svg-container:active {
           cursor: grabbing;
     }

    .svg {
        width: 100%;
        height: 100%;
        user-select: none;
    }

    .toggle-btn {
        background-color: lightgray;
        border: 1px solid black;
        padding: 5px 10px;
        margin-left: 10px;
        cursor: pointer;
    }

    .delete-btn {
        background-color: transparent;
        border: none;
        color: red;
        font-size: 20px;
        font-weight: bold;
        cursor: pointer;
        padding: 0;
        margin: 0;
    }
    .delete-btn:hover {
        color: darkred;
    }
</style>

<RadzenStack Orientation="Orientation.Horizontal">
    <RadzenDataGrid @ref="grid"
                    TItem="GRID_OBJECT"
                    Density="Density.Compact"
                    Data="@objects"
                    AllowPaging="true"
                    PageSize="10"
                    ColumnWidth="100px"
                    Style="width:100%; height:420px;"
                    AllowFiltering="true"
                    AllowSorting="true"
                    @bind-Value=@selectedItems>
        <HeaderTemplate>
            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween">
                <RadzenStack Orientation="Orientation.Horizontal" Gap="2rem">
                    <RadzenColumn>
                        <RadzenButton Icon="refresh"
                                      Click="@LoadDataGrid"
                                      Variant="Variant.Text"
                                      ButtonStyle="ButtonStyle.Base" />
                    </RadzenColumn>
                </RadzenStack>
            </RadzenStack>
        </HeaderTemplate>
        <Columns>
            <RadzenDataGridColumn Title="Действие" Frozen="true" FrozenPosition="FrozenColumnPosition.Right" Width="30px">
                <Template Context="item">
                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween">
                        <RadzenColumn>
                            <RadzenButton Icon="add"
                                          Variant="Variant.Text"
                                          ButtonStyle="ButtonStyle.Base"
                                          Size="ButtonSize.ExtraSmall" />

                        </RadzenColumn>

                    </RadzenStack>
                </Template>

            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="Id" Title="ID" Frozen="true" />
            <RadzenDataGridColumn Property="Code" Title="Код" Frozen="true" />
            <RadzenDataGridColumn Property="Name" Title="Название" Frozen="true" />
        </Columns>
    </RadzenDataGrid>

</RadzenStack>


<RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End" Gap="0.5rem" Style="margin-top: 1rem;">
    <RadzenButton ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="add" 
                  Click="@(args => CreateNewNode(NodeShape.Image))"/>
    <RadzenToggleButton Icon="link_off" ToggleIcon="add_link"
                        Click=@(() => IsConnecting = !IsConnecting)
                        Variant="Variant.Text"
                        @bind-Value="isActive"
                        ButtonStyle="ButtonStyle.Base"/>
</RadzenStack>
<RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End" Gap="0.5rem" Style="margin-top: 1rem;" class="svg-container" 
               @onclick="DeselectNode" 
               @onwheel="OnZoom" 
               @onmousemove="Drag" 
               @onmouseup="StopDrag">
    <svg viewBox="@viewBox">
        <defs>
            <marker id="arrow"  refX="22" refY="5"
                    markerWidth="8" markerHeight="8" orient="auto">
                <path d="M 0 0 L 10 5 L 0 10 z" fill="red" />
            </marker>
        </defs>

        <!-- Линии соединений -->
        @foreach (var connection in Connections)
        {
            <line x1="@connection.Source.X" y1="@connection.Source.Y"
                  x2="@connection.Target.X" y2="@connection.Target.Y"
                  stroke="@(SelectedConnection == connection ? "red" : "black")"
                  stroke-width="3"
                  stroke-dasharray="@(SelectedConnection == connection ? "5,5" : "0")"
                  marker-end="url(#arrow)"
                  @onclick="() => SelectConnection(connection)"
            @onclick:stopPropagation />

            @if (SelectedConnection == connection)
            {
                <foreignObject x="@(CalculateCenterX(connection))" y="@(CalculateCenterY(connection)-10)" width="20" height="20">
                    <button @onclick="() => DeleteLink(connection)" class="delete-btn" style="border:none; background-color: transparent; font-size: 20px; cursor: pointer;">
                        ×
                    </button>
                </foreignObject>
            }
        }

        
        <!-- Узлы -->
        @foreach (var node in Nodes)
        {
            <g @onmousedown="(e) => StartNodeDrag(e, node)"
               @onclick="(e) => SelectNode(node)"
                @onclick:stopPropagation
               @ondblclick="(e) => EditNode(node)">




            @if (node.Shape == NodeShape.Circle)
            {
                    <circle stroke-dasharray="@(SelectedNode == node ? "5,5" : "0")"  cx="@node.X" cy="@node.Y" r="40" stroke="black" stroke-width="3" fill="@node.FillColor" />
            }
            else if (node.Shape == NodeShape.Image)
            {
                    <image stroke-dasharray="@(SelectedNode == node ? "5,5" : "0")" x="@(node.X - 30)" y="@(node.Y - 30)" width="60" height="60" href="@node.ImageSrc" />
            }
            else if (node.Shape == NodeShape.Square)
            {
                    <rect stroke-dasharray="@(SelectedNode == node ? "5,5" : "0")" x="@(node.X)" y="@(node.Y)" width="60" height="60" stroke="black" stroke-width="3"
                      fill="@node.FillColor"/>
            }
            else if (node.Shape == NodeShape.Diamond)
            {
                    <polygon stroke-dasharray="@(SelectedNode == node ? "5,5" : "0")" points="@($"{node.X},{node.Y - 40} {node.X - 40},{node.Y} {node.X},{node.Y + 40} {node.X + 40},{node.Y}")"
                         stroke="black" stroke-width="3" fill="@node.FillColor"/>
            
            }
            else if (node.Shape == NodeShape.Arrow)
            {
                    <polygon stroke-dasharray="@(SelectedNode == node ? "5,5" : "0")"  points="@($"{node.X - 30},{node.Y - 20} {node.X - 30},{node.Y + 20} {node.X},{node.Y + 20} {node.X},{node.Y + 30} {node.X + 40},{node.Y} {node.X},{node.Y - 30} {node.X},{node.Y - 20} {node.X - 30},{node.Y - 20}")"
                             stroke="black" stroke-width="3" fill="@node.FillColor" />

            }
            <text x="@(node.X)" y="@(node.Y-20)" text-anchor="middle" alignment-baseline="auto" >@node.Name</text>
                @if (SelectedNode == node)
                {
                    <foreignObject x="@(node.X + 40)" y="@(node.Y - 20)" width="20" height="20">
                        <button @onclick="() => DeleteNode(node)" class="delete-btn">
                            ×
                        </button>
                    </foreignObject>
                }


            </g>
        }
        
    </svg>
</RadzenStack>
@code {
    private bool isActive = true;
    private IList<GRID_OBJECT> selectedItems = new List<GRID_OBJECT>();
    private IList<GRID_OBJECT> objects = new List<GRID_OBJECT>();
    RadzenDataGrid<GRID_OBJECT> grid;
    private List<Node> Nodes = new List<Node>();
    private List<Connection> Connections = new List<Connection>();
    private Node SelectedNode;
    private Connection SelectedConnection;
    private bool IsDragging = false;
    private int OffsetX;
    private int OffsetY;
    private Node CurrentNodeBeingDragged;
    private bool IsConnecting = false;
    private int gridHeight = 400;
    [Inject] private DialogService DialogService { get; set; }
    public string ImageSrc { get; set; }
    private enum NodeShape { Circle, Square, Diamond, Arrow, Image }
    private int CountNode = 0;

    public class GRID_OBJECT
    {
        public long Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
    }

    private class Node
    {
        public string Name { get; set; }
        public int X { get; set; }
        public int Y { get; set; }
        public string FillColor { get; set; } = "red";
        public NodeShape Shape { get; set; }
        public string ImageSrc { get; set; } 
    }

    private class Connection
    {
        public Node Source { get; set; }
        public Node Target { get; set; }
    }
    private void SelectConnection(Connection connection)
    {
        if (SelectedConnection == connection)
        {
            SelectedConnection = null;
        }
        else
        {
            SelectedConnection = connection;
        }
    }
    private void CreateNewNode(NodeShape shape)
    {
        CountNode++;
        var random = new Random();
        var newNode = new Node
            {
                X = random.Next(50, 400),
                Y = random.Next(50, 400),
                Name = $"Node_{CountNode}",
                Shape = shape
            };
        if (shape == NodeShape.Image)
        {
            newNode.ImageSrc = "/images/diagram/start_wfl.svg";
        }

        Nodes.Add(newNode);
    }

    private void SelectNode(Node node)
    {
        if (IsConnecting)
        {
            if (SelectedNode == null)
            {
                SelectedNode = node;
            }
            else
            {
                if (SelectedNode != node)
                {
                    Connections.Add(new Connection { Source = SelectedNode, Target = node });
                }
                SelectedNode = null;
            }
        }
        else
        {
            SelectedNode = node;
        }
    }

    private void ChangeColor()
    {
        if (SelectedNode != null)
        {
            SelectedNode.FillColor = SelectedNode.FillColor == "red" ? "blue" : "red";
            StateHasChanged();
        }
    }

    private void StartNodeDrag(MouseEventArgs e, Node node)
    {
        IsDragging = true;
        CurrentNodeBeingDragged = node;
        OffsetX = (int)e.ClientX - node.X;
        OffsetY = (int)e.ClientY - node.Y;
    }

    private void Drag(MouseEventArgs e)
    {
        if (IsDragging && CurrentNodeBeingDragged != null)
        {
            CurrentNodeBeingDragged.X = (int)e.ClientX - OffsetX;
            CurrentNodeBeingDragged.Y = (int)e.ClientY - OffsetY;
            StateHasChanged();
        }
    }

    private void StopDrag()
    {
        IsDragging = false;
        CurrentNodeBeingDragged = null;
    }

    private void ToggleConnectionMode()
    {
        IsConnecting = !IsConnecting;
        SelectedNode = null;
    }

    private async Task EditNode(Node node)
    {
        var res = await DialogService.Confirm("Enter new color:", node.FillColor);
        if (res == true)
        {
            node.FillColor = "Black";
            StateHasChanged();
        }
    }
    private void DeselectNode()
    {
        SelectedConnection = null;
        SelectedNode = null;
        StateHasChanged();
    }
    private void DeleteNode(Node node)
    {
        Connections.RemoveAll(c => c.Source == node || c.Target == node);
        Nodes.Remove(node);
        SelectedNode = null;
        StateHasChanged();
    }
    private void DeleteLink(Connection link)
    {
        Connections.Remove(link);
        SelectedConnection = null;
        StateHasChanged();
    }
    private double CalculateCenterX(Connection connection)
    {
        return (connection.Source.X + connection.Target.X) / 2 - 10;
    }

    private double CalculateCenterY(Connection connection)
    {
        return (connection.Source.Y + connection.Target.Y) / 2 - 10;
    }
    

    private double scale = 1.0;
    private double viewBoxX = 0, viewBoxY = 0;
    private double startX, startY;
    private bool isPanning = false;

    private string viewBox => $"{Format(viewBoxX)} {Format(viewBoxY)} {Format(800 / scale)} {Format(600 / scale)}";

    private string Format(double value) => value.ToString("0.##", System.Globalization.CultureInfo.InvariantCulture);

    private void OnZoom(WheelEventArgs e)
    {
        if (!e.ShiftKey) return; // Зум работает только при зажатом Shift

        double oldScale = scale;
        if (e.DeltaY < 0) scale *= 1.1;  // Увеличение масштаба
        if (e.DeltaY > 0) scale /= 1.1;  // Уменьшение масштаба

        scale = Math.Clamp(scale, 0.5, 3);

        // Корректировка центра зума
        double zoomFactor = scale / oldScale;
        viewBoxX = (e.ClientX + viewBoxX) - e.ClientX * zoomFactor;
        viewBoxY = (e.ClientY + viewBoxY) - e.ClientY * zoomFactor;

        StateHasChanged(); // Принудительное обновление UI
    }

    private void StartPan(MouseEventArgs e)
    {
        isPanning = true;
        startX = e.ClientX;
        startY = e.ClientY;
    }

    private void DoPan(MouseEventArgs e)
    {
        if (!isPanning) return;

        double dx = (startX - e.ClientX) / scale;
        double dy = (startY - e.ClientY) / scale;

        viewBoxX += dx;
        viewBoxY += dy;

        startX = e.ClientX;
        startY = e.ClientY;

        StateHasChanged(); // Принудительное обновление UI
    }

    private void EndPan(MouseEventArgs e)
    {
        isPanning = false;
    }
    [Inject]
    private EWA.Services.AMLService _amlserv { get; set; }

    protected async Task LoadDataGrid()
    {
        /*var result = await _amlserv.GetIndWFL();

        if (result != null)
        {
            objects = result.Select(kvp => new GRID_OBJECT
                {
                    Id = kvp.Value.id,
                    Code = kvp.Value.code,
                    Name = kvp.Value.name
                }).ToList();
        }
        */
        await grid.Reload();
        StateHasChanged();

    }

}

