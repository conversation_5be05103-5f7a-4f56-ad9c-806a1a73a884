@using EWA.Models
@using EWA.Services
@using Oracle.ManagedDataAccess.Client
@using EWA.Components.Pages.GLOBAL;

@rendermode InteractiveServer
@inject DialogService DialogService
@inject NavigationManager Navigation
@inject RepService _repService
@inject EWA.Services.RepService.GET_SPR _getspr
@inject DialogService DialogService
@inject SIBService.UserService UServ
@inject SIBService.RulesService RServ
@inject EWA.Services.FormServices _formService




<RadzenStack>
    <RadzenTemplateForm TItem="EWA.Models.SIB_Models.SIB_USERS" Data="@user" Visible="@(user != null)" Submit="@SaveAsync">
    <RadzenCard Gap="0.5rem">
        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.End" JustifyContent="JustifyContent.Stretch" Wrap="FlexWrap.Wrap" Gap="0.5rem">
            @foreach (REP_Models.ColumnMetadata meta in InParams._metadata)
            {// 250409 Terentev
                if (meta.EDITABLE == 1)
                {
                    <RadzenFormField Text=@meta.NAME Variant="Variant.Flat" Style="width: 100%;">
                        <ChildContent>
                            @if (!string.IsNullOrEmpty(meta.COL_REF))
                            {
                                <RadzenDropDown TValue="string"
                                Data="GetLoadedReferenceData(meta.COL_REF)"
                                ValueChanged="@((value) => SetValue(meta.CODE, value))"
                                Value="@GetValue(meta.CODE)"
                                TextProperty="NAME_SPR"
                                ValueProperty="CODE_SPR"
                                Disabled="@(meta.EDITABLE != 1)" />
                            }
                            else if (meta.DATATYPE == "VARCHAR2")
                            {
                                <RadzenTextBox Value="@GetValue(meta.CODE)" TValue="string"
                                ValueChanged="@((value) => SetValue(meta.CODE, value))"
                                               Disabled="@(meta.EDITABLE != 1)"                     MaxLength=@meta.DATALENGTH/>
                            }
                            else if (meta.DATATYPE == "NUMBER")
                            {
                                <RadzenNumeric TValue="decimal?" ShowUpDown="false"
                                Value="@GetValueNum(meta.CODE)"
                                ValueChanged="@((value) => SetValueNum(meta.CODE, value))"
                                               Disabled="@(meta.EDITABLE != 1)" MaxLength=@meta.DATALENGTH />
                            }
                            else if (meta.DATATYPE == "DATE")
                            {

                                <RadzenDatePicker DateFormat="dd.MM.yyyy"
                                Value=@GetValueDT(meta.CODE)
                                ValueChanged="@((DateTime valueDT) => SetValueDT(meta.CODE, valueDT))"
                                                  Disabled="@(meta.EDITABLE != 1)">
                                </RadzenDatePicker>

                            }
                            else if (meta.DATATYPE == "CLOB")
                            {
                                <RadzenTextArea Value="@GetValue(meta.CODE)" Cols="21"
                                ValueChanged="@((value) => SetValue(meta.CODE, value))"
                                                Disabled="@(meta.EDITABLE != 1)" />
                            }
                            <!-- добавить для разных типов-->
                        </ChildContent>
                        <Helper>
                            @if (_validator.ContainsKey(meta.CODE))
                            {
                                <RadzenText TextStyle="TextStyle.Caption" Style=@($"color:var({@_validator[meta.CODE].color})")>@_validator[meta.CODE].message</RadzenText>
                            }
                            else if (meta.ISMANDATORY == 1)
                            {
                                <RadzenText TextStyle="TextStyle.Caption" Style=@($"color:var(--rz-info)")>* обязательно для заполнения</RadzenText>
                            }
                        </Helper>
                    </RadzenFormField>
                }
            }
            <RadzenFormField Text="Роли пользователя" Variant="Variant.Flat" Style="width: 100%;">
                <ChildContent>
                    <RadzenDropDown Data="@roles" 
                                    Multiple="true" 
                                    style="width: 100%" 
                                    TextProperty="NAME" 
                                    @bind-Value="@userRoles" 
                                    ValueProperty="ID" 
                                    Name="Roles" />
                </ChildContent>
            </RadzenFormField>
            <RadzenFormField Text="Модули пользователя" Variant="Variant.Flat" Style="width: 100%;">
                <ChildContent>
                    <RadzenDropDown Data="@modules"
                                    Multiple="true"
                                    style="width: 100%"
                                    TextProperty="NAME"
                                    @bind-Value="@userModules"
                                    ValueProperty="ID"
                                    Name="Modules" SelectedItemsText="записей выбрано" />
                </ChildContent>
            </RadzenFormField>
        </RadzenStack>
    </RadzenCard>

    <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End">
            <RadzenButton ButtonType="ButtonType.Submit" Text="@InParams._butsub_name" Variant="Variant.Flat" />
            <RadzenButton ButtonStyle="ButtonStyle.Light" Text="Отмена" Click="@CloseAsync" Variant="Variant.Flat" />
    </RadzenStack>
  </RadzenTemplateForm>
</RadzenStack>

@code {
    [Inject]
    protected EWA.Services.SIBService.SecurityService Security { get; set; }
    [Parameter] public FormParams InParams { get; set; }
    private string ErrorMessage = String.Empty;
    protected EWA.Models.SIB_Models.SIB_USERS user;
    protected IEnumerable<EWA.Models.SIB_Models.SIB_ROLES> roles;
    protected IEnumerable<decimal> userRoles = Enumerable.Empty<decimal>();
    protected IEnumerable<EWA.Models.SIB_Models.SIB_MODULES> modules;
    protected IEnumerable<decimal> userModules = Enumerable.Empty<decimal>();

    private decimal InUserId { get; set; }

    private Dictionary<string, IEnumerable<REP_Models.SPRMetadata>> _referenceData = new();
    bool form_res = false;
    private Dictionary<string, object> _formValues = new Dictionary<string, object>();

    //250410 Terentev

    private Dictionary<string, EWA.Models.REP_Models.Rep_Validator> _validator = new();





    protected override async Task OnInitializedAsync()
    {
        //InUserId = (decimal)InParams._seldata["ID"];
        object value = InParams._seldata["ID"];
        decimal InUserId = 0;
        if (value != null && decimal.TryParse(value.ToString(), out var result))
        {
            InUserId = result;
        }

        user = await UServ.GetUserById(InUserId);
        await base.OnInitializedAsync();
        InitializeFormValues();

        roles = await UServ.GetAllRoles();
        userRoles = await UServ.GetUserRolesById(InUserId);
        modules = await UServ.GetAllModules();
        userModules = await UServ.GetUserModulesById(InUserId);
    }

    private string GetValue(string key)
    {
        return _formValues.TryGetValue(key, out var value) && value is string str ? str : string.Empty;
    }

    private void SetValue(string key, string value)
    {
        _formValues[key] = string.IsNullOrWhiteSpace(value) ? null : value;
    }
    private decimal? GetValueNum(string key)

    // 250415 Terentev
    {
        if (!_formValues.ContainsKey(key))
            return null;
        if (_formValues[key] == DBNull.Value || _formValues[key] == null)
            return null;

        return Convert.ToDecimal(_formValues[key]);
    }

    private void SetValueNum(string key, decimal? value)
    {
        _formValues[key] = value;
    }

    private DateTime? GetValueDT(string key)
    {
        if (_formValues.TryGetValue(key, out var valueDT) && valueDT is DateTime date)
        {
            return date;
        }
        return null;
    }

    private void SetValueDT(string key, DateTime valueDT)
    {
        if (_formValues.ContainsKey(key))
        {
            _formValues[key] = valueDT;
        }
        else
        {
            _formValues.Add(key, valueDT);
        }
    }
    private void SetValueDT_(string key, DateTime valueDT)
    {
        string formattedDate = valueDT.ToString("dd.MM.yyyy HH:mm:ss");
        if (_formValues.ContainsKey(key))
        {
            _formValues[key] = formattedDate;
        }
        else
        {
            _formValues.Add(key, formattedDate);
        }
    }

    // 250414 Terentev
    private async Task<(Dictionary<string, object> formValues, bool form_res, string errorMessage)> SaveAsync()
    {
        string errorMessage = null;
        bool form_res = true;
        var formValues = new Dictionary<string, object>(_formValues);
        bool isValidator = true;
        try
        {
                _validator = new();
                _formService.InitValidatorColor(InParams._metadata, _validator);
                isValidator = _formService.CheckValidator(InParams._metadata, _validator, formValues);
                if (isValidator == false)
                {
                    return (formValues, false, "");
                }
                foreach (var meta in InParams._metadata)
                {
                    if (!_formValues.ContainsKey(meta.CODE))
                    {
                        _formValues[meta.CODE] = null;
                    }
                }
        }
        catch (Exception ex)
        {
                DialogService.Close(new { Values = formValues, Result = form_res});
                errorMessage = $"General Error: {ex.Message}";
                return (formValues, false, errorMessage);
        }

        try
        {
            var dtnow = DateTime.Now;
            var usrnow = Security.User.CODE;
            formValues["DT_CHANGE"] = dtnow;
            formValues["USER_CHANGE"] = usrnow;
            user.NAME = formValues["NAME"] is null ? null : formValues["NAME"].ToString();
            user.POSITION = formValues["POSITION"] is null ? null : formValues["POSITION"].ToString();
            user.DEPARTMENT = formValues["DEPARTMENT"] is null ? null : formValues["DEPARTMENT"].ToString();
            user.PHONE = formValues["PHONE"] is null ? null : formValues["PHONE"].ToString();
            user.EMAIL = formValues["EMAIL"] is null ? null : formValues["EMAIL"].ToString();
            user.DT_CHANGE = dtnow;
            user.USER_CHANGE = usrnow;

            var assignedRoles = roles.Where(items => userRoles.Contains(items.ID)).ToList();
            var assignedModules = modules.Where(items => userModules.Contains(items.ID)).ToList();
            var newRoles = assignedRoles.Select(itemr => new EWA.Models.SIB_Models.SIB_USERROLES_LNK
                {
                    ID_USER = user.ID,
                    ID_ROLE = itemr.ID,
                    DT_CHANGE = dtnow,
                    USER_CHANGE = usrnow,
                    Roles = itemr
                }).ToList();
            var newModules = assignedModules.Select(itemm => new EWA.Models.SIB_Models.SIB_USERMODULES_LNK
                {
                    ID_USER = user.ID,
                    ID_MODULE = itemm.ID,
                    DT_CHANGE = dtnow,
                    USER_CHANGE = usrnow,
                    Modules = itemm
                }).ToList();
            user.UserRoles = newRoles;
            user.UserModules = newModules;
        }
        catch (Exception ex)
        {
            DialogService.Close(new { Values = formValues, Result = form_res });
            errorMessage = $"General Error: {ex.Message}";
            return (formValues, false, errorMessage);
        }
        if (1 == 1)
        {
#pragma warning disable CS4014
            InvokeAsync(async () =>
            {
                await Task.Delay(1000);
                if (formValues.TryGetValue("CODE", out var userCode))
                {
                    isValidator = _formService.CheckValidator(InParams._metadata, _validator, formValues);
                    if (isValidator)
                    {
                        (form_res, errorMessage) = await UServ.CreateUpdateUser(user, InParams._sql);
                    }
                }
                DialogService.Close();
            });
            #pragma warning restore CS4014
            await BusyDialog();
            if (!form_res)
            {
                string errorTitle = "Ошибка редактирования";
                await DialogService.OpenAsync<ErrorGrid>(errorTitle,
                                          new Dictionary<string, object> { { "_errorMessage", errorMessage } },
                                          new DialogOptions { Draggable = true, Resizable = true }
                 );
                ErrorMessage = errorMessage;
                return (formValues, false, ErrorMessage);
            }
        }
        DialogService.Close(new { Values = formValues, Result = form_res, errorMessage = errorMessage });
        return (formValues, form_res, errorMessage);
    }
    async Task BusyDialog()
    {
        await DialogService.OpenAsync("", ds =>
    @<RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.End" JustifyContent="JustifyContent.Stretch" Wrap="FlexWrap.Wrap" Gap="0.5rem">
        <RadzenProgressBarCircular ProgressBarStyle="ProgressBarStyle.Primary" Value="100" ShowValue="false" Mode="ProgressBarMode.Indeterminate" Size="ProgressBarCircularSize.Small" />
    </RadzenStack>
    , new DialogOptions()
    {
        ShowTitle = false,
        Style = "min-height:auto;min-width:auto;width:auto;background-color:transparent",
        CloseDialogOnEsc = false,
        Draggable = true,
        Resizable = true
    });
    }
    private async Task<(Dictionary<string, object> formValues, bool form_res, string errorMessage)> CloseAsync()
    {
        string errorMessage = null;
        var formValues = new Dictionary<string, object>(_formValues);
        form_res = false;
        DialogService.Close(new { Values = formValues, Result = form_res, errorMessage = errorMessage });
        return (formValues, form_res, errorMessage);
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadReferenceDataAsync();
    }

    private async Task LoadReferenceDataAsync()
    {
        foreach (REP_Models.ColumnMetadata meta in InParams._metadata)
        {
            if (!string.IsNullOrEmpty(meta.COL_REF))
            {
                _referenceData[meta.COL_REF] = await GetReferenceDataAsync(meta.COL_REF);
            }
        }
    }

    private async Task<IEnumerable<REP_Models.SPRMetadata>> GetReferenceDataAsync(string Code)
    {
        var (sprMetadata, isShortDim, metadata_param, query) = await _getspr.GetSPRDataAsync(Code);
        return sprMetadata;
    }

    private IEnumerable<REP_Models.SPRMetadata> GetLoadedReferenceData(string Code)
    {
        return _referenceData.TryGetValue(Code, out var data) ? data : Enumerable.Empty<REP_Models.SPRMetadata>();
    }

    private void InitializeFormValues()
    {
        foreach (var keyValuePair in InParams._seldata)
        {
            _formValues[keyValuePair.Key] = keyValuePair.Value;
        }
    }

}