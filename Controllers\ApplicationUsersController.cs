using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.AspNetCore.OData.Routing.Controllers;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.OData.Results;
using Microsoft.AspNetCore.OData.Deltas;
using System.ComponentModel.DataAnnotations.Schema;

using EWA.Data;
using EWA.Models;
using Microsoft.EntityFrameworkCore;

namespace EWA.Controllers
{
    
    [Authorize]
    [Route("odata/Identity/susers")]
    public partial class ApplicationUsersController : ODataController
    {
        private readonly DBContext context;
        private readonly UserManager<SIB_Models.SIB_USERS> userManager;
        partial void OnUserUpdated(SIB_Models.SIB_USERS user);
        public ApplicationUsersController(DBContext context, UserManager<SIB_Models.SIB_USERS> userManager)
        {
            this.context = context;
            this.userManager = userManager;
        }

        partial void OnUsersRead(ref IQueryable<SIB_Models.SIB_USERS> users);

        [EnableQuery]
        [HttpGet]
        public IEnumerable<SIB_Models.SIB_USERS> Get()
        {
            var users = userManager.Users;
            OnUsersRead(ref users);

            return users;
        }

        [EnableQuery]
        [HttpGet("{key}")]
        public SingleResult<SIB_Models.SIB_USERS> GetApplicationUser(string key)
        {
            var user = context.SIB_USERS.Where(i => i.ID.ToString() == key)
                                        .Include(u => u.UserRoles)
                                        .Include(m => m.UserModules);
            return SingleResult.Create(user);
        }



        [HttpPatch("{key}")]
        public async Task<IActionResult> Patch(string key, [FromBody] SIB_Models.SIB_USERS data)
        {
            var user = await userManager.FindByIdAsync(key);

            if (user == null)
            {
                return NotFound();
            }
            OnUserUpdated(data);
            IdentityResult result = null;
            user.UserRoles = null;
            result = await userManager.UpdateAsync(user);
            if (data.UserRoles != null)
            {
                result = await userManager.RemoveFromRolesAsync(user, await userManager.GetRolesAsync(user));

                if (result.Succeeded) 
                {
                    result = await userManager.AddToRolesAsync(user, data.UserRoles.Select(r => r.ID_ROLE.ToString()));
                }
            }

            if (!string.IsNullOrEmpty(data.PASSWORDHASH))
            {
                result = await userManager.RemovePasswordAsync(user);

                if (result.Succeeded)
                {
                    result = await userManager.AddPasswordAsync(user, data.PASSWORDHASH);
                }

                if (!result.Succeeded)
                {
                    return IdentityError(result);
                }
            }

            if (result != null && !result.Succeeded)
            {
                return IdentityError(result);
            }

            return new NoContentResult();
        }
        
        private IActionResult IdentityError(IdentityResult result)
        {
            var message = string.Join(", ", result.Errors.Select(error => error.Description));

            return BadRequest(new { error = new { message } });
        }
    }
}