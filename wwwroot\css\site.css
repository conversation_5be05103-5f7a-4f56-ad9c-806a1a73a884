@font-face {
    font-family: 'Nunito Sans';
    src: url('../fonts/NunitoSans-Regular.eot');
    src: url('../fonts/NunitoSans-Regular.eot?#iefix') format('embedded-opentype'),
        url('../fonts/NunitoSans-Regular.woff2') format('woff2'),
        url('../fonts/NunitoSans-Regular.woff') format('woff'),
        url('../fonts/NunitoSans-Regular.ttf') format('truetype'),
        url('../fonts/NunitoSans-Regular.svg#NunitoSans-Regular') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Nunito Sans';
    src: url('../fonts/NunitoSans-Bold.eot');
    src: url('../fonts/NunitoSans-Bold.eot?#iefix') format('embedded-opentype'),
        url('../fonts/NunitoSans-Bold.woff2') format('woff2'),
        url('../fonts/NunitoSans-Bold.woff') format('woff'),
        url('../fonts/NunitoSans-Bold.ttf') format('truetype'),
        url('../fonts/NunitoSans-Bold.svg#NunitoSans-Bold') format('svg');
    font-weight: bold;
    font-style: normal;
}

@font-face {
    font-family: 'Nunito Sans';
    src: url('../fonts/NunitoSans-Italic.eot');
    src: url('../fonts/NunitoSans-Italic.eot?#iefix') format('embedded-opentype'),
        url('../fonts/NunitoSans-Italic.woff2') format('woff2'),
        url('../fonts/NunitoSans-Italic.woff') format('woff'),
        url('../fonts/NunitoSans-Italic.ttf') format('truetype'),
        url('../fonts/NunitoSans-Italic.svg#NunitoSans-Italic') format('svg');
    font-weight: normal;
    font-style: italic;
}

@font-face {
    font-family: 'Nunito Sans';
    src: url('../fonts/NunitoSans-BoldItalic.eot');
    src: url('../fonts/NunitoSans-BoldItalic.eot?#iefix') format('embedded-opentype'),
        url('../fonts/NunitoSans-BoldItalic.woff2') format('woff2'),
        url('../fonts/NunitoSans-BoldItalic.woff') format('woff'),
        url('../fonts/NunitoSans-BoldItalic.ttf') format('truetype'),
        url('../fonts/NunitoSans-BoldItalic.svg#NunitoSans-BoldItalic') format('svg');
    font-weight: bold;
    font-style: italic;
}

#exon-tab {
    border: 2px solid var(--rz-base-300);
    border-radius: 3px;
    border-width: thin;
    
}

#exon-radius {
    border-radius: var(--rz-menu-border-radius);
}

#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}

:root, .rz-software{
    --rz-text-font-family: 'Nunito Sans', "Source Sans Pro", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    --rz-primary: #2066b0;
    --rz-primary-light: #89a2ce;
    --rz-primary-lighter: #b2c2e1;
    --rz-primary-dark: #19549b;
    --rz-primary-darker: #114286;
    --rz-danger: #832452;
    --rz-danger-light: #b18595;
    --rz-danger-lighter: #ceb0bb;
    --rz-danger-dark: #6e1a40;
    --rz-danger-darker: #59102f;
}
    .rz-software-dark {
    --rz-primary: #2066b0;
    --rz-primary-light: #19549b;
    --rz-primary-lighter: #114286;
    --rz-primary-dark: #89a2ce;
    --rz-primary-darker: #b2c2e1;
    --rz-danger: #832452;
    --rz-danger-light: #6e1a40;
    --rz-danger-lighter: #59102f;
    --rz-danger-dark: #b18595;
    --rz-danger-darker: #ceb0bb;
    }


}

body {
    font-family: var(--rz-text-font-family);
    color: var(--rz-text-color);
    font-size: var(--rz-body-font-size);
    line-height: var(--rz-body-line-height);
    background-color: var(--rz-body-background-color);
}

.rz-body {
    --rz-body-padding: 0;
}

a {
    color: var(--rz-link-color);
}

a:hover,
a:focus {
    color: var(--rz-link-hover-color);
}

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

.blazor-error-boundary::after {
    content: "An error has occurred."
}

.loading-progress {
    position: relative;
    display: block;
    width: 8rem;
    height: 8rem;
    margin: 20vh auto 1rem auto;
}

.loading-progress circle {
    fill: none;
    stroke: #e0e0e0;
    stroke-width: 0.6rem;
    transform-origin: 50% 50%;
    transform: rotate(-90deg);
}

.loading-progress circle:last-child {
    stroke: #1b6ec2;
    stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
    transition: stroke-dasharray 0.05s ease-in-out;
}

.loading-progress-text {
    position: absolute;
    text-align: center;
    font-weight: bold;
    inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
}

.loading-progress-text:after {
    content: var(--blazor-load-percentage-text, "Loading");
}

/* 20.01.2025 */
.excel-loading {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,.7);
    z-index: 1;
    min-height: 50px;
}

.excel-loading-content {
    position: absolute;
    left: 50%;
    top: 15%;
    transform: translate(-50%,-50%);
}

.excel-container {
    position: relative;
    min-height: 50px;
}

.rzi-circle-o-notch::before {
    content: "refresh";  /* название Material icon */
    display: inline-block;
    font-size: 2rem;  /* размер иконки */
    animation: rotate 0.5s linear infinite;  /* 0.5s - скорость анимации, значение меньше - быстрее */        
}

@keyframes rotate {
    to { transform: rotate(360deg); }
}

/* Стили для окна блокировки */
.session-lock-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-color: rgba(0, 0, 0, 0.8) !important;
    z-index: 9999 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.session-lock-dialog {
    background-image: linear-gradient(to left, var(--rz-base-500), var(--rz-secondary-darker)) !important;
    padding: 2rem !important;
    max-width: 400px !important;
    width: 100% !important;
}

.session-lock-dialog h3 {
    margin-bottom: 1rem !important;
    font-size: 1.5rem !important;
    color: white !important;
}

.session-lock-dialog p {
    color: white !important;
    margin-bottom: 1rem !important;
}

.session-lock-dialog input {
    margin: 1rem 0 !important;
    padding: 0.75rem !important;
    width: 100% !important;
    border-radius: 4px !important;
    border: 1px solid var(--rz-base-300) !important;
    background-color: white !important;
    color: black !important;
}

.session-lock-dialog button {
    margin: 0.5rem !important;
    padding: 0.75rem 1.5rem !important;
    background-color: var(--rz-primary) !important;
    color: white !important;
    border: none !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-weight: bold !important;
}

.session-lock-dialog button:hover {
    background-color: var(--rz-primary-dark) !important;
}

.password-error {
    color: var(--rz-danger) !important;
    margin-bottom: 0.5rem !important;
    font-size: 0.875rem !important;
}
