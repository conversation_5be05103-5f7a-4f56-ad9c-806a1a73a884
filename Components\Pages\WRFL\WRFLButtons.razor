﻿@attribute [Authorize]

@inject TooltipService tooltipService
@inject DialogService DialogService
@inject WFKLService _wflService

@using <PERSON><PERSON><PERSON>
@using System.Collections
@using EWA.Enums
@using EWA.Models
@using EWA.Services

@*придумать свойство для StartWorkflow*@
@if (TabRef.CodeObj == "WWORKFLOW")
{
    <RadzenButton EWAType="Button" EWAID="WFL1" ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="policy" Click="StartWRKFL"
                  MouseEnter="@(args => ShowTooltip(args, "Запустить процесс"))" />
}
@if (TabRef.CodeObj == "WACTIVATION")
{
    <RadzenButton EWAType="Button" EWAID="WFL2" ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="stop_circle" Click="@(() => ActionWAction(1))" Disabled="@check_wAction(1)"
                  MouseEnter="@(args => ShowTooltip(args, "Остановить процесс"))" />

    <RadzenButton EWAType="Button" EWAID="WFL3" ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="play_pause" Click="@(() => ActionWAction(2))" Disabled="@check_wAction(2)"
                  MouseEnter="@(args => ShowTooltip(args, "Пауза"))" />
}

@code{
    [Parameter] public IList<IDictionary<string, object>> selectedItems { get; set; }
    [Parameter] public TabData TabRef { get; set; }
    [Parameter] public RadzenDataGrid<IDictionary<string, object>> GridRef { get; set; }
    [Parameter] public EventCallback OnChanged { get; set; }
    [Parameter] public Dictionary<string, object> selectedRowData { get; set; }
    [Parameter] public Func<Task> ReloadDef { get; set; }
    [Parameter] public IEnumerable<IDictionary<string, object>> queryResults { get; set; }
    [Parameter] public int Count { get; set; }
    [Parameter] public FormParams InParams { get; set; }
    [Parameter] public EventCallback<(IEnumerable<IDictionary<string, object>>, int)> QueryResultsChanged { get; set; }

    [Inject] protected TooltipService TooltipService { get; set; }

    void ShowTooltip(ElementReference elementReference, string buttomName, TooltipOptions options = null) => tooltipService.Open(elementReference, buttomName, options);


    private async Task StartWRKFL()
    {
        string taskCode;
        string status = "Ожидайте...";
        if (selectedItems?.Count > 0 && selectedItems[0].ContainsKey("CODE"))
        {
            taskCode = selectedItems[0]["CODE"]?.ToString();

            if (!string.IsNullOrEmpty(taskCode))
            {
                await DialogService.OpenAsync<WRFL.WRFL_StatusDiag>("Статус активации задания",
                   new Dictionary<string, object>() { { "TaskCode", taskCode } },
                   new DialogOptions() { CloseDialogOnOverlayClick = true }
                );
            }
        }
        else
        {
            await DialogService.OpenAsync("Статус активации задания", ds =>
    @<div>Ошибка запуска. Для запуска задания, выберите его из списка</div>
       , new DialogOptions() { CloseDialogOnOverlayClick = true });
        }
    }

    private async Task ActionWAction(int idact)
    {
        decimal id = Convert.ToDecimal(selectedItems[0]["ID"]);
        decimal act_status = 0;
        var formValues = new Dictionary<string, object>();
        bool resB = false;
        if (idact == 1)
        {
            var res = await _wflService.StopWFL(id);
            resB = res.is_res;
        }
        else if (idact == 2)
        {
            string status_id = selectedItems[0]["STATUS_ID"].ToString();
            if (int.TryParse(status_id, out int statusInt))
            {
                if (statusInt == 2)
                {
                    act_status = 6;
                }
                else if (statusInt == 6)
                {
                    act_status = 2;
                }
                var res = await _wflService.PauseWFL(id, act_status);
                resB = res.is_res;
            }
        }

        if (resB)
        {
            await NotifyChangedAsync();
        }
    }
    private bool check_wAction(int idcheck)
    {
        bool check = false;
        if (selectedItems.Count() == 0)
        {
            check = true;
        }
        else
        {
            string status_id = selectedItems[0]["STATUS_ID"].ToString();
            if (int.TryParse(status_id, out int statusInt))
            {
                if (idcheck == 1 && (statusInt == 3 || statusInt == 4 || statusInt == 5))
                {
                    check = true;
                }
                else if (idcheck == 2 && (statusInt < 2 || statusInt == 3 || statusInt == 4 || statusInt == 5))
                {
                    check = true;
                }
            }
        }
        return check;
    }

    private async Task NotifyChangedAsync()
    {
        if (GridRef is not null)
            await GridRef.Reload();

        if (OnChanged.HasDelegate)
            await OnChanged.InvokeAsync();
    }
}