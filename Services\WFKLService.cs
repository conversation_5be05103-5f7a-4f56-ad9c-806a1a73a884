﻿using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Drawing.Charts;
using DocumentFormat.OpenXml.Office.Word;
using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Office2016.Drawing.ChartDrawing;
using DocumentFormat.OpenXml.Spreadsheet;
using EWA.Data;
using EWA.Models;
using Microsoft.Extensions.Logging;
using Microsoft.OData.Edm.Vocabularies;
using Oracle.ManagedDataAccess.Client;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Xml.Linq;
using static EWA.Models.REP_Models;
using static EWA.Models.WFL_Models;
using static EWA.Services.UIService;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using static System.Runtime.CompilerServices.RuntimeHelpers;

namespace EWA.Services
{
    public interface IWFLActivMonitoringService
    {
        bool IsRunning { get; }
        Task StartAsync();
        Task StopAsync();
    }
    public class HostBuilderService
    {
        private readonly IConfiguration _configuration;

        public HostBuilderService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public IHost CreateHost()
        {
            return Host.CreateDefaultBuilder()
                .ConfigureServices((hostContext, services) =>
                {
                    services.AddSingleton(new DBService(_configuration.GetConnectionString("Rep_Connection")));
                    //services.AddHostedService<WFLActivMonitoringService>();
                })
                .Build();
        }
    }

    
    
    //public class WFLActivMonitoringService : BackgroundService
    public class WFLActivMonitoringService : IWFLActivMonitoringService
    {
        private readonly DBService _dbService;
        private readonly string _dbname;
        private readonly ILogger<WFLActivMonitoringService> _logger;
        private CancellationTokenSource _cts;
        private Task _runningTask;
        public bool IsRunning => _runningTask != null && !_runningTask.IsCompleted;
        public WFLActivMonitoringService(IConfiguration configuration, ILogger<WFLActivMonitoringService> logger)
        {
            _dbname = configuration.GetConnectionString("Rep_Connection");
            _dbService = new DBService(_dbname);
            _logger = logger;
        }
        public Task StartAsync()
        {
            if (IsRunning)
                return Task.CompletedTask;

            _cts = new CancellationTokenSource();
            _runningTask = Task.Run(() => ExecuteAsync(_cts.Token));
            return _runningTask;
        }

        public async Task StopAsync()
        {
            if (!IsRunning)
                return;
            try
            {
                _cts.Cancel();
                await _runningTask;
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Сервис остановлен по запросу.");
            }
            finally
            {
                _cts.Dispose();
                _runningTask = null;
            }
        }
        private async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                while (!stoppingToken.IsCancellationRequested)
                {
                    var (wflActivList, isError, errorMessage) = await GetWFLActiv();
                    if (isError) return;

                    if (wflActivList.Count > 0)
                    {
                        var tasks = wflActivList.Select(async wflActiv =>
                        {
                            if (stoppingToken.IsCancellationRequested) return;

                            await UpdWFStatus(wflActiv.CodeWrfl, wflActiv.Id, 2, null);
                            var t2 = await ExecuteWFL(wflActiv.Id, stoppingToken);
                            if (t2.IS_ERR)
                            {
                                await UpdWFStatus(wflActiv.CodeWrfl, wflActiv.Id, 4, t2.ERR_MESS);
                            }
                        }).ToList();
                        await Task.WhenAll(tasks);
                    }
                    await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
                }
            }
            catch (OperationCanceledException)
            {
                // Корректное завершение по запросу отмены
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ошибка в ExecuteAsync.");
            }
            finally
            {
                _logger.LogInformation("ExecuteAsync завершена.");
            }
        }

        private async Task ExecuteAsync_OLD(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                var (wflActivList, isError, errorMessage) = await GetWFLActiv();

                if (isError) return;
                else if(wflActivList.Count > 0)
                {
                    var tasks = wflActivList.Select(wflActiv => Task.Run(async() =>
                    {
                        var wfl1 = await UpdWFStatus(wflActiv.CodeWrfl, wflActiv.Id, 2, null);

                        var t2 = await ExecuteWFL(wflActiv.Id, stoppingToken);
                        if (t2.IS_ERR)
                        {
                            var wfl2 = await UpdWFStatus(wflActiv.CodeWrfl, wflActiv.Id, 4, t2.ERR_MESS);
                        }
                    }, stoppingToken)).ToList();
                    await Task.WhenAll(tasks);
                }
                await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
            }
        }
        public async Task<(decimal idact,string Code, WFLInfo wflData, bool IS_ERR, string ERR_MESS)> GetWFLInfoByActiv(decimal idact)
        {
            string sql_query = "select a.id as id_activ, t.id,t.code, t.name, t.list_obj,t.lnk_obj" + Environment.NewLine
                              + "  from WRFL_WORKFLOW t" + Environment.NewLine
                              + "  join wrfl_workflow_activation a on a.id_wrfl = t.id" + Environment.NewLine
                             + $" where a.id = {idact}";
            bool IS_ERR = false;
            string ERR_MESS = string.Empty;
            WFLInfo wflData = new WFLInfo();

            try
            {
                var (res, errormes) = await _dbService.GetDataSimple(sql_query);

                if (!string.IsNullOrEmpty(errormes))
                {
                    IS_ERR = true;
                    ERR_MESS = errormes;
                }
                else
                {
                    var item = res.FirstOrDefault();
                    if (item == null)
                    {
                        IS_ERR = true;
                        ERR_MESS = "Запись не найдена";
                    }
                    else
                    {
                        wflData = new WFLInfo
                        {
                            IdActiv = Convert.ToInt32(item["ID_ACTIV"]),
                            Id = Convert.ToInt32(item["ID"]),
                            Code = item["CODE"].ToString(),
                            Name = item["NAME"].ToString(),
                            ListObj = !string.IsNullOrEmpty(item["LIST_OBJ"]?.ToString()) ? JsonSerializer.Deserialize<List<WFLOBJ>>(item["LIST_OBJ"].ToString()) : new List<WFLOBJ>(),
                            LinkObj = !string.IsNullOrEmpty(item["LNK_OBJ"]?.ToString()) ? JsonSerializer.Deserialize<List<WFLOBJ_Link>>(item["LNK_OBJ"].ToString()) : new List<WFLOBJ_Link>()
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                IS_ERR = true;
                ERR_MESS = ex.Message;
            }
            return (idact,wflData.Code, wflData, IS_ERR, ERR_MESS);
        }
        public async Task<(decimal idact, string Code, WRKLInfo wrklData, bool IS_ERR, string ERR_MESS)> GetWRKLInfoByActiv(string code,decimal idAct)
        {
            string sql_query = "select l.id_w as idW, w.id as id, w.code as Code, w.list_task,w.lnk_task" +Environment.NewLine
                              +"  from wrfl_worklet_log l" +Environment.NewLine
                              +"  join wrfl_worklet w on w.id = l.id_wrfl" +Environment.NewLine
                             +$" where l.id = {idAct} and upper(w.code) = upper('{code}')";
                
            bool IS_ERR = false;
            string ERR_MESS = string.Empty;
            WRKLInfo wrklData = new WRKLInfo();

            try
            {
                var (res, errormes) = await _dbService.GetDataSimple(sql_query);

                if (!string.IsNullOrEmpty(errormes))
                {
                    IS_ERR = true;
                    ERR_MESS = errormes;
                }
                else
                {
                    var item = res.FirstOrDefault();
                    if (item == null)
                    {
                        IS_ERR = true;
                        ERR_MESS = "Запись не найдена";
                    }
                    else
                    {
                        wrklData = new WRKLInfo
                        {
                            IdActiv = idAct,
                            IdActivW = Convert.ToInt32(item["IDW"]),
                            Id = Convert.ToInt32(item["ID"]),
                            Code = item["CODE"].ToString(),
                            ListObj = !string.IsNullOrEmpty(item["LIST_TASK"]?.ToString()) ? JsonSerializer.Deserialize<List<WFLOBJ>>(item["LIST_TASK"].ToString()) : new List<WFLOBJ>(),
                            LinkObj = !string.IsNullOrEmpty(item["LNK_TASK"]?.ToString()) ? JsonSerializer.Deserialize<List<WFLOBJ_Link>>(item["LNK_TASK"].ToString()) : new List<WFLOBJ_Link>()
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                IS_ERR = true;
                ERR_MESS = ex.Message;
            }
            return (idAct,code, wrklData, IS_ERR, ERR_MESS);
        }
        public async Task<(decimal idAct, decimal idActW, string Code, WTaskInfo ObjData, bool IS_ERR, string ERR_MESS)> GetTaskInfoByActiv(string code, decimal idAct, decimal idActW)
        {
            string sql_query = "select l.id_t as idActT, t.id as id, t.code as Code, l.data_action"+Environment.NewLine
                              +"  from wrfl_task_log l" +Environment.NewLine
                              +"  join wrfl_task t on t.id = l.id_task" +Environment.NewLine
                             +$" where l.id = {idAct} and (l.id_w = {idActW} or ({idActW} = 0 and l.id_w is null)) and upper(t.code) = upper('{code}')";
            bool IS_ERR = false;
            string ERR_MESS = string.Empty;
            WTaskInfo ObjData = new WTaskInfo();

            try
            {
                var (res, errormes) = await _dbService.GetDataSimple(sql_query);

                if (!string.IsNullOrEmpty(errormes))
                {
                    IS_ERR = true;
                    ERR_MESS = errormes;
                }
                else
                {
                    var item = res.FirstOrDefault();
                    if (item == null)
                    {
                        IS_ERR = true;
                        ERR_MESS = "Запись не найдена";
                    }
                    else
                    {
                        ObjData = new WTaskInfo
                        {
                            IdActiv = Convert.ToInt32(item["IDACTT"]),
                            IdActivW = idActW,
                            IdActivM = idAct,
                            Id = Convert.ToInt32(item["ID"]),
                            Code = item["CODE"].ToString(),
                            SQLAction = item["DATA_ACTION"].ToString()
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                IS_ERR = true;
                ERR_MESS = ex.Message;
            }
            return (idAct, idActW, code, ObjData, IS_ERR, ERR_MESS);
        }

        public async Task<(List<WRFL_WORKFLOW_ACTIVATION> wflActivList, bool IS_ERR, string ERR_MESS)> GetWFLActiv()
        {
            string sql_query = $@"select id, id_wrfl, code_wrfl from wrfl_workflow_activation where status = 1";
            bool IS_ERR = false;
            string ERR_MESS = string.Empty;
            List<WRFL_WORKFLOW_ACTIVATION> wflActivList = new List<WRFL_WORKFLOW_ACTIVATION>();

            try
            {
                var (res, errormes) = await _dbService.GetDataSimple(sql_query);

                if (!string.IsNullOrEmpty(errormes))
                {
                    IS_ERR = true;
                    ERR_MESS = errormes;
                }
                else if (res == null)
                {
                    IS_ERR = true;
                    ERR_MESS = "Нет ожидающих заданий";
                }
                else
                {
                    var item = res.FirstOrDefault();
                    if (item == null)
                    {
                        IS_ERR = true;
                        ERR_MESS = "Нет ожидающих заданий";
                    }
                    else
                    {
                        foreach (var itemr in res)
                        {
                            var wflActiv = new WRFL_WORKFLOW_ACTIVATION
                            {
                                Id = Convert.ToInt32(itemr["ID"]),
                                IdWrfl = Convert.ToInt32(itemr["ID_WRFL"]),
                                CodeWrfl = itemr["CODE_WRFL"].ToString()
                            };
                            wflActivList.Add(wflActiv);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                IS_ERR = true;
                ERR_MESS = ex.Message;
            }
            return (wflActivList, IS_ERR, ERR_MESS);
        }
        public async Task<(List<WRFL_WORKFLOW_ACTIVATION> wflActivList, bool IS_ERR, string ERR_MESS)> GetWFLActivStatus(decimal idActiv)
        {
            string sql_query = $@"select status, id, code_wrfl from wrfl_workflow_activation where id = {idActiv}";
            bool IS_ERR = false;
            string ERR_MESS = string.Empty;
            List<WRFL_WORKFLOW_ACTIVATION> wflActivList = new List<WRFL_WORKFLOW_ACTIVATION>();

            try
            {
                var (res, errormes) = await _dbService.GetDataSimple(sql_query);

                if (!string.IsNullOrEmpty(errormes))
                {
                    IS_ERR = true;
                    ERR_MESS = errormes;
                }
                else if (res == null)
                {
                    IS_ERR = true;
                    ERR_MESS = "Нет активных заданий";
                }
                else
                {
                    var item = res.FirstOrDefault();
                    if (item == null)
                    {
                        IS_ERR = true;
                        ERR_MESS = "Нет активных заданий";
                    }
                    else
                    {
                        foreach (var itemr in res)
                        {
                            var wflActiv = new WRFL_WORKFLOW_ACTIVATION
                            {
                                Id = Convert.ToInt32(itemr["ID"]),
                                CodeWrfl = itemr["CODE_WRFL"].ToString(),
                                StatusID = Convert.ToInt32(itemr["STATUS"])
                            };
                            wflActivList.Add(wflActiv);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                IS_ERR = true;
                ERR_MESS = ex.Message;
            }
            return (wflActivList, IS_ERR, ERR_MESS);
        }
        public async Task<(bool IS_END, bool IS_ERR, string ERR_MESS)> ExecuteWFL(decimal idAct, CancellationToken token)
        {
            bool IS_END = false;
            bool IS_ERR = false;
            string ERR_MESS = string.Empty;
            decimal status  = 2;
            WFLInfo T_wflData = new WFLInfo();
            List<WRKLInfo> T_wrklData = new List<WRKLInfo>();
            List<WTaskInfo> T_taskData = new List<WTaskInfo>();
            List<WorkflowGraphNode> WFL_Data = new List<WorkflowGraphNode>();
            string wflCode = string.Empty;
            List<string> ChldCodesAll = new List<string>();
            List<string> ChldCodes = new List<string>();

            try
            {

                token.ThrowIfCancellationRequested();

                var wfl_active = await GetWFLInfoByActiv(idAct);
                if (wfl_active.IS_ERR)
                {
                    IS_END = true;
                    IS_ERR = true;
                    status = 4;
                    ERR_MESS = wfl_active.ERR_MESS;
                    await UpdWFStatus(wflCode, idAct, status, ERR_MESS);
                    return (IS_END, IS_ERR, ERR_MESS);
                }
                T_wflData = wfl_active.wflData;
                wflCode = T_wflData.Code;

                foreach (var wkl_t in T_wflData.ListObj)
                {
                    if (wkl_t.Type == "W")
                    {
                        var wkl_active = await GetWRKLInfoByActiv(wkl_t.Code, idAct);
                        if (wkl_active.IS_ERR)
                        {
                            IS_END = true;
                            IS_ERR = true;
                            status = 4;
                            ERR_MESS = wkl_active.ERR_MESS;
                            await UpdWFStatus(wflCode, idAct, status, ERR_MESS);
                            return (IS_END, IS_ERR, ERR_MESS);
                        }
                        if (!T_wrklData.Any(x => x.IdActivW == wkl_active.wrklData.IdActivW))
                        {
                            T_wrklData.Add(wkl_active.wrklData);
                        }
                    }
                    else if (wkl_t.Type == "T")
                    {
                        var tsk_active = await GetTaskInfoByActiv(wkl_t.Code, idAct, 0);
                        if (tsk_active.IS_ERR)
                        {
                            IS_END = true;
                            IS_ERR = true;
                            status = 4;
                            ERR_MESS = tsk_active.ERR_MESS;
                            await UpdWFStatus(wflCode, idAct, status, ERR_MESS);
                            return (IS_END, IS_ERR, ERR_MESS);
                        }
                        if (!T_taskData.Any(x => x.IdActiv == tsk_active.ObjData.IdActiv))
                        {
                            T_taskData.Add(tsk_active.ObjData);
                        }
                    }
                }
                if (T_wrklData.Count > 0)
                {

                    foreach (var t_wrk in T_wrklData)
                    {
                        foreach (var item in t_wrk.ListObj)
                        {
                            var tsk_active = await GetTaskInfoByActiv(item.Code, idAct, t_wrk.IdActivW);
                            if (tsk_active.IS_ERR)
                            {
                                IS_END = true;
                                IS_ERR = true;
                                status = 4;
                                ERR_MESS = tsk_active.ERR_MESS;
                                await UpdWFStatus(wflCode, idAct, status, ERR_MESS);
                                return (IS_END, IS_ERR, ERR_MESS);
                            }
                            if (!T_taskData.Any(x => x.IdActiv == tsk_active.ObjData.IdActiv))
                            {
                                T_taskData.Add(tsk_active.ObjData);
                            }
                        }
                    }
                }
                //добавляем воркфлоу
                WFL_Data.Add(new WorkflowGraphNode
                {
                    IdActv = T_wflData.IdActiv,
                    Id = T_wflData.Id,
                    Code = T_wflData.Code,
                    StatusObj = 1,
                    Type = WorkflowObjectType.WFL
                });


                //добавляем ворклеты
                foreach (var w_data in T_wrklData)
                {
                    WFL_Data.Add(new WorkflowGraphNode
                    {
                        IdActv = w_data.IdActiv,
                        IdActvW = w_data.IdActivW,
                        Id = w_data.Id,
                        Code = w_data.Code,
                        StatusObj = 0,
                        Type = WorkflowObjectType.WLT
                    });
                }
                //добавляем таски
                foreach (var t_data in T_taskData)
                {
                    WFL_Data.Add(new WorkflowGraphNode
                    {
                        IdActv = t_data.IdActivM,
                        IdActvW = t_data.IdActivW,
                        IdActvT = t_data.IdActiv,
                        Id = t_data.Id,
                        Code = t_data.Code,
                        StatusObj = 0,
                        Type = WorkflowObjectType.TSK,
                        SqlQuery = t_data.SQLAction
                    });
                }
                var codeToNode = WFL_Data.ToDictionary(n => n.Code);

                // собираем для ворклетов и воркфлоу AllChildren
                foreach (var node in WFL_Data)
                {
                    switch (node.Type)
                    {
                        case WorkflowObjectType.WFL:
                            var wflObjects = T_wflData.ListObj;
                            node.AllChildren = wflObjects
                                .Select(obj => codeToNode.GetValueOrDefault(obj.Code))
                                .Where(n => n != null)
                                .ToList();
                            break;

                        case WorkflowObjectType.WLT:
                            var wrkl = T_wrklData.FirstOrDefault(w => w.IdActivW == node.IdActvW);
                            if (wrkl != null)
                            {
                                node.AllChildren = wrkl.ListObj
                                    .Select(obj => codeToNode.GetValueOrDefault(obj.Code))
                                    .Where(n => n != null)
                                    .ToList();
                            }
                            break;

                        case WorkflowObjectType.TSK:
                            node.AllChildren = new List<WorkflowGraphNode>(); // Task не пихаем
                            break;
                    }
                }

                // строим связи
                // из WFL
                foreach (var link in T_wflData.LinkObj)
                {
                    if (codeToNode.TryGetValue(link.SrcObj, out var src) &&
                        codeToNode.TryGetValue(link.TrgObj, out var trg))
                    {
                        src.Children.Add(trg);
                        trg.DependsOn.Add(src);
                    }
                }

                // из WRKL
                foreach (var wrkl in T_wrklData)
                {
                    foreach (var link in wrkl.LinkObj)
                    {
                        if (codeToNode.TryGetValue(link.SrcObj, out var src) &&
                            codeToNode.TryGetValue(link.TrgObj, out var trg))
                        {
                            src.Children.Add(trg);
                            trg.DependsOn.Add(src);
                        }
                    }
                }

                token.ThrowIfCancellationRequested();
                IS_END = true;
                if (IS_ERR)
                {
                    status = 4;
                }
                else if (!IS_ERR && IS_END)
                {
                    status = 3;
                    await ProcessWorkflowAsync(WFL_Data, token);
                }
                else
                {
                    status = 5;
                    if (string.IsNullOrEmpty(ERR_MESS))
                    {
                        ERR_MESS = "Прочие ошибки";
                    }
                }
                if (status > 3)
                {
                    await UpdWFStatus(wflCode, idAct, status, ERR_MESS);
                }
            }
            catch (OperationCanceledException)
            {
                IS_ERR = true;
                status = 5;
                ERR_MESS = "ExecuteWFL отменён.";
                if (idAct > 0)
                {
                    await UpdWFStatus(wflCode, idAct, status, ERR_MESS);
                }
                throw;
                
            }
            catch (Exception ex)
            {
                IS_ERR = true;
                status = 5;
                ERR_MESS = $"Ошибка в ExecuteWFL. {ex.Message}.";
                if (idAct > 0)
                {
                    await UpdWFStatus(wflCode, idAct, status, ERR_MESS);
                }
            }


            return (IS_END,IS_ERR, ERR_MESS);
        }

        async Task ProcessWorkflowAsync(List<WorkflowGraphNode> WFL_Data, CancellationToken cancellationToken)
        {
            decimal StatusWFLDB = 0;
            
            var wflNode = WFL_Data.FirstOrDefault(x => x.Type == WorkflowObjectType.WFL);
            if (wflNode == null) return;

            var lockObj = new object();

            while (wflNode.StatusObj < 3 && StatusWFLDB < 3)
            {
                cancellationToken.ThrowIfCancellationRequested();
                var tasks = new List<Task>();

                var sts_tmp = await GetWFLActivStatus(wflNode.IdActv);
                if (!sts_tmp.IS_ERR)
                {

                    var w_data = sts_tmp.wflActivList;
                    foreach (var itm in sts_tmp.wflActivList)
                    {
                        StatusWFLDB = itm.StatusID;
                    }
                }

                if (StatusWFLDB == 6)
                {
                    while (StatusWFLDB == 6)
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        await Task.Delay(1000, cancellationToken);
                        var sts_pau = await GetWFLActivStatus(wflNode.IdActv);
                        if (!sts_pau.IS_ERR)
                        {
                            var w_data = sts_pau.wflActivList;
                            foreach (var itm in sts_pau.wflActivList)
                            {
                                StatusWFLDB = itm.StatusID;
                            }
                        }
                    }
                }


                foreach (var node in WFL_Data.Where(x => x.Type != WorkflowObjectType.WFL))
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        switch (node.Type)
                        {
                            case WorkflowObjectType.TSK:
                                await ProcessTSKAsync(node, WFL_Data, lockObj);
                                break;

                            case WorkflowObjectType.WLT:
                                await ProcessWLTAsync(node, lockObj);
                                break;
                        }
                    }, cancellationToken));
                }

                await Task.WhenAll(tasks);

                if (WFL_Data.Where(x => x.Type != WorkflowObjectType.WFL).All(x => x.StatusObj >= 3) || StatusWFLDB == 5)
                {
                    decimal maxStatus = 3;
                    
                    string err_mess = string.Empty;
                    lock (lockObj)
                    {
                        maxStatus = WFL_Data.Where(x => x.Type != WorkflowObjectType.WFL).Max(x => x.StatusObj);
                        if (StatusWFLDB == 5)
                        {
                            maxStatus = StatusWFLDB;
                        }
                        wflNode.StatusObj = maxStatus;
                    }
                    
                    if (maxStatus == 4)
                    {
                        err_mess = "Имеются ошибки. См. детализацию.";
                    }
                    else if (maxStatus == 5)
                    {
                        err_mess = "Вызвана отмена операции.";
                    }
                    var wfl1 = await UpdWFStatus(wflNode.Code, wflNode.IdActv, maxStatus, err_mess);
                }
                await Task.Delay(50, cancellationToken);
            }
        }


        async Task ProcessWLTAsync(WorkflowGraphNode wlt, object lockObj)
        {
            if (wlt.StatusObj >= 3)
                return;

            if (wlt.StatusObj < 2)
            {
                if (wlt.DependsOn?.Any() == true)
                {
                    var minStatus = wlt.DependsOn.Min(x => x.StatusObj);
                    if (minStatus >= 3)
                    {
                        lock (lockObj) wlt.StatusObj = 2;
                        var w1 = await UpdWLStatus(wlt.Code, wlt.IdActvW, 2, null);
                    }
                }
                else
                {
                    lock (lockObj) wlt.StatusObj = 2;
                    var w1 = await UpdWLStatus(wlt.Code, wlt.IdActvW, 2, null);
                }
            }

            if (wlt.StatusObj == 2 && wlt.AllChildren?.Any() == true)
            {
                var minChildStatus = wlt.AllChildren.Min(x => x.StatusObj);
                if (minChildStatus >= 3)
                {
                    lock (lockObj) wlt.StatusObj = 3;
                    var w1 = await UpdWLStatus(wlt.Code, wlt.IdActvW, 3, null);
                }
            }
        }

        async Task ProcessTSKAsync(WorkflowGraphNode task, List<WorkflowGraphNode> allNodes, object lockObj)
        {
            var taskName = task.Code.ToUpper();
            //кидаем лок
            lock (lockObj)
            {
                if (task.IsProcessing || task.StatusObj >= 3)
                    return;
                task.IsProcessing = true;
            }
            try
            {
                if (task.DependsOn?.Any() == true)
                {
                    var minStatus = task.DependsOn.Min(x => x.StatusObj);
                    if (task.StatusObj == 0)
                    {
                        if (minStatus >= 2)
                        {
                            var t = await UpdTStatus(task.Code, task.IdActvT, 1);
                            lock (lockObj) task.StatusObj = 1;
                        }
                    }
                    else if (task.StatusObj == 1)
                    {
                        if (minStatus >= 3)
                        {
                            var t = await UpdTStatus(task.Code, task.IdActvT, 2);
                            lock (lockObj) task.StatusObj = 2;
                        }
                    }
                    else if (task.StatusObj == 2)
                    {
                        var taskName1 = taskName;
                        var res = await ActionTask(task.Code,task.SqlQuery);
                        if (res.IS_ERR)
                        {
                            var t1 = await UpdTStatus(task.Code, task.IdActvT, 4, res.ERR_MESS);
                            lock (lockObj) task.StatusObj = 4;
                        }
                        else
                        {
                            var t = await UpdTStatus(task.Code, task.IdActvT, 3, null);
                            lock (lockObj) task.StatusObj = 3;
                        }
                    }
                }   
                else
                {
                    if (task.IdActvW > 0)
                    {
                        var wlt = allNodes.FirstOrDefault(x => x.Type == WorkflowObjectType.WLT && x.IdActvW == task.IdActvW);
                        if (wlt != null && wlt.StatusObj == 2 && task.StatusObj < 3)
                        {
                            if (task.StatusObj == 0)
                            {
                                var t = await UpdTStatus(task.Code, task.IdActvT, 1);
                                lock (lockObj) task.StatusObj = 1;
                            }
                            if (task.StatusObj == 1)
                            {
                                var t = await UpdTStatus(task.Code, task.IdActvT, 2);
                                lock (lockObj) task.StatusObj = 2;
                            }
                            if (task.StatusObj == 2)
                            {

                                var taskName1 = taskName;
                                var res = await ActionTask(task.Code, task.SqlQuery);
                                if (res.IS_ERR)
                                {
                                    lock (lockObj) task.StatusObj = 4;
                                    var t1 = await UpdTStatus(task.Code, task.IdActvT, 4, res.ERR_MESS);
                                }
                                else
                                {
                                    var t = await UpdTStatus(task.Code, task.IdActvT, 3, null);
                                    lock (lockObj) task.StatusObj = 3;
                                }
                            }
                        }
                    }
                    else if (task.IdActvW == 0)
                    {
                        var wlt = allNodes.FirstOrDefault(x => x.Type == WorkflowObjectType.WLT && x.IdActvW == task.IdActvW);
                        if (task.StatusObj < 3)
                        {
                            if (task.StatusObj == 0)
                            {
                                var t = await UpdTStatus(task.Code, task.IdActvT, 1);
                                lock (lockObj) task.StatusObj = 1;
                            }
                            if (task.StatusObj == 1)
                            {
                                var t = await UpdTStatus(task.Code, task.IdActvT, 2);
                                lock (lockObj) task.StatusObj = 2;
                            }
                            if (task.StatusObj == 2)
                            {

                                var taskName1 = taskName;
                                var res = await ActionTask(task.Code, task.SqlQuery);
                                if (res.IS_ERR)
                                {
                                    lock (lockObj) task.StatusObj = 4;
                                    var t1 = await UpdTStatus(task.Code, task.IdActvT, 4, res.ERR_MESS);
                                }
                                else
                                {
                                    var t = await UpdTStatus(task.Code, task.IdActvT, 3, null);
                                    lock (lockObj) task.StatusObj = 3;
                                }
                            }
                        }
                    }
                }
            }
            finally
            {
                lock (lockObj)
                {
                    task.IsProcessing = false;
                }
            }
        }

        public async Task<(string Code, bool IS_ERR, string ERR_MESS)> UpdWFStatus(string code, decimal idact, decimal in_status, string? err_mess = null)
        {
            bool is_err = false;
            string mess = string.Empty;

            Dictionary<string, Rep_Param> parameters = new Dictionary<string, Rep_Param>();
            parameters.Add("IN_ERR_MESS", new Rep_Param(0, "CLOB", ParameterDirection.Input, null, 0, err_mess));



            string sql_query = "update wrfl_workflow_activation" + Environment.NewLine
                             + $"   set status = {in_status}" + Environment.NewLine
                             + $"        , dts = case when {in_status} >= 2 then nvl(dts,sysdate) else dts end" + Environment.NewLine
                             + $"        , dte = case when {in_status} >= 3 then sysdate else null end" + Environment.NewLine
                             + $"         , err = :IN_ERR_MESS" + Environment.NewLine
                             + $" where id = {idact}";
            try
            {
                var (res, errormes, outparam) = await _dbService.ExecuteDataWithOut(sql_query, parameters);
                if (!string.IsNullOrEmpty(errormes))
                {
                    is_err = true;
                    mess = errormes;
                }

            }
            catch (Exception e)
            {
                is_err = true;
                mess = e.Message;
            }
            return (code, is_err, mess);
        }
        public async Task<(string Code, bool IS_ERR, string ERR_MESS)> UpdWLStatus(string code, decimal idact, decimal in_status, string? err_mess = null)
        {
            bool is_err = false;
            string mess = string.Empty;

            Dictionary<string, Rep_Param> parameters = new Dictionary<string, Rep_Param>();
            parameters.Add("IN_ERR_MESS", new Rep_Param(0, "CLOB", ParameterDirection.Input, null, 0, err_mess));



            string sql_query = "update wrfl_worklet_log" + Environment.NewLine
                             + $"   set status = {in_status}" + Environment.NewLine
                             + $"        , dts = case when {in_status} >= 2 then nvl(dts,sysdate) else dts end" + Environment.NewLine
                             + $"        , dte = case when {in_status} >= 3 then sysdate else null end" + Environment.NewLine
                             + $"         , err = :IN_ERR_MESS" + Environment.NewLine
                             + $" where id_w = {idact}";
            try
            {
                var (res, errormes, outparam) = await _dbService.ExecuteDataWithOut(sql_query, parameters);
                if (!string.IsNullOrEmpty(errormes))
                {
                    is_err = true;
                    mess = errormes;
                }

            }
            catch (Exception e)
            {
                is_err = true;
                mess = e.Message;
            }
            return (code, is_err, mess);
        }
        public async Task<(string Code, bool IS_ERR, string ERR_MESS)> UpdTStatus(string code, decimal idact, decimal in_status, string? err_mess = null)
        {
            bool is_err = false;
            string mess = string.Empty;
            
            Dictionary<string, Rep_Param> parameters = new Dictionary<string, Rep_Param>();
            parameters.Add("IN_ERR_MESS", new Rep_Param(0,"CLOB",ParameterDirection.Input,null,0,err_mess));



            string sql_query = "update wrfl_task_log" + Environment.NewLine
                             + $"   set status = {in_status}"+Environment.NewLine
                             + $"        , dts = case when {in_status} >= 2 then nvl(dts,sysdate) else dts end" +Environment.NewLine
                             + $"        , dte = case when {in_status} >= 3 then sysdate else null end"+Environment.NewLine
                             +$"         , err = :IN_ERR_MESS" + Environment.NewLine
                             + $" where id_t = {idact}";
            try
            {
                var (res, errormes, outparam) = await _dbService.ExecuteDataWithOut(sql_query, parameters);
                if (!string.IsNullOrEmpty(errormes))
                {
                    is_err = true;
                    mess = errormes;
                }

            }
            catch (Exception e)
            {
                is_err = true;
                mess = e.Message;
            }
            return (code, is_err, mess);
        }
        public async Task<(bool IS_ERR, string ERR_MESS)> ActionTask(string code, string query)
        {
            bool is_err = false;
            string mess = string.Empty;
            Dictionary<string, Rep_Param> parameters = new Dictionary<string, Rep_Param>();
            var t = code;
            string sql_query = query;
            try
            {
                var (res, errormes, outparam) = await _dbService.ExecuteDataWithOut(sql_query, parameters);
                if (!string.IsNullOrEmpty(errormes))
                {
                    is_err = true;
                    mess = errormes;
                }

            }
            catch (Exception e)
            {
                is_err = true;
                mess = e.Message;
            }
            return (is_err, mess);
        }

    }



    public class WFKLService
    {
        private DBService dbService;
        private readonly DBContext _context;
        private readonly RepService _repService;
        private readonly AMLService _amlService;

        private readonly string dbname;
        public WFKLService(IConfiguration configuration, DBContext context, RepService repService, AMLService amlService)
        {
            dbname = configuration.GetConnectionString("Rep_Connection");
            _context = context;
            _repService = repService;
            _amlService = amlService;
        }
        public async Task<Dictionary<string, (int id, string code, string name, string typet,string typetname)>> GetDataDiagram(string codeT)
        {
            
            var result = new Dictionary<string, (int id, string code, string name, string typet, string typetname)>();
            string query_sql = string.Empty;
            string base_sql = "select t.id, t.code, t.name, 'T' as TypeT, 'Узел' as TypeTName " + Environment.NewLine
                      + "  from wrfl_task t " + Environment.NewLine;
            string add_sql = "union" + Environment.NewLine
                           + "select w.id, w.code, w.name, 'W' as TypeT, 'Ворклет' as TypeTName " + Environment.NewLine
                           +"  from wrfl_worklet w";
            if (codeT == "WWORKLET")
            {
                query_sql = base_sql;
            }
            else
            {
                query_sql = base_sql + add_sql;
            }
            
            dbService = new DBService(dbname);
            var (items, errorMessage) = await dbService.GetDataSimple(query_sql);

            if (!string.IsNullOrEmpty(errorMessage))
            {
                Console.WriteLine($"Error: {errorMessage}");
                return result;
            }
            else
            {
                foreach (var item in items)
                {
                    var id = item.ContainsKey("ID") && item["ID"] != null ? Convert.ToInt32(item["ID"]) : 0;
                    var code = item.ContainsKey("CODE") ? item["CODE"]?.ToString() : null;
                    var name = item.ContainsKey("NAME") ? item["NAME"]?.ToString() : null;
                    var typet = item.ContainsKey("TYPET") ? item["TYPET"]?.ToString() : null;
                    var typetname = item.ContainsKey("TYPETNAME") ? item["TYPETNAME"]?.ToString() : null;

                    if (code != null)
                    {
                        result[code] = (id, code, name, typet, typetname);
                    }
                }
            }
            return result;
        }

        public async Task<(bool is_res, bool IS_ERR, string ERR_MESS)> StopWFL(decimal idact)
        {
            bool is_err = false;
            bool is_res = true;
            string mess = string.Empty;
            Dictionary<string, Rep_Param> parameters = new Dictionary<string, Rep_Param>();

            string sql_query = "update WRFL_WORKFLOW_ACTIVATION" + Environment.NewLine
                             + $"   set status = 5" + Environment.NewLine
                             + $" where id = {idact}";
            try
            {
                dbService = new DBService(dbname);
                var (res, errormes, outparam) = await dbService.ExecuteDataWithOut(sql_query, parameters);
                if (!string.IsNullOrEmpty(errormes))
                {
                    is_err = true;
                    mess = errormes;
                }

            }
            catch (Exception e)
            {
                is_err = true;
                mess = e.Message;
                is_res = false;
            }
            return (is_res,is_err, mess);
        }
        public async Task<(bool is_res, bool IS_ERR, string ERR_MESS)> PauseWFL(decimal idact, decimal status)
        {
            bool is_err = false;
            bool is_res = true;
            string mess = string.Empty;
            Dictionary<string, Rep_Param> parameters = new Dictionary<string, Rep_Param>();

            string sql_query = "update WRFL_WORKFLOW_ACTIVATION" + Environment.NewLine
                             + $"   set status = {status}" + Environment.NewLine
                             + $" where id = {idact}";
            try
            {
                dbService = new DBService(dbname);
                var (res, errormes, outparam) = await dbService.ExecuteDataWithOut(sql_query, parameters);
                if (!string.IsNullOrEmpty(errormes))
                {
                    is_err = true;
                    mess = errormes;
                }

            }
            catch (Exception e)
            {
                is_err = true;
                mess = e.Message;
                is_res = false;
            }
            return (is_res, is_err, mess);
        }

        public async Task<(bool is_err, string mess)> StartWRFKL(string code)
        {
            bool is_err = false;
            string mess = string.Empty;
            decimal id_activation = 0;
            WFLInfo wflData = new WFLInfo();
            List<WRKLInfo> wrklDataList = new List<WRKLInfo>();
            List<WTaskInfo> wfObjDataList = new List<WTaskInfo>();

            var res_start = await ADDStartInfo(code);
            if (res_start.IS_ERR)
            {
                is_err = true;
                mess = res_start.ERR_MESS;
                var u_sts = await UpdWFLStatus(code, res_start.idAct, 5, mess);
                return (is_err, mess);
            }
            id_activation = res_start.idAct;

            var res_info = await GetWFLInfo(code);
            if (res_info.IS_ERR)
            {
                is_err = true;
                mess = res_info.ERR_MESS;
                var u_sts = await UpdWFLStatus(code, id_activation, 5, mess);
                return (is_err, mess);
            }
            

            if (wflData != null)
            {
                wflData = res_info.wflData;
                wflData.IdActiv = id_activation;
                var u_param = await UpdWFLData(code, id_activation, wflData.StartParamStr);
                wrklDataList.Clear();
                wfObjDataList.Clear();
                
                foreach (var item in wflData.ListObj)
                {
                    if (item.Type == "W")
                    {
                        decimal id_wactiv = 0;
                        var t_res = await GetWRKLInfo(item.Code);
                        if (t_res.IS_ERR)
                        {
                            is_err = true;
                            mess = t_res.ERR_MESS;
                            var u_sts1 = await UpdWFLStatus(code, id_activation, 5, mess);
                            return (is_err, mess);
                        }
                        wrklDataList.Add(t_res.wrklData);
                        var w_res = await ADDWrokletInfo(item.Code, id_activation);
                        id_wactiv = w_res.idAct;
                        wrklDataList.Where(x => x.Code == item.Code).ToList()
                                                .ForEach(x => x.IdActiv = id_wactiv);
                    }
                    else if (item.Type == "T")
                    {
                        decimal id_wactiv = 0;
                        var tsk_res = await GetTaskInfo(item.Code);
                        wfObjDataList.Add(tsk_res.ObjData);
                        if (tsk_res.ObjData.TYPE_OBJ == "AML_INDICATOR" || tsk_res.ObjData.TYPE_OBJ == "AML_METHOD")
                        {
                            string sqlAction = string.Empty;
                            string sqlParam = string.Empty;
                            string code_obj = tsk_res.ObjData.CODE_OBJ;
                            string dts = wflData.StartParam.FirstOrDefault(p => p.Code == "DTS")?.Value;
                            string dte = wflData.StartParam.FirstOrDefault(p => p.Code == "DTE")?.Value;
                            var res_sql = await _amlService.GetSQLQuery(code_obj, dts, dte);
                            foreach (var s_item in res_sql)
                            {
                                var s_sqlOut = s_item.Value.sql_out;
                                var s_sqlParam = s_item.Value.sql_paramList;
                                var s_isErr = s_item.Value.is_err;
                                var s_mess = s_item.Value.mess;
                                if (!s_isErr)
                                {
                                    sqlAction = s_sqlOut;
                                    sqlParam = s_sqlParam;
                                }
                            }
                            wfObjDataList.Where(x => x.Code == item.Code).ToList()
                                         .ForEach(x =>
                                         {
                                             x.SQLAction = sqlAction;
                                             x.SQLParam = sqlParam;
                                         });
                        }
                        wfObjDataList.Where(x => x.Code == item.Code).ToList()
                                             .ForEach(x => x.IdActivM = id_activation);
                    }
                }
                if(wrklDataList!= null)
                {
                    foreach (var w_item in wrklDataList)
                    {
                        foreach (var wt_item in w_item.ListObj)
                        {
                            var tsk_res = await GetTaskInfo(wt_item.Code);
                            wfObjDataList.Add(tsk_res.ObjData);
                            if (tsk_res.ObjData.TYPE_OBJ == "AML_INDICATOR" || tsk_res.ObjData.TYPE_OBJ == "AML_METHOD")
                            {
                               string sqlAction = string.Empty;
                               string sqlParam = string.Empty;
                                    string code_obj = tsk_res.ObjData.CODE_OBJ;
                                    string dts = wflData.StartParam.FirstOrDefault(p => p.Code == "DTS")?.Value;
                                    string dte = wflData.StartParam.FirstOrDefault(p => p.Code == "DTE")?.Value;
                                    var res_sql = await _amlService.GetSQLQuery(code_obj, dts, dte);
                                    foreach (var s_item in res_sql)
                                    {
                                        var s_sqlOut = s_item.Value.sql_out;
                                        var s_sqlParam = s_item.Value.sql_paramList;
                                        var s_isErr = s_item.Value.is_err;
                                        var s_mess = s_item.Value.mess;
                                        if (!s_isErr)
                                        {
                                            sqlAction = s_sqlOut;
                                            sqlParam = s_sqlParam;
                                        }
                                    }
                                wfObjDataList.Where(x => x.Code == wt_item.Code).ToList()
                                             .ForEach(x =>
                                             {
                                                 x.SQLAction = sqlAction;
                                                 x.SQLParam = sqlParam;
                                             });
                            }
                            wfObjDataList.Where(x => x.Code == wt_item.Code).ToList()
                                                 .ForEach(x => x.IdActiv = w_item.IdActiv);
                        }

                            
                    }
                   
                }
                foreach (var task_item in wfObjDataList)
                {
                    var tt = await ADDTaskInfo(task_item.Code, id_activation, task_item.IdActiv, task_item.SQLAction, task_item.SQLParam);
                    if (tt.IS_ERR)
                    {
                        is_err = true;
                        mess += $"{task_item.Code}: {tt.ERR_MESS}; "+ Environment.NewLine;
                    }
                }
                decimal status = 1;
                if (is_err)
                {
                    status = 5;
                }


                var u_sts = await UpdWFLStatus(wflData.Code, wflData.IdActiv, status, mess);
                if (u_sts.IS_ERR)
                {
                    is_err = true;
                    mess = u_sts.ERR_MESS;
                    var u_sts2 = await UpdWFLStatus(wflData.Code, wflData.IdActiv, 5, mess);
                }
            }
                        
            return (is_err, mess);
        }
        
        public async Task<(string Code, WFLInfo wflData, bool IS_ERR, string ERR_MESS)> GetWFLInfo(string code)
        {
            string sql_query = $@"select id,code, name, list_obj,lnk_obj,start_param from WRFL_WORKFLOW t where upper(t.code) = upper('{code}')";
            bool IS_ERR = false;
            string ERR_MESS = string.Empty;
            WFLInfo wflData = new WFLInfo();
           
            try
            {
                dbService = new DBService(dbname);

                var (res, errormes) = await dbService.GetDataSimple(sql_query);

                if (!string.IsNullOrEmpty(errormes))
                {
                    IS_ERR = true;
                    ERR_MESS = errormes;
                }
                else
                {
                    var item = res.FirstOrDefault();
                    if (item == null)
                    {
                        IS_ERR = true;
                        ERR_MESS = "Запись не найдена";
                    }
                    else
                    {
                        wflData = new WFLInfo
                        {
                            Id = Convert.ToInt32(item["ID"]),
                            Code = item["CODE"].ToString(),
                            Name = item["NAME"].ToString(),
                            ListObj = !string.IsNullOrEmpty(item["LIST_OBJ"]?.ToString()) ? JsonSerializer.Deserialize<List<WFLOBJ>>(item["LIST_OBJ"].ToString()) : new List<WFLOBJ>(),
                            LinkObj = !string.IsNullOrEmpty(item["LNK_OBJ"]?.ToString()) ? JsonSerializer.Deserialize<List<WFLOBJ_Link>>(item["LNK_OBJ"].ToString()) : new List<WFLOBJ_Link>(),
                            StartParam = !string.IsNullOrEmpty(item["START_PARAM"]?.ToString()) ? JsonSerializer.Deserialize<List<WFLPARAM>>(item["START_PARAM"].ToString()) : new List<WFLPARAM>(),
                            StartParamStr = item["START_PARAM"].ToString()
                        };
                    }
                    
                }
            }
            catch (Exception ex)
            {
                IS_ERR = true;
                ERR_MESS = ex.Message;
            }
            return (code, wflData, IS_ERR, ERR_MESS);
        }
        public async Task<(string Code, WRKLInfo wrklData, bool IS_ERR, string ERR_MESS)> GetWRKLInfo(string code)
        {
            string sql_query = $@"select id,code, name,type_obj, code_obj, list_task,lnk_task from wrfl_worklet t where upper(t.code) = upper('{code}')";
            bool IS_ERR = false;
            string ERR_MESS = string.Empty;
            WRKLInfo wrklData = new WRKLInfo();

            try
            {
                dbService = new DBService(dbname);

                var (res, errormes) = await dbService.GetDataSimple(sql_query);

                if (!string.IsNullOrEmpty(errormes))
                {
                    IS_ERR = true;
                    ERR_MESS = errormes;
                }
                else
                {
                    var item = res.FirstOrDefault();
                    if (item == null)
                    {
                        IS_ERR = true;
                        ERR_MESS = "Запись не найдена";
                    }
                    else
                    {
                        wrklData = new WRKLInfo
                        {
                            Id = Convert.ToInt32(item["ID"]),
                            Code = item["CODE"].ToString(),
                            Name = item["NAME"].ToString(),
                            TYPE_OBJ = item["TYPE_OBJ"].ToString(),
                            CODE_OBJ = item["CODE_OBJ"].ToString(),
                            ListObj = !string.IsNullOrEmpty(item["LIST_TASK"]?.ToString()) ? JsonSerializer.Deserialize<List<WFLOBJ>>(item["LIST_TASK"].ToString()) : new List<WFLOBJ>(),
                            LinkObj = !string.IsNullOrEmpty(item["LNK_TASK"]?.ToString()) ? JsonSerializer.Deserialize<List<WFLOBJ_Link>>(item["LNK_TASK"].ToString()) : new List<WFLOBJ_Link>()
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                IS_ERR = true;
                ERR_MESS = ex.Message;
            }
            return (code, wrklData, IS_ERR, ERR_MESS);
        }

        public async Task<(string Code, WTaskInfo ObjData, bool IS_ERR, string ERR_MESS)> GetTaskInfo(string code)
        {
            string sql_query = $@"select id,code, name,type_obj, code_obj from wrfl_task t where upper(t.code) = upper('{code}')";
            bool IS_ERR = false;
            string ERR_MESS = string.Empty;
            WTaskInfo ObjData = new WTaskInfo();

            try
            {
                dbService = new DBService(dbname);

                var (res, errormes) = await dbService.GetDataSimple(sql_query);

                if (!string.IsNullOrEmpty(errormes))
                {
                    IS_ERR = true;
                    ERR_MESS = errormes;
                }
                else
                {
                    var item = res.FirstOrDefault();
                    if (item == null)
                    {
                        IS_ERR = true;
                        ERR_MESS = "Запись не найдена";
                    }
                    else
                    {
                        ObjData = new WTaskInfo
                        {
                            Id = Convert.ToInt32(item["ID"]),
                            Code = item["CODE"].ToString(),
                            Name = item["NAME"].ToString(),
                            TYPE_OBJ = item["TYPE_OBJ"].ToString(),
                            CODE_OBJ = item["CODE_OBJ"].ToString()
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                IS_ERR = true;
                ERR_MESS = ex.Message;
            }
            return (code, ObjData, IS_ERR, ERR_MESS);
        }
        public async Task<(string Code, decimal idAct, bool IS_ERR, string ERR_MESS)> ADDStartInfo(string code)
        {
            bool is_err = false;
            string mess = string.Empty;
            decimal idAct = 0;
            Dictionary<string, Rep_Param> parameters = new Dictionary<string, Rep_Param>();

            parameters.Add("OUT_ID", new Rep_Param(1, "NUMBER", ParameterDirection.Output, 32,
                                            0, null));
            string sql_query = "Declare" + Environment.NewLine
                               + "l_cnt number(32):=0;" + Environment.NewLine
                               + "l_code varchar2(100);" + Environment.NewLine
                               + "l_id number(32):= 0;" + Environment.NewLine
                               + "l_wid number(32):= 0;" + Environment.NewLine
                               + "Begin" + Environment.NewLine
                               + $"l_code := upper('{code}');" + Environment.NewLine
                               + "if l_code is not null then" + Environment.NewLine
                               + "   select count(1) into l_cnt from wrfl_workflow where upper(code) = l_code;" + Environment.NewLine
                               + "end if;" + Environment.NewLine
                               + "if l_cnt = 1 then" + Environment.NewLine
                               + " l_id := ewa_row_seq.nextval;" + Environment.NewLine
                               + "select id into l_wid from wrfl_workflow where upper(code) = l_code;" + Environment.NewLine
                               + " insert into wrfl_workflow_activation(id,id_wrfl,code_wrfl,status)" + Environment.NewLine
                               + " values(l_id,l_wid, l_code,0)" + Environment.NewLine
                               + " returning id into :OUT_ID;" + Environment.NewLine
                               + "end if;" + Environment.NewLine
                               + "end;";
            try
            {
                dbService = new DBService(dbname);
                var (res, errormes, outparam) = await dbService.ExecuteDataWithOut(sql_query, parameters);
                if (!string.IsNullOrEmpty(errormes))
                {
                    is_err = true;
                    mess = errormes;
                }
                else
                {
                    foreach (var prm in outparam.Where(x => x.Value != null && x.Value != DBNull.Value))
                    {
                        if (prm.Key == "OUT_ID" && prm.Value is Oracle.ManagedDataAccess.Types.OracleDecimal oracleDecimal)
                        {
                            idAct = oracleDecimal.Value;
                        }

                    }
                }
            }
            catch (Exception e)
            {
                is_err = true;
                mess = e.Message;
            }
            return (code, idAct, is_err, mess);
        }
        public async Task<(string Code, bool IS_ERR, string ERR_MESS)> UpdWFLStatus(string code, decimal idact, decimal in_status,string errmess)
        {
            bool is_err = false;
            string mess = string.Empty;
            Dictionary<string, Rep_Param> parameters = new Dictionary<string, Rep_Param>();
            parameters.Add("IN_ERR_MESS", new Rep_Param(0, "CLOB", ParameterDirection.Input, null, 0, errmess));

            string sql_query = "update WRFL_WORKFLOW_ACTIVATION" + Environment.NewLine
                             +$"   set status = {in_status}" + Environment.NewLine
                             + "      ,ERR = :IN_ERR_MESS" + Environment.NewLine
                             + $" where id = {idact}";
            try
            {
                dbService = new DBService(dbname);
                var (res, errormes, outparam) = await dbService.ExecuteDataWithOut(sql_query, parameters);
                if (!string.IsNullOrEmpty(errormes))
                {
                    is_err = true;
                    mess = errormes;
                }
                
            }
            catch (Exception e)
            {
                is_err = true;
                mess = e.Message;
            }
            return (code, is_err, mess);
        }
        public async Task<(string Code, bool IS_ERR, string ERR_MESS)> UpdWFLData(string code, decimal idact, string in_data)
        {
            bool is_err = false;
            string mess = string.Empty;
            Dictionary<string, Rep_Param> parameters = new Dictionary<string, Rep_Param>();

            string sql_query = "update WRFL_WORKFLOW_ACTIVATION" + Environment.NewLine
                             + $"   set param = '{in_data}'" + Environment.NewLine
                             + $" where id = {idact}";
            try
            {
                dbService = new DBService(dbname);
                var (res, errormes, outparam) = await dbService.ExecuteDataWithOut(sql_query, parameters);
                if (!string.IsNullOrEmpty(errormes))
                {
                    is_err = true;
                    mess = errormes;
                }

            }
            catch (Exception e)
            {
                is_err = true;
                mess = e.Message;
            }
            return (code, is_err, mess);
        }



        public async Task<(string Code, decimal idAct, bool IS_ERR, string ERR_MESS)> ADDWrokletInfo(string code, decimal idAct)
        {
            bool is_err = false;
            string mess = string.Empty;
            Dictionary<string, Rep_Param> parameters = new Dictionary<string, Rep_Param>();
            parameters.Add("OUT_ID", new Rep_Param(1, "NUMBER", ParameterDirection.Output, 32,
                                            0, null));
            string sql_query = "Declare" + Environment.NewLine
                               + "l_cnt number(32):=0;" + Environment.NewLine
                               + "l_code varchar2(100);" + Environment.NewLine
                               + "l_id number(32):= 0;" + Environment.NewLine
                               + "l_idw number(32):= 0;" + Environment.NewLine
                               + "l_wid number(32):= 0;" + Environment.NewLine
                               + "Begin" + Environment.NewLine
                               + $"l_code := upper('{code}');" + Environment.NewLine
                               + "if l_code is not null then" + Environment.NewLine
                               + "   select count(1) into l_cnt from wrfl_worklet where upper(code) = l_code;" + Environment.NewLine
                               + "end if;" + Environment.NewLine
                               + "if l_cnt = 1 then" + Environment.NewLine
                               + $" l_id := {idAct};" + Environment.NewLine
                               + " l_idw := ewa_row_seq.nextval;" + Environment.NewLine
                               + "select id into l_wid from wrfl_worklet where upper(code) = l_code;" + Environment.NewLine
                               + " insert into wrfl_worklet_log(id,id_w,id_wrfl,code_wrfl,status)" + Environment.NewLine
                               + " values(l_id,l_idw,l_wid, l_code,0)" + Environment.NewLine
                               + " returning id_w into :OUT_ID;" + Environment.NewLine
                               + "end if;" + Environment.NewLine
                               + "end;";
            try
            {
                dbService = new DBService(dbname);
                var (res, errormes, outparam) = await dbService.ExecuteDataWithOut(sql_query, parameters);
                if (!string.IsNullOrEmpty(errormes))
                {
                    is_err = true;
                    mess = errormes;
                }
                else
                {
                    foreach (var prm in outparam.Where(x => x.Value != null && x.Value != DBNull.Value))
                    {
                        if (prm.Key == "OUT_ID" && prm.Value is Oracle.ManagedDataAccess.Types.OracleDecimal oracleDecimal)
                        {
                            idAct = oracleDecimal.Value;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                is_err = true;
                mess = e.Message;
            }
            return (code, idAct,is_err, mess);
        }
        public async Task<(string Code, decimal idAct, bool IS_ERR, string ERR_MESS)> ADDTaskInfo(string code, decimal idAct, decimal idActW, string sql_act,string sql_param)
        {
            bool is_err = false;
            string mess = string.Empty;
            Dictionary<string, Rep_Param> parameters = new Dictionary<string, Rep_Param>();
            parameters.Add("OUT_ID", new Rep_Param(1, "NUMBER", ParameterDirection.Output, 32,
                                            0, null));
            sql_act = sql_act.Replace("'", "''");
            string sql_query = "Declare" + Environment.NewLine
                               + "l_cnt number(32):=0;" + Environment.NewLine
                               + "l_code varchar2(100);" + Environment.NewLine
                               + "l_id number(32):= 0;" + Environment.NewLine
                               + "l_idw number(32);" + Environment.NewLine
                               + "l_idt number(32);" + Environment.NewLine
                               + "l_tid number(32):= 0;" + Environment.NewLine
                               + "l_action clob;" + Environment.NewLine
                               + "l_param clob;" + Environment.NewLine
                               + "Begin" + Environment.NewLine
                               + $"l_code := upper('{code}');" + Environment.NewLine
                               + $"l_action := '{sql_act}';" + Environment.NewLine
                               + $"l_param := '{sql_param}';" + Environment.NewLine
                               + "if l_code is not null then" + Environment.NewLine
                               + "   select count(1) into l_cnt from wrfl_task where upper(code) = l_code;" + Environment.NewLine
                               + "end if;" + Environment.NewLine
                               + "if l_cnt = 1 then" + Environment.NewLine
                               + $" l_id := {idAct};" + Environment.NewLine
                               + $" l_idw := {idActW};" + Environment.NewLine
                               + " l_idt := ewa_row_seq.nextval;" + Environment.NewLine
                               + "select id into l_tid from wrfl_task where upper(code) = l_code;" + Environment.NewLine
                               + " insert into wrfl_task_log(id,id_w, id_t,id_task,code_task,data_action,data_param,status)" + Environment.NewLine
                               + " values(l_id,l_idw,l_idt, l_tid, l_code,l_action,l_param,0)" + Environment.NewLine
                               + " returning id_t into :OUT_ID;" + Environment.NewLine
                               + "end if;" + Environment.NewLine
                               + "end;";
            try
            {
                dbService = new DBService(dbname);
                var (res, errormes, outparam) = await dbService.ExecuteDataWithOut(sql_query, parameters);
                if (!string.IsNullOrEmpty(errormes))
                {
                    is_err = true;
                    mess = errormes;
                }
                else
                {
                    foreach (var prm in outparam.Where(x => x.Value != null && x.Value != DBNull.Value))
                    {
                        if (prm.Key == "OUT_ID" && prm.Value is Oracle.ManagedDataAccess.Types.OracleDecimal oracleDecimal)
                        {
                            idAct = oracleDecimal.Value;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                is_err = true;
                mess = e.Message;
            }
            return (code, idAct,is_err, mess);
        }

    }
}
