﻿@attribute [Authorize]
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Ra<PERSON>zen
@using Radzen.Blazor
@using EWA.Services
@using System
@using System.Collections.Generic
@using System.Linq
@using System.Threading.Tasks

<RadzenCard class=" rz-pt-0" Variant="Variant.Text" >
    <RadzenStack Orientation="Orientation.Vertical"   >
        <RadzenFormField Text="Подразделение" Variant="Variant.Text" Style="flex: 1;">
                <RadzenTextBox Value="@user.DEPARTMENT" Disabled="true"/>
            </RadzenFormField>
        <RadzenFormField Text="Контактный телефон" Variant="Variant.Text" Style="flex: 1;">
                <RadzenTextBox Value="@user.PHONE" Disabled="true" />
            </RadzenFormField>
        <RadzenFormField Text="Адрес электронной почты" Variant="Variant.Text" Style="flex: 1;">
                <RadzenTextBox Value="@user.EMAIL" Disabled="true" />
            </RadzenFormField>
    </RadzenStack>
</RadzenCard>


@code {
    [Inject]
    protected DialogService DialogService { get; set; }
    [Inject]
    protected SIBService.SecurityService Security { get; set; }
    [Parameter] public EWA.Models.SIB_Models.SIB_USERS user { get; set; }
}