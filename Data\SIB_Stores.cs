﻿using Microsoft.AspNetCore.Identity;
using EWA.Models;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;
using Humanizer;
using Microsoft.Extensions.Logging;

namespace EWA.Data
{
    public class SIB_Stores
    {
        public class SIB_UserStore : IUserStore<SIB_Models.SIB_USERS>, IUserPasswordStore<SIB_Models.SIB_USERS>, IQueryableUserStore<SIB_Models.SIB_USERS>
        {
            private readonly DBContext _context;
            private readonly ILogger<SIB_UserStore> _logger;
            private readonly PasswordHasher<SIB_Models.SIB_USERS> _passwordHasher;
            public SIB_UserStore(DBContext context, ILogger<SIB_UserStore> logger)
            {
                _context = context;
                _logger = logger;
                _passwordHasher = new PasswordHasher<SIB_Models.SIB_USERS>();
            }
            public IQueryable<SIB_Models.SIB_USERS> Users => _context.SIB_USERS;
            public async  Task<IdentityResult> CreateAsync(SIB_Models.SIB_USERS user, CancellationToken cancellationToken)
            {
                _logger.LogInformation($"CreateAsync called for user: {user.CODE}");
                _context.SIB_USERS.Add(user);
                await _context.SaveChangesAsync(cancellationToken);
                return IdentityResult.Success;
                //return _context.SaveChangesAsync(cancellationToken).ContinueWith(task => IdentityResult.Success, cancellationToken);
            }
            public Task<IdentityResult> DeleteAsync(SIB_Models.SIB_USERS user, CancellationToken cancellationToken)
            {
                _context.SIB_USERS.Remove(user);
                return _context.SaveChangesAsync(cancellationToken).ContinueWith(task => IdentityResult.Success, cancellationToken);
            }
            public Task<SIB_Models.SIB_USERS> FindByIdAsync(string userId, CancellationToken cancellationToken)
            {
                if (decimal.TryParse(userId, out var decimalId))
                {
                    return _context.SIB_USERS.FindAsync(new object[] { decimalId }, cancellationToken).AsTask();
                }
                return Task.FromResult<SIB_Models.SIB_USERS>(null);
            }
            public Task<SIB_Models.SIB_USERS> FindByNameAsync(string normalizedUserName, CancellationToken cancellationToken)
            {
                return _context.SIB_USERS.SingleOrDefaultAsync(u => u.CODE.ToUpper() == normalizedUserName, cancellationToken);
            }
            public Task<string> GetNormalizedUserNameAsync(SIB_Models.SIB_USERS user, CancellationToken cancellationToken)
            {
                return Task.FromResult(user.NormCODE);
            }
            public Task<string> GetUserIdAsync(SIB_Models.SIB_USERS user, CancellationToken cancellationToken)
            {
                return Task.FromResult(user.ID_STR);
            }
            public Task<string> GetUserNameAsync(SIB_Models.SIB_USERS user, CancellationToken cancellationToken)
            {
                return Task.FromResult(user.CODE);
            }
            public Task SetNormalizedUserNameAsync(SIB_Models.SIB_USERS user, string normalizedName, CancellationToken cancellationToken)
            {
                //не надо апдейтить CODE на UPPER!
                //user.CODE = normalizedName;
                return Task.CompletedTask;
            }
            public Task SetUserNameAsync(SIB_Models.SIB_USERS user, string userName, CancellationToken cancellationToken)
            {
                user.CODE = userName;
                return Task.CompletedTask;
            }
            public async Task<IdentityResult> UpdateAsync(SIB_Models.SIB_USERS user, CancellationToken cancellationToken)
            {
                //_context.SIB_USERS.Update(user);
                //return _context.SaveChangesAsync(cancellationToken).ContinueWith(task => IdentityResult.Success, cancellationToken);
                try
                {
                    var trackedEntity = await _context.SIB_USERS.FindAsync(new object[] { user.ID }, cancellationToken);
                    if (trackedEntity == null)
                    {
                        return IdentityResult.Failed(new IdentityError { Description = $"User with ID '{user.ID}' not found." });
                    }
                    _context.Entry(trackedEntity).CurrentValues.SetValues(user);

                    await _context.SaveChangesAsync(cancellationToken);
                    return IdentityResult.Success;
                }
                catch (Exception ex)
                {
                    return IdentityResult.Failed(new IdentityError { Description = $"Update failed: {ex.Message}" });
                }

            }
            public void Dispose()
            {
            }
            public Task SetPasswordHashAsync(SIB_Models.SIB_USERS user, string passwordHash, CancellationToken cancellationToken)
            {
                user.PASSWORDHASH = passwordHash;
                return Task.CompletedTask;
            }
            public Task<string> GetPasswordHashAsync(SIB_Models.SIB_USERS user, CancellationToken cancellationToken)
            {
                return Task.FromResult(user.PASSWORDHASH);
            }
            public Task<bool> HasPasswordAsync(SIB_Models.SIB_USERS user, CancellationToken cancellationToken)
            {
                return Task.FromResult(user.PASSWORDHASH != null);
            }
        }
    }
}

