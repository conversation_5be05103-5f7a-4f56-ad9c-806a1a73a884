﻿@page "/Error"
@inject NavigationManager Navigation
@inject ILogger<Error> Logger

<PageTitle>О<PERSON><PERSON><PERSON><PERSON>а</PageTitle>

<h3 class="text-danger">Произошла ошибка</h3>

<p>К сожалению, что-то пошло не так. Попробуйте позже.</p>

<button class="btn btn-primary" @onclick="GoHome">На главную</button>

@code {
    private void GoHome()
    {
        Navigation.NavigateTo("/");
    }

    protected override void OnInitialized()
    {
        Logger.LogError("Пользователь попал на страницу /Error");
    }
}
