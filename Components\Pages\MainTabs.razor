﻿@attribute [Authorize]
@inject IJSRuntime JSRuntime
@inherits LayoutComponentBase
@inject NavigationManager NavigationManager
@inject DialogService DialogService
@inject ContextMenuService ContextMenuService
@inject TooltipService TooltipService
@inject NotificationService NotificationService
@inject UIService.TabService TabService
@inject EWA.Services.RepService.MenuService MenuService
@using EWA.Enums
@using EWA.Models
@using System
@using System.Collections.Generic
@using System.Linq
@using System.Threading.Tasks
@using System.Reflection

@using System.Net.Http
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.AspNetCore.Authorization

@using <PERSON><PERSON>zen
@using Ra<PERSON>zen.Blazor
@using EWA.Services
@using EWA.Components
@using static EWA.Models.REP_Models

<style>
    ul[role=tablist] {
        flex-wrap: wrap;
    }

</style>


<RadzenButton Visible="@(tb.SubTabs.Count() > 0)" ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="tab_close" Click="ClearTabs" Text="Закрыть все вкладки"/>

<RadzenStack style="height: 100%; width:100%" Orientation="Orientation.Vertical">
    @RenderTabs(tb)
</RadzenStack>





@code {
    protected override void OnInitialized()
    {
        TabService.OnAddTopTab = (namegrid, codeobj, nameobj, main_app_Code) => AddTopTab(namegrid, codeobj, nameobj, main_app_Code);
        TabService.OnAddBottomTab = (tb) => AddBottomTab(tb);
        TabService.OnChangeSelectedRow = (rowdata, paramdata, tab) => ChangeSelectedRow(rowdata, paramdata, tab);
        TabService.OnChangeLoadState = (loadstate, tab) => ChangeLoadState(loadstate, tab);
    }
    private void ClearTabs()
    {
        tb = new TabData
            {
                Index = -1,
                CodeObj = "head",
                NameObj = "hname",
                PathIndex = "->",
                Show = true,
                IsSelect = true,
                CountLvl = 1
            };
        Tabs.Clear();
    }
    private Dictionary<int, TabData> Tabs = new();

    private TabData tb = new TabData
        {
            Index = -1,
            CodeObj = "head",
            NameObj = "hname",
            PathIndex="->",
            Show = true,
            IsSelect = true,
            CountLvl = 1
        };

    private RenderFragment RenderTabs(TabData tab) => __builder =>
    {
        if (tab.ParentTab != null)
        {
            <RadzenTabsItem Text="@(tab.CodeObj + tab.Index)" Visible="@tab.Show" 
                            Style="@GetStyle(tab.load_state)">
                <Template>
                    <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Start" Gap="0rem">
                        <RadzenButton ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Click="@(() => Change_Buttom_tabs(tab.Index, tab.ParentTab))" Title=@(tab.NameObj)> @(tab.TabNameShrt)</RadzenButton>
                        <RadzenButton Icon="close" ButtonStyle="ButtonStyle.Base" Size="ButtonSize.ExtraSmall" Variant="Variant.Text" @onclick:stopPropagation Click="@(() => RemoveTab(@tab.ParentTab, tab.Index))" Disabled="@(tab.Kind == TabPageKind.AutoMode ? true : false)" />
                    </RadzenStack>
                </Template>
                <ChildContent>
                    <DynamicComponent Type="GetComponentType(tab.NameGrid)"
                                      Parameters="@(new Dictionary<string, object> {{ "Tab", tab}
                                                                                   })" />
                    @if (tab.SubTabs.Where(x => x.Value.Kind != TabPageKind.Modal).Count() > 0)
                    {
                        <RadzenTabs RenderMode="TabRenderMode.Client" @bind-SelectedIndex="tab.selectedIndex">
                            <Tabs>
                                @foreach (var tb in tab.SubTabs.Where(x => x.Value.Kind != TabPageKind.Modal))
                                {
                                    @RenderTabs(tb.Value);
                                }
                            </Tabs>

                        </RadzenTabs>
                    }
                </ChildContent>
            </RadzenTabsItem>
        }

        if (tab.SubTabs.Count > 0 && tab.ParentTab == null)
        {
            <RadzenTabs RenderMode="TabRenderMode.Client" @bind-SelectedIndex="tab.selectedIndex">
                <Tabs>
                    @foreach (var tb in tab.SubTabs)
                    {
                        @RenderTabs(tb.Value);
                    }
                </Tabs>
            </RadzenTabs>
        }
    };

    
    private string GetStyle(int load_state)
    {
        return load_state switch
        {
            0 => "border-top-color: var(--rz-tabs-background-color); border: var(--rz-tabs-border);  box-shadow: var(--rz-tabs-shadow)",
            1 => "border-top-color: var(--rz-danger)",
            2 => "border-top-color: var(--rz-warning)"
        };
    }

    
    private void Change_Buttom_tabs(int index, TabData parentTab)
    {
        parentTab.SetSelectedIndex(index);
    }

    private void AddTopTab(string namegrid, string codeobj, string nameobj, string main_app_Code)
    {
        tb.AddSubTab(namegrid, codeobj, nameobj, new(), new(), "", main_app_Code, TabPageKind.Standart);
        StateHasChanged();
    }



    private void AddBottomTab(TabDataPrm tbPrm)
    {
        if (tbPrm.pagekind == TabPageKind.Modal)
        {
            TabData tb = tbPrm.parentTab.AddModalSubTab(tbPrm.namegrid, tbPrm.codeobj, tbPrm.nameobj, tbPrm.transitionParams, tbPrm.linkedParam, tbPrm.visibilityFormula, tbPrm.parentTab.Main_App_Code);

            ModalEditGrid(tb, tbPrm.nameobj);
            tb.RemoveModalTab();
            return;
        }
        if (tbPrm.pagekind == TabPageKind.CntPage)
        {
            string[] numbersAsStrings=tbPrm.lstpage.Split(',');
            foreach (string number in numbersAsStrings)
            {
                int num = int.Parse(number.Trim());
                List<REP_Models.ParamMetadata> transitionParams_ = new List<ParamMetadata>();
                foreach (var prm in tbPrm.transitionParams)
                {
                    var tr = (ParamMetadata)prm.Clone();
                    if (tr.IS_SYSTEM == 1 && tr.TYPE_SYSTEM == "CNT_PAGE")
                    {
                        tr.DEFAULTVALUE = num.ToString();
                    }
                    transitionParams_.Add(tr);
                }

                tbPrm.parentTab.AddSubTab(tbPrm.namegrid, tbPrm.codeobj, tbPrm.nameobj, transitionParams_, tbPrm.linkedParam, tbPrm.visibilityFormula, string.Empty, tbPrm.pagekind);
            }
        }
        if (tbPrm.pagekind == TabPageKind.Standart || tbPrm.pagekind == TabPageKind.AutoMode)
        {
            tbPrm.parentTab.AddSubTab(tbPrm.namegrid, tbPrm.codeobj, tbPrm.nameobj, tbPrm.transitionParams, tbPrm.linkedParam, tbPrm.visibilityFormula, string.Empty, tbPrm.pagekind);
            
        }
        StateHasChanged();
    }


    private void RemoveTab(TabData parentTab, int index)
    {
        parentTab.RemoveTab(index);
        StateHasChanged();
    }

    private void ChangeSelectedRow(IDictionary<string, object> rowdata, IDictionary<string, Rep_Param> paramdata, Models.TabData tab)
    {
        tab.ChangeSelectedRow(rowdata, paramdata);
        StateHasChanged();
    }

    public void ChangeLoadState(int loadstate, Models.TabData tab)
    {
        tab.load_state = loadstate;
        StateHasChanged();
    }


    private Type GetComponentType(string componentName)
    {

        return Assembly.GetExecutingAssembly().GetTypes()
            .FirstOrDefault(t => t.Name == componentName);
    }

    private void ModalEditGrid(TabData tb, string name)
    {

        string code_form = "EditGrid";
        Type componentType = AppDomain.CurrentDomain.GetAssemblies()
                                        .SelectMany(assembly => assembly.GetTypes())
                                        .FirstOrDefault(type => type.Name == code_form);

         DialogService.Open(name,
                            ds => (RenderFragment)(builder =>
                                {
                                    builder.OpenComponent(0, Type.GetType(componentType.FullName));
                                    builder.AddComponentParameter(1, "Tab", tb);
                                    builder.CloseComponent();
                                }),
                            new DialogOptions { Draggable = true, Resizable = true }
                     );

        
    }


}