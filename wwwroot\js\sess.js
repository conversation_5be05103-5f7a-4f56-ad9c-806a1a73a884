window.initializeActivityTracking = (dotnetRef, timeoutMinutes = 1) => {
    const events = ['mousemove', 'keydown', 'click', 'scroll'];
    const sessionTimeoutMs = timeoutMinutes * 60 * 1000;
    const checkInterval = 30000; // проверка каждые 30 секунд
    const throttleDelay =  5000; // обновление активности в localstorage раз в 5 секунд
    
    let sessionTimeoutId;
    let lastUpdateTime = 0;
    
    // функция сброса таймера
    const resetSessionTimer = () => {
        const now = new Date().getTime();
        if (now - lastUpdateTime > throttleDelay) {
            localStorage.setItem('lastActivityTime', now.toString());
            lastUpdateTime = now;
            console.log('Activity time updated');
        }
    };

    // функция проверки таймаута
    const checkSessionTimeout = () => {
        const lastActivity = parseInt(localStorage.getItem('lastActivityTime') || '0');
        const now = new Date().getTime();
        const elapsed = now - lastActivity;
        
        if (elapsed >= sessionTimeoutMs) {
            localStorage.setItem('isSessionLocked', 'true');
            dotnetRef.invokeMethodAsync('OnSessionTimeout');
        }
    };
    
    // обработка события storage для синхронизации между вкладками
    const handleStorageChange = (event) => {
        console.log(`Storage event: ${event.key} = ${event.newValue}`);
        
        if (event.key === 'isSessionLocked') {
            if (event.newValue === 'true' && dotnetRef) {
                // другая вкладка заблокировала сессию, блокируем текущую
                console.log('Session locked from another tab');
                dotnetRef.invokeMethodAsync('OnSessionTimeout');
            } else if (event.newValue === 'false' && dotnetRef) {
                // другая вкладка разблокировала сессию, разблокируем текущую
                console.log('Session unlocked from another tab');
                dotnetRef.invokeMethodAsync('HandleSessionUnlock');
            }
        } else if (event.key === 'sessionUnlockTime') {
            // доп. триггер для гарантии срабатывания разблокировки
            const isLocked = localStorage.getItem('isSessionLocked');
            if (isLocked === 'false' && dotnetRef) {
                console.log('Session unlock triggered by timestamp');
                dotnetRef.invokeMethodAsync('HandleSessionUnlock');
            }
        } else if (event.key === 'lastActivityTime') {
            // активность в другой вкладке, сбрасываем таймер
            resetSessionTimer();
        }
    };
    
    // прослушиватель событий активности
    events.forEach(event => {
        document.addEventListener(event, resetSessionTimer);
    });
    
    // прослушиватель изменения localStorage
    window.addEventListener('storage', handleStorageChange);
    
    // инициализация при загрузке
    resetSessionTimer();
    
    // установка интервала проверки таймаута
    sessionTimeoutId = setInterval(checkSessionTimeout, checkInterval);
    
    // Возвращаем функцию очистки
    return () => {
        events.forEach(event => {
            document.removeEventListener(event, resetSessionTimer);
        });
        window.removeEventListener('storage', handleStorageChange);
        clearInterval(sessionTimeoutId);
    };
};

window.blockUI = function () {
    console.log('blockUI');
    document.body.style.pointerEvents = 'none';
    const overlay = document.querySelector('.session-lock-overlay');
    if (overlay) {
        overlay.style.pointerEvents = 'auto';
    }
}

window.unblockUI = function () {
    console.log('unblockUI');
    const isLocked = localStorage.getItem('isSessionLocked');
    if (isLocked !== 'true') {
        document.body.style.pointerEvents = 'auto';
    }
}

window.clearSessionStorage = function () {
    localStorage.removeItem('lastActivityTime');
    localStorage.removeItem('isSessionLocked');
}


// автоматическая очистка при переходе на страницу логина
//if (window.location.pathname.toLowerCase() === '/login' || 
if (window.location.pathname.toLowerCase() === '/account/logout') {
    window.clearSessionStorage();
}


// функция разблокировки сессии
window.unlockSession = function() {
    // установка флага разблокировки в localStorage
    localStorage.setItem('isSessionLocked', 'false');
    // добавление временной метки для гарантии срабатывания события storage
    localStorage.setItem('sessionUnlockTime', new Date().getTime().toString());
    console.log('Session unlocked JS');
    return true;
}


window.getSessionState = function() {
    return {
        isLocked: localStorage.getItem('isSessionLocked') === 'true',
        lastActivityTime: localStorage.getItem('lastActivityTime')
    };
};

/*
// проверка состояния сессии
window.checkSessionOnLoad = function () {
    const isLocked = localStorage.getItem('isSessionLocked');
    return isLocked === 'true';
}

// очистка при закрытии вкладки/браузера
window.addEventListener('beforeunload', function () {
    window.clearSessionStorage();
});
*/


