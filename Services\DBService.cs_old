﻿
using Microsoft.EntityFrameworkCore.Migrations.Operations;
using Oracle.ManagedDataAccess.Client;
using System;
using System.Data;
using System.Reflection;
using static EWA.Models.REP_Models;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;
using System.Text.RegularExpressions;
using EWA.Models;
using Microsoft.EntityFrameworkCore.Storage.Internal;
using System.Globalization;
using DocumentFormat.OpenXml.Spreadsheet;
using Irony.Parsing;
using Microsoft.AspNetCore.Http;
using System.Runtime.InteropServices;
using DocumentFormat.OpenXml.Drawing.Charts;
using static EWA.Models.AML_Models;
using Microsoft.AspNetCore.Components;
using DocumentFormat.OpenXml.Office.Word;

namespace EWA.Services
{
    public class DBService
    {
        private readonly string _connectionString;
        
        private EWA.Services.SIBService.SecurityService _security;

        public DBService(string connectionString)
        {
            _connectionString = connectionString;
        }

        public DBService(string connectionString, SIBService.SecurityService security)
        {
            _connectionString = connectionString;
            _security = security;
        }

        private readonly string[] dateformats = { "dd.MM.yyyy H:mm:ss", "dd.MM.yyyy HH:mm:ss", "dd/MM/yyyy", "dd/MM/yyyy HH:mm:ss",
            "MM/dd/yyyy", "MM/dd/yyyy HH:mm:ss", "yyyy-MM-dd",  "MM.dd.yy", "dd.MM.yyyy" };

        private static bool TryParseDate(string input, out DateTime dateResult, string[] dateformats)
        {
            return DateTime.TryParseExact(
                input,
                dateformats,
                CultureInfo.InvariantCulture,
                DateTimeStyles.None,
                out dateResult
            );
        }

        private OracleDbType GetColumnType(string typeName)
        {
            return typeName.ToUpper() switch
            {
                "NUMBER" => OracleDbType.Decimal, // or typeof(decimal)
                "FLOAT" => OracleDbType.Decimal,
                "INT" => OracleDbType.Long,
                "VARCHAR2" => OracleDbType.Varchar2,
                "CHAR" => OracleDbType.Char,
                "CLOB" => OracleDbType.Clob,
                "BOOL" => OracleDbType.Long,
                "DATE" => OracleDbType.Date,
                "TIMESTAMP(6)" => OracleDbType.TimeStamp,
                _ => OracleDbType.Varchar2,
            };
            
        }
        private void AddParameters(OracleCommand command, Dictionary<string, Rep_Param> parameters)
        {
            if (parameters == null) return;
            /*для OUT переменных с типом "строка" обязателен size.. берем из DATALENGTH иначе х...им 4к*/
            foreach (var param in parameters)
            {
                if (command.Parameters.Contains(param.Key)) continue;

                var oracleType = GetColumnType(param.Value.DATATYPE);
                var parameter = new OracleParameter
                {
                    ParameterName = param.Key,                  
                    Value = param.Value.Val ?? DBNull.Value,
                    OracleDbType = oracleType,
                    Direction = param.Value.Direct,                    
                };
                
                if((oracleType == OracleDbType.Varchar2 || oracleType == OracleDbType.Char) && param.Value.DATALENGTH.HasValue)
                {
                    parameter.Size = param.Value.DATALENGTH ?? 4000;
                }

                if (param.Value.DATATYPE == "DATE")
                {   
                    if (TryParseDate(parameter.Value.ToString(), out DateTime result, dateformats))
                    {
                        //  parameter.OracleDbType = GetColumnType(param.Value.DATATYPE);
                       //   parameter.Value = result.Date;
                       parameter.Value = result;
                    }
                    else
                    {
                        parameter.Value = DBNull.Value;
                    }
                }
                if (param.Value.DATATYPE == "NUMBER")
                {
                    if (decimal.TryParse(parameter.Value.ToString(), out decimal number))
                    {
                        //  parameter.OracleDbType = GetColumnType(param.Value.DATATYPE);
                        //   parameter.Value = result.Date;
                        parameter.Value = number;
                        int g = 0;
                    }
                    else
                    {
                        parameter.Value = DBNull.Value;
                    }
                }
                command.Parameters.Add(parameter);
            }
            command.Parameters.Add(new OracleParameter
            {
                ParameterName = "EWA_OUT_RESULT",
                OracleDbType = OracleDbType.Int32,
                Value = 1,
                Direction = ParameterDirection.Output,
            });

            command.Parameters.Add(new OracleParameter
            {
                ParameterName = "EWA_OUT_MESSAGE",
                OracleDbType = OracleDbType.Varchar2,
                Value = "",
                Size = 4000,
                Direction = ParameterDirection.Output,
            });

         


            if (_security != null)
            {
                command.Parameters.Add(new OracleParameter
                {
                    ParameterName = "EWA_IN_USERCODE",
                    OracleDbType = OracleDbType.Varchar2,
                    Value = _security.User.CODE,
                    Size = 100,
                    Direction = ParameterDirection.Input,
                });

                command.Parameters.Add(new OracleParameter
                {
                    ParameterName = "EWA_IN_USERNAME",
                    OracleDbType = OracleDbType.Varchar2,
                    Value = _security.User.NAME,
                    Size = 100,
                    Direction = ParameterDirection.Input,
                });
            }
            command.Parameters.Add(new OracleParameter
            {
                ParameterName = "EWA_IN_CURDATE",
                OracleDbType = OracleDbType.Date,
                Value = DateTime.Now,                
                Direction = ParameterDirection.Input,
            });

            int gg = 0;

        }

        public async Task<(IEnumerable<IDictionary<string, object>> Items, string ErrorMessage)> GetDataSimple(string baseSql,
                                                                                    Dictionary<string, object> parameters = null)
        {
            var items = new List<IDictionary<string, object>>();
            string errorMessage = null;

            try
            {
                using (var connection = new OracleConnection(_connectionString))
                {
                    await Task.Run(() => connection.Open());

                    var Sql = $@"{baseSql}";

                    using (var command = new OracleCommand(Sql, connection))
                    {
                        command.BindByName = true;

                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                command.Parameters.Add(new OracleParameter(param.Key, param.Value ?? DBNull.Value));
                            }
                        }
                        using (var transaction = connection.BeginTransaction())
                        {
                            using (var reader = await Task.Run(() => command.ExecuteReader()))
                            {
                                while (await Task.Run(() => reader.Read()))
                                {
                                    var item = new Dictionary<string, object>();

                                    for (int i = 0; i < reader.FieldCount; i++)
                                    {
                                        item[reader.GetName(i)] = reader.GetValue(i);
                                    }

                                    items.Add(item);
                                }
                            }
                            transaction.Commit();
                        }
                    }
                }
            }
            catch (OracleException ex)
            {
                errorMessage = $"Oracle Error: {ex.Message}";
            }
            catch (Exception ex)
            {
                errorMessage = $"General Error: {ex.Message}";
            }

            return (items, errorMessage);
        }
        public async Task<(IEnumerable<SPRShort> Items, string ErrorMessage)> GetDataSPRShort(string baseSql, Dictionary<string, object> parameters)
        {
            var items = new List<SPRShort>();
            string errorMessage = null;
            try           
            {
                using (var connection = new OracleConnection(_connectionString))
                {
                    await Task.Run(() => connection.Open());

                    using (var command = new OracleCommand(baseSql, connection))
                    {
                        command.BindByName = true;
                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                if (param.Value is DateTime dateValue)
                                {
                                    string oracleDateFormat = dateValue.ToString("yyyy-MM-dd HH:mm:ss");
                                    command.Parameters.Add(new OracleParameter(param.Key, oracleDateFormat));

                                }
                                else
                                {
                                    command.Parameters.Add(new OracleParameter(param.Key, param.Value ?? DBNull.Value));
                                }
                            }
                        }
                        DataSet dataSet = new DataSet();
                        using (var dataAdapter = new OracleDataAdapter())
                        {
                            dataAdapter.SelectCommand = command;
                            await Task.Run(() => dataAdapter.Fill(dataSet));
                            System.Data.DataTable dataTable = dataSet.Tables[0];

                            if (dataTable.Columns.Count == 3)
                            {
                                items = dataTable.Rows.Cast<DataRow>().Select(x => new SPRShort
                                {
                                    ID = Convert.ToDecimal(x[0]),
                                    Name = x[2]?.ToString(),
                                    Code = x[1] != DBNull.Value ? x[1]?.ToString() : null
                                }).ToList();

                            }
                            else
                            {
                                items = dataTable.Rows.Cast<DataRow>().Select(x => new SPRShort { ID = Convert.ToDecimal(x[0]), Name = x[1].ToString() }).ToList();
                            }
                        }
                    }
                }
            }
            catch (OracleException ex)
            {
                errorMessage = $"Oracle Error: {ex.Message}";
            }
            catch (Exception ex)
            {
                errorMessage = $"General Error: {ex.Message}";
            }

            return (items, errorMessage);
        }

        public async Task<(IEnumerable<SPRShort> Items, string ErrorMessage)> GetDataSPRShort(string baseSql,  Dictionary<string, object> parameters, string datatype, List<REP_Models.ColumnMetadata> metadata_column)
        {
            var items = new List<SPRShort>();
            string errorMessage = null;
            try
            {
                using (var connection = new OracleConnection(_connectionString))
                {
                    await Task.Run(() => connection.Open());

                    using (var command = new OracleCommand(baseSql, connection))
                    {
                        command.BindByName = true;
                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                if (param.Value is DateTime dateValue)
                                {
                                    string oracleDateFormat = dateValue.ToString("yyyy-MM-dd HH:mm:ss");
                                    command.Parameters.Add(new OracleParameter(param.Key, oracleDateFormat));

                                }
                                else
                                {
                                    command.Parameters.Add(new OracleParameter(param.Key, param.Value ?? DBNull.Value));
                                }
                            }
                        }
                        DataSet dataSet = new DataSet();
                        using (var dataAdapter = new OracleDataAdapter())
                        {
                            dataAdapter.SelectCommand = command;
                            await Task.Run(() => dataAdapter.Fill(dataSet));
                            System.Data.DataTable dataTable = dataSet.Tables[0];

                            if (dataTable.Columns.Count == 3)
                            {/*
                                items = dataTable.Rows.Cast<DataRow>().Select(x => new SPRShort
                                {                                   

                                    ID = datatype == "NUMBER" ? Convert.ToDecimal(x[0]) : x[0].ToString(),                                 
                                    Name = metadata_column[2].DOMAINCODE=="NAME" || metadata_column[2].NAME == "NAME"?  x[2]?.ToString(): x[1].ToString(),
                                    Code= metadata_column[2].DOMAINCODE == "NAME" || metadata_column[2].NAME == "NAME" ? x[1]?.ToString() : x[2].ToString()
                                    //Code = x[1] != DBNull.Value ? x[1]?.ToString() : null
                                }).ToList();
                                */
                                items = dataTable.Rows.Cast<DataRow>().Select(x => new SPRShort
                                {

                                    ID = Convert.ToDecimal(x[metadata_column.FindIndex(x => x.DOMAINCODE.ToUpper() == "ID")]),
                                    Name = x[metadata_column.FindIndex(x => x.DOMAINCODE.ToUpper() == "NAME")]?.ToString(),
                                    Code = x[metadata_column.FindIndex(x => x.DOMAINCODE.ToUpper() == "CODE")]?.ToString(),
                                    //Code = x[1] != DBNull.Value ? x[1]?.ToString() : null
                                }).ToList();

                                

                            }
                            else
                            {
                                items = dataTable.Rows.Cast<DataRow>().Select(x => new SPRShort { ID = datatype == "NUMBER" ? Convert.ToDecimal(x[0]) : x[0].ToString(), Name = x[1].ToString() }).ToList();
                            }
                        }
                    }
                }
            }
            catch (OracleException ex)
            {
                errorMessage = $"Oracle Error: {ex.Message}";
            }
            catch (Exception ex)
            {
                errorMessage = $"General Error: {ex.Message}";
            }

            return (items, errorMessage);
        }
        public async Task<(IEnumerable<SPRShort> Items, string ErrorMessage)> GetDataDIMShort(string baseSql)
        {
            var items = new List<SPRShort>();
            string errorMessage = null;
            try
            {
                using (var connection = new OracleConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new OracleCommand(baseSql, connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var sprShort = new SPRShort
                                {
                                    ID = reader.GetDecimal(reader.GetOrdinal("ID")),
                                    Code = reader.GetString(reader.GetOrdinal("CODE")),
                                    Name = reader.GetString(reader.GetOrdinal("NAME"))
                                    
                                };

                                items.Add(sprShort);
                            }
                        }
                    }
                }
            }
            catch (OracleException ex)
            {
                errorMessage = $"Oracle Error: {ex.Message}";
            }
            catch (Exception ex)
            {
                errorMessage = $"General Error: {ex.Message}";
            }

            return (items, errorMessage);
        }
        public async Task<(IEnumerable<IDictionary<string, object>> Items, int count, string ErrorMessage)> GetDataPagination(
                        string baseSql,
                        int skip,
                        int take,
                        Dictionary<string, object> parameters = null,
                        string filter = null,
                        string orderBy = null)
        {
            var items = new List<IDictionary<string, object>>();
            int count = 0;
            string errorMessage = null;
            try
            {
                using (var connection = new OracleConnection(_connectionString))
                {
                    await Task.Run(() => connection.Open());
                    using (var enableParallelCommand = new OracleCommand("ALTER SESSION ENABLE PARALLEL QUERY", connection))
                    {
                        await enableParallelCommand.ExecuteNonQueryAsync();
                    }

                    var paginatedSql = $@"
                SELECT * FROM (
                    SELECT a.*, ROWNUM rnum FROM (
                        SELECT r.*, count(1) over () cn FROM ({baseSql}) r
                        {(string.IsNullOrEmpty(filter) ? "" : "WHERE " + filter)}
                        {(string.IsNullOrEmpty(orderBy) ? "" : "ORDER BY " + orderBy)}
                    ) a
                    WHERE ROWNUM <= :take + :skip
                )
                WHERE rnum > :skip";
                    paginatedSql = queryReplace(paginatedSql, parameters);

                    using (var command = new OracleCommand(paginatedSql, connection))
                    {
                        command.BindByName = true;
                        command.Parameters.Add(new OracleParameter("skip", OracleDbType.Decimal, skip, ParameterDirection.Input));
                        command.Parameters.Add(new OracleParameter("take", OracleDbType.Decimal, take, ParameterDirection.Input));

                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                               
                                if (param.Value is DateTime dateValue)
                                {                                   
                                        command.Parameters.Add(new OracleParameter
                                        {
                                            ParameterName = param.Key,
                                            OracleDbType = OracleDbType.Date,
                                            Value = param.Value
                                        });
                                }
                                else
                                {
                                    if (param.Key == "p_dpt")
                                    {
                                        command.Parameters.Add(new OracleParameter(param.Key, OracleDbType.Decimal, param.Value ?? DBNull.Value,  ParameterDirection.Input));
                                    }
                                    else
                                    {
                                        command.Parameters.Add(new OracleParameter(param.Key, param.Value ?? DBNull.Value));
                                    }
                                }
                            }
                        }
                        using (var transaction = connection.BeginTransaction())
                        {
                            using (var reader = await Task.Run(() => command.ExecuteReader()))
                            {
                                while (await Task.Run(() => reader.Read()))
                                {
                                    var item = new Dictionary<string, object>();

                                    for (int i = 0; i < reader.FieldCount; i++)
                                    {
                                        if (reader.GetName(i)=="RNUM")
                                        {
                                            continue;
                                        }
                                        if (reader.GetName(i) == "CN")
                                        {
                                            count = Convert.ToInt32(reader.GetValue(i));
                                        }
                                        item[reader.GetName(i)] = reader.GetValue(i);
                                    }

                                    items.Add(item);
                                }
                            }
                            transaction.Commit();
                        }

                    }
                }
            }
            catch (OracleException ex)
            {
                errorMessage = $"Oracle Error: {ex.Message}";
            }
            catch (Exception ex)
            {
                errorMessage = $"General Error: {ex.Message}";
            }

            return (items, count, errorMessage);
        }

        public async Task<PaginationResult> GetDataPagination1(
                       string baseSql,
                       int skip,
                       int take,
                       Dictionary<string, Rep_Param> parameters,
                       Dictionary<string, Rep_Param> filterprm,
                       string filter = null,
                       string orderBy = null)
        {
            
            var items = new List<IDictionary<string, object>>();
            int count = 0;
            string errorMessage = null;
            PaginationResult res = new PaginationResult
            {
                Items = Enumerable.Empty<IDictionary<string, object>>(),
                Count = 0,
                ErrorMessage = ""
            };
            try
            {   
                using (var connection = new OracleConnection(_connectionString))
                {
                    await Task.Run(() => connection.Open());
                    using (var enableParallelCommand = new OracleCommand("ALTER SESSION ENABLE PARALLEL QUERY", connection))
                    {
                        await enableParallelCommand.ExecuteNonQueryAsync();
                    }

                    var paginatedSql = $@"
                SELECT * FROM (
                    SELECT a.*, ROWNUM rnum FROM (
                        SELECT r.*, count(1) over () cn FROM ({baseSql}) r
                        {(string.IsNullOrEmpty(filter) ? "" : "WHERE " + filter)}
                        {(string.IsNullOrEmpty(orderBy) ? "" : "ORDER BY " + orderBy)}
                    ) a
                    WHERE ROWNUM <= :take + :skip
                )
                WHERE rnum > :skip";
                    paginatedSql = queryReplace(paginatedSql, parameters);

                    using (var command = new OracleCommand(paginatedSql, connection))
                    {
                        command.BindByName = true;
                        command.Parameters.Add(new OracleParameter("skip", OracleDbType.Decimal, skip, ParameterDirection.Input));
                        command.Parameters.Add(new OracleParameter("take", OracleDbType.Decimal, take, ParameterDirection.Input));

                        AddParameters(command, parameters);
                        AddParameters(command, filterprm);
                        

                        using (var transaction = connection.BeginTransaction())
                        {
                            using (var reader = await Task.Run(() => command.ExecuteReader()))
                            {
                                while (await Task.Run(() => reader.Read()))
                                {
                                    var item = new Dictionary<string, object>();

                                    for (int i = 0; i < reader.FieldCount; i++)
                                    {
                                        if (reader.GetName(i) == "RNUM")
                                        {
                                            continue;
                                        }
                                        if (reader.GetName(i) == "CN")
                                        {
                                            count = Convert.ToInt32(reader.GetValue(i));
                                            
                                        }
                                        item[reader.GetName(i)] = reader.GetValue(i);
                                    }

                                    items.Add(item);
                                }
                            }
                            transaction.Commit();
                        }

                    }
                }
            }
            catch (OracleException ex)
            {
                res.ErrorMessage = $"Oracle Error: {ex.Message}";
            }
            catch (Exception ex)
            {
                res.ErrorMessage = $"General Error: {ex.Message}";
            }
            res.Count = count;
            res.Items = items;
            return (res);
        }

       

     public async Task<(bool res, string errorMessage)> ExecuteDataTempory2(string hint, string target_table, string query,
                                                                               string typeobj, Dictionary<string, Rep_Param> parameters, string colInfo,
                                                                               CancellationToken cancellationToken)
        {
            string errorMessage = null;
            bool res = true;
            try
            {
                foreach (var f in parameters)
                {
                    if (f.Value.Val == null)
                    {
                        string rep1 = $"{f.Key}";
                        string rep2 = $":{f.Key}";
                        query = Regex.Replace(query, rep2, "null", RegexOptions.IgnoreCase);
                        query = Regex.Replace(query, rep1, "null", RegexOptions.IgnoreCase);

                        int g = 0;
                    }
                }
                using (var connection = new OracleConnection(_connectionString))
                {
                    await connection.OpenAsync(cancellationToken);
                    using (var enableParallelCommand = new OracleCommand("ALTER SESSION ENABLE PARALLEL DML", connection))
                    {
                        await enableParallelCommand.ExecuteNonQueryAsync(cancellationToken);
                    }



                    if (typeobj == "GET_PROCEDURE")
                    {



                    }
                    else
                    {
                        query = queryReplace(query, parameters);
                    }
                    using (var command = new OracleCommand())
                    {
                        command.Connection = connection;

                        command.BindByName = true;

                        AddParameters(command, parameters);

                        if (typeobj == "GET_PROCEDURE")
                        {
                            command.CommandText = query;
                            command.Parameters.Add(new OracleParameter
                            {
                                ParameterName = "p_Table_name",
                                OracleDbType = OracleDbType.Varchar2,
                                Size = 100,
                                Value = target_table,
                                Direction = ParameterDirection.Input,
                            });
                        }


                        else
                        {
                            command.CommandText = $@" insert /*+ APPEND PARALLEL*/ into {target_table}
                            select /*+ APPEND PARALLEL*/* from({query})";
                        }
                        await Task.Run(() => command.ExecuteNonQueryAsync(cancellationToken));

                    }
                }
            }
            catch (OracleException ex)
            {
                errorMessage = $"Oracle Error: {ex.Message}";
                res = false;
            }
            catch (Exception ex)
            {
                errorMessage = $"General Error: {ex.Message}";
                res = false;
            }

            return (res, errorMessage);
        }


        public async Task<(bool res, string errorMessage)> ExecuteDataTempory1(string hint, string target_table, string query, 
                                                                               string typeobj, Dictionary<string, Rep_Param> parameters, string colInfo,
                                                                               CancellationToken cancellationToken)
        {
            string errorMessage = null;
            bool res = true;
            try
            {
                foreach (var f in parameters)
                {
                    if (f.Value.Val==null)
                    {
                       // string rep1 = $"{f.Key}";
                        string rep2 = $":{f.Key}";
                        query =Regex.Replace(query, rep2, "null", RegexOptions.IgnoreCase);
                      //  query = Regex.Replace(query, rep1, "null", RegexOptions.IgnoreCase);

                        int g = 0;
                    }
                }
                using (var connection = new OracleConnection(_connectionString))
                {
                    await connection.OpenAsync(cancellationToken);
                    using (var enableParallelCommand = new OracleCommand("ALTER SESSION ENABLE PARALLEL DML", connection))
                    {
                        await enableParallelCommand.ExecuteNonQueryAsync(cancellationToken);
                    }

                    

                    if (typeobj == "GET_PROCEDURE")
                    {
                      


                    }
                    else 
                    { 
                        query = queryReplace(query, parameters); 
                    }
                    using (var command = new OracleCommand())
                    {
                        command.Connection = connection;

                        command.BindByName = true;

                        AddParameters(command, parameters);

                        if (typeobj == "GET_PROCEDURE")
                        {
                            command.CommandText = query;
                            command.Parameters.Add(new OracleParameter
                            {
                                ParameterName = "p_Table_name",
                                OracleDbType = OracleDbType.Varchar2,
                                Size=100,
                                Value = target_table,
                                Direction = ParameterDirection.Input,
                            });
                        }


                      

                        else
                        {
                            command.CommandText = $@"
                        DECLARE
                            CURSOR cur IS 
                                select {hint} * from ({query}) src;
                                TYPE rw is record {colInfo}; 
                                TYPE row_t IS TABLE OF rw INDEX BY PLS_INTEGER;
                                v_row row_t;
                                g INTEGER;
                        BEGIN
g:=0;
                            OPEN cur;
                                LOOP
IF g = 0 THEN 
begin
g:=1;
                                    FETCH cur BULK COLLECT INTO v_row limit 50;
end;
else
begin

FETCH cur BULK COLLECT INTO v_row limit 10000;
end;
end if;
                                    FORALL indx IN 1 .. v_row.count
                                    INSERT /*+ APPEND PARALLEL (8)*/ INTO {target_table}
                                    VALUES v_row(indx);
                                    COMMIT;

                                    EXIT WHEN cur%NOTFOUND;
                                END LOOP;
                            CLOSE cur;
                        END;";
                        }
                        await Task.Run(() => command.ExecuteNonQueryAsync(cancellationToken));

                    }
                }
            }
            catch (OracleException ex)
            {
                errorMessage = $"Oracle Error: {ex.Message}";
                res = false;
            }
            catch (Exception ex)
            {
                errorMessage = $"General Error: {ex.Message}";
                res = false;
            }

            return (res, errorMessage);
        }

        public async Task<(bool res, string errorMessage)> GetDataSingle1(string query, IDictionary<string, object> data,
                                  Dictionary<string, Rep_Param> parameters,
                                  string pkcolumns)
        {
            string errorMessage = null;
            bool res = true;
            string singlesql = "select * from (" +
                query +
                ")" +
                "where  " + pkcolumns;



            try
            {
                using (var connection = new OracleConnection(_connectionString))
                {
                    await Task.Run(() => connection.Open());
                    using (var enableParallelCommand = new OracleCommand("ALTER SESSION ENABLE PARALLEL DML", connection))
                    {
                        await enableParallelCommand.ExecuteNonQueryAsync();
                    }

                    using (var command = new OracleCommand(singlesql, connection))
                    {
                        command.BindByName = true;

                        AddParameters(command, parameters);

                        using (var reader = await Task.Run(() => command.ExecuteReader()))
                        {
                            while (await Task.Run(() => reader.Read()))
                            {


                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    data[reader.GetName(i)] = reader.GetValue(i);
                                }


                            }
                        }
                    }
                }
            }
            catch (OracleException ex)
            {
                errorMessage = $"Oracle Error: {ex.Message}";
                res = false;
            }
            catch (Exception ex)
            {
                errorMessage = $"General Error: {ex.Message}";
                res = false;
            }
            return (res, errorMessage);
        }

        private async Task <(bool res, string errorMessage)> InitOutPrm(Dictionary<string, object> outprm, Dictionary<string, Rep_Param> parameters)
        {
            string errorMessage = null;
            bool res = true;
            try
            {
                foreach (var param in parameters.Where(x => x.Key != "IN_CN"))
                {
                    if (param.Key.StartsWith("OUT"))
                    {
                        outprm.Add(param.Key, param.Value);
                    }
                }
                outprm.Add("EWA_OUT_RESULT", 1);
                outprm.Add("EWA_OUT_MESSAGE", "");

                return (res, errorMessage);
            }
            catch (Exception ex)
            {

                return (res, errorMessage);
            }
        }
        public async Task<(bool res, string errorMessage)> RowAction(string query, Dictionary<string, Rep_Param> parameters, 
                                                                     Dictionary<string, object> formValues,
                                                                     Dictionary<string, Rep_Param> tdelparameters, string tdelsql,
                                                                     Dictionary<string, Rep_Param> tinsertparameters, string tinsertsql,
                                                                     bool is_tempory, string action,
                                                                     string selquery, Dictionary<string, Rep_Param> singleparameters, string pkcolumns)
        {

            string errorMessage = string.Empty;
            bool res = true;
            Dictionary<string, object> outprm = new Dictionary<string, object>();
           // Dictionary<string, object> rowData = new Dictionary<string, object>();

            (res, errorMessage) = await InitOutPrm(outprm, parameters);
            if (!res)
            { return (res, errorMessage);}
            
                using (var connection = new OracleConnection(_connectionString))
                {
                    await Task.Run(() => connection.Open());

                    var transaction=connection.BeginTransaction();

                    try
                    {
                    /*обновление удаление редактирование*/

                    query=query.Replace("COMMIT;", " ");
                    using (var command = new OracleCommand(query, connection))
                        {
                            command.BindByName = true;
                            AddParameters(command, parameters);

                            await command.ExecuteNonQueryAsync();
                            foreach (var param in outprm)
                            {
                                outprm[param.Key] = command.Parameters[param.Key].Value;

                                if (param.Key == "EWA_OUT_RESULT")
                                {                                    
                                    if (command.Parameters[param.Key].Value.ToString() == "-1")
                                    {
                                        res = false;
                                    }
                                }
                                if (param.Key == "EWA_OUT_MESSAGE")
                                {
                                    errorMessage = command.Parameters[param.Key].Value.ToString();
                                }
                            }
                        }
                        if (!res)
                        {
                            transaction.Rollback();
                            return (res, errorMessage);
                        }

                        /*обновление значений*/
                        foreach (var prm in outprm.Where(x => x.Value != null && x.Value != DBNull.Value && x.Key!= "EWA_OUT_RESULT" && x.Key != "EWA_OUT_MESSAGE"))
                        {
                            string key = prm.Key.Substring(4);
                            formValues[key] = prm.Value;
                            if (is_tempory && (action == "DEL" || action == "EDIT")) { tdelparameters["IN_" + key].Val = prm.Value;}

                        }

                        foreach (var prm in singleparameters.Where(x=> x.Value.Val != null && x.Value.Val.ToString()== "IS_PK" && x.Value.Val!=null))
                        {
                           
                           formValues.TryGetValue(prm.Key.Substring(3), out var value);
                           prm.Value.Val = value;
                        }
                      

                        /*удаление из временной*/
                        if (is_tempory && (action == "DEL" || action == "EDIT"))
                        {
                            using (var command = new OracleCommand(tdelsql, connection))
                            {
                                command.BindByName = true;
                                AddParameters(command, tdelparameters);

                                await command.ExecuteNonQueryAsync();

                            }
                        }
                    /*зачитка строки*/
                    if (action == "ADD" || action == "EDIT")
                    {
                        (res, errorMessage) = await GetDataSingleImpl(selquery, formValues, singleparameters, pkcolumns, connection);
                        if (!res)
                        {
                            transaction.Rollback();
                            return (res, errorMessage);
                        }
                    }
                        /*добавление во временную*/
                        if (is_tempory && (action == "ADD" || action == "EDIT"))
                        {
                             foreach (var prm in tinsertparameters)
                        {
                            prm.Value.Val = formValues[prm.Key.Substring(3)];
                        }
                        
                            using (var command = new OracleCommand(tinsertsql, connection))
                            {
                                command.BindByName = true;
                                AddParameters(command, tinsertparameters);

                                await command.ExecuteNonQueryAsync();

                            }
                          
                        }

                        if (res){transaction.Commit();}
                        else{transaction.Rollback();
                             return (res, errorMessage);}
                    }

                    catch (OracleException ex)
                    {
                        transaction.Rollback();
                        errorMessage = $"Oracle Error: {ex.Message}";
                        res = false;
                        return (res, errorMessage);
                    }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    errorMessage = $"Oracle Error: {ex.Message}";
                    res = false;
                    return (res, errorMessage);
                }
            }
                


            
           
            return (res, errorMessage);
        }


        private async Task<(bool res, string errorMessage)> GetDataSingleImpl(string query, IDictionary<string, object> data,
                                 Dictionary<string, Rep_Param> parameters,
                                 string pkcolumns, OracleConnection connection)
        {
            string errorMessage = null;
            bool res = true;
            string singlesql = "select * from (" +
                query +
                ") " +
                "where  " + pkcolumns;



            try
            {
                    using (var command = new OracleCommand(singlesql, connection))
                    {
                        command.BindByName = true;

                        AddParameters(command, parameters);

                        using (var reader = await Task.Run(() => command.ExecuteReader()))
                        {
                            while (await Task.Run(() => reader.Read()))
                            {


                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    data[reader.GetName(i)] = reader.GetValue(i);
                                }


                            }
                        }
                    }
               
            }
            catch (OracleException ex)
            {
                errorMessage = $"Oracle Error: {ex.Message}";
                res = false;
            }
            catch (Exception ex)
            {
                errorMessage = $"General Error: {ex.Message}";
                res = false;
            }
            return (res, errorMessage);
        }


        public async Task<(bool res, string errorMessage, Dictionary<string, object> outprm)> ExecuteDataWithOut(string query, Dictionary<string, Rep_Param> parameters = null)
        {

            string errorMessage = null;
            bool res = true;
            Dictionary<string, object> outprm = new Dictionary<string, object>();

            foreach (var param in parameters.Where(x => x.Key != "IN_CN"))
            {
                
                if (param.Key.StartsWith("OUT"))
                {
                    outprm.Add(param.Key, param.Value);
                }
            }
            outprm.Add("EWA_OUT_RESULT", 1);
            outprm.Add("EWA_OUT_MESSAGE", "");

            try
            {
                
                using (var connection = new OracleConnection(_connectionString))
                {
                    await Task.Run(() => connection.Open());
                    using (var enableParallelCommand = new OracleCommand("ALTER SESSION ENABLE PARALLEL DML", connection))
                    {
                        await enableParallelCommand.ExecuteNonQueryAsync();
                    }

                    using (var command = new OracleCommand(query, connection))
                    {
                        command.BindByName = true;
                        AddParameters(command, parameters);                        

                        await command.ExecuteNonQueryAsync();
                        foreach (var param in outprm)
                        {
                            outprm[param.Key] = command.Parameters[param.Key].Value;

                            if (param.Key== "EWA_OUT_RESULT")
                            {
                                //if (command.Parameters[param.Key].Value as int? == -1)
                                if (command.Parameters[param.Key].Value.ToString() == "-1")
                                {
                                    res = false;
                                }
                            }
                            if (param.Key == "EWA_OUT_MESSAGE")
                            {
                                    errorMessage = command.Parameters[param.Key].Value.ToString();                               
                            }
                        }
                    }
                }
            }
            catch (OracleException ex)
            {
                errorMessage = $"Oracle Error: {ex.Message}";
                res = false;
            }
            catch (Exception ex)
            {
                errorMessage = $"General Error: {ex.Message}";
                res = false;
            }
            return (res, errorMessage, outprm);
        }

        public async Task<decimal> GetSessionID()
        {
            decimal id_out = 0;
            bool success = false;
            string errorMessage = "";
            Dictionary<string, object> outPrm = new Dictionary<string, object>();
            Dictionary<string, Rep_Param> parameters = new Dictionary<string, Rep_Param>();
            parameters.Clear();
            parameters.Add("OUT_p_id", new Rep_Param(1, "NUMBER", ParameterDirection.Output, null, null, null));
            string query = $@"
                    begin  
                     :OUT_p_id:=EWA_Log_SEQ.NextVal;
                    
                    end;";

            (success, errorMessage, outPrm) = await ExecuteDataWithOut(query, parameters);

            decimal.TryParse(outPrm["OUT_p_id"].ToString(), out id_out);
            return id_out;
        }


        public async Task<(bool res, string errorMessage)> ExecuteData1(string query, Dictionary<string, Rep_Param> parameters = null)
        {
            string errorMessage = null;
            bool res = true;
            Dictionary<string, object> outprm = new Dictionary<string, object>();
            try
            {
                using (var connection = new OracleConnection(_connectionString))
                {
                    await Task.Run(() => connection.Open());
                    using (var enableParallelCommand = new OracleCommand("ALTER SESSION ENABLE PARALLEL DML", connection))
                    {
                        await enableParallelCommand.ExecuteNonQueryAsync();
                    }

                    using (var command = new OracleCommand(query, connection))
                    {
                        command.BindByName = true;

                        if (parameters != null)
                        {
                            AddParameters(command, parameters);
                        }
                        await Task.Run(() => command.ExecuteNonQueryAsync());
                        //  await command.ExecuteNonQueryAsync();
                        foreach (var param in outprm)
                        {
                            outprm[param.Key] = command.Parameters[param.Key].Value;
                        }
                    }
                }
            }
            catch (OracleException ex)
            {
                errorMessage = $"Oracle Error: {ex.Message}";
                res = false;
            }
            catch (Exception ex)
            {
                errorMessage = $"General Error: {ex.Message}";
                res = false;
            }
            return (res, errorMessage);
        }

        public async Task<(bool res, string errorMessage)> ExecuteDataTempory(string hint, string target_table, string query, string typeobj, Dictionary<string, object> parameters = null)
        {
            string errorMessage = null;
            bool res = true;
            try
            {
                using (var connection = new OracleConnection(_connectionString))
                {
                    await Task.Run(() => connection.Open());
                    using (var enableParallelCommand = new OracleCommand("ALTER SESSION ENABLE PARALLEL DML", connection))
                    {
                        await enableParallelCommand.ExecuteNonQueryAsync();
                    }

                    query = queryReplace(query, parameters);

                    if (typeobj == "GET_PROCEDURE")
                    {
                        query = "select * from Table(" + query + ")";


                    }
                    using (var command = new OracleCommand())
                    {
                        command.Connection = connection;

                        command.BindByName = true;
                        
                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                if (param.Value is DateTime dateValue)
                                {
                                    command.Parameters.Add(new OracleParameter
                                    {
                                        ParameterName = param.Key,
                                        OracleDbType = OracleDbType.Date,
                                        Value = param.Value,

                                    });
                                }
                                else
                                {
                                    command.Parameters.Add(new OracleParameter(param.Key, param.Value ?? DBNull.Value));
                                }
                            }
                        }

                        if (typeobj == "GET_PROCEDURE")
                        {
                            command.CommandText = "insert into " + target_table + " " + query;
                        }

                        else
                        {
                            command.CommandText = $@"
                        DECLARE
                            CURSOR cur IS 
                                select {hint} * from ({query}) src;
                                TYPE row_t IS TABLE OF cur%ROWTYPE INDEX BY PLS_INTEGER;
                                v_row row_t;
                        BEGIN
                            OPEN cur;
                                LOOP
                                    FETCH cur BULK COLLECT INTO v_row limit 10000;
                                    FORALL indx IN 1 .. v_row.count
                                    INSERT /*+ APPEND PARALLEL (8)*/ INTO {target_table}
                                    VALUES v_row(indx);
                                    COMMIT;
                                    EXIT WHEN cur%NOTFOUND;
                                END LOOP;
                            CLOSE cur;
                        END;";
                        }
                        await Task.Run(() => command.ExecuteNonQueryAsync());
                       
                    }
                }
            }
            catch (OracleException ex)
            {
                errorMessage = $"Oracle Error: {ex.Message}";
                res = false;
            }
            catch (Exception ex)
            {
                errorMessage = $"General Error: {ex.Message}";
                res = false;
            }

            return (res, errorMessage);
        }

        private string queryReplace(string _query, Dictionary<string, Rep_Param> _parameters)
        {


            foreach (var param in _parameters)
            {
                string constant = param.Key;
                string pattern10 = $@"\s+\(\s*:{constant}\s+is\s+null\s+or\s+";
                string pattern11 = $@"\s+or\s+:{constant}\s+is\s+null\s*\)\s+";
                string pattern12 = $@"\s+or\s+:{constant}\s+is\s+null\s+or\s+";
                string startPattern = $@"\/\*bf\(:{constant}\)\*\/";
                string endPattern = $@"\/\*ef\(:{constant}\)\*\/";
                string pattern = $"{startPattern}.*{endPattern}";

                if (param.Value.Val == null)
                {
                    _query = Regex.Replace(_query, pattern, String.Empty, RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }
                else
                {
                    _query = Regex.Replace(_query, pattern10, " (", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                    _query = Regex.Replace(_query, pattern11, ") ", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                    _query = Regex.Replace(_query, pattern12, " or ", RegexOptions.IgnoreCase | RegexOptions.Singleline);


                }
            }

            return _query;
        }

        private string queryReplace(string _query, Dictionary<string, object> _parameters)
        {


            foreach (var param in _parameters)
            {
                string constant = param.Key;
                string pattern10 = $@"\s+\(\s*:{constant}\s+is\s+null\s+or\s+";
                string pattern11 = $@"\s+or\s+:{constant}\s+is\s+null\s*\)\s+";
                string pattern12 = $@"\s+or\s+:{constant}\s+is\s+null\s+or\s+";
                string startPattern = $@"\/\*bf\(:{constant}\)\*\/";
                string endPattern = $@"\/\*ef\(:{constant}\)\*\/";
                string pattern = $"{startPattern}.*{endPattern}";

                if (param.Value == null)
                {
                    _query = Regex.Replace(_query, pattern, String.Empty, RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }
                else
                {
                    _query = Regex.Replace(_query, pattern10, " (", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                    _query = Regex.Replace(_query, pattern11, ") ", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                    _query = Regex.Replace(_query, pattern12, " or ", RegexOptions.IgnoreCase | RegexOptions.Singleline);


                }
            }

            return _query;
        }
        public async Task<(bool res, string errorMessage)> ExecuteData(string query, Dictionary<string, object> parameters = null)
        {
            string errorMessage = null;
            bool res = true;
            Dictionary<string, object> outprm = new Dictionary<string, object>();
            try
            {
                using (var connection = new OracleConnection(_connectionString))
                {
                    await Task.Run(() => connection.Open());
                    using (var enableParallelCommand = new OracleCommand("ALTER SESSION ENABLE PARALLEL DML", connection))
                    {
                        await enableParallelCommand.ExecuteNonQueryAsync();
                    }

                    using (var command = new OracleCommand(query, connection))
                    {
                        command.BindByName = true;

                        if (parameters != null)
                        {
                            foreach (var param in parameters.Where(x=>x.Key!="IN_CN"))
                            {
                                //var rowCount = new OracleParameter("var_RowCount", OracleDbType.Int32, ParameterDirection.Output);
                                if (param.Key.StartsWith("OUT"))
                                {
                                    outprm.Add(param.Key, param.Value);
                                    OracleDbType type= OracleDbType.Varchar2;

                                    if (param.Value.GetType()==typeof(decimal))
                                    {
                                        type=OracleDbType.Decimal;
                                    }
                                    if (param.Value.GetType() == typeof(DateTime))
                                    {
                                        type = OracleDbType.Date;
                                    }

                                    command.Parameters.Add(new OracleParameter(param.Key, type, ParameterDirection.Output));
                                }
                                else
                                {
                                    command.Parameters.Add(new OracleParameter(param.Key, param.Value ?? DBNull.Value));
                                }
                            }
                        }
                        await Task.Run(() => command.ExecuteNonQueryAsync());
                      //  await command.ExecuteNonQueryAsync();
                            foreach (var param in outprm)
                            {
                                outprm[param.Key] = command.Parameters[param.Key].Value;
                            }
                    }
                }
            }
            catch (OracleException ex)
            {
                errorMessage = $"Oracle Error: {ex.Message}";
                res = false;
            }
            catch (Exception ex)
            {
                errorMessage = $"General Error: {ex.Message}";
                res = false;
            }
            return (res, errorMessage);
        }


        public async Task<(bool res, string errorMessage)> GetDataSingle(string query, IDictionary<string, object> data, 
                           Dictionary<string, object> parameters,
                           string pkcolumns)
        {
            string errorMessage = null;
            bool res = true;
            string singlesql = "select * from (" +
                query +
                ")" +
                "where  "+ pkcolumns;

           

            try
            {
                using (var connection = new OracleConnection(_connectionString))
                {
                    await Task.Run(() => connection.Open());
                    using (var enableParallelCommand = new OracleCommand("ALTER SESSION ENABLE PARALLEL DML", connection))
                    {
                        await enableParallelCommand.ExecuteNonQueryAsync();
                    }

                    using (var command = new OracleCommand(singlesql, connection))
                    {
                        command.BindByName = true;

                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                command.Parameters.Add(new OracleParameter(param.Key, param.Value ?? DBNull.Value));
                            }
                        }

                        using (var reader = await Task.Run(() => command.ExecuteReader()))
                        {
                            while (await Task.Run(() => reader.Read()))
                            {
                               

                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    data[reader.GetName(i)] = reader.GetValue(i);
                                }

                                
                            }
                        }
                    }
                }
            }
            catch (OracleException ex)
            {
                errorMessage = $"Oracle Error: {ex.Message}";
                res = false;
            }
            catch (Exception ex)
            {
                errorMessage = $"General Error: {ex.Message}";
                res = false;
            }
            return (res, errorMessage);
        }

        public async Task<(string, string, string)> BuildTemporyTable(string tableName, List<ColumnMetadata> metadata)
        {
            var columns = new List<string>();
            string upperSel = "";
            string errorSel="";
            
            foreach (var column in metadata.Where(x=>x.CODE!="ERRORINFO"))
            {
                string erroratom = "";
                string selatom = "";
                string dataType = column.DATATYPE.ToUpper();
                if (dataType== "VARCHAR2")
                {
                    
                    erroratom =
                        " case " +
                       $" when {column.CODE} is null then ''"+
                       $"  when length({column.CODE})>{column.DATALENGTH} then 'Усечение данных {column.NAME}({column.CODE}) c ' || length({column.CODE}) || ' по {column.DATALENGTH} значение ' || {column.CODE} || chr(10) " +
                        "  else '' "+
                        " end ";
                    selatom =
                        " case " +
                       $"  when length({column.CODE})>{column.DATALENGTH} then substr({column.CODE}, 0, {column.DATALENGTH}) " +
                       $"  else {column.CODE}" +
                       $" end as {column.CODE} ";
                }

                if (dataType == "NUMBER" && column.DATALENGTH>0)
                {
                    string fmt=string.Concat(Enumerable.Repeat('9', (int)(column.DATALENGTH - column.DATAPRECISION)));
                    erroratom =
                        " case "+
                       $"  when {column.CODE} is null then ''" +
                       $"  when round({column.CODE}, {column.DATAPRECISION})!={column.CODE} or to_char({column.CODE}, '{fmt}') like '%#%' then 'Значение {column.NAME}({column.CODE}) ' || {column.CODE} || ' не удолетворяет типу Number({column.DATALENGTH}, {column.DATAPRECISION})' || chr(10) " +
                        "  else '' " +
                        " end ";
                    selatom =
                        " case " +
                       $"  when round({column.CODE}, {column.DATAPRECISION})!={column.CODE} or to_char({column.CODE}, '{fmt}') like '%#%' then 0" +
                       $"  else {column.CODE}" +
                       $" end as {column.CODE} ";
                }

                if (string.IsNullOrEmpty(selatom))
                {
                    selatom = column.CODE;
                }

                if (string.IsNullOrEmpty(upperSel))
                {
                    upperSel = "select " + selatom;
                }
                else
                {
                    upperSel= upperSel+", " + selatom;
                }
                    

                if (!string.IsNullOrEmpty(erroratom)) 
                {
                    if (!string.IsNullOrEmpty(errorSel))
                    {
                        errorSel = errorSel + " || " + erroratom;
                    }
                    else
                    {
                        errorSel = erroratom;
                    }
                }

                if (column.DATATYPE == "VARCHAR2" || column.DATATYPE == "CHAR" || column.DATATYPE == "NUMBER")
                {
                    if (column.DATALENGTH > 0 && column.DATAPRECISION > 0)
                    {
                        dataType = dataType + "(" + column.DATALENGTH.ToString() + ", " + column.DATAPRECISION.ToString() + ")";
                    }
                    else if (column.DATALENGTH > 0)
                    {

                        if (dataType == "VARCHAR2" )
                        {
                            dataType = dataType + "(" + column.DATALENGTH.ToString() + " char)";
                        }
                        else
                        {
                            dataType = dataType + "(" + column.DATALENGTH.ToString() + ")";
                        }
                        
                    }
                }

                columns.Add($"{column.CODE} {dataType}");

            }
            columns.Add("ERRORINFO VARCHAR2(4000)");

            upperSel = upperSel + " , " + errorSel + " as ERRORINFO";
            string colInfo= $"({string.Join(", ", columns)})";

            string createTableQuery = $"CREATE TABLE {tableName} ({string.Join(", ", columns)})";
            return (createTableQuery, colInfo, upperSel);
        }
    }
}

