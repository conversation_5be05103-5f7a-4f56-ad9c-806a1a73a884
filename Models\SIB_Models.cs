﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace EWA.Models
{
    public class SIB_Models
    {
        public class EWADomainSettings
        {
            public string DomainCode { get; set; }
            public string LdapServer { get; set; }
            public int LdapPort { get; set; }
            public string BaseDN { get; set; }
            public string LdapPath => $"LDAP://{LdapServer}";
            public string Username { get; set; }
            public string Password { get; set; }
            public bool Enc { get; set; } = false;
        }
        public class EWADomainGrop
        { 
            public string ADGroupName { get; set; }
            public string CodeGroupNormal => ADGroupName?.ToUpper();
        }
        public class EWADomainUser
        { 
            public string ADLogin { get; set; }
            public string ADFullLogin { get; set; }
            public string ADUserName { get; set; }
            public string ADUserEMail { get; set; }
            public string ADUserTitle { get; set; }
            public string ADUserDepart { get; set; }
            public List<EWADomainGrop> ADUserGroups = new List<EWADomainGrop>();
            public bool IsEmpty => (string.IsNullOrWhiteSpace(ADLogin) || string.IsNullOrWhiteSpace(ADFullLogin));
        }
        public class EWAConnectionSettings
        {
            public string UserId { get; set; }
            public string Password { get; set; }
            public bool Enc { get; set; } = false;
            public string DBName { get; set; }
            public string HostName { get; set; }
            public string HostPort { get; set; }
            public string ConnTimeout { get; set; }
        }
        public class EWAClaim
        {
            public string Type { get; set; }
            public string Value { get; set; }
        }
        public partial class EWAAuthenticationState
        {
            public bool IsAuthenticated { get; set; }
            public string Name { get; set; }
            public string UCode { get; set; }
            public IEnumerable<EWAClaim> Claims { get; set; }

            public string SessionId { get; set; }
            public string SessionInfo { get; set; }
           
        }
        public class EWABlckCfg
        {
            public bool LockoutEnabled { get; set; }
            public bool TLockOutEnabled { get; set; }
            public decimal TLockOutEnd { get; set; }
            public decimal TAccessFailedCount  { get; set; }
            public decimal AccessFailedCount { get; set; }
            public decimal MxDInactiveLL { get; set; }
            public decimal MxDInactiveSC   { get; set; }
        }
        public class EWAPswdCfg
        {
            public decimal MinLength { get; set; }
            public bool HaveDigit { get; set; }
            public bool HaveNAlphanum { get; set; }
            public bool HaveUpperCase { get; set; }
            public bool HaveLowerCase { get; set; }
            public decimal HaveUniqueChars { get; set; }
            public decimal UniquePswdCnt { get; set; }
            public string LstNAlphanum { get; set; }
            public string ForbiddenPatterns { get; set; }
            public decimal UniquePswdPeriod { get; set; }
            public decimal MxDActivPswd { get; set; }

        }
        public class SIB_MODULES
        {
            public decimal ID { get; set; }
            public string CODE { get; set; }
            public string? NAME { get; set; }
            public DateTime DT_CHANGE { get; set; }
            public string USER_CHANGE { get; set; }
            public string NormCode => CODE.ToUpper();
            public string NormName => NAME.ToUpper();
            public ICollection<SIB_USERMODULES_LNK> UserModules { get; set; } = new List<SIB_USERMODULES_LNK>();
        }
        public class SIB_USERS
        {
            [Key]
            public decimal ID { get; set; }
            public string CODE { get; set; }
            public string? NAME { get; set; }
            public string? EXT_LOGIN { get; set; }
            public string? EXT_DOMAIN { get; set; }
            public decimal? IS_ADMIN { get; set; }
            public string? PROFILE_PHOTO { get; set; }
            public string? POSITION { get; set; }
            public string? DEPARTMENT { get; set; }
            public string? PHONE { get; set; }
            public string? EMAIL { get; set; }
            public DateTime? DT_LAST_LOGIN { get; set; }
            public DateTime DT_CREATE { get; set; }
            public DateTime DT_CHANGE { get; set; }
            public string USER_CREATE { get; set; }
            public string USER_CHANGE { get; set; }
            public decimal? IS_ACTIVE { get; set; }
            public decimal? IS_BLOCK { get; set; }
            public DateTime? DT_BLOCK { get; set; }
            public string? TYPE_BLOCK { get; set; }
            public decimal? LOGIN_FAIL_COUNT { get; set; }
            public decimal? IS_TECHN_PASS { get; set; }
            public string? TECHN_PASS { get; set; }
            public string PASSWORDHASH { get; set; }
            
            
            public bool IStechPSWD => IS_TECHN_PASS == 1;
            public bool ISblock => IS_BLOCK == 1;
            public bool ISactive => IS_ACTIVE == 1;
            public bool ISadmin => IS_ADMIN == 1;
            public bool ISdomain => (!string.IsNullOrEmpty(EXT_LOGIN) || !string.IsNullOrWhiteSpace(EXT_LOGIN));
            public bool ISEmpty => (string.IsNullOrEmpty(CODE) || string.IsNullOrWhiteSpace(CODE));
            public string NormCODE => CODE.ToUpper();
            public string NormName => NAME.ToUpper();
            public string ID_STR => ID.ToString();
            [NotMapped]
            public bool JustLoggin { get; set; } = false;
            [NotMapped]
            public string CONFIRMPASSWORD { get; set; }
            [NotMapped]
            public string NEWPASSWORD { get; set; }
            [NotMapped]
            public string SessionId { get; set; }
            [NotMapped]
            public string SessionInfo { get; set; }

            public ICollection<SIB_USERROLES_LNK> UserRoles { get; set; } = new List<SIB_USERROLES_LNK>();
            public ICollection<SIB_USERMODULES_LNK> UserModules { get; set; } = new List<SIB_USERMODULES_LNK>();
   
        }
        public class SIB_USERMODULES_LNK
        {
            public decimal ID_USER  { get; set; } 
            public decimal ID_MODULE { get; set; }
            public DateTime DT_CHANGE  { get; set; }
            public string USER_CHANGE { get; set; }
            public SIB_USERS Users { get; set; }
            public SIB_MODULES Modules { get; set; }

        }
        public class SIB_ATTR_SPR
        {
            [Key]
            public decimal ID { get; set; }
            public string CODE { get; set; }
            public string NAME { get; set; }
            public string TYPE_ATTR { get; set; }
            public DateTime DT_CHANGE { get; set; }
            public string USER_CHANGE { get; set; }
        }
        public class RuleAtrSpr
        { 
            public string Code { get; set; }
            public string TypeCode { get; set; }
        }
        public class SIB_USERS_ATTR
        {
            public decimal ID_USER { get; set; }
            public decimal ID_ATTR { get; set; }
            public string VALUE_ATTR { get; set; }
            public DateTime DT_CHANGE { get; set; }
            public string USER_CHANGE { get; set; }
        }
        public class SIB_OBJECT_ATTR
        {
            public decimal ID_OBJECT { get; set; }
            public decimal ID_ATTR { get; set; }
            public string VALUE_ATTR { get; set; }
            public DateTime DT_CHANGE { get; set; }
            public string USER_CHANGE { get; set; }

        }
        public class SIB_RULES
        {
            [Key]
            public decimal ID { get; set; }
            public string CODE { get; set; }
            public string RULE { get; set; }
            public string RULE_J { get; set; }
            public string ACCESS_TYPE { get; set; }
            public DateTime DT_CREATE { get; set; }
            public DateTime DT_CHANGE { get; set; }
            public string USER_CREATE { get; set; }
            public string USER_CHANGE { get; set; }
            public int IS_ACTIVE { get; set; }
            public string DESCRIPTION { get; set; }
            public string USER_ATTRS { get; set; }
            public string OBJECT_ATTRS { get; set; }
            public int ACTION_USE { get; set; }
            public int ACTION_ADD { get; set; }
            public int ACTION_EDIT { get; set; }
            public int ACTION_DEL { get; set; }
        }
        public class SIB_POLICY
        {
            public decimal ID_USER { get; set; }
            public decimal ID_OBJECT { get; set; }
            public decimal ID_RULE { get; set; }
            public string ACCESS_TYPE { get; set; }
            public decimal ACTION_GET { get; set; }
            public decimal ACTION_ADD { get; set; }
            public decimal ACTION_EDIT { get; set; }
            public decimal ACTION_DEL { get; set; }
            public DateTime DT_CHANGE { get; set; }
            public string USER_CHANGE { get; set; }
        }
        public class SIB_ROLES
        {
            [Key]
            public decimal ID { get; set; }
            public string CODE { get; set; }
            public string NAME { get; set; }
            public DateTime DT_CHANGE { get; set; }
            public string USER_CHANGE { get; set; }
            public string NormCode => CODE.ToUpper();
            public string NormName => NAME.ToUpper();
            public ICollection<SIB_USERROLES_LNK> UserRoles { get; set; } = new List<SIB_USERROLES_LNK>();
        }
        public class SIB_USERROLES_LNK
        {
            public decimal ID_USER { get; set; }
            public decimal ID_ROLE { get; set; }
            public DateTime DT_CHANGE { get; set; }
            public string USER_CHANGE { get; set; }
            public SIB_USERS Users { get; set; }
            public SIB_ROLES Roles { get; set; }
        }

        public class REP_OBJECT
        {
            public decimal ID { get; set; }
            public string CODE { get; set; }
            public string NAME { get; set; }
            public string TYPE_OBJECT { get; set; }
            public DateTime CREATE_DT { get; set; }
            public DateTime CHANGE_DT { get; set; }
            public string CREATE_USER { get; set; }
            public string CHANGE_USER { get; set; }
        }
        /*
        public class DropDownOption
        {
            public string CODE { get; set; }
            public string VALUE { get; set; }
        }
        */
        public class RuleModelIns
        {
            public decimal fRuleId { get; set;}
            public string fAccessType { get; set; }
            public decimal fActionUse { get; set; }
            public decimal fActionAdd { get; set; }
            public decimal fActionEdit { get; set; }
            public decimal fActionDel { get; set; }
            public int? UserId { get; set; }
            public string fRule { get; set; }
            public string parsedOAttrsL { get; set; }
            public string parsedObjAttrs { get; set; }
            public string parsedUAttrsL { get; set; }
            public string parsedUserAttrs { get; set; }
            public string subjCode { get; set; }
            
        }
        public class RuleModel
        {
            [Key]
            public string CODE_GROUP { get; set; }
            public int NUM { get; set; }
            public string TYPE { get; set; }
            public string PAR_GROUP { get; set; }
            public string CODE { get; set; }
            public string ATTR { get; set; }
            public string OPER_ATTR { get; set; }
            public string VAL { get; set; }
            public string OPER_UNION { get; set; }
            public string TYPE_ATTR { get; set; }
        }
        public class SIBRepData
        {
            public string Alias { get; set; }
            public string Code { get; set; }
            public List<string> Methods { get; set; }
        }
        public class SIBDomainGroupInfo
        {
            public string CodeGroup { get; set; }
            public string NameGroup { get; set; }
            public string CodeGroupNormal => CodeGroup?.ToUpper();
        }

        public class SIBDomainGroupAppGroup
        {
            public string CodeGroup { get; set; }
            public string CodeSIB { get; set; }
            public string TypeSIB { get; set; }
        }

        public class SIBDomainUserInfo
        {
            public string Domain { get; set; }
            public string DomainNorm => Domain?.ToUpper();
            public string FullUserName => $"{UsernameNorm}@{DomainNorm}";
            public string Username { get; set; }
            public string UsernameNorm => Username?.ToUpper();
            public string DisplayName { get; set; }
            public string Email { get; set; }
            public string Department { get; set; }
            public string Title { get; set; }
            public bool IsAdd { get; set; }
            public bool IsNew { get; set; }
            public bool NeedUpd { get; set; }
            public string LocalUserName { get; set; }
            public List<string> Groups => LstGroup?.Where(g => !string.IsNullOrWhiteSpace(g.NameGroup))
                                                   .Select(g => g.NameGroup)
                                                   .ToList() ?? new List<string>();
            public string StrGroup => string.Join(", ", LstGroup.Select(g => g.NameGroup));
            public List<SIBDomainGroupInfo> LstGroup { get; set; } = new List<SIBDomainGroupInfo>();
        }
    }
}
