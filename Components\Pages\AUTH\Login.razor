@layout LoginLayout
@page "/login"

@using System
@using System.Collections.Generic
@using System.Linq
@using System.Threading.Tasks
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@using EWA.Services


<RadzenText Text="Авторизация" TextStyle="Radzen.Blazor.TextStyle.H5" class="mb-4 rz-mb-5" Style="color: #fbf6f6" TagName="Radzen.Blazor.TagName.H2" />
<RadzenRow>
    <RadzenColumn SizeMD="12">
        <RadzenTemplateForm 
         Action="@($"account/login?redirectUrl={redirectUrl}&domain={useDomainAuth.ToString().ToLower()}")" 
         Data="@("login")" Method="post">
            <RadzenAlert Shade="Radzen.Shade.Lighter" Variant="Radzen.Variant.Flat" Size="Radzen.AlertSize.Small" AlertStyle="Radzen.AlertStyle.Danger" Visible="@errorVisible">@error_str</RadzenAlert>
            <RadzenAlert Shade="Radzen.Shade.Lighter" Variant="Radzen.Variant.Flat" Size="Radzen.AlertSize.Small" AlertStyle="Radzen.AlertStyle.Info" Visible="@infoVisible">@info</RadzenAlert>
            <RadzenLogin LoginText="Войти"
                         UserText="Логин"
                         PasswordText="Пароль"
                         ResetPasswordText="Восстановление пароля"
                         AllowResetPassword="false"
                         AllowRegister="false"
                         UserRequired="Не заполнено имя пользователя"
                         PasswordRequired="Не заполнен пароль"
                         Username="@username"
                         Password="@password"
                         Login="@OnLogin"
                         Style="color: #ffffff" />
            <RadzenAlert Shade="Radzen.Shade.Lighter" Variant="Radzen.Variant.Flat" Size="Radzen.AlertSize.Small" AlertStyle="Radzen.AlertStyle.Info" AllowClose="false" ShowIcon="false" class="rz-mt-5">
                <RadzenCheckBox TriState="false" @bind-Value="useDomainAuth" />
                <RadzenLabel Text="Использовать доменную авторизацию" Component="checkbox" Style="color: white; margin-left: 1rem " />
            </RadzenAlert>
        </RadzenTemplateForm>
    </RadzenColumn>
</RadzenRow>

@code {



    [Inject] protected IJSRuntime JSRuntime { get; set; }
    [Inject] protected NavigationManager NavigationManager { get; set; }
    [Inject] protected DialogService DialogService { get; set; }
    [Inject] protected TooltipService TooltipService { get; set; }
    [Inject] protected ContextMenuService ContextMenuService { get; set; }
    [Inject] protected NotificationService NotificationService { get; set; }
    [Inject] protected SIBService.SecurityService Security { get; set; }
    [Inject] protected SIBService.SessionTimeoutService SessionTimeout { get; set; }
    [Inject] protected SIBService.AuthMonitorService AuthMonitorService { get; set; }

    protected string redirectUrl;
    protected string error;
    protected string error_str = string.Empty;
    protected string info;
    protected bool errorVisible;
    protected bool infoVisible;
    private string username;
    private string password;
    private bool canSubmit = false;
    //для AD
    private bool useDomainAuth =true;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                await AuthMonitorService.InitializeAsync();
            }
            catch (Exception e)
            {
                var err = e.Message;
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        
        var query = System.Web.HttpUtility.ParseQueryString(new Uri(NavigationManager.ToAbsoluteUri(NavigationManager.Uri).ToString()).Query);

        error = query.Get("error");
        if (!string.IsNullOrEmpty(error))
        {
            error_str = error.ToString();
        }
        info = query.Get("info");
        redirectUrl = query.Get("redirectUrl");
        errorVisible = !string.IsNullOrEmpty(error);
        infoVisible = !string.IsNullOrEmpty(info);
        username = query.Get("username");
        //useDomainAuth = query.Get("useDomainAuth");
    }

    private async Task OnLogin(LoginArgs args)
    {
        try 
        {
            if (string.IsNullOrEmpty(args.Username) || string.IsNullOrEmpty(args.Password))
            {
                return;
            }
            await SessionTimeout.ClearSession();
            username = args.Username;
            password = args.Password;
        }
        catch (Exception ex)
        {
            errorVisible = true;
            error_str = "Ошибка при попытке входа";
            StateHasChanged();
        }
    }
}