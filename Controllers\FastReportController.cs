using Microsoft.AspNetCore.Mvc;
using FastReport;
using FastReport.Export.PdfSimple;
using Oracle.ManagedDataAccess.Client;
using FastReport.Data;
using FastReport.Utils;  // доступ к RegisteredObjects
using System.IO;
using System.Linq;
using System.Configuration;
using Microsoft.Extensions.Configuration;
using System;
using System.Reflection.Emit;

namespace Fr.Controllers
{
    [Route("api/[controller]")]
    public class FastReportController : Controller
    {
        private readonly IConfiguration _configuration;
        private readonly EWA.Services.RepService _repService;


        private readonly EWA.Services.RepService.LogService _logService;

        public FastReportController(IConfiguration configuration, EWA.Services.RepService repService, EWA.Services.RepService.LogService logService)
        {
            _configuration = configuration;
            _repService = repService;
            _logService = logService;
            RegisteredObjects.AddConnection(typeof(OracleDataConnection));
        }

        [HttpGet("TestConnectionProperties")]
        public IActionResult TestConnectionProperties()
        {
            try
            {
                using (var report = new Report())
                {
                    // Создаем тестовое Oracle подключение через рефлексию
                    var connection = new OracleDataConnection();

                    var connectionType = connection.GetType();

                    // Устанавливаем ConnectionString через рефлексию
                    var connectionStringProperty = connectionType.GetProperty("ConnectionString");
                    if (connectionStringProperty != null)
                    {
                        connectionStringProperty.SetValue(connection, "Data Source=test;User Id=test;Password=test;");
                    }

                    var properties = connectionType.GetProperties();

                    var result = new
                    {
                        ConnectionType = connectionType.FullName,
                        Properties = properties.Select(p => new {
                            Name = p.Name,
                            Type = p.PropertyType.Name,
                            CanRead = p.CanRead,
                            CanWrite = p.CanWrite
                        }).ToArray(),
                        HasCommandTimeout = connectionType.GetProperty("CommandTimeout") != null,
                        CommandTimeoutProperty = connectionType.GetProperty("CommandTimeout") != null ?
                            new {
                                Type = connectionType.GetProperty("CommandTimeout").PropertyType.Name,
                                CanRead = connectionType.GetProperty("CommandTimeout").CanRead,
                                CanWrite = connectionType.GetProperty("CommandTimeout").CanWrite
                            } : null
                    };

                    return Ok(result);
                }
            }
            catch (Exception ex)
            {
                return BadRequest($"Ошибка при тестировании свойств подключения: {ex.Message}");
            }
        }

        [HttpGet("GetReportPdf")]
        //public async IActionResult GetReportPdf()
        public async Task<IActionResult> GetReportPdf(CancellationToken cancellationToken = default)
        {
            // параметры логирования
            string logIdStr = HttpContext.Request.Query["logId"];
            decimal.TryParse(logIdStr, out decimal logId);

            try
            {
                // имя отчета из параметров запроса
                string reportName = HttpContext.Request.Query["reportName"];

                if (string.IsNullOrEmpty(reportName))
                {
                    if (logId > 0)
                    {
                        await _logService.LogE(logId, "Имя отчета не задано.");
                    }
                    return BadRequest("Имя отчета не задано.");
                }

                // Путь к файлу отчета
                var reportPath = Path.Combine(Directory.GetCurrentDirectory(), "Reports", reportName);

                if (!System.IO.File.Exists(reportPath))
                {
                    if (logId > 0)
                    {
                        await _logService.LogE(logId, "Не найден файл с шаблоном отчета.");
                    }
                    return BadRequest("Не найден файл с шаблоном отчета.");
                }

                using (var report = new Report())
                {
                    // строка подключения FR_connection из appsettings.json
                    //string connectionString = _configuration.GetConnectionString("FR_connection");
                    //string connectionString = await _repService.GetConnectionStringAsync("FR");

                    /*
                    string codeObj = HttpContext.Request.Query["codeObj"];
                    var infoobject = await _repService.GetInfoObjectAsync(codeObj);
                    string connectionString = await _repService.GetConnectionStringAsync(infoobject.AppCode);
                    */

                    string appCode = HttpContext.Request.Query["appCode"];
                    string connectionString = await _repService.GetConnectionStringAsync(appCode);

                    report.Load(reportPath);

                    // замена первого подключения в отчете
                    if (report.Dictionary.Connections.Count > 0)
                    {
                        var connection = report.Dictionary.Connections[0];

                        var connectionType = connection.GetType();
                        var connectionStringProperty = connectionType.GetProperty("ConnectionString");

                        if (connectionStringProperty != null)
                        {
                            connectionStringProperty.SetValue(connection, connectionString);
                        }

                        // Установка CommandTimeout = 0 (без ограничения времени выполнения)
                        var commandTimeoutProperty = connectionType.GetProperty("CommandTimeout");
                        if (commandTimeoutProperty != null && commandTimeoutProperty.CanWrite)
                        {
                            try
                            {
                                commandTimeoutProperty.SetValue(connection, 0);
                            }
                            catch (Exception ex)
                            {
                                // Логируем ошибку, но не прерываем выполнение
                                if (logId > 0)
                                {
                                    await _logService.LogE(logId, $"Предупреждение: не удалось установить CommandTimeout: {ex.Message}");
                                }
                            }
                        }
                    }

                    // параметры из строки запроса
                    var queryParameters = HttpContext.Request.Query;

                    if (queryParameters.Count == 3)   // только reportName, appCode, logId
                    {
                        if (logId > 0)
                        {
                            await _logService.LogE(logId, "Отсутствуют значения параметров.");
                        }
                        return BadRequest($"Отсутствуют значения параметров.");
                    }

                    // установка параметров отчета
                    foreach (var param in queryParameters)
                    {
                        if (param.Key == "reportName" || param.Key == "appCode" || param.Key == "logId")
                            continue;  // пропускаем служебные параметры

                        string valueStr = param.Value.ToString();

                        // параметр в отчете по имени
                        var reportParameter = report.Parameters.FindByName(param.Key);

                        if (reportParameter == null)
                        {
                            if (logId > 0)
                            {
                                await _logService.LogE(logId, $"Параметр '{param.Key}' не найден в отчете.");
                            }
                            return BadRequest($"Параметр '{param.Key}' не найден в отчете.");
                        }

                        object value = valueStr; // Значение по умолчанию (строка)

                        // приведение значения к типу параметра из отчета
                        if (reportParameter.DataType == typeof(DateTime))
                        {
                            if (DateTime.TryParse(valueStr, out DateTime dateValue))
                            {
                                value = dateValue;
                            }
                            else
                            {
                                if (logId > 0)
                                {
                                    await _logService.LogE(logId, $"Неверный формат даты для параметра '{param.Key}'.");
                                }
                                return BadRequest($"Неверный формат даты для параметра '{param.Key}'.");
                            }
                        }
                        else if (reportParameter.DataType == typeof(int))
                        {
                            if (int.TryParse(valueStr, out int intValue))
                            {
                                value = intValue;
                            }
                            else
                            {
                                if (logId > 0)
                                {
                                    await _logService.LogE(logId, $"Неверный формат целого числа для параметра '{param.Key}'.");
                                }
                                return BadRequest($"Неверный формат целого числа для параметра '{param.Key}'.");
                            }
                        }

                        report.SetParameterValue(param.Key, value);
                    }

                    await report.PrepareAsync(cancellationToken);

                    // экспорт PDF в память
                    using (var ms = new MemoryStream())
                    {
                        PDFSimpleExport pdfExport = new PDFSimpleExport();
                        pdfExport.Export(report, ms);
                        ms.Position = 0;

                        if (logId > 0)
                        {
                            // Проверяем статус отчета перед LogS
                            var currentStatus = await _logService.GetLogStatus(logId);
                            if (currentStatus == "W") // Записываем LogS только если статус "Work"
                            {
                                await _logService.LogS(logId);   // завершение логирования
                            }
                            // Если currentStatus == null (запись не найдена) или != "W" (уже финальный статус), не записываем LogS
                        }
                        return File(ms.ToArray(), "application/pdf");
                    }
                }
            }
            catch (Exception ex)
            {
                // Проверяем, связана ли ошибка с отменой
                bool isCancellationError = ex is OperationCanceledException ||
                                         ex.Message.Contains("The operation was canceled") ||
                                         ex.Message.Contains("пользователем запрошена отмена") ||
                                         ex.Message.Contains("ORA-01013");

                if (logId > 0 && !isCancellationError)
                {
                    // Записываем в лог только если это не ошибка отмены
                    await _logService.LogE(logId, ex.Message);
                }

                return BadRequest($"Ошибка при генерации отчета: {ex.Message}");
            }
        }
    }
}
