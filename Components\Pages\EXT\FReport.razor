@rendermode InteractiveServer
@using System.Web
@using System.Text.Json;
@using EWA.Models
@using EWA.Services
@using System.Data
@using static EWA.Models.REP_Models
@implements IDisposable

@inject ILogger<FReport> Logger
@inject EWA.Services.RepService _repService
@inject EWA.Services.RepService.GET_SPR _getspr
@inject EWA.Services.RepService.LogService _logService
@inject EWA.Services.SIBService.SecurityService Security
@inject DialogService DialogService
@inject GridServices GridService
@inject TooltipService tooltipService


<!-- <h3>Report MemoryStream</h3> -->
<!-- <button class="btn btn-primary" @onclick="GenerateReport">Сгенерировать отчет</button>  -->

<RadzenStack Orientation="Orientation.Vertical" Style="background-color: var(--rz-grid-header-background-color); height: auto; width:100%;" Gap="0">
    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween" AlignItems="AlignItems.Center" Gap="0.2" Style="margin: 4px 16px 0 8px;">
        <RadzenText><strong>@Tab.NameObj</strong></RadzenText>
        @if (isLoading)
        {
            <RadzenText TextAlign="TextAlign.Center">Идет формирование отчета. Подождите, пожалуйста.</RadzenText>
        }
        <RadzenButton ButtonStyle="ButtonStyle.Base" Icon="refresh" Variant="Variant.Text" Click="@LoadDataBegin"
                      Disabled="@isLoading" MouseEnter="@(args => ShowTooltip(args, "Глобальный фильтр"))" />
    </RadzenStack>
</RadzenStack>

@if (EmptyText != "Нет записей для отображения")
{
    <RadzenTextBox Value="@EmptyText" TValue="string" Style="width: 100%; min-width: 500px;" ReadOnly="true" />
}
else
{
    @if (!string.IsNullOrEmpty(pdfUrl))
    {
        <div class="mt-3" style="position: relative;">
            <iframe width="100%" height="800px" src="@pdfUrl"></iframe>

            @if (isLoading)
            {
                <!-- поверх iframe -->
                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: white; z-index: 1000;">
                    <div class="excel-container">
                        <div class="excel-loading">
                            <div class="excel-loading-content">
                                <i class="rzi rzi-circle-o-notch"></i>
                                <div>Формирование отчета...</div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
}

@code {
    private RadzenLabel myLabel = new();
    private int i = 0;
    private string pdfUrl;
    private DBService dbService;
    private bool isInit = true;
    [Parameter]
    public TabData Tab { get; set; }

    private FormParams _PublicParams = new FormParams();
    public Dictionary<string, object> OldParams = new();
    private Dictionary<string, Rep_Param> globalFilter = new();
    string EmptyText = "Нет записей для отображения";
    private bool isLoading = false;
    private decimal currentLogId;

    private long? ID_OBJECT;
    private string query = string.Empty;
    private Models.REP_Models.IMetadataObject metaObject = null;
    private List<Models.REP_Models.ParamMetadata> metadata_param = null;
    private Dictionary<string, IEnumerable<EWA.Models.REP_Models.SPRShort>> SprShortDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprShortSelDict> SprSelShotDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprLongFiltrMeta> SprLongDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprLongSelDict> SprSelLongDict = new();
    private Dictionary<string, object> selectedRowData = new Dictionary<string, object>();
    private string dbname_query = string.Empty;
    private string appCode;

    protected override async Task OnInitializedAsync()
    {
        // подписка на событие завершения логирования
        EWA.Services.RepService.LogService.OnLogCompleted += OnLogCompleted;

        ID_OBJECT = await _repService.GetIdObjectAsync(Tab.CodeObj);
        metaObject = await _repService.GetMetaObjectTableAsync(ID_OBJECT.Value);
        metadata_param = JsonSerializer.Deserialize<List<Models.REP_Models.ParamMetadata>>(metaObject.ParamQuery);
        var infoobject = await _repService.GetInfoObjectAsync(Tab.CodeObj);
        dbname_query = await _repService.GetConnectionStringAsync(infoobject.AppCode);
        appCode = infoobject.AppCode;
        dbService = new DBService(dbname_query);
        if (metaObject is REP_Models.REP_TABLES repTables)
        {
            query = repTables.SqlQuery;
        }

        for (int j = 0; j < metadata_param.Count; j++)
        {
            if (Tab.LinkedParam.Any(x => x.Value == metadata_param[j].CODE))
            {
                metadata_param[j].HIDDENVALUE = 1;
            }

            if (Tab.TransitionParams.Any(x => x.CODE == metadata_param[j].CODE))
            {
                metadata_param[j] = Tab.TransitionParams.First(x => x.CODE == metadata_param[j].CODE);

            }
        }


        foreach (var prm in metadata_param)
        {
            object val = null;
            if (prm.DEFAULTVALUE != null)
            {
                if (prm.DEFAULTVALUE.ToString().Substring(0, 1) == "=")
                {
                    val = await GetDefaultVal(prm.DEFAULTVALUE.ToString().Substring(1), prm.DATATYPE);
                }
                else
                {
                    val = prm.DEFAULTVALUE;
                    if (prm.DATATYPE == "NUMBER")
                    {
                        val = Convert.ToDecimal(val);
                    }
                }
            }
            globalFilter.Add(prm.CODE, new Rep_Param(prm.ISMANDATORY, prm.DATATYPE, ParameterDirection.Input, prm.DATALENGTH,
                                                        prm.DATAPRECISION, val)
                           );
        }

        foreach ((string dd, string param, string datatype) in metadata_param.Where(x => x.DIMCODE != null).Select(x => (x.DIMCODE, x.CODE, x.DATATYPE)))
        {
            if (SprShortDict.ContainsKey(dd) == false && SprLongDict.ContainsKey(dd) == false)
            {
                var (_sprMetadata, _isShortDim, _SprLongFiltrMeta) = await _getspr.GetSPRDataAsync1(dd, dbname_query, datatype);
                if (_isShortDim)
                {
                    SprShortDict.Add(dd, _sprMetadata);
                }
                else
                {
                    SprLongDict.Add(dd, _SprLongFiltrMeta);
                }
            }
            if (SprLongDict.ContainsKey(dd))
            {
                SprSelLongDict.Add(param, new EWA.Models.REP_Models.Rep_SprLongSelDict { KeySprLongDict = dd, ValPrm = null });
            }
            else
            {
                SprSelShotDict.Add(param, new EWA.Models.REP_Models.Rep_SprShortSelDict { KeySprShortDict = dd, ValPrm = null });
            }
        }

        if (metadata_param.Count(x => x.ISMANDATORY == 1 && x.DEFAULTVALUE == null && x.HIDDENVALUE == 0) > 0 &&
               Tab.ParentTab.CodeObj != "head")
        {
            var (formValues, formRes) = await FiltrRow();

            if (formRes)
            {
                globalFilter.Clear();

                foreach (var kvp in formValues)
                {
                    var meta = metadata_param.First(x => x.CODE == kvp.Key);
                    globalFilter.Add(kvp.Key, new Rep_Param(meta.ISMANDATORY, meta.DATATYPE, ParameterDirection.Input, meta.DATALENGTH,
                                                       meta.DATAPRECISION, kvp.Value)
                               );
                }
            }
            DialogService.Close(true);
        }
    }

    private async Task<object> GetDefaultVal(string defval, string datatype)
    {
        object val;
        string query = "select " + defval + " from dual";
        Dictionary<string, object> data = new Dictionary<string, object>();

        await dbService.GetDataSingle(query, data, null, "1=1");
        val = data.FirstOrDefault().Value/*.ToString()*/;
        if (datatype == "DATE")
        {
            val = DateTime.ParseExact(val.ToString(), "yyyyMMdd", System.Globalization.CultureInfo.InvariantCulture).ToString("dd.MM.yyyy");
        }
        if (datatype == "NUMBER")
        {
            val = Convert.ToDecimal(val);
        }
        return val;
    }

    protected override async Task OnParametersSetAsync()
    {
        EmptyText = "Нет записей для отображения";

        if (Tab.IsSelect)
        {
            foreach (var kvp in Tab.Prm)
            {
                globalFilter[kvp.Key].Val = kvp.Value;

            }

            if (!GridService.MenyCheck(Tab.VisibilityFormula, Tab.ParentTab.RowData, Tab.ParentTab.ParamData))
            {
                EmptyText = "Переход недоступен";
                OldParams.Clear();
                Tab.isLoad = false;
                StateHasChanged();
                return;
            }

            if (Tab.Prm.Count(x => x.Value == null) > 0)
            {
                EmptyText = "Не введены связанные значения";
                OldParams.Clear();
                myLabel.Text = EmptyText;
                Tab.isLoad = false;
                StateHasChanged();

                return;
            }
            foreach (var m in metadata_param.Where(x => x.ISMANDATORY == 1))
            {
                if (globalFilter.Where(x => x.Value.Val == null).Count(x => x.Key == m.CODE) > 0)
                {
                    EmptyText = "Не введены обязательные параметры";
                    Tab.isLoad = false;
                    StateHasChanged();
                    return;
                }
            }

            if (!GridService.AreDictionariesEqual(Tab.Prm, OldParams) || (isInit && Tab.TransitionParams.Count == 0))
            {
                GridService.CopyDictionaries(OldParams, Tab.Prm);
                isInit = false;
                Tab.is_complete = false;
                await GenerateReport();
                Tab.isLoad = false;
            }
            Tab.isLoad = false;
        }

        if (!Tab.Show)
        {
            if (currentLogId > 0 && isLoading)
            {
                // Записываем LogC при скрытии вкладки
                _logService.LogC(currentLogId).Wait();
                currentLogId = 0;
            }
        }
    }

    private void ShowTooltip(ElementReference elementReference, string mes)
    {
        tooltipService.Open(elementReference, mes);
    }

    private async Task<(Dictionary<string, object> formValues, bool formRes)> FiltrRow()
    {
        _PublicParams._metadata = metadata_param;
        _PublicParams._action = "GlobalFILTR";
        _PublicParams._seldata = selectedRowData;
        _PublicParams._SprShortDict = SprShortDict;
        _PublicParams._SprSelShortDict = SprSelShotDict;
        _PublicParams._SprLongDict = SprLongDict;
        _PublicParams._SprSelLongDict = SprSelLongDict;
        _PublicParams._globalFilter = globalFilter;

        var result = await DialogService.OpenAsync<GLOBAL.FormGrid>("Фильтр ",
                        new Dictionary<string, object> { { "InParams", _PublicParams } },
                        new DialogOptions { Draggable = true, Resizable = true }
        );
        var formValues = new Dictionary<string, object>();
        bool formRes = false;
        if (result != null)
        {
            formValues = result.Values as Dictionary<string, object>;
            formRes = result.Result;
        }

        return (formValues, formRes);
    }

    private void CloseModal()
    {
        DialogService.Dispose();
    }

    private string GetLogJSon(LogInfo lg)
    {
        string jsonString = JsonSerializer.Serialize<LogInfo>(lg, new JsonSerializerOptions()
            {
                WriteIndented = true, // Добавляем отступы для красивого вывода
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

        return jsonString;
    }

    private async Task LoadDataBegin()
    {
        if (metadata_param.Where(x => x.HIDDENVALUE == 0).Count() == 0 ||
            EmptyText == "Переход недоступен" ||
            EmptyText == "Не введены связанные значения")
        {
            return;
        }

        var (formValues, formRes) = await FiltrRow();

        if (formRes)
        {
            globalFilter.Clear();

            foreach (var kvp in formValues)
            {
                string paramName = kvp.Key;
                object paramValue = kvp.Value;
                var meta = metadata_param.First(x => x.CODE == kvp.Key);
                globalFilter.Add(kvp.Key, new Rep_Param(meta.ISMANDATORY, meta.DATATYPE, ParameterDirection.Input, meta.DATALENGTH,
                                                        meta.DATAPRECISION, kvp.Value)
                                );
            }
            Tab.is_complete = false;
            DialogService.Close(true);
            await GenerateReport();
        }
    }

    protected async Task GenerateReport()
    {
        var infoobject = await _repService.GetInfoObjectAsync(Tab.CodeObj);
        bool success = false;
        string errorMessage = "";
        decimal logid = 0;

        try
        {
            isLoading = true;
            StateHasChanged();

            // запись в лог перед формированием отчета 
            LogInfo lg = new LogInfo
                {
                    _globalFilter = globalFilter,
                    _dbname = dbname_query.Split(';')[0],
                    _query = $"{Tab.CodeObj}.frx"
                };

            string jsonString = GetLogJSon(lg);

            (success, errorMessage, logid) = await _logService.LogI(Security.User.CODE, infoobject.AppCode, Tab.Index.ToString(),
                                                                  Tab.PathIndex, "R", jsonString, Tab.CodeObj, infoobject.AppCode, Security.User.SessionId, Security.User.SessionInfo);

            // сохраняем id лога для отслеживания завершения
            this.currentLogId = logid;

            string reportName = query;
            if (string.IsNullOrEmpty(reportName))
            {
                reportName = $"{Tab.CodeObj}.frx";   // REP_OBJECT.Code
            }

            // параметры отчета
            var parameters = new Dictionary<string, string>();
            // параметры, переданные через Params
            if (globalFilter.Count > 0)
            {
                foreach (var param in globalFilter)
                {
                    parameters[param.Key] = param.Value.Val?.ToString() ?? "";
                }
            }

            var queryString = HttpUtility.ParseQueryString(string.Empty);
            queryString["reportName"] = reportName;
            queryString["appCode"] = appCode;
            queryString["logId"] = logid.ToString();

            foreach (var param in parameters)
            {
                queryString[param.Key] = param.Value;
            }

            pdfUrl = $"/api/FastReport/GetReportPdf?{queryString}";
        }
        catch (Exception ex)
        {
            if (logid > 0)
            {
                await _logService.LogE(logid, ex.Message);
            }
            isLoading = false;
            Tab.is_complete = true; // При ошибке тоже считаем завершенным
        }
        finally
        {
            // Tab.is_complete устанавливается в OnLogCompleted когда отчет действительно готов
        }
    }

    private void OnLogCompleted(decimal logId)
    {
        if (this.currentLogId == logId)
        {
            isLoading = false;
            Tab.is_complete = true; // Отчет действительно готов
            InvokeAsync(StateHasChanged);
        }
    }

    public void Dispose()
    {
        if (currentLogId > 0 && isLoading)
        {
            // Записываем LogC при закрытии компонента
            _logService.LogC(currentLogId).Wait();
            currentLogId = 0;
        }

        EWA.Services.RepService.LogService.OnLogCompleted -= OnLogCompleted;
    }
}