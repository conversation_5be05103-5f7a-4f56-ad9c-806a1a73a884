<CascadingAuthenticationState>
<Router AppAssembly="@typeof(Program).Assembly">
    <Found Context="routeData">
        <AuthorizeRouteView RouteData="@routeData">
            <NotAuthorized>
                <RedirectToLogin IsAuthenticated="@context.User.Identity.IsAuthenticated" />
            </NotAuthorized>
            <Authorizing></Authorizing>
        </AuthorizeRouteView>
    </Found>
    <NotFound>
        <PageTitle>Not found</PageTitle>
        <LayoutView Layout="@typeof(MainLayout)">
            <RadzenRow>
                <RadzenColumn Size="12" style="margin-top: 5rem; margin-bottom: 5rem">
                    <RadzenText Text="Page not found" TextStyle="TextStyle.DisplayH1" style="margin: 0; margin-bottom: 2rem" TextAlign="TextAlign.Center" />
                    <RadzenText Text="Sorry, but there's nothing here!" TextStyle="TextStyle.H6" style="margin: 0" TextAlign="TextAlign.Center" TagName="TagName.P" />
                </RadzenColumn>
            </RadzenRow>
        </LayoutView>
    </NotFound>
</Router>
</CascadingAuthenticationState>
