﻿@attribute [Authorize]

<RadzenFormField Text="Время" Style="flex: 1; width:80px;" Variant="Variant.Text">
    <RadzenNumeric @bind-Value="@currentTime" Disabled="true" />
</RadzenFormField>
@code {
    private string currentTime;
    private Timer _timer;
    protected override void OnInitialized()
    {
        UpdateTime();
        _timer = new Timer(state => InvokeAsync(() => UpdateTime()), null, 0, 1000);
    }
    private void UpdateTime()
    {
        currentTime = DateTime.Now.ToString("HH:mm:ss");
        StateHasChanged();
    }
    public void Dispose()
    {
        _timer?.Dispose();
    }
}