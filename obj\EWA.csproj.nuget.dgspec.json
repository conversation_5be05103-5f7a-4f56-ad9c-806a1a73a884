{"format": 1, "restore": {"D:\\Projects\\Blazor\\ewa_augment\\EWA.csproj": {}}, "projects": {"D:\\Projects\\Blazor\\ewa_augment\\EWA.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\Blazor\\ewa_augment\\EWA.csproj", "projectName": "EWA", "projectPath": "D:\\Projects\\Blazor\\ewa_augment\\EWA.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\Blazor\\ewa_augment\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ClosedXML": {"target": "Package", "version": "[0.102.3, )"}, "ClosedXML.Report": {"target": "Package", "version": "[0.2.11, )"}, "FastReport.Data.OracleODPCore": {"target": "Package", "version": "[2025.2.0, )"}, "FastReport.OpenSource": {"target": "Package", "version": "[2025.2.0, )"}, "FastReport.OpenSource.Export.PdfSimple": {"target": "Package", "version": "[2025.2.0, )"}, "FastReport.OpenSource.Web": {"target": "Package", "version": "[2025.2.0, )"}, "Microsoft.AspNetCore.Authentication.Negotiate": {"target": "Package", "version": "[8.0.18, )"}, "Microsoft.AspNetCore.HeaderPropagation": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.OData": {"target": "Package", "version": "[8.2.5, )"}, "Microsoft.CodeAnalysis.CSharp": {"target": "Package", "version": "[4.8.0, )"}, "Microsoft.CodeAnalysis.Common": {"target": "Package", "version": "[4.8.0, )"}, "Microsoft.CodeAnalysis.VisualBasic": {"target": "Package", "version": "[4.8.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.3, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.3, )"}, "NCalcSync": {"target": "Package", "version": "[5.3.1, )"}, "Oracle.EntityFrameworkCore": {"target": "Package", "version": "[8.21.121, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[3.21.130, )"}, "Radzen.Blazor": {"target": "Package", "version": "[6.6.1, )"}, "System.DirectoryServices.AccountManagement": {"target": "Package", "version": "[9.0.6, )"}, "Z.Blazor.Diagrams": {"target": "Package", "version": "[3.0.3, )"}, "Z.Blazor.Diagrams.Core": {"target": "Package", "version": "[3.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.317/PortableRuntimeIdentifierGraph.json"}}}}}