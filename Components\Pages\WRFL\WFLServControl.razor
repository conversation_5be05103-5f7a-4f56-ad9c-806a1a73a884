﻿@inject EWA.Services.IWFLActivMonitoringService MonitoringService

<RadzenStack Orientation="Orientation.Horizontal">
    <RadzenText Text=@($"Сервис загрузки: {(MonitoringService.IsRunning ? "Работает" : "Остановлен")}") Style="width: 100%;"/>
    <RadzenToggleButton Change=ToggleService Text=@($"{(MonitoringService.IsRunning ? "Выкл" : "Вкл")}") ButtonStyle="ButtonStyle.Primary" />
</RadzenStack>

@code {
    
    private async Task ToggleService(bool value)
    {
        if (value && !MonitoringService.IsRunning)
        {
            await MonitoringService.StartAsync();
        }
        else
        {
            await MonitoringService.StopAsync();
        }
    }
}