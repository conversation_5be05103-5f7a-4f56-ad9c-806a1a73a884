﻿@attribute [Authorize]

@using System
@using System.Collections.Generic
@using System.Linq
@using System.Text
@using System.Text.Json
@using System.Text.RegularExpressions
@using System.Threading.Tasks
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Radzen
@using Radzen.Blazor
@using Microsoft.AspNetCore.Identity
@using EWA.Models
@using EWA.Services

@inject TooltipService tooltipService

<style>
    .rz-selectbutton .rz-button.rz-button-sm {
        display: inline-block;
        background-color: var(--rz-grid-header-background-color);
        color: var(--rz-input-placeholder-color);
        border: var(--rz-border-normal);
    }

        .rz-selectbutton .rz-button.rz-button-sm.rz-state-active {
            background-color: var(--rz-primary-light);
            color: var(--rz-selectbar-selected-color);
            border: var(--rz-selectbar-border);
        }

    :root {
        --rz-input-line-height: 1rem;
        --rz-input-height: 2rem;
    }
</style>
<RadzenStack>
    <RadzenFieldset Text="Редактор правил" Collapsible="true" Style="width: 100%; height:100%;">
        <RadzenDataGrid @ref="grid"
                        TItem="SIB_Models.RuleModel"
                        Data="@rules_h"
                        AllowSorting="false"
                        Density="Density.Compact"
                        RowRender="@RowRender"
                        LoadChildData="@LoadChildData"
                        RowCollapse="@(args => grid.ColumnsCollection.ToList().ForEach(c => c.ClearFilters()))"
                        EditMode="DataGridEditMode.Single"
                        RowUpdate="@OnUpdateRow"
                        RowCreate="@OnCreateRow"
                        AllowPaging="true"
                        PagerPosition="PagerPosition.Bottom"
                        AllowColumnResize="true"
                        ColumnWidth="100px"
                        Style="width:100%; height:400px;"
                        @bind-Value=@selectedItems
                        SelectionMode="DataGridSelectionMode.Single"
                        PagerHorizontalAlign="HorizontalAlign.Center"
                        ShowPagingSummary="true"
                        PageSizeText="записей на странице"
                        EmptyText="Нет записей для отображения"
                        PagingSummaryFormat="@pagingSummaryFormat"
                        PageSize="@pageDefault"
                        PageSizeOptions="@pageSizeOptions">
            <HeaderTemplate>
                <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween">
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="2rem">
                        <RadzenColumn>
                            <RadzenButton Icon="add"
                                          Click="@InsertCond"
                                          Disabled="@(RowToInsert.Count() > 0)"
                                          Variant="Variant.Text"
                                          ButtonStyle="ButtonStyle.Base"
                                          MouseLeave="TooltipService.Close"
                                          MouseEnter="@(args => ShowTooltip(args, "Добавить условие", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                            <RadzenButton Icon="library_add"
                                          Click="@InsertGroup"
                                          Disabled="@(RowToInsert.Count() > 0)"
                                          Variant="Variant.Text"
                                          ButtonStyle="ButtonStyle.Base"
                                          MouseLeave="TooltipService.Close"
                                          MouseEnter="@(args => ShowTooltip(args,"Добавить группу", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                            <RadzenButton Icon="content_copy"
                                          Variant="Variant.Text"
                                          ButtonStyle="ButtonStyle.Base"
                                          Click="@CopySelectedRecord"
                                          Disabled="@(selectedItems.Count == 0 || selectedItems.Any(item => item.CODE == "GR_MAIN"))"
                                          MouseLeave="TooltipService.Close"
                                          MouseEnter="@(args => ShowTooltip(args,"Создать копию строки", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                        </RadzenColumn>
                        <RadzenColumn>
                            <RadzenButton Icon="edit"
                                          Click="@EditSelectedRow"
                                          Disabled="@(selectedItems.Count == 0 || selectedItems.Any(item => item.CODE == "GR_MAIN"))"
                                          Variant="Variant.Text"
                                          ButtonStyle="ButtonStyle.Base"
                                          MouseLeave="TooltipService.Close"
                                          MouseEnter="@(args => ShowTooltip(args,"Редактировать строку", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                            <RadzenButton Icon="delete"
                                          Click="@RemoveRow"
                                          Disabled="@(selectedItems.Count == 0 || selectedItems.Any(item => item.CODE == "GR_MAIN"))"
                                          Variant="Variant.Text"
                                          ButtonStyle="ButtonStyle.Base"
                                          MouseLeave="TooltipService.Close"
                                          MouseEnter="@(args => ShowTooltip(args, "Удалить строку", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                        </RadzenColumn>
                        <RadzenColumn>
                            <RadzenToggleButton Icon="preview" ToggleIcon="preview_off"
                                                Click=@(() => FieldVisible = !FieldVisible)
                                                Variant="Variant.Text"
                                                @bind-Value="isActive"
                                                ButtonStyle="ButtonStyle.Base"
                                                MouseLeave="TooltipService.Close"
                                                MouseEnter="@(args => ShowTooltip(args, "Видимость полей", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                            <RadzenButton Icon="expand_circle_down"
                                          Variant="Variant.Text"
                                          ButtonStyle="ButtonStyle.Base"
                                          Click="@(args => ToggleRowsExpand(true))"
                                          MouseLeave="TooltipService.Close"
                                          MouseEnter="@(args => ShowTooltip(args, "Развернуть все", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                            <RadzenButton Icon="expand_circle_up" Variant="Variant.Text" ButtonStyle="ButtonStyle.Base"
                                          MouseLeave="TooltipService.Close"
                                          MouseEnter="@(args => ShowTooltip(args, "Свернуть все", new TooltipOptions(){ Position = TooltipPosition.Top }))"
                                          Click="@(args => ToggleRowsExpand(false))" />

                        </RadzenColumn>
                    </RadzenStack>
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                        <RadzenButton Icon="deployed_code_update"
                                      Click="@CheckRule"
                                      Variant="Variant.Text"
                                      ButtonStyle="ButtonStyle.Base"
                                      MouseLeave="TooltipService.Close"
                                      MouseEnter="@(args => ShowTooltip(args,"Создать правило", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                        <RadzenButton Icon="cancel"
                                      Click="@RestRules"
                                      Variant="Variant.Text"
                                      ButtonStyle="ButtonStyle.Base"
                                      MouseLeave="TooltipService.Close"
                                      MouseEnter="@(args => ShowTooltip(args, "Откатить", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                        <RadzenButton Icon="clear_all"
                                      Click="@ClearAllRules"
                                      Variant="Variant.Text"
                                      ButtonStyle="ButtonStyle.Base"
                                      MouseLeave="TooltipService.Close"
                                      MouseEnter="@(args => ShowTooltip(args, "Очистить", new TooltipOptions(){ Position = TooltipPosition.Top }))" />

                    </RadzenStack>
                </RadzenStack>
            </HeaderTemplate>
            <Columns>
                <RadzenDataGridColumn Title="Код Группы" Property="CODE" MinWidth="50px" Width="100px">
                    <Template Context="data">
                       @{
                            var groupName = data.CODE;
                            string displayName = GetDisplayName(groupName);
                        }
                        @if (data.TYPE == "GROUP")
                        {
                            <strong>
                                @displayName
                            </strong>
                        }
                        else
                        {
                            <span>
                                @displayName
                            </span>
                        }
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Код родительской группы" Property="PAR_GROUP" MinWidth="50px" Width="100px">
                    <Template Context="data">
                        @{
                            bool showWarning = data.TYPE == "COND" && data.PAR_GROUP == "GR_MAIN" && rules.Any(r => r.TYPE == "GROUP" && r.CODE_GROUP != "GR_MAIN");
                            string displayName;
                            if (data.PAR_GROUP == null)
                            {
                                displayName = "";
                            }
                            else
                            {
                                displayName = GetDisplayName(data.PAR_GROUP);
                            }
                        }
                        <span style="@(showWarning ? "color: var(--rz-danger)" : "")" title="@(showWarning ? "Необходимо сменить родительскую группу!" :  data.PAR_GROUP)">
                            @(displayName)
                        </span>
                    </Template>
                    <EditTemplate Context="rule">
                        @{
                            var displayNames = groupCodes
                            /*.Where(code => code != rule.CODE_GROUP) */
                            .Select(code => new
                            {
                                Value = code,
                                Text = GetDisplayName(code)
                            })
                            .ToList();
                        }
                        <RadzenDropDown @bind-Value="rule.PAR_GROUP"
                                        Data="@displayNames"
                                        Style="width:100%; "
                                        Placeholder="Выберите группу"
                                        TextProperty="Text"
                                        ValueProperty="Value"
                                        TValue="string"
                                        Change="@(value => OnParGroupChange(value, rule))"
                                        Disabled="@(rule.CODE == "GR_MAIN")" />
                    </EditTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="№" Property="NUM" Width="25px" MinWidth="10px" Visible="@FieldVisible" />
                <RadzenDataGridColumn Title="Код" Property="CODE" Width="75px" MinWidth="25px" Visible="@FieldVisible">
                    <Template Context="data">
                        @{
                            var groupName = data.CODE;
                            string displayName = GetDisplayName(groupName);
                        }
                        @if (data.TYPE == "GROUP")
                        {
                            <strong>
                                @displayName
                            </strong>
                        }
                        else
                        {
                            <span>
                                @displayName
                            </span>
                        }
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Тип" Property="TYPE" Width="100px" MinWidth="50">
                    <Template Context="rule">
                        @(typeGroup.FirstOrDefault(x => x.Value == rule.TYPE).Key)
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Объединение" Property="OPER_UNION" Width="70px" MinWidth="30px">
                    <Template Context="rule">
                        @(logicConditions.FirstOrDefault(x => x.Value == rule.OPER_UNION).Key)
                    </Template>
                    <EditTemplate Context="rule">
                        <RadzenDropDown @bind-Value="rule.OPER_UNION"
                                        Data="@logicConditions"
                                        TextProperty="Key"
                                        ValueProperty="Value"
                                        Style="width:100%;"
                                        AllowClear="true"
                                        Disabled="@(!CheckCntRow(rule.PAR_GROUP) || rule.CODE == "GR_MAIN")"
                                        TValue="string" />
                    </EditTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Тип атрибута" Property="TYPE_ATTR" Width="120px" MinWidth="50px">
                    <Template Context="rule">
                        <span title="@(type_attr.FirstOrDefault(x => x.Value == rule.TYPE_ATTR).Key)">
                            @(type_attr.FirstOrDefault(x => x.Value == rule.TYPE_ATTR).Key)
                        </span>
                    </Template>
                    <EditTemplate Context="rule">
                        <RadzenDropDown Name="TYPEATTR"
                                        @bind-Value="rule.TYPE_ATTR"
                                        Data="@type_attr"
                                        TextProperty="Key"
                                        Change="onChooseTypeAttr"
                                        ValueProperty="Value"
                                        Style="width:100%; display:block;"
                                        Placeholder="Выберите тип атрибута"
                                        TValue="string"
                                        Disabled="@(rule.TYPE == "GROUP")" />
                        <RadzenRequiredValidator Component="TYPEATTR" Text="Обязательное поле!" Popup="true" Visible="@(rule.TYPE == "COND")" />
                    </EditTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Атрибут" Property="ATTR" Width="100px" MinWidth="50px">
                    <Template Context="rule">
                        <span title="@(Attrs_Dic.FirstOrDefault(x => x.Key == rule.ATTR)?.Value)">
                            @(Attrs_Dic.FirstOrDefault(x => x.Key == rule.ATTR)?.Value)
                        </span>
                    </Template>
                    <EditTemplate Context="rule">
                        <RadzenDropDown Name="ATTR"
                                        @bind-Value="rule.ATTR"
                                        Data="@Attrs"
                                        TextProperty="Value"
                                        ValueProperty="Key"
                                        Style="width:100%;"
                                        Placeholder="Выберите значение атрибута"
                                        TValue="string"
                                        Disabled="@(rule.TYPE == "GROUP" || rule.TYPE_ATTR == null)"
                                        AllowFiltering="true"
                                        FilterCaseSensitivity=FilterCaseSensitivity.CaseInsensitive />
                        <RadzenRequiredValidator Component="ATTR" Text="Обязательное поле!" Popup="true" Visible="@(rule.TYPE == "COND")" />
                    </EditTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Оператор" Property="OPER_ATTR" Width="70px" MinWidth="30px">
                    <Template Context="rule">
                        <span title=" @(conditions.FirstOrDefault(x => x.Value == rule.OPER_ATTR).Key)">
                            @(conditions.FirstOrDefault(x => x.Value == rule.OPER_ATTR).Key)
                        </span>
                    </Template>
                    <EditTemplate Context="rule">
                        <RadzenDropDown @bind-Value="rule.OPER_ATTR"
                                        Data="@conditions"
                                        TextProperty="Key"
                                        ValueProperty="Value"
                                        Style="width:100%; "
                                        Placeholder="Выберите оператор"
                                        TValue="string"
                                        Disabled="@(rule.TYPE == "GROUP")" />
                    </EditTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Значение" Property="VAL">
                    <EditTemplate Context="rule">
                        <RadzenTextBox @bind-Value="rule.VAL" Style="width:100%;" Disabled="@(rule.TYPE == "GROUP")" />
                    </EditTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Действие" Frozen="true" FrozenPosition="FrozenColumnPosition.Right" MinWidth="80px">
                    <Template Context="rule">
                        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween">
                            <RadzenColumn>
                                <RadzenButton Icon="add"
                                              Click="@InsertCond"
                                              Disabled="@(RowToInsert.Count() > 0)"
                                              Variant="Variant.Text"
                                              ButtonStyle="ButtonStyle.Base"
                                              Size="ButtonSize.ExtraSmall"
                                              MouseLeave="TooltipService.Close"
                                              MouseEnter="@(args => ShowTooltip(args, "Добавить условие", new TooltipOptions(){ Position = TooltipPosition.Top, Delay = 1000 }))" />
                                <RadzenButton Icon="library_add"
                                              Click="@InsertGroup"
                                              Disabled="@(RowToInsert.Count() > 0)"
                                              Variant="Variant.Text"
                                              ButtonStyle="ButtonStyle.Base"
                                              Size="ButtonSize.ExtraSmall"
                                              MouseLeave="TooltipService.Close"
                                              MouseEnter="@(args => ShowTooltip(args,"Добавить группу", new TooltipOptions(){ Position = TooltipPosition.Top, Delay = 1000 }))" />
                            </RadzenColumn>
                            <RadzenColumn>
                                <RadzenButton Icon="edit"
                                              Click="@EditSelectedRow"
                                              Disabled="@(rule.CODE == "GR_MAIN")"
                                              Variant="Variant.Text"
                                              ButtonStyle="ButtonStyle.Base"
                                              Size="ButtonSize.ExtraSmall"
                                              MouseLeave="TooltipService.Close"
                                              MouseEnter="@(args => ShowTooltip(args,"Редактировать строку", new TooltipOptions(){ Position = TooltipPosition.Top, Delay = 1000 }))" />
                                <RadzenButton Icon="delete"
                                              Click="@RemoveRow"
                                              Disabled="@(rule.CODE == "GR_MAIN")"
                                              Variant="Variant.Text"
                                              ButtonStyle="ButtonStyle.Base"
                                              Size="ButtonSize.ExtraSmall"
                                              MouseLeave="TooltipService.Close"
                                              MouseEnter="@(args => ShowTooltip(args, "Удалить строку", new TooltipOptions(){ Position = TooltipPosition.Top, Delay = 1000 }))" />
                            </RadzenColumn>

                        </RadzenStack>
                    </Template>
                    <EditTemplate Context="rule">
                        <RadzenButton Size="ButtonSize.ExtraSmall"
                                      Icon="check"
                                      Click="@(args => grid.UpdateRow(rule))" />
                        <RadzenButton Size="ButtonSize.ExtraSmall"
                                      Icon="close"
                                      Click="@((args) => CancelEdit(rule))"
                                      Style="margin-left: 5px;" />
                    </EditTemplate>
                </RadzenDataGridColumn>

            </Columns>
        </RadzenDataGrid>
    </RadzenFieldset>

    <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" Style="margin-top: 1rem;">
        <RadzenFieldset Text="Результат" Style="width: 100%">
            <RadzenTextArea Value="@ruleResult_STR" Style="width: 100%; height: 200px; resize:vertical" ReadOnly="true" />
        </RadzenFieldset>
    </RadzenStack>

    <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End" Gap="0.5rem" Style="margin-top: 1rem;">
        <RadzenButton @ref="saveButton" ButtonType="ButtonType.Submit" Icon="save" Text="Сохранить" Click="@SaveClick" Variant="Variant.Flat" Disabled="false" />
        <RadzenButton ButtonStyle="ButtonStyle.Light" Text="Отмена" Click="@CancelClick" Variant="Variant.Flat" />
    </RadzenStack>
</RadzenStack>
@code {
    [Inject] protected IJSRuntime JSRuntime { get; set; }
    [Inject] protected NavigationManager NavigationManager { get; set; }
    [Inject] protected DialogService DialogService { get; set; }
    [Inject] protected TooltipService TooltipService { get; set; }
    [Inject] protected ContextMenuService ContextMenuService { get; set; }
    [Inject] protected NotificationService NotificationService { get; set; }
    [Inject] protected EWA.Services.SIBService.UserService UServ { get; set; }

    [Parameter] public string FRule { get; set; }
    [Parameter] public string SRule { get; set; }
    [Parameter] public string UserAttrs { get; set; }
    [Parameter] public string ObjectAttrs { get; set; }

    RadzenDataGrid<SIB_Models.RuleModel> grid;
    IEnumerable<SIB_Models.RuleModel> rules_h;
    private SIB_Models.RuleModel selectedRow;

    private List<SIB_Models.RuleModel> rules = new();
    private IList<SIB_Models.RuleModel> selectedItems = new List<SIB_Models.RuleModel>();
    private List<SIB_Models.RuleModel> RowToInsert = new List<SIB_Models.RuleModel>();
    private List<SIB_Models.RuleModel> RowToUpdate = new List<SIB_Models.RuleModel>();
    private List<string> groupCodes = new List<string> { "GR_MAIN" };
    private List<DropDownAttr> Attrs_Dic;
    private List<DropDownAttr> Attrs = new List<DropDownAttr>(); //16.04.2025 Sheshko
    private List<DropDownAttr> AttrsVal = new List<DropDownAttr>();



    private SIB_Models.RuleModel currentRule = new SIB_Models.RuleModel();
    IEnumerable<int> pageSizeOptions = new int[] { 10, 25, 50 };
    int ruleCounter = 1;
    int pageDefault = 10;
    
    string pagingSummaryFormat = "Страница {0} из {1} <b>(всего {2} записей)</b>";
    private string defgroup = "GR_MAIN";
    private string ruleResult = string.Empty;
    private string ruleResult_STR = string.Empty;
    string ruleResult_old = string.Empty; //26.04.2025 Sheshko
    string ruleResult_new = string.Empty;
    private string attr_user = string.Empty;
    private string attr_obj = string.Empty;

    private Dictionary<string, (string Value, string tattr)> AttSpr = new Dictionary<string, (string Value, string tattr)>();
    private Dictionary<string, string> conditions = new Dictionary<string, string>
    {
        { "Равно", "=" },
        { "Больше", ">" },
        { "Меньше", "<" },
        { "Меньше либо равно", "<=" },
        { "Больше либо равно", ">=" },
        { "Не равно", "!=" },
        { "Входит в", "IN" },
        { "Не входит в", "NOT IN" },
        { "Содержит", "LIKE" },
        { "Не содержит", "NOT LIKE" },
        { "Между", "BETWEEN" }
    };
    private Dictionary<string, string> logicConditions = new Dictionary<string, string>
    {
        { "И", "AND" },
        { "ИЛИ", "OR" }
    };
    private Dictionary<string, string> typeGroup = new Dictionary<string, string>
    {
        { "Группа", "GROUP"},
        { "Условие", "COND"}
    };
    private Dictionary<string, string> type_attr = new Dictionary<string, string>
    {
        { "Атрибут объекта", "O" },
        { "Пользовательский", "U" },
    };

    bool FieldVisible = false;
    private bool is_start = false;
    private bool isActive = true;
    private bool allRowsExpanded = false; // По умолчанию установите значение на false
    private bool CheckRuleCl = false; //26.04.2025 Sheshko

    private RadzenButton saveButton;

    async Task ToggleRowsExpand(bool expand)
    {
        allRowsExpanded = expand;
        if (expand)
        {
            await grid.ExpandRows(rules);
        }
        else
        {
            await grid.CollapseRows(grid.View);
        }
    }
    void ShowTooltip(ElementReference elementReference, string buttomName, TooltipOptions options = null) => tooltipService.Open(elementReference, buttomName, options);

    void Reset()
    {
        RowToInsert.Clear();
        RowToUpdate.Clear();
    }
    void Reset(SIB_Models.RuleModel row)
    {
        RowToInsert.Remove(row);
        RowToUpdate.Remove(row);
    }
    private void ClearData()
    {
        Reset();
        rules.Clear();
        ruleCounter = 1;
        currentRule = new SIB_Models.RuleModel();
        ruleResult_STR = string.Empty;
        groupCodes = new List<string> { "GR_MAIN" };
        rules.Add(new SIB_Models.RuleModel
            {
                NUM = ruleCounter,
                CODE_GROUP = defgroup,
                CODE = defgroup,
                TYPE = "GROUP"
            });
        rules_h = rules.Where(r => r.PAR_GROUP == null);
    }

    private async Task ClearAllRules()
    {
        var res = await DialogService.Confirm("Очистить правило?", "Очистка", new ConfirmOptions() { OkButtonText = "Да", CancelButtonText = "Нет" });
        if (res == true)
        {
            ClearData();
            await grid.Reload();
            StateHasChanged();
        }
    }

    private async Task RestRules()
    {
        var res = await DialogService.Confirm("Восстановить правило?", "Откат", new ConfirmOptions() { OkButtonText = "Да", CancelButtonText = "Нет" });
        if (res == true)
        {
            ClearData();
            attr_user = UserAttrs;
            attr_obj = ObjectAttrs;
            ruleResult = FRule;
            ruleResult_STR = SRule;
            if (!string.IsNullOrEmpty(ruleResult))
            {
                rules = JsonSerializer.Deserialize<List<SIB_Models.RuleModel>>(ruleResult);
                groupCodes.Clear();
                groupCodes.AddRange(rules.Where(r => r.TYPE == "GROUP")
                                        .Select(r => r.CODE_GROUP)
                                        .Distinct());
            }
            else
            {
                rules.Add(new SIB_Models.RuleModel
                    {
                        NUM = ruleCounter,
                        CODE_GROUP = defgroup,
                        CODE = defgroup,
                        TYPE = "GROUP"
                    });
            }
            ruleCounter = rules.Any() ? rules.Max(r => r.NUM) + 1 : 0;
            rules_h = rules.Where(r => r.PAR_GROUP == null);
            await grid.Reload();
            StateHasChanged();
        }
    }

    //void CancelEdit(SIB_Models.RuleModel rule)

    private async Task CancelEdit(SIB_Models.RuleModel rule)
    {
        Reset();
        ruleResult_new = JsonSerializer.Serialize(rules, new JsonSerializerOptions { WriteIndented = true });
        if (ruleResult_old != ruleResult_new)
        {
            var res = await DialogService.Confirm("Правила были отредактированы. Сохранить изменения?", "Сохранить", new ConfirmOptions() { OkButtonText = "Да", CancelButtonText = "Нет" });
            if (res == true)
            {
                saveButton.Visible = false;
                grid.CancelEditRow(rule);
                CheckRuleCl = true;
            }
            else
            {
                saveButton.Visible = true;
            }
        }
        else
        {
            grid.CancelEditRow(rule);
            CheckRuleCl = false;
            saveButton.Visible = true;
        }
    }

    void InsertGroup()
    {
        Reset();
        string newGroupCode = $"GR_{groupCodes.Count}";
        string parentGroup = defgroup;
        if (selectedItems != null && selectedItems.Count > 0)
        {
            selectedRow = selectedItems[0];
            parentGroup = string.IsNullOrEmpty(selectedRow.PAR_GROUP) ? defgroup : selectedRow.PAR_GROUP;
        }
        int rule_int = ruleCounter++;
        var row = new SIB_Models.RuleModel
            {
                NUM = rule_int,
                CODE_GROUP = newGroupCode,
                CODE = newGroupCode,
                TYPE = "GROUP",
                PAR_GROUP = parentGroup,
            };
        RowToInsert.Add(row);
        grid.InsertRow(row);
    }

    void InsertCond()
    {
        Reset();
        string codeGroup = defgroup;
        if (selectedItems != null && selectedItems.Count > 0)
        {
            selectedRow = selectedItems[0];
            codeGroup = string.IsNullOrEmpty(selectedRow.CODE_GROUP) ? defgroup : selectedRow.CODE_GROUP;
        }
        int rul_num = ruleCounter++;
        var row = new SIB_Models.RuleModel
        {
                NUM = rul_num,
                CODE_GROUP = codeGroup,
                CODE = $"CND_{rul_num}",
                TYPE = "COND",
                PAR_GROUP = codeGroup,
        };
        RowToInsert.Add(row);
        grid.InsertRow(row);
    }

    private async Task RemoveRow()
    {
        if (selectedItems != null && selectedItems.Count > 0)
        {
            var res = await DialogService.Confirm("Удалить строку?", "Удаление", new ConfirmOptions() { OkButtonText = "Да", CancelButtonText = "Нет" });
            if (res == true)
            {
                selectedRow = selectedItems[0];
                if (selectedRow.TYPE == "GROUP")
                {
                    groupCodes.Remove(selectedRow.CODE_GROUP);
                }
                rules.Remove(selectedRow);
                await grid.Reload();
                StateHasChanged();
            }
        }
    }

    void EditSelectedRow()
    {
        if (selectedItems != null && selectedItems.Count > 0)
        {
            selectedRow = selectedItems[0];
            saveButton.Visible = false;
            CheckRuleCl = false;
            ruleResult_old = JsonSerializer.Serialize(rules, new JsonSerializerOptions { WriteIndented = true });
            if (selectedRow.TYPE == "COND")
            {
                onChooseTypeAttr(selectedRow.TYPE_ATTR);
            }
            grid.EditRow(selectedRow);
        }
    }

    void OnUpdateRow(SIB_Models.RuleModel row)
    {
        CheckRuleCl = true;
        ruleResult_new = JsonSerializer.Serialize(rules, new JsonSerializerOptions { WriteIndented = true });
        if (ruleResult_old != ruleResult_new)
        {
            saveButton.Visible = false;
        }
        Reset(row);
        grid.Reload();
        StateHasChanged();
    }

    void OnCreateRow(SIB_Models.RuleModel row)
    {
        rules.Add(row);
        if (row.TYPE == "GROUP")
        {
            groupCodes.Add(row.CODE);
        }
        RowToInsert.Clear();
        grid.Reload();
        StateHasChanged();
    }
    
    private void OnParGroupChange(object value, SIB_Models.RuleModel rule)
    {
        var selectedGroup = value as string;
        rule.PAR_GROUP = selectedGroup;
        if (rule.TYPE == "COND")
        {
            rule.CODE_GROUP = selectedGroup;
        }
    }
    void RowRender(RowRenderEventArgs<SIB_Models.RuleModel> args)
    {
        args.Expandable = rules.Where(e => e.PAR_GROUP == args.Data.CODE).Any();
    }
    void LoadChildData(DataGridLoadChildDataEventArgs<SIB_Models.RuleModel> args)
    {
        args.Data = rules.Where(e => e.PAR_GROUP == args.Item.CODE);
    }
    protected override bool ShouldRender()
    {
        return is_start;
    }
    /*protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        base.OnAfterRender(firstRender);
        if (firstRender)
        {
            await grid.ExpandRow(rules_h.FirstOrDefault());
        }
    }*/

    private void CheckRule()
    {
        ruleResult_STR = BuildRuleString(rules);
        CheckRuleCl = true;  //26.04.2025 Sheshko
        saveButton.Visible = true;
    }

    public string BuildRuleString(List<SIB_Models.RuleModel> rules)
    {
        var ruleDict = rules
                     .Where(r => r.PAR_GROUP != null)
                     .GroupBy(r => r.PAR_GROUP)
                     .ToDictionary(g => g.Key, g => g.ToList());
        return BuildGroup(ruleDict, "GR_MAIN");
    }

    private string BuildGroup(Dictionary<string, List<SIB_Models.RuleModel>> ruleDict, string groupCode)
    {
        if (!ruleDict.ContainsKey(groupCode))
            return string.Empty;
        var conditions = new List<string>();
        foreach (var rule in ruleDict[groupCode])
        {
            if (rule.TYPE == "GROUP")
            {
                var nestedGroup = BuildGroup(ruleDict, rule.CODE);
                if (!string.IsNullOrEmpty(nestedGroup))
                {
                    conditions.Add(nestedGroup);
                }
            }
            else if (rule.TYPE == "COND")
            {
                var condition = $"{rule.ATTR} {rule.OPER_ATTR} {rule.VAL}";
                conditions.Add(condition);
            }
        }
        var groupString = string.Join($" {ruleDict[groupCode].Last().OPER_UNION} ", conditions);
        return conditions.Count > 1 ? $"({groupString})" : groupString;
    }

    private bool CheckCntRow(string codeGroup)
    {
        string groupToCheck = string.IsNullOrEmpty(codeGroup) ? defgroup : codeGroup;
        var conditionCount = rules.Count(rule => rule.TYPE == "COND" && rule.CODE_GROUP == groupToCheck);
        var groupCount = rules.Count(rule => rule.TYPE == "GROUP" && rule.PAR_GROUP == groupToCheck);
        return conditionCount >= 1 || groupCount >= 1;
    }
    protected override async Task OnInitializedAsync()
    {
        AttSpr = await UServ.GetAttrAsync();
        Attrs_Dic = AttSpr.Select(kv => new DropDownAttr
            {
                Key = kv.Key,
                Value = kv.Value.Value,
                Tattr = kv.Value.tattr
            }).ToList();
        attr_user = UserAttrs;
        attr_obj = ObjectAttrs;
        ruleResult = FRule;
        ruleResult_STR = SRule;
        if (!string.IsNullOrEmpty(ruleResult))
        {
            rules = JsonSerializer.Deserialize<List<SIB_Models.RuleModel>>(ruleResult);
            groupCodes.Clear();
            groupCodes.AddRange(rules.Where(r => r.TYPE == "GROUP")
                                    .Select(r => r.CODE_GROUP)
                                    .Distinct());
            ruleCounter = rules.Any() ? rules.Max(r => r.NUM) + 1 : 0;

            foreach (var rule in rules.Where(r => r.TYPE == "COND"))
            {
                if (AttSpr.TryGetValue(rule.ATTR, out var attr))
                {
                    rule.TYPE_ATTR = attr.tattr;
                }
            }
        }
        else
        {
            rules.Add(new SIB_Models.RuleModel
                {
                    NUM = ruleCounter,
                    CODE_GROUP = defgroup,
                    CODE = defgroup,
                    TYPE = "GROUP"
                });
        }
        rules_h = rules.Where(r => r.PAR_GROUP == null);
        await InvokeAsync(async () =>
        {
            await grid.ExpandRow(rules_h.First());
        });
        ///await ToggleRowsExpand(true);
        is_start = true;
    }

    private void SaveClick()
    {
        if (CheckRuleCl)
        {
            List<string> tmp_attrs = rules.Where(r => r.TYPE == "COND")
                                          .Select(r => r.ATTR)
                                          .Distinct().ToList();
            attr_user = string.Join(", ",Attrs_Dic
                              .Where(a => tmp_attrs.Contains(a.Key) && a.Tattr == "U")
                              .Select(a => a.Key));
            attr_obj = string.Join(", ", Attrs_Dic
                             .Where(a => tmp_attrs.Contains(a.Key) && a.Tattr == "O")
                             .Select(a => a.Key));
            ruleResult = JsonSerializer.Serialize(rules, new JsonSerializerOptions { WriteIndented = true });
            DialogService.Close(new { status = 1, rResult = ruleResult, rResultS = ruleResult_STR, uattr = attr_user, oattr = attr_obj });
        }
        else
        {
            DialogService.Alert($"Для сохранения нажмите 'Создать правило' ", "Предупреждение");
        }
    }
    private async Task CancelClick()
    {
        //26.04.2025 Sheshko
        ruleResult_new = JsonSerializer.Serialize(rules, new JsonSerializerOptions { WriteIndented = true }); //26.04.2025 Sheshko
        if (ruleResult_old != ruleResult_new)
        {
            var res = await DialogService.Confirm("Правила были отредактированы. Сохранить изменения?", "Сохранить", new ConfirmOptions() { OkButtonText = "Да", CancelButtonText = "Нет" });
            if (res == true)
            {
                SaveClick();
                CheckRuleCl = false;
            }
            else
            {
                DialogService.Close(new { status = 0, rResult = string.Empty, rResultS = string.Empty, uattr = string.Empty, oattr = string.Empty });
            }
        }
        else
        {
            DialogService.Close(new { status = 0, rResult = string.Empty, rResultS = string.Empty, uattr = string.Empty, oattr = string.Empty });
        }
    }
    public class DropDownAttr
    {
        public string Key { get; set; }
        public string Value { get; set; }
        public string Tattr { get; set; }
    }
    private string GetDisplayName(string groupName)
    {
        var formats = new Dictionary<string, string>
        {
            { @"GR_(\d+)", "Группа {0}" },
            { @"CND_(\d+)", "Условие {0}" },
            { @"GR_MAIN", "Основная группа" }
        };
        foreach (var format in formats)
        {
            var match = Regex.Match(groupName, format.Key);
            if (match.Success)
            {
                return string.Format(format.Value, match.Groups[1].Value);
            }
        }
        return groupName;
    }

    private void CopySelectedRecord()
    {
        Reset();
        int rul_num = ruleCounter++;
        string CodeGroup;
        if (selectedItems != null && selectedItems.Count > 0)
        {
            selectedRow = selectedItems[0];
        }
        if (selectedRow.TYPE == "GROUP")
        {
            CodeGroup = $"GR_{groupCodes.Count}";
        }
        else
        {
            CodeGroup = $"CND_{rul_num}";
        }
        if (selectedRow.TYPE == "COND")
        {
            onChooseTypeAttr(selectedRow.ATTR);
            onChooseTypeAttrVal(selectedRow.VAL);
        }
        if (selectedRow.TYPE_ATTR == null)
        {
            onChooseTypeAttr(selectedRow.ATTR);
        }
        var row = new SIB_Models.RuleModel
            {
                CODE_GROUP = CodeGroup,
                NUM = rul_num,
                TYPE = selectedRow.TYPE ?? string.Empty,
                PAR_GROUP = selectedRow.PAR_GROUP ?? string.Empty,
                CODE = CodeGroup,
                ATTR = selectedRow.ATTR ?? string.Empty,
                TYPE_ATTR = selectedRow.TYPE_ATTR ?? string.Empty,
                OPER_ATTR = selectedRow.OPER_ATTR ?? string.Empty,
                VAL = selectedRow.VAL ?? string.Empty,
                OPER_UNION = selectedRow.OPER_UNION ?? string.Empty
            };
        RowToInsert.Add(row);
        grid.InsertRow(row);
    }
    private void onChooseTypeAttr(object value)
    {
        Attrs = Attrs_Dic
                        .Where(x => x.Tattr == value.ToString())
                        .ToList();
    }
    private void onChooseTypeAttrVal(object value)
    {
        AttrsVal = Attrs_Dic
                   .Where(x => x.Tattr == value.ToString())
                   .ToList();
    }
}