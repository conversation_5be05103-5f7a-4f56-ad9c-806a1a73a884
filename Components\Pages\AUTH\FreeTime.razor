﻿@implements IDisposable
@using Microsoft.AspNetCore.Components.Web

 <RadzenDropDown @bind-Value="selectedValue"
                  Data="@items"
                  TextProperty="Text"
                  ValueProperty="Value"
                  Style="width: 200px" 
                  Placeholder="Выберите игру" />

@if (selectedValue == "1")
{
    <div @ref="GameAreaRef" tabindex="0" @onkeydown="HandleKeyPress" style="outline: none;">
        <div style="display: grid; grid-template-columns: repeat(@Width, 20px); width: max-content;">
            @for (int y = 0; y < Height; y++)
            {
                @for (int x = 0; x < Width; x++)
                {
                    var active = ActiveBlockGrid[y, x];
                    var fixedCell = Grid[y, x];
                    var color = active ? "blue" : fixedCell ? "gray" : "white";
                    <div style="width: 20px; height: 20px; border: 1px solid #ccc; background-color:@color;"></div>
                }
            }
        </div>
    </div>
    @if (!IsRunning)
    {
        <RadzenButton ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="toys_and_games" Click="@StartGame" Text="Начало игры" />
    }
    else
    {
        <RadzenToggleButton Change=TogglePause Variant="Variant.Text" Text=@($"{(IsPaused ? "Продолжить" : "Пауза")}") ButtonStyle="ButtonStyle.Base" Icon="play_pause" />
        <RadzenButton ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="stop" Click="@StopGame" Text="Завершить" />
    }

    @if (IsGameOver)
    {
        <RadzenText style="color:red; font-weight:bold;">Game Over!</RadzenText>
    }
}
@if (selectedValue == "2")
{
    <RadzenButton ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="toys_and_games" Click="@ResetGame" />
    <RadzenText Text="@statusMessage" />
    <table class="minesweeper">
        @for (int r = 0; r < Rows; r++)
        {   
            <tr>
                @for (int c = 0; c < Cols; c++)
                {
                    var rr = r;
                    var cc = c;
                    var cell = Field[rr, cc];
                    
                    <td class="@GetCellClass(cell)"
                    @onclick="() => OpenCell(rr, cc)"
                    @oncontextmenu:preventDefault="true"
                    @oncontextmenu="() => FlagCell(rr, cc)">
                    @GetCellContent(cell)
                    </td>
                }
            </tr>
        }
    </table>
}
@code {
    private string selectedValue ="0";
    private List<dynamic> items = new()
    {
      new { Text = "Тетрис", Value = "1" },
      new { Text = "Сапер", Value = "2" }
    };

    private const int Width = 10;
    private const int Height = 20;
    private bool IsPaused = false;
    private bool IsRunning = false;
    private bool[,] Grid = new bool[Height, Width];             // Фиксированные блоки
    private bool[,] ActiveBlockGrid = new bool[Height, Width];  // Падающая фигура

    private (int x, int y) BlockPosition; // Верхняя точка I-фигуры

    private System.Timers.Timer timer;
    private bool IsGameOver = false;
    private void SpawnBlock()
    {
        var rand = new Random();
        var (shape, color) = Shapes[rand.Next(Shapes.Count)];

        CurrentBlock = new Tetromino
            {
                Shape = shape,
                X = 3,
                Y = 0,
                Color = color
            };

        if (!CanPlace(CurrentBlock, 0, 0))
        {
            IsGameOver = true;
            IsRunning = false;
            timer.Stop();
        }

        DrawBlock(CurrentBlock, true);
        InvokeAsync(StateHasChanged);
    }
    private bool CanPlace(Tetromino block, int offsetX, int offsetY)
    {
        for (int y = 0; y < 4; y++)
        {
            for (int x = 0; x < 4; x++)
            {
                if (block.Shape[y, x] == 1)
                {
                    int newX = block.X + x + offsetX;
                    int newY = block.Y + y + offsetY;

                    if (newX < 0 || newX >= Width || newY >= Height)
                        return false;

                    if (newY >= 0 && Grid[newY, newX])
                        return false;
                }
            }
        }

        return true;
    }

    private void DrawBlock(Tetromino block, bool place)
    {
        for (int y = 0; y < 4; y++)
        {
            for (int x = 0; x < 4; x++)
            {
                if (block.Shape[y, x] == 1)
                {
                    int gridY = block.Y + y;
                    int gridX = block.X + x;

                    if (gridY >= 0 && gridY < Height && gridX >= 0 && gridX < Width)
                        ActiveBlockGrid[gridY, gridX] = place;
                }
            }
        }
    }

    private void ClearFullRows()
    {
        for (int y = Height - 1; y >= 0; y--)
        {
            bool fullRow = true;

            for (int x = 0; x < Width; x++)
            {
                if (!Grid[y, x])
                {
                    fullRow = false;
                    break;
                }
            }

            if (fullRow)
            {
                // Сдвигаем все строки выше вниз
                for (int row = y; row > 0; row--)
                {
                    for (int x = 0; x < Width; x++)
                    {
                        Grid[row, x] = Grid[row - 1, x];
                    }
                }

                // Очищаем верхнюю строку
                for (int x = 0; x < Width; x++)
                {
                    Grid[0, x] = false;
                }

                // Повторно проверяем ту же строку после сдвига
                y++;
            }
        }
    }

    private void StartGame()
    {
        ClearGrid();
        IsGameOver = false;
        IsPaused = false;
        IsRunning = true;
        // NextBlock = GetRandomBlock();
        SpawnBlock();
        timer.Start();
        _ = Task.Delay(100).ContinueWith(_ =>
        {
            InvokeAsync(async () => await GameAreaRef.FocusAsync());
        });
    }
    private void StopGame()
    {
        timer.Stop();
        ClearGrid();
        IsRunning = false;
        IsPaused = false;
        IsGameOver = false;
        InvokeAsync(StateHasChanged);
    }

    private void TogglePause()
    {
        if (IsPaused)
        {
            timer.Start();
            IsPaused = false;
            _ = Task.Delay(100).ContinueWith(_ =>
        {
            InvokeAsync(async () => await GameAreaRef.FocusAsync());
        });
        }
        else
        {
            timer.Stop();
            IsPaused = true;
        }
    }
    private int[,] RotateClockwise(int[,] shape)
    {
        int[,] result = new int[4, 4];

        for (int y = 0; y < 4; y++)
            for (int x = 0; x < 4; x++)
                result[x, 3 - y] = shape[y, x]; // поворот 90° по часовой стрелке

        return result;
    }

    private bool CanPlaceShape(int[,] shape, int xPos, int yPos)
    {
        for (int y = 0; y < 4; y++)
        {
            for (int x = 0; x < 4; x++)
            {
                if (shape[y, x] == 1)
                {
                    int newX = xPos + x;
                    int newY = yPos + y;

                    if (newX < 0 || newX >= Width || newY >= Height)
                        return false;

                    if (newY >= 0 && Grid[newY, newX])
                        return false;
                }
            }
        }
        return true;
    }

    private void HandleKeyPress(KeyboardEventArgs e)
    {
        if (IsGameOver || !IsRunning || IsPaused) return;

        DrawBlock(CurrentBlock, false);

        switch (e.Key)
        {
            case "ArrowLeft":
                if (CanPlace(CurrentBlock, -1, 0)) CurrentBlock.X--;
                break;
            case "ArrowRight":
                if (CanPlace(CurrentBlock, 1, 0)) CurrentBlock.X++;
                break;
            case "ArrowDown":
                MoveBlockDown();
                return;
            case "ArrowUp":
                var rotated = RotateClockwise(CurrentBlock.Shape);
                if (CanPlaceShape(rotated, CurrentBlock.X, CurrentBlock.Y))
                    CurrentBlock.Shape = rotated;
                break;
        }

        DrawBlock(CurrentBlock, true);
        StateHasChanged();
    }



    private void ClearGrid()
    {
        Grid = new bool[Height, Width];
        ActiveBlockGrid = new bool[Height, Width];
    }

    private void MoveBlockDown()
    {
        if (!IsRunning || IsPaused || IsGameOver) return;

        InvokeAsync(() =>
        {
            DrawBlock(CurrentBlock, false);

            if (CanPlace(CurrentBlock, 0, 1))
            {
                CurrentBlock.Y += 1;
                DrawBlock(CurrentBlock, true);
            }
            else
            {
                // Зафиксировать в основной сетке
                for (int y = 0; y < 4; y++)
                {
                    for (int x = 0; x < 4; x++)
                    {
                        if (CurrentBlock.Shape[y, x] == 1)
                        {
                            int gx = CurrentBlock.X + x;
                            int gy = CurrentBlock.Y + y;

                            if (gy >= 0 && gy < Height && gx >= 0 && gx < Width)
                                Grid[gy, gx] = true;
                        }
                    }
                }

                ClearFullRows();
                SpawnBlock();
            }

            StateHasChanged();
        });
    }

    private void DrawActiveBlock()
    {
        for (int i = 0; i < 4; i++)
        {
            int y = BlockPosition.y + i;
            if (y < Height)
                ActiveBlockGrid[y, BlockPosition.x] = true;
        }
    }

    private void EraseActiveBlock()
    {
        for (int y = 0; y < Height; y++)
            for (int x = 0; x < Width; x++)
                ActiveBlockGrid[y, x] = false;
    }

    private bool CanPlaceBlock()
    {
        for (int i = 0; i < 4; i++)
        {
            int y = BlockPosition.y + i;
            if (y >= Height || Grid[y, BlockPosition.x])
                return false;
        }
        return true;
    }

    private bool CanMoveDown()
    {
        for (int i = 0; i < 4; i++)
        {
            int newY = BlockPosition.y + i + 1;
            if (newY >= Height) return false;
            if (Grid[newY, BlockPosition.x]) return false;
        }
        return true;
    }


    private void MoveBlockLeft()
    {
        if (BlockPosition.x <= 0) return;

        for (int i = 0; i < 4; i++)
        {
            int y = BlockPosition.y + i;
            if (y < Height && Grid[y, BlockPosition.x - 1])
                return;
        }

        EraseActiveBlock();
        BlockPosition.x -= 1;
        DrawActiveBlock();
        StateHasChanged();
    }

    private void MoveBlockRight()
    {
        if (BlockPosition.x >= Width - 1) return;

        for (int i = 0; i < 4; i++)
        {
            int y = BlockPosition.y + i;
            if (y < Height && Grid[y, BlockPosition.x + 1])
                return;
        }

        EraseActiveBlock();
        BlockPosition.x += 1;
        DrawActiveBlock();
        StateHasChanged();
    }

    public void Dispose()
    {
        timer?.Dispose();
    }

    public class Tetromino
    {
        public int[,] Shape { get; set; } // Матрица 4x4
        public int X { get; set; } // Левый верх
        public int Y { get; set; }

        public string Color { get; set; }
    }
    private List<(int[,], string)> Shapes = new()
{
    // I
    (new int[4, 4]
    {
        {0,0,0,0},
        {1,1,1,1},
        {0,0,0,0},
        {0,0,0,0}
    }, "cyan"),

    // O
    (new int[4, 4]
    {
        {0,1,1,0},
        {0,1,1,0},
        {0,0,0,0},
        {0,0,0,0}
    }, "yellow"),

    // T
    (new int[4, 4]
    {
        {0,1,0,0},
        {1,1,1,0},
        {0,0,0,0},
        {0,0,0,0}
    }, "purple"),

    // S
    (new int[4, 4]
    {
        {0,1,1,0},
        {1,1,0,0},
        {0,0,0,0},
        {0,0,0,0}
    }, "green"),

    // Z
    (new int[4, 4]
    {
        {1,1,0,0},
        {0,1,1,0},
        {0,0,0,0},
        {0,0,0,0}
    }, "red"),

    // J
    (new int[4, 4]
    {
        {1,0,0,0},
        {1,1,1,0},
        {0,0,0,0},
        {0,0,0,0}
    }, "blue"),

    // L
    (new int[4, 4]
    {
        {0,0,1,0},
        {1,1,1,0},
        {0,0,0,0},
        {0,0,0,0}
    }, "orange")
};
    private Tetromino CurrentBlock;
    private ElementReference GameAreaRef;


    const int Rows = 9;
    const int Cols = 9;
    const int MinesCount = 10;

    Cell[,] Field = new Cell[Rows, Cols];
    bool gameOver = false;
    string statusMessage = "Играйте!";

    class Cell
    {
        public bool IsMine { get; set; }
        public bool IsOpen { get; set; }
        public bool IsFlagged { get; set; }
        public int NeighborMines { get; set; }
    }

    protected override void OnInitialized()
    {
        ResetGame();
        timer = new System.Timers.Timer(500);
        timer.Elapsed += (_, __) => MoveBlockDown();
    }

    void ResetGame()
    {
        gameOver = false;
        statusMessage = "Играйте!";
        Field = new Cell[Rows, Cols];

        for (int r = 0; r < Rows; r++)
            for (int c = 0; c < Cols; c++)
                Field[r, c] = new Cell();

        PlaceMines();
        CalculateNeighborMines();
    }

    void PlaceMines()
    {
        var rnd = new Random();
        int minesPlaced = 0;
        while (minesPlaced < MinesCount)
        {
            int r = rnd.Next(Rows);
            int c = rnd.Next(Cols);
            if (!Field[r, c].IsMine)
            {
                Field[r, c].IsMine = true;
                minesPlaced++;
            }
        }
    }

    void CalculateNeighborMines()
    {
        for (int r = 0; r < Rows; r++)
        {
            for (int c = 0; c < Cols; c++)
            {
                if (Field[r, c].IsMine)
                    continue;

                int count = 0;
                for (int nr = r - 1; nr <= r + 1; nr++)
                {
                    for (int nc = c - 1; nc <= c + 1; nc++)
                    {
                        if (nr >= 0 && nr < Rows && nc >= 0 && nc < Cols)
                        {
                            if (Field[nr, nc].IsMine)
                                count++;
                        }
                    }
                }
                Field[r, c].NeighborMines = count;
            }
        }
    }

    void OpenCell(int r, int c)
    {
        // границы
        if (r < 0 || r >= Rows || c < 0 || c >= Cols)
            return;

        if (gameOver)
            return;

        var cell = Field[r, c];

        if (cell.IsFlagged || cell.IsOpen)
            return;

        cell.IsOpen = true;

        if (cell.IsMine)
        {
            gameOver = true;
            statusMessage = "Вы проиграли! Мина сработала.";
            RevealAllMines();
            StateHasChanged();
            return;
        }

        if (cell.NeighborMines == 0)
        {
            // flood fill
            for (int nr = r - 1; nr <= r + 1; nr++)
            {
                for (int nc = c - 1; nc <= c + 1; nc++)
                {
                    if (nr == r && nc == c)
                        continue;
                    OpenCell(nr, nc);
                }
            }
        }

        if (CheckWin())
        {
            gameOver = true;
            statusMessage = "Поздравляем! Вы выиграли!";
        }
    }

    void FlagCell(int r, int c)
    {
        // Проверка на выход за границы массива
        if (r < 0 || r >= Rows || c < 0 || c >= Cols)
            return;

        if (gameOver || Field[r, c].IsOpen)
            return;

        Field[r, c].IsFlagged = !Field[r, c].IsFlagged;
    }


    bool CheckWin()
    {
        for (int r = 0; r < Rows; r++)
            for (int c = 0; c < Cols; c++)
                if (!Field[r, c].IsMine && !Field[r, c].IsOpen)
                    return false;
        return true;
    }

    void RevealAllMines()
    {
        for (int r = 0; r < Rows; r++)
            for (int c = 0; c < Cols; c++)
                if (Field[r, c].IsMine)
                    Field[r, c].IsOpen = true;
    }

    string GetCellClass(Cell cell)
    {
        if (!cell.IsOpen)
            return cell.IsFlagged ? "flagged" : "closed";

        return cell.IsMine ? "mine" : "open";
    }

    string GetCellContent(Cell cell)
    {
        if (!cell.IsOpen)
            return cell.IsFlagged ? "🚩" : "";

        if (cell.IsMine)
            return "💣";

        return cell.NeighborMines > 0 ? cell.NeighborMines.ToString() : "";
    }
}


<style>
    .minesweeper {
        border-collapse: collapse;
        user-select: none;
    }

        .minesweeper td {
            width: 30px;
            height: 30px;
            text-align: center;
            vertical-align: middle;
            border: 1px solid #666;
            font-weight: bold;
            font-size: 18px;
            cursor: pointer;
        }

    .closed {
        background-color: #ccc;
    }

    .open {
        background-color: #eee;
        cursor: default;
    }

    .mine {
        background-color: red;
        color: black;
    }

    .flagged {
        background-color: #ccc;
        color: red;
    }
</style>