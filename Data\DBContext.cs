﻿using Microsoft.EntityFrameworkCore;
using System.Reflection.Emit;
using EWA.Models;
using static EWA.Models.REP_Models;

namespace EWA.Data
{
    public partial class DBContext : DbContext

        {
            public DBContext(DbContextOptions<DBContext> options) : base(options)
            {

            }
           
            //Репозиторий
            public DbSet<REP_Models.REP_OBJECT_TYPE> REP_OBJECT_TYPE { get; set; }
            public DbSet<REP_Models.REP_OBJECT> REP_OBJECT { get; set; }
            public DbSet<REP_Models.REP_TABLES> REP_TABLES { get; set; }
            public DbSet<REP_Models.REP_OBJECT_REL> REP_OBJECT_REL { get; set; }
            public DbSet<REP_DATA_SRC> REP_DATA_SRC { get; set; }
            
            //настройки
            public DbSet<REP_Models.GLB_CONFIG> GLB_CONFIG { get; set; }

            //Админка
            public DbSet<SIB_Models.SIB_USERS> SIB_USERS { get; set; }

            public DbSet<SIB_Models.SIB_ROLES> SIB_ROLES { get; set; }
            public DbSet<SIB_Models.SIB_MODULES> SIB_MODULES { get; set; }

        public DbSet<SIB_Models.SIB_USERROLES_LNK> SIB_USERROLES_LNK { get; set; }
            public DbSet<SIB_Models.SIB_USERMODULES_LNK> SIB_USERMODULES_LNK { get; set; }
            partial void OnModelBuilding(ModelBuilder builder);

            protected override void OnModelCreating(ModelBuilder builder)
            {
                base.OnModelCreating(builder);

                //Репозиторий
                builder.Entity<REP_Models.REP_OBJECT>()
                   .HasOne(o => o.RepObjectType)
                   .WithMany(t => t.RepObjects)
                   .HasForeignKey(o => o.TypeObject);

                builder.Entity<REP_TABLES>()
                    .HasOne(t => t.RepObject)
                    .WithOne(o => o.RepTables)
                    .HasForeignKey<REP_TABLES>(t => t.IdRepTables);

                builder.Entity<REP_OBJECT_REL>()
                    .HasKey(r => r.IdRel);

                builder.Entity<GLB_CONFIG>()
                    .HasKey(c => new { c.CFG_GROUP, c.CFG_CODE, c.CFG_PARAM });


                //Админка
                builder.Entity<SIB_Models.SIB_USERROLES_LNK>()
                    .HasKey(ur => new { ur.ID_USER, ur.ID_ROLE });
                builder.Entity<SIB_Models.SIB_USERROLES_LNK>()
                    .HasOne(ur => ur.Users)
                    .WithMany(u => u.UserRoles)
                    .HasForeignKey(ur => ur.ID_USER);

                builder.Entity<SIB_Models.SIB_USERROLES_LNK>()
                    .HasOne(ur => ur.Roles)
                    .WithMany(r => r.UserRoles)
                    .HasForeignKey(ur => ur.ID_ROLE);
                builder.Entity<SIB_Models.SIB_USERMODULES_LNK>()
                    .HasKey(ur => new { ur.ID_USER, ur.ID_MODULE });
                builder.Entity<SIB_Models.SIB_USERMODULES_LNK>()
                    .HasOne(ur => ur.Users)
                    .WithMany(u => u.UserModules)
                    .HasForeignKey(ur => ur.ID_USER);
                builder.Entity<SIB_Models.SIB_USERMODULES_LNK>()
                    .HasOne(ur => ur.Modules)
                    .WithMany(r => r.UserModules)
                    .HasForeignKey(ur => ur.ID_MODULE);
                builder.Entity<SIB_Models.SIB_USERS>()
                       .HasKey(u => u.ID);
                builder.Entity<SIB_Models.SIB_ROLES>()
                       .HasKey(r => r.ID);
                builder.Entity<SIB_Models.SIB_MODULES>()
                       .HasKey(r => r.ID);
                
                this.OnModelBuilding(builder);
            }
    }
}
