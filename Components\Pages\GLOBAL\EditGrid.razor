@rendermode InteractiveServer
@inject EWA.Services.RepService.ClipboardService ClipboardService

@inject NavigationManager Navigation
@inject RepService _repService
@inject SIBModule SModule
@inject WFLModule WModule
@inject EWA.Services.RepService.GET_SPR _getspr
@inject EWA.Services.RepService.MenuGrid MenuGrid
@inject EWA.Services.RepService.LogService _logService
@inject UIService.TabService TabService
@inject DialogService DialogService
@inject ContextMenuService ContextMenuService
@inject IJSRuntime JSRuntime
@inject GridServices GridService
@implements IDisposable
@inject IExcelService _excelService
@inject EWA.Services.SIBService.RulesService RServ
@inject TooltipService tooltipService
@inject NotificationService NotificationService
@inject SIBService.UserService UServ


@using System.Collections.Generic
@using System.Data
@using EWA.Components.Pages.SIB
@using EWA.Components.Pages.WRFL
@using EWA.Enums
@using Microsoft.Extensions.Configuration
@using System.Text.Json
@using Radzen.Blazor
@using Oracle.ManagedDataAccess.Client
@using Radzen
@using EWA.Services
@using EWA.Controllers
@using EWA.Models
@using EWA.Components.Pages
@using System.Net.Http
@using System.Reflection
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Mvc
@using System.Globalization
@using static EWA.Models.REP_Models
<style>
    .rz-treenode-content .rz-treenode-label {
        flex: 1;
    }
</style>

<RadzenTabs/>
<RadzenStack Orientation="Orientation.Vertical" Style="display: flex; flex-direction: column; height: 100%;" Gap="0"
              >
    <RadzenStack Orientation="Orientation.Vertical" Style="background-color: var(--rz-grid-header-background-color); height: auto; width:100%;" Gap="0"  >
        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween" AlignItems="AlignItems.Center" Gap="0.2" Style="margin: 4px 16px 0 8px;"
                     >
            <RadzenText > <strong> @Tab.NameObj</strong> </RadzenText>
            @if (Tab.load_state!=0)
            {
                <RadzenText TextAlign="TextAlign.Center" >Подождите, идет отбор данных...</RadzenText>
            }
            else if (isLoading == true)
            {
                <RadzenText TextAlign="TextAlign.Center" >Идет выполнение запроса. Подождите, пожалуйста.</RadzenText>
            }
            <RadzenSlider TValue="int" Value="gridHeight" Min="10" Max="100" Step="10" ShowValue="true" 
                          Change="@(args => {gridHeight = args;  userChngGridHeight = true;})" />
        </RadzenStack>
        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween" Wrap="FlexWrap.Wrap">
            <RadzenStack Orientation="Orientation.Horizontal" Gap="1.5rem">
                <RadzenStack Orientation="Orientation.Horizontal" Gap="0rem">
                    <RadzenButton EWAType = "Button" EWAID ="GLB1" ButtonStyle="ButtonStyle.Base" Icon="refresh" Variant="Variant.Text" Click="@LoadDataBegin"
                                  MouseEnter="@(args => ShowTooltip(args, "Глобальный фильтр"))" 
                                  Disabled="@isLoading" />
                    @if (is_load)
                    {
                        <RadzenButton EWAType = "Button" EWAID ="GLB2" ButtonStyle="ButtonStyle.Base" Icon="autorenew" Variant="Variant.Text" Click="@ReloadAsync" MouseEnter="@(args => ShowTooltip(args, "Обновить"))"
                                      Disabled="@isLoading" />
                    }
                </RadzenStack>
                @if (is_load)
                {
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="0rem">
                        @if (repositoryInfo.ColAdd == true)
                        {   
                            <RadzenButton EWAType = "Button" EWAID ="GLB3" ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="add" Click="@AddRow"
                                          MouseEnter="@(args => ShowTooltip(args, "Добавить запись"))" />
                        }
                        @if (repositoryInfo.ColUpd == true)
                        {
                            <RadzenButton EWAType = "Button" EWAID ="GLB4" ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="edit" Click="@EditRow" 
                                     Disabled="@(selectedItems.Count !=1)" MouseEnter="@(args => ShowTooltip(args, "Редактировать запись"))" />
                        }   
                        @if (repositoryInfo.ColDel == true)
                        {
                            <RadzenButton EWAType = "Button" EWAID ="GLB5" ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="delete" Click="@DelRow"
                                        Disabled="@(selectedItems.Count !=1)" MouseEnter="@(args => ShowTooltip(args, "Удалить запись"))" />
                        }
                        <RadzenButton EWAType = "Button" EWAID ="GLB6" ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="table_rows" Click="@ViewRow" Disabled="@(selectedItems.Count !=1)" MouseEnter="@(args => ShowTooltip(args, "Просмотр"))" />
                    </RadzenStack>
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="0rem">
                        <WRFLButtons selectedRowData="selectedRowData"
                                     selectedItems="selectedItems"
                                     TabRef="Tab"
                                     GridRef="grid"
                                     ReloadDef="ReloadDef"
                                     queryResults="queryResults"
                                     InParams="_PublicParams"
                                     Count="count"
                                     OnChanged="StateHasChanged"
                                     QueryResultsChanged="@OnQueryResultsChanged" />
                        <SIBButtons selectedRowData="selectedRowData"
                                    selectedItems="selectedItems"
                                    TabRef="Tab"
                                    GridRef="grid"
                                    ReloadDef="ReloadDef"
                                    queryResults="queryResults"
                                    InParams="_PublicParams"
                                    Count="count"
                                    OnChanged="StateHasChanged"
                                    QueryResultsChanged="@OnQueryResultsChanged" />

                    </RadzenStack>
                }
            </RadzenStack>
            @if(is_load)
            {
                <RadzenStack Orientation="Orientation.Horizontal" Gap="1.5rem">
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="0rem">
                        @if (Tab.load_state != 0)
                        {
                            <RadzenButton ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="alarm_add"
                                          Click="@(args =>FilterAplyAsyncTmp(CancellationTokenSource.CreateLinkedTokenSource(_mainCts.Token).Token))"
                            Disabled="@isLoading"
                                      MouseEnter="@(args => ShowTooltip(args, "Перечитать"))" />
                       }
                        <RadzenButton ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="filter_arrow_right"
                                  Disabled="@isLoading"
                                  Click="@FilterAplyAsync"
                                  MouseEnter="@(args => ShowTooltip(args, "Применить фильтр"))" />
                        <RadzenToggleButton @ref=@RadzenToggleButtonList["filter_alt"]
                                            Disabled="@isLoading"
                                        ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="filter_alt" 
                                        Click="@(() => FilterReloadDataAsync(true, "filter_alt", isFiltering switch
                                                                                     {
                                                                                      true => "Включить автофильтр",
                                                                                      false => "Отключить автофильтр"
                                                                                    }))" 
                                        MouseEnter="@(args => ShowTooltip("filter_alt", isFiltering switch
                                                                                     {
                                                                                      false => "Включить автофильтр",
                                                                                      true => "Отключить автофильтр"
                                                                                    }) )" />
                        <RadzenButton ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="filter_alt_off"
                                      Disabled="@isLoading"
                                  Click="@FilterClearDataAsync"
                                  MouseEnter="@(args => ShowTooltip(args, "Очистить фильтр"))" />
                        <RadzenToggleButton @ref=@RadzenToggleButtonList["filter_list"]
                                        ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="@icon" 
                                            Disabled="@isLoading"
                                        Click="@(args => ToggleFiltering("filter_list", allowFiltering switch
                                                                                     {
                                                                                      true => "Показать фильтр",
                                                                                      false => "Скрыть фильтр"
                                                                                    }) )"
                                        MouseEnter="@(args => ShowTooltip("filter_list", allowFiltering switch
                                                                                     {
                                                                                      false => "Показать фильтр",
                                                                                      true => "Скрыть фильтр"
                                                                                    }) )" />
                    </RadzenStack>
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="0rem">
                        <RadzenProfileMenu  MouseEnter="@(args => ShowTooltip(args, "Переходы"))" Disabled="@check_linked_services()" ShowIcon="true" Style="background-color:var(--rz-grid-header-background-color);">
                            <ChildContent>
                                    <RadzenTree 
                                            @ref="tree"
                                            Expand="@OnExpandM"
                                            Collapse="@OnCollapseM" Change="@OnChangeM"
                                            
                                            Style="@($" max-height: {gridHeight}vh; width:250px;")"
                                            Class="rz-display-block">
                                    @foreach (var root in repositoryInfo.MenuGrid.Where(m => m.CODE_PARENT == "MAIN" && m.AUTO_MODE != 1))
                                    {
                                        @BuildTreeItem(root)
                                    }
                                </RadzenTree>
                            </ChildContent>
                            <Template>
                                <RadzenIcon Icon="linked_services" />
                            </Template>
                        </RadzenProfileMenu>
                        <RadzenProfileMenu MouseEnter="@(args => ShowTooltip(args, "Экспорт"))" ShowIcon="true" Click="@ExportData" 
                            Style="@($"max-height: {gridHeight}vh; background-color:var(--rz-grid-header-background-color);")">
                            <ChildContent>
                                <RadzenProfileMenuItem Text="XLSX (все)" Value="XLS_All" Icon="backup_table" />
                                <RadzenProfileMenuItem Text="CSV (все)" Value="CSV_All" Icon="csv" />
                                <RadzenProfileMenuItem Text="XLSX (страница)" Value="XLS_Cur" Icon="backup_table" />
                                <RadzenProfileMenuItem Text="CSV (страница)" Value="CSV_Cur" Icon="csv" />
                            </ChildContent>
                            <Template>
                                <RadzenIcon Icon="export_notes" />
                            </Template>
                        </RadzenProfileMenu>
                    </RadzenStack>
                </RadzenStack>
            }
        </RadzenStack>
   </RadzenStack>

    <RadzenDataGrid Data="@queryResults" LoadData="@LoadDataAsync"
                 SelectionMode="DataGridSelectionMode.Single"
                 AllowMultiColumnSorting="true" ShowMultiColumnSortingIndex="true"
                 EqualsText="Равно"
                 NotEqualsText="Не равно"
                 LessThanText="Меньше чем"
                 LessThanOrEqualsText="Меньше или равно"
                 GreaterThanText="Больше чем"
                 GreaterThanOrEqualsText="Больше или равно"
                 IsNullText="Значение отсутствует"
                 IsNotNullText="Значение заполнено"
                 ContainsText="Содержит"
                 DoesNotContainText="Не содержит"
                 StartsWithText="Начинается с.."
                 EndsWithText="Оканчивается на.."
                 IsEmptyText="Пусто"
                 IsNotEmptyText="Не пусто"
                 ClearFilterText="Очистить"
                 ApplyFilterText="Применить"
                 AllowSorting="true"
                 AllowFiltering="@allowFiltering"
                 FilterMode="FilterMode.SimpleWithMenu"
                 FilterCleared="@OnFilterCleared"
                 AllowPaging="true"
                 ShowPagingSummary="true"
                 PageSize="@pageDefault"
                 PageSizeOptions="@pageSizeOptions"
                 PagingSummaryTemplate="@GetPagingSummary"
                 AllowColumnResize="true"
                 Density="Density.Compact"
                 PageSizeText="записей на странице"
                 EmptyText=@EmptyText
                 PagerHorizontalAlign="HorizontalAlign.Center"
                 TItem="IDictionary<string, object>"
                 Count="@count"
                 class="rz-datagrid-lg"
                 CellContextMenu="@OnCellContextMenu"
                 FilterCaseSensitivity="@filterCaseSensitivity" LogicalFilterOperator="@logicalFilterOperator"
                 CellRender=@CellRender
                 RowClick="@((args) => OnRowClick(args))"
                 @ref="grid"                        
                 IsLoading=@isLoading
                 Style="@($"height: {gridHeight}vh; max-height:80vh; min-height:10vh;")"
                   >
   
   <Columns>
        @foreach (var column in repositoryInfo.Column.Where(x => x.VIEWVISIBLE == 1))
        {
            @if (SprSelShotDict.ContainsKey(column.CODE))
            {
                <RadzenDataGridColumn @key="@column.CODE"
                                          Title="@column.NAME"
                                          Type="@GridService.GetColumnType(column.DATATYPE)"
                                          Filterable="true"
                                          FilterValue="@SprSelShotDict[@column.CODE].ValPrm"
                                          Sortable="@GridService.GetColumnSort(column.DATATYPE, is_load)"
                                          Property="@GridService.GetPropertyName(column.CODE)"
                                          Width="150px"
                                          TItem="IDictionary<string, object>">
                        <Template>
                            @context[@column.CODE + "#N"]
                           
                        </Template>
                        <FilterTemplate>
                            <RadzenDropDown @bind-Value="@SprSelShotDict[@column.CODE].ValPrm" TextProperty="Name" ValueProperty="@(column.DOMAINCODE?.ToUpper() == "CODE" ? "Code" : "ID")" Style="width:100%;"
                                            Data="@SprShortDict[@SprSelShotDict[@column.CODE].KeySprShortDict]"
                                        AllowFiltering="true"
                                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                                            AllowClear="true"/>
                            </FilterTemplate>


                </RadzenDataGridColumn>
            }
            else if (SprSelLongDict.ContainsKey(column.CODE))
            {
                <RadzenDataGridColumn @key="@column.CODE"
                                          Title="@column.NAME"
                                          Type="@GridService.GetColumnType(column.DATATYPE)"
                                          Filterable="true"
                                          FilterValue="@(SprSelLongDict[@column.CODE].ValPrm!=null ? SprSelLongDict[@column.CODE].ValPrm.First().Value :
                                                       null)"
                                      Sortable="@GridService.GetColumnSort(column.DATATYPE, is_load)"
                                      Property="@GridService.GetPropertyName(column.CODE)"
                                          Width="150px"
                                          TItem="IDictionary<string, object>">
                        <Template>
                            @context[@column.CODE + "#N"]
                        </Template>

                        <FilterTemplate>
                            <RadzenDropDownDataGrid @ref=gridDict[column.CODE] Data="@dataDict[column.CODE]" ColumnWidth="200px"
                                                    LoadData="@(arg => LoadData(arg, column.CODE, ""))"
                                                    Count=@SprLongCountDict[column.CODE]
                                                    TValue="IDictionary<string, object>"
                                                    OpenOnFocus=true
                                                    Change="@(arg => OnChange(arg, column.CODE))"                                                   
                                                    PageSize="10"
                                                    ShowPagingSummary="true"
                                                    AllowSorting="true" Value="@SprSelLongDict[@column.CODE].ValPrm"
                                                    TextProperty="@(GridService.GetColumnPropertyExpression("LastName", typeof(string)))"
                                                    AllowClear="true"
                                                    Context="context1">
                                <HeaderTemplate>
                                    <RadzenButton Icon="filter_list" ButtonStyle="ButtonStyle.Light" Size="ButtonSize.Small" @onclick:stopPropagation Click="@(args => OpenFilterDialog(column.CODE))"></RadzenButton>
                                        <RadzenTextBox @bind-Value="@SprLongFiltrDict[column.CODE]" />
                                        <RadzenButton Icon="find_in_page" ButtonStyle="ButtonStyle.Light" Size="ButtonSize.Small" @onclick:stopPropagation Click="@(args => Find(column.CODE))"></RadzenButton>
                                </HeaderTemplate>
                                <ValueTemplate >
                                    @if (context1 != null)
                                        @if (context1.Count>1)
                                        {
                                            {
                                                @string.Join(", ", SprLongDict[SprSelLongDict[column.CODE].KeySprLongDict].Column.Where(c => c.DATATYPE == "VARCHAR2").Take(gridDict[column.CODE].MaxSelectedLabels).Select(c => context1[c.CODE]))
                                            }
                                        }
                                </ValueTemplate>
                                <Columns>
                                    @foreach (var g_column in SprLongDict[SprSelLongDict[column.CODE].KeySprLongDict].Column)
                                    {
                                        <RadzenDropDownDataGridColumn @key=@g_column.CODE Title="@g_column.NAME" Type="@GridService.GetColumnType(g_column.DATATYPE)"
                                                                      Filterable="true"
                                                                      Property="@GridService.GetColumnPropertyExpression(g_column.CODE, GridService.GetColumnType(g_column.DATATYPE))">
                                                    <Template Context="context2">
                                                @context2[@g_column.CODE]
                                            </Template>

                                        </RadzenDropDownDataGridColumn>
                                    }
                                </Columns>
                            </RadzenDropDownDataGrid>
                                </FilterTemplate>


                </RadzenDataGridColumn>
            }
            else if (column.DOMAINCODE == "Bool")
            {
                <RadzenDataGridColumn @key="@column.CODE" 
                                      Title="@column.NAME"
                                      Type="typeof(bool?)"
                                      Filterable="true"
                                      Sortable="@GridService.GetColumnSort(column.DATATYPE, is_load)"
                                      Property="@GridService.GetPropertyName(column.CODE)"
                                      Width="150px"
                                      TItem="IDictionary<string, object>">
                    <Template>
                        @{
                            var value = context[@column.CODE];
                            bool? boolValue = null;

                            if (value != null && value != DBNull.Value)
                            {
                                // Если значение не null, проверяем строку
                                boolValue = value.ToString() == "1";
                            }
                            <RadzenCheckBox TriState="true" TValue="bool?" Value="@boolValue" />
                         }

                    </Template>
                    
                </RadzenDataGridColumn>
            } 
            else if (column.DATATYPE == "DATE")
            {
                    <RadzenDataGridColumn @key="@column.CODE"
                                          Title="@column.NAME"
                                          Type="@GridService.GetColumnType(column.DATATYPE)"                                        
                                          Sortable="@GridService.GetColumnSort(column.DATATYPE, is_load)"
                                          Property="@GridService.GetPropertyName(column.CODE)"
                                          Width="150px"
                                          TItem="IDictionary<string, object>"
                                          FormatString="{0:d}"
                                          >
                        <Template>
                        @{
                            @if (column.DOMAINCODE == "ExactDate")
                            {
                                @context[@column.CODE]
                            }
                            else
                            {
                             var value = context[@column.CODE];                            
                             var dateValue = value as DateTime?;
                             @(dateValue.HasValue ? dateValue.Value.ToString("dd.MM.yyyy") : "")
                            }
                        }
                        </Template>
                    </RadzenDataGridColumn>
            }
            else
            {
                    <RadzenDataGridColumn @key="@column.CODE"
                                          Title="@column.NAME"
                                          Type="@GridService.GetColumnType(column.DATATYPE)"
                                        
                                          Sortable="@GridService.GetColumnSort(column.DATATYPE, is_load)"
                                          Property="@GridService.GetPropertyName(column.CODE)"
                                          Width="150px"
                                          TItem="IDictionary<string, object>"
                                          >
                        <Template>
                        @{
                           if (context.ContainsKey(@column.CODE))
                           {
                            var value = context[@column.CODE];

                            if (column.DATATYPE == "NUMBER" && SprSelLongDict.ContainsKey(column.CODE)==false)
                            {
                                var numValue = GridService.GetValueNum(value);  
                                @(numValue.HasValue ? numValue.Value.ToString(column.DATAPRECISION > 0 ? $"F{column.DATAPRECISION}" : "") : "")


                            }
                            else if (column.DATATYPE == "DATE")
                            {
                                        var dateValue = value as DateTime?;
                                        @(dateValue.HasValue ? dateValue.Value.ToString("dd.MM.yyyy") : "")
                            }
                            else if (column.DATATYPE == "BOOL")
                            {
                                        @((value as int?) == 1 ? true : false)
                            }
                            else
                            {
                                        @value?.ToString()
                            }
                           }
                            
                        }
                        
                        </Template>
                       
                    </RadzenDataGridColumn>
             }
        }
   </Columns>
</RadzenDataGrid>
</RadzenStack>



@code 
{

    [Inject] protected TooltipService TooltipService { get; set; }
    [Inject] public EWA.Services.RepService.ISequenceService Seq { get; set; }
    [Inject] protected EWA.Services.SIBService.SecurityService Security { get; set; }

    [Parameter] public TabData Tab { get; set; }

    private RadzenDataGrid<IDictionary<string, object>> grid;

    private DBService dbService;


    private IEnumerable<IDictionary<string, object>> queryResults;
    private Dictionary<string, RadzenDropDownDataGrid<IDictionary<string, object>>> gridDict = new();
    private Dictionary<string, IEnumerable<IDictionary<string, object>>> dataDict = new();
    private Dictionary<string, int> SprLongCountDict = new();
    private Dictionary<string, string> SprLongFiltrDict = new();
    private Dictionary<string, object> param = new Dictionary<string, object>();
    private Dictionary<string, RadzenToggleButton> RadzenToggleButtonList = new();
    private Dictionary<string, bool?> FilterCheckBox = new Dictionary<string, bool?>();
    Dictionary<string, object> chng_param;
    private Dictionary<string, Rep_Param> globalFilter = new(StringComparer.InvariantCultureIgnoreCase);
    private Dictionary<string, object> OldParams = new Dictionary<string, object>();//Предыдущие параметры передынные из MainTabs
    private Dictionary<string, object> selectedRowData = new Dictionary<string, object>();
    private Dictionary<string, bool> expandedState = new Dictionary<string, bool>();
    private Dictionary<string, IEnumerable<REP_Models.SPRMetadata>> _referenceData = new();

    private TooltipOptions toolTipOpt = new TooltipOptions() { Delay = 100, Duration = 1000 };
    private RepositoryInfo repositoryInfo=new RepositoryInfo();
    private FormParams _PublicParams = new FormParams();

    private bool is_new = true;
    bool isLoading = false;
    private decimal nextValue = 0;
    private DateTime timequery;
    private bool is_request = false;
    string chng_query = string.Empty;
    string name_tmptab = string.Empty;
    string tmp_query = string.Empty;
    private string hint { get; set; }
    string totalTime = string.Empty;
    string EmptyText = "Нет записей для отображения";
    string trans_params = string.Empty;
    int count = 0;
    int total_count = 0;
    private bool isInit = true;
    private int count_lvl = 1;
    private int gridHeight = 64;
    private bool userChngGridHeight = false;
    private bool isFiltering = false;
    private bool allowFiltering = false;
    private string icon = "filter_list_off";
    private bool flag = false;
    private int oper = 0;

    private string panelMenuStyle = string.Empty;

    IEnumerable<int> pageSizeOptions = new int[] { 10, 25, 50, 100 };
    LogicalFilterOperator logicalFilterOperator = LogicalFilterOperator.And;
    FilterCaseSensitivity filterCaseSensitivity = FilterCaseSensitivity.CaseInsensitive;

    private IList<IDictionary<string, object>> selectedItems = new List<IDictionary<string, object>>();//!!!
                                                                                                       // private List<REP_Models.ParamMetadata> transition_params;

    private RadzenButton ref_SaveAsync;
    protected async Task SearchInputKeyPressed(KeyboardEventArgs args)
    {
        //F2 - включить автофильтрацию
        //  F4 - показать / скрыть фильтр
        // F8 - применить фильтр
        //  F9 - очистить фильтр
        if (args.Key == "F2")
        {
            await RadzenToggleButtonList["filter_alt"].Element.FocusAsync();

            await FilterReloadDataAsync(true, "filter_alt", isFiltering switch
            {
                true => "Включить автофильтр",
                false => "Отключить автофильтр"
            });

        }
                
    }
    protected async Task SearchInputKeyPressed1(KeyboardEventArgs args)
    {
        //F2 - включить автофильтрацию
        //  F4 - показать / скрыть фильтр
        // F8 - применить фильтр
        //  F9 - очистить фильтр
        if (args.Key == "F2")
        {
            await RadzenToggleButtonList["filter_alt"].Element.FocusAsync();

            await FilterReloadDataAsync(true, "filter_alt", isFiltering switch
            {
                true => "Включить автофильтр",
                false => "Отключить автофильтр"
            });

        }
                
    }
    private void ShowTooltip(ElementReference elementReference, string  mes)
    {
        tooltipService.Open(elementReference, mes);
    }

     private void ShowTooltip(string code, string  mes)
    {
        tooltipService.Open(RadzenToggleButtonList[code].Element, mes, toolTipOpt);
    }
    private async Task OnChange1(object _)
    {
        await grid.Reload();
    }
    
    void CellRender(DataGridCellRenderEventArgs<IDictionary<String, object>> dd)
    {
        bool color = false;

        if (dd.Data.Keys.Contains("ERRORINFO"))
        {
            if (selectedItems.Count > 0 && GridService.AreRowEqual(selectedItems[0], dd.Data, repositoryInfo.Column) &&
                dd.Data["ERRORINFO"] != null && !string.IsNullOrEmpty(dd.Data["ERRORINFO"].ToString())) 
            {
                dd.Attributes.Add("style", $"background-color: var(--rz-danger-light);");
                color = true;
            }
            else if (dd.Data["ERRORINFO"] != null && !string.IsNullOrEmpty(dd.Data["ERRORINFO"].ToString()))
            {
                dd.Attributes.Add("style", $"background-color: var(--rz-danger-lighter);");
                color = true;
            } 
        }

        if (selectedItems.Count > 0 && GridService.AreRowEqual(selectedItems[0], dd.Data, repositoryInfo.Column) && !color)
        {
            dd.Attributes.Add("style", $"background-color: var(--rz-primary-light);");
        }
    }

    RenderFragment GetPagingSummary(PagingInformation tr) => __builder =>
    {

        <PagingSummaryTemplate>
            <RadzenIcon Icon="database" Style="color: var(--rz-primary); font-size: var(--rz-pager-summary-font-size);" />
            Страница @tr.CurrentPage из @tr.NumberOfPages <b>(всего @tr.TotalCount из @total_count записей)</b> @totalTime
        </PagingSummaryTemplate>

    };
    private async Task FilterReloadDataAsync(bool is_click, string name, string mes)
    {
        if (is_click)
        {
            isFiltering = !isFiltering;
            ShowTooltip(name, mes);
        }

        if (FilterClearDataAsync_w || isFiltering || FilterAply || FilterAplyTmp)
        {
            await LoadDataAsync(Reloadargs);
        }

    }
    private async Task ToggleFiltering(string name, string mes)
    {
        ShowTooltip(name, mes);
        allowFiltering = !allowFiltering;
        icon = allowFiltering ? "filter_list" : "filter_list_off";
    }

    private async Task OpenFilterDialog(string PARAMCODE)
    {

        string code_form = "FormGrid";
        Type componentType = AppDomain.CurrentDomain.GetAssemblies()
                                        .SelectMany(assembly => assembly.GetTypes())
                                        .FirstOrDefault(type => type.Name == code_form);

        _PublicParams._metadata = SprLongDict[SprSelLongDict[PARAMCODE].KeySprLongDict].Param;
        _PublicParams._action = "FILTR";
        _PublicParams._seldata = null;


        var result = await DialogService.OpenAsync("Дополнительные фильтры " + repositoryInfo.Title,
                        ds => (RenderFragment)(builder =>
                            {
                                builder.OpenComponent(0, Type.GetType(componentType.FullName));
                                builder.AddComponentParameter(1, "InParams", _PublicParams);                        
                                builder.CloseComponent();
                            }),
                        new DialogOptions { Draggable = true, Resizable = true }
                     );

        // Если диалог был подтверждён, обновляем значения фильтров
        if (result != null)
        {
            param.Clear();
            var formValues = new Dictionary<string, object>();
            formValues = result.Values as Dictionary<string, object>;
            foreach (var kvp in formValues.Where(x => x.Key != "p_NameFilter" ))
            {
                string paramName = kvp.Key;
                object paramValue = kvp.Value;

                param.Add(paramName, paramValue);
            }
            flag = true;
            await LoadData(new LoadDataArgs { Skip = 0, Top = 10 }, PARAMCODE, "");

        }

        await gridDict[PARAMCODE].FocusAsync();

    }
    private async Task Find(string PARAMCODE)
    {
        string filter = "";
        if (String.IsNullOrEmpty(@SprLongFiltrDict[PARAMCODE]) == false)
        {


            filter = string.Join(" or ",
                        SprLongDict[SprSelLongDict[PARAMCODE].KeySprLongDict].Column.Where(x => x.DATATYPE == "VARCHAR2").Select(x =>"upper("+ x.CODE +")"+
                    " like upper('%" + @SprLongFiltrDict[PARAMCODE] + "%')")
                        );


        }
        await LoadData(new LoadDataArgs { Skip = 0, Top = 10 }, PARAMCODE, filter);

    }

    async Task LoadData(LoadDataArgs args, string PARAMCODE, string filter)
    {
        if (flag == false)
        {
            return;
        }
        int skip = args.Skip ?? 0;
        int take = args.Top ?? 10;


        var result = await dbService.GetDataPagination(
        baseSql: SprLongDict[SprSelLongDict[PARAMCODE].KeySprLongDict].sqlQuery,
         skip: skip,
        take: take,
        parameters: param,
        filter: filter,
        orderBy: args.OrderBy);

        if (string.IsNullOrEmpty(result.ErrorMessage))
        {
            dataDict[PARAMCODE] = result.Items;
            SprLongCountDict[PARAMCODE] = result.count;

        }
        else
        {
            //надо будет в модальном окне показать
        }

    }


    async void OnChange(object value, string PARAMCODE)
    {
        SprSelLongDict[PARAMCODE].ValPrm = (IDictionary<string, object>)value;
        gridDict[PARAMCODE].OpenOnFocus = false;
        //  selectedItem = (IDictionary<string, object>)value;
        await JSRuntime.InvokeVoidAsync("Radzen.closeAllPopups");

    }

    protected void OnRowClick(DataGridRowMouseEventArgs<IDictionary<string, object>> args)
    {

        if (selectedItems.Count > 0 && GridService.AreDictionariesEqual1(selectedItems[0], args.Data))
        {
            selectedItems.Clear();
            TabService.ChangeSelectedRow(null, globalFilter, Tab);

        }
        else
        {
            selectedItems.Clear();
            selectedItems.Add(args.Data);
            TabService.ChangeSelectedRow(selectedItems[0], globalFilter, Tab);
        }

    }



    private void OnHintChanged(string value)
    {        
        hint = value;
    }

    void OnCellContextMenu(DataGridCellMouseEventArgs<IDictionary<string, object>> args)
    {
        var columnName = args.Column.Title;
        var columnCode = args.Column.Property;
        var columnValue = args.Data.ContainsKey(columnCode) ? args.Data[columnCode] : "что-то пошло не так";

        var contextMenuItems = new List<ContextMenuItem>
        {

        new ContextMenuItem() {
            Text = "Посмотреть детально",
            Value = new { Key = 2, ColumnName = columnName, ColumnValue = columnValue },
            Icon = "visibility"},
        new ContextMenuItem() {
            Text = "Копировать",
            Value = new { Key = 3, ColumnName = columnName, ColumnValue = columnValue },
            Icon = "visibility"}
        };

        ContextMenuService.Open(args, contextMenuItems, OnMenuItemClick);
    }

    void OnMenuItemClick(MenuItemEventArgs args)
    {
        var conData = args.Value;
        if (conData != null)
        {
            var properties = conData.GetType().GetProperties();
            var keyProperty = properties.FirstOrDefault(p => p.Name == "Key");
            if (keyProperty != null)
            {
                var keyValue = keyProperty.GetValue(conData);
                if (keyValue.ToString() == "2")
                {
                    var columnNameProp = properties.FirstOrDefault(p => p.Name == "ColumnName");
                    var columnValueProp = properties.FirstOrDefault(p => p.Name == "ColumnValue");
                    var columnName = columnNameProp.GetValue(conData);
                    var columnValue = columnValueProp.GetValue(conData);
                    DialogService.Open<ColumnDetail>("Детально", 
                        new Dictionary<string, object> {{ "ColumnName", columnName }, { "Value", columnValue }},
                        new DialogOptions { Draggable = true, Resizable = true }
                    );
                }
                else if (keyValue.ToString() =="3")
                {
                    var columnValueProp = properties.FirstOrDefault(p => p.Name == "ColumnValue");
                    var columnValue = columnValueProp.GetValue(conData);
                    ClipboardService.WriteTextAsync(columnValue.ToString());
                }
            }
        }

    }


    private List<IDictionary<string, object>> RemoveSelectedRow(List<IDictionary<string, object>> data, Dictionary<string, object> rowToRemove)
    {

        return data.Where(item => !GridService.AreDictionariesEqual(item as Dictionary<string, object>, rowToRemove)).ToList();
    }

    private Dictionary<string, object> OutPutDict = new Dictionary<string, object> { {"VARCHAR2", String.Empty },
                                                                                     {"CHAR", String.Empty },
                                                                                     {"DATE", new DateTime()   },
                                                                                     {"NUMBER", new Decimal()   },
                                                                                     {"INT", new Decimal()   },
                                                                                   };



    async Task ReadSingleRow(IDictionary<string, object> data)
    {

        var parameters = new Dictionary<string, Rep_Param>();
        string pkcolumns = "";
        foreach (string prm in repositoryInfo.Param.Select(x => x.CODE))
        {
            string paramName = ":" + prm;            
            parameters.Add(paramName, globalFilter[prm]);
        }

        pkcolumns = string.Join(" and ", repositoryInfo.Column.FindAll(x => x.IS_PK == 1).Select(i => i.CODE + "=:IN_" + i.CODE));

        foreach (var prm in repositoryInfo.Column.FindAll(x => x.IS_PK == 1))
        {
            string paramName = ":IN_" + prm.CODE;
            parameters.Add(paramName, new Rep_Param(prm.ISMANDATORY, prm.DATATYPE, ParameterDirection.Input, prm.DATALENGTH,
                                                    prm.DATAPRECISION, data[prm.CODE]));

        }

        var (success, errorMessage) = await dbService.GetDataSingle1(repositoryInfo.Query, data, parameters, pkcolumns);


    }

    private string GetDataSource(string _connectionstring)
    {
        int index =_connectionstring.IndexOf(";");
        string datasource = string.Empty;
        if (index!=-1)
        {
            datasource = _connectionstring.Substring(0, index);
        }
        return datasource;

    }

    private string GetLogJSon(LogInfo lg)
    {
        string jsonString = JsonSerializer.Serialize<LogInfo>(lg, new JsonSerializerOptions()
                {
                    WriteIndented = true, // Добавляем отступы для красивого вывода
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

        return jsonString;

    }
    private void NotificationAction(NotificationSeverity notStatus, string status, string info)
    {
        NotificationService.Notify(new NotificationMessage
            {
                Severity = notStatus,
                Summary = status,
                Detail = info,
                Duration = 4000 
            });
    }
    private async Task AddRow()
    {
        var val = GridService.getSibFormInfo("ADD", Tab.CodeObj, "Добавление " + Tab.NameObj);  //repositoryInfo.Title
        string T_UserCode = string.Empty;
        int? T_ObjectId = null;
        selectedRowData.Clear();
        await SetPublicParamsMetadata();
        //?_PublicParams._metadata = repositoryInfo.Column;
        _PublicParams._globalFilter = globalFilter;
        _PublicParams._seldata = selectedRowData;
        _PublicParams._sql = repositoryInfo.AddSql;    
        _PublicParams._action = "ADD";

        LogInfo lg = new LogInfo
            {
                _globalFilter = globalFilter,
                _dbname=GetDataSource(repositoryInfo.Dbname_Query),
                _query = repositoryInfo.AddSql,
                _is_tempory = repositoryInfo.is_tempory,
                _name_tmptab = name_tmptab,

                                     };

        string jsonString = GetLogJSon(lg);

        bool success = false;
        string errorMessage = "";
        decimal logid = 0;

        (success, errorMessage, logid)=await _logService.LogI(Security.User.CODE, repositoryInfo.AppCode, Tab.Index.ToString(), 
                                                              Tab.PathIndex, "I", jsonString, Tab.CodeObj, Tab.Main_App_Code, Security.User.SessionId, Security.User.SessionInfo);

        var result = await DialogService.OpenAsync(val.Item2,
                       ds => (RenderFragment)(builder =>
                           {
                               builder.OpenComponent(0, val.Item1);
                               builder.AddComponentParameter(1, "InParams", _PublicParams);
                               builder.CloseComponent();
                           }),
                       new DialogOptions { Draggable = true, Resizable = true }
                     );

        var formValues = new Dictionary<string, object>();
        bool formRes = false;
        string error=string.Empty;
        if (result != null)
        {
            formValues = result.Values as Dictionary<string, object>;
            formRes = result.Result;
            error = result.errorMessage;


            if (formRes)
            {
                await RServ.UpdatePolicy(userCode: Security.User.CODE, Tab.CodeObj, formValues);
                foreach (var vals in formValues)
                {
                    lg._data.Add(vals.Key, new LogData { _oldValue = null, _newValue = vals.Value });
                }
                jsonString = GetLogJSon(lg);
                (success, errorMessage) = await _logService.LogS(logid, jsonString);

                queryResults = queryResults.Prepend(formValues);
                count = count + 1;
                total_count = total_count + 1;
                selectedItems.Clear();
                selectedItems.Add(formValues);
                NotificationAction(NotificationSeverity.Success, "Успешно!", "Данные сохранены");
            }
            else
            {
                if (error != null && error != string.Empty)
                {
                    (success, errorMessage) = await _logService.LogE(logid, error);
                    NotificationAction(NotificationSeverity.Error, "Ошибка!", "Данные не сохранены");
                }
                else
                {
                    (success, errorMessage) = await _logService.LogC(logid);
                    NotificationAction(NotificationSeverity.Warning, "Внимание!", "Данные не сохранены");
                }
            }
        }
        else 
        { 
            (success, errorMessage) = await _logService.LogC(logid);
            NotificationAction(NotificationSeverity.Warning, "Внимание!", "Данные не сохранены");
        }
    }
    private async Task EditRow()
    {
        var formValues = new Dictionary<string, object>();
        bool formRes = false;
        selectedRowData.Clear();
        string T_UserCode = string.Empty;
        int? T_ObjectId = null;

        if (selectedItems != null && selectedItems.Count > 0)
        {
            foreach (var kvp in selectedItems[0])
            {
                string key = kvp.Key;
                object value = kvp.Value;
                if (!selectedRowData.ContainsKey(key))
                {
                    selectedRowData.Add(key, value);
                }
            }
            var val = GridService.getSibFormInfo("EDIT", Tab.CodeObj, "Редактирование " + Tab.NameObj);  //repositoryInfo.Title
            await SetPublicParamsMetadata();
            //?_PublicParams._metadata = repositoryInfo.Column;
            _PublicParams._globalFilter = globalFilter;
            _PublicParams._seldata = selectedRowData;
            _PublicParams._sql = repositoryInfo.UpdSql;
            _PublicParams._action = "EDIT";

            LogInfo lg = new LogInfo
            {
                _globalFilter = globalFilter,
                    _dbname = GetDataSource(repositoryInfo.Dbname_Query),
                    _query = repositoryInfo.UpdSql,
                    _is_tempory = repositoryInfo.is_tempory,
                _name_tmptab = name_tmptab,

                                     };

            string jsonString = GetLogJSon(lg);

            bool success = false;
            string errorMessage = "";
            decimal logid = 0;
            string error = string.Empty;

            (success, errorMessage, logid)=await _logService.LogI(Security.User.CODE, repositoryInfo.AppCode, Tab.Index.ToString(), 
                                                                  Tab.PathIndex, "U", jsonString, Tab.CodeObj, Tab.Main_App_Code, Security.User.SessionId, Security.User.SessionInfo);

            var result = await DialogService.OpenAsync(val.Item2,
                           ds => (RenderFragment)(builder =>
                               {
                                   builder.OpenComponent(0, val.Item1);
                                   builder.AddComponentParameter(1, "InParams", _PublicParams);                             
                                   builder.CloseComponent();
                               }),
                           new DialogOptions { Draggable = true, Resizable = true }
                         );

            if (result != null)
            {
                formValues = result.Values as Dictionary<string, object>;
                formRes = result.Result;
                error = result.errorMessage;            

                if (formRes)
                {
                    await RServ.UpdatePolicy(userCode: Security.User.CODE, Tab.CodeObj, formValues);
                    foreach (var vals in formValues)
                    {
                        selectedRowData.TryGetValue(vals.Key, out var oldvalue);
                        lg._data.Add(vals.Key, new LogData { _oldValue = oldvalue, _newValue = vals.Value });
                    }
                    jsonString =  GetLogJSon(lg);
                    (success, errorMessage) = await _logService.LogS(logid, jsonString);

                    foreach (string key in formValues.Select(x => x.Key))
                    {
                        selectedItems[0][key] = formValues[key];
                    }
                    NotificationAction(NotificationSeverity.Success, "Успешно!", "Данные обновлены");
                }
                else
                {
                    if (error!=null && error!=string.Empty)
                    {
                        (success, errorMessage) = await _logService.LogE(logid, error);
                        NotificationAction(NotificationSeverity.Error, "Ошибка!", "Данные не обновлены");
                    }
                    else
                    {
                        (success, errorMessage) = await _logService.LogC(logid);
                        NotificationAction(NotificationSeverity.Warning, "Внимание!", "Данные не обновлены");
                    }
                }
            }
            else 
            { 
                (success, errorMessage) = await _logService.LogC(logid);

                NotificationAction(NotificationSeverity.Warning, "Внимание!", "Данные не обновлены");

            }
        }
    }

    private async Task SetPublicParamsMetadata()
    {
        if (repositoryInfo.Cfg.Count((x => x.CODE == "FOR_OPER")) > 0)
        {
            string col = repositoryInfo.Cfg.FirstOrDefault(x => x.CODE == "FOR_OPER").VALUE;
            List<ColumnMetadata> meta = new List<ColumnMetadata>();
            IDictionary<string, object> dictionary = selectedItems[0];
            meta = JsonSerializer.Deserialize<List<ColumnMetadata>>(dictionary[col].ToString());

            RepositoryInfo rep = new RepositoryInfo();
            rep.Param = new List<ParamMetadata>();
            rep.Dbname_Query=repositoryInfo.Dbname_Query;
            rep.AppCode = repositoryInfo.AppCode;
            rep.Column = meta;
            Dictionary<string, IEnumerable<REP_Models.SPRShort>> _SprShortDict = new();   
            Dictionary<string, EWA.Models.REP_Models.Rep_SprLongFiltrMeta> _SprLongDict = new();
            Dictionary<string, EWA.Models.REP_Models.Rep_SprShortSelDict> _SprSelShotDict = new();    
            Dictionary<string, EWA.Models.REP_Models.Rep_SprLongSelDict> _SprSelLongDict = new();


            (_SprShortDict, _SprLongDict) = await GetSpr(rep);
            foreach ((string dimcode, string param) in meta.Where(x => x.DIMCODE != null).Select(x => (x.DIMCODE, x.CODE)))
            {
                if (_SprLongDict.ContainsKey(dimcode))
                {

                    _SprSelLongDict.Add(param, new REP_Models.Rep_SprLongSelDict { KeySprLongDict = dimcode, ValPrm = null });

                }
                else
                {
                    _SprSelShotDict.Add(param, new EWA.Models.REP_Models.Rep_SprShortSelDict { KeySprShortDict = dimcode, ValPrm = null });
                }
            }
            _PublicParams._metadata = meta;
            _PublicParams._SprLongDict = _SprLongDict;
            _PublicParams._SprShortDict = _SprShortDict;
            _PublicParams._SprSelLongDict = _SprSelLongDict;
            _PublicParams._SprSelShortDict = _SprSelShotDict;
            int g = 0;

        }
        else
        {
            _PublicParams._metadata = repositoryInfo.Column;
        }

    }
    private async Task ViewRow()
    {
        var formValues = new Dictionary<string, object>();
        bool formRes = false;

        selectedRowData.Clear();
        if (selectedItems != null && selectedItems.Count > 0)
        {
            foreach (var kvp in selectedItems[0])
            {
                string key = kvp.Key;
                object value = kvp.Value;
                if (!selectedRowData.ContainsKey(key))
                {
                    selectedRowData.Add(key, value);
                }
            }           

            var val = GridService.getSibFormInfo("VIEW", Tab.CodeObj, "Детальный просмотр " + Tab.NameObj);  //repositoryInfo.Title

            await SetPublicParamsMetadata();
            //? _PublicParams._metadata = repositoryInfo.Column;
            _PublicParams._globalFilter = globalFilter;
            _PublicParams._seldata = selectedRowData;
            _PublicParams._sql = null;
            _PublicParams._action = "VIEW";

            var result = await DialogService.OpenAsync(val.Item2,
                           ds => (RenderFragment)(builder =>
                               {
                                   builder.OpenComponent(0, val.Item1);
                                   builder.AddComponentParameter(1, "InParams", _PublicParams);
                                   builder.CloseComponent();
                               }),
                           new DialogOptions { Draggable = true, Resizable = true }
                         );
            selectedItems.Clear();

        }
    }
    private async Task DelRow()
    {
        var formValues = new Dictionary<string, object>();
        bool formRes = false;
        string T_UserCode = string.Empty;
        int? T_ObjectId = null;

        selectedRowData.Clear();
        if (selectedItems != null && selectedItems.Count > 0)
        {
            LogInfo lg = new LogInfo
            {
             _globalFilter = globalFilter,
                    _dbname = GetDataSource(repositoryInfo.Dbname_Query),
             _query = repositoryInfo.DelSql,
                    _is_tempory = repositoryInfo.is_tempory,
             _name_tmptab = name_tmptab,
            };


            foreach (var kvp in selectedItems[0])
            {
                string key = kvp.Key;
                object value = kvp.Value;
                lg._data.Add(key, new LogData { _oldValue = value, _newValue = null});

                if (!selectedRowData.ContainsKey(key))
                {
                    selectedRowData.Add(key, value);
                }
                if ((Tab.CodeObj == "SRULES" && kvp.Key == "RULE_ID") 
                   || 
                   ((Tab.CodeObj == "SOBJECT" || Tab.CodeObj == "SOBJECTATTR") && kvp.Key == "ID"))
                {
                    if (value != null && value != DBNull.Value)
                    {
                        T_ObjectId = Convert.ToInt32(value);
                    }
                }
                if ((Tab.CodeObj == "SUSERS" || Tab.CodeObj == "SUSERSATTR") && kvp.Key == "USER_CODE")
                {
                    T_UserCode = value.ToString();
                }

            }



            string jsonString = GetLogJSon(lg);

            bool success = false;
            string errorMessage = "";
            decimal logid = 0;
            string error = string.Empty;

            (success, errorMessage, logid)=await _logService.LogI(Security.User.CODE, repositoryInfo.AppCode, Tab.Index.ToString(), 
                                                                  Tab.PathIndex, "D", jsonString, Tab.CodeObj, Tab.Main_App_Code, Security.User.SessionId, Security.User.SessionInfo);


            var val = GridService.getSibFormInfo("DEL", Tab.CodeObj, "Удаление " + Tab.NameObj);  //repositoryInfo.Title

            await SetPublicParamsMetadata();
            //? _PublicParams._metadata = repositoryInfo.Column;
            _PublicParams._globalFilter = globalFilter;
            _PublicParams._seldata = selectedRowData;
            _PublicParams._sql = repositoryInfo.DelSql;
            _PublicParams._action = "DEL";


            var result = await DialogService.OpenAsync(val.Item2,
                           ds => (RenderFragment)(builder =>
                               {
                                   builder.OpenComponent(0, val.Item1);
                                   builder.AddComponentParameter(1, "InParams", _PublicParams);
                                   builder.CloseComponent();
                               }),
                           new DialogOptions { Draggable = true, Resizable = true }
                         );


            if (result != null)
            {
                formValues = result.Values as Dictionary<string, object>;
                formRes = result.Result;
                error = result.errorMessage;

                if (formRes)
                {
                    await RServ.UpdatePolicy(userCode: Security.User.CODE, Tab.CodeObj, selectedRowData);
                    (success, errorMessage) = await _logService.LogS(logid, jsonString);
                    count = count - 1;
                    total_count = total_count - 1;
                    List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();
                    /*удаляем строку*/
                    foreach (Dictionary<string, object> f in queryResults.Where(x => x != selectedItems[0]))
                    {
                        list.Add(f);
                    }
                    //Cast list to IEnumerable
                    queryResults = (IEnumerable<Dictionary<string, object>>)list;
                    selectedItems.Clear();
                    NotificationAction(NotificationSeverity.Success, "Успешно!", "Данные удалены");
                }
                else
                {
                    if (error != null && error != string.Empty)
                    {
                        (success, errorMessage) = await _logService.LogE(logid, error);
                        NotificationAction(NotificationSeverity.Error, "Ошибка!", "Данные не удалены");
                    }
                    else
                    {
                        (success, errorMessage) = await _logService.LogC(logid);
                        NotificationAction(NotificationSeverity.Warning, "Внимание!", "Данные не удалены");
                    }

                }
            }
            else
            { 
                (success, errorMessage) = await _logService.LogC(logid);
                NotificationAction(NotificationSeverity.Warning, "Внимание!", "Данные не удалены");
            }
        }
    }
    private async Task<(Dictionary<string, object> formValues, bool formRes)> FiltrRow()
    {
        string code_form = "FormGrid";
        Type componentType = AppDomain.CurrentDomain.GetAssemblies()
                                        .SelectMany(assembly => assembly.GetTypes())
                                        .FirstOrDefault(type => type.Name == code_form);


        _PublicParams._metadata = repositoryInfo.Param;
        _PublicParams._action = "GlobalFILTR";
        _PublicParams._seldata = selectedRowData;
        _PublicParams._globalFilter = globalFilter;       

        ///var result = await DialogService.OpenAsync("Фильтр " + repositoryInfo.Title,
        var result = await DialogService.OpenAsync("Фильтр " + Tab.NameObj,
                            ds => (RenderFragment)(builder =>
                                {
                                    builder.OpenComponent(0, Type.GetType(componentType.FullName));
                                    builder.AddComponentParameter(1, "InParams", _PublicParams);
                                    builder.CloseComponent();
                                }),
                            new DialogOptions { Draggable = true, Resizable = true }
                     );

        var formValues = new Dictionary<string, object>();
        bool formRes = false;
        if (result != null)
        {
            formValues = result.Values as Dictionary<string, object>;
            formRes = result.Result;
        }

        return (formValues, formRes);
    }
    private void CloseModal()
    {
        DialogService.Dispose();
    }


    private Dictionary<string, IEnumerable<REP_Models.SPRShort>> SprShortDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprShortSelDict> SprSelShotDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprLongFiltrMeta> SprLongDict = new();
    private Dictionary<string, EWA.Models.REP_Models.Rep_SprLongSelDict> SprSelLongDict = new();


    Type GetComponentType(string componentName)
    {
        var assembly = Assembly.GetExecutingAssembly();
        return assembly.GetTypes().FirstOrDefault(t => t.Name == componentName);
    }


    private async Task<(List<REP_Models.ParamMetadata>, Dictionary<string, string>)> GetTransitionParam(string AppCode, string parent, string child, long id_trans)
    {
        List<REP_Models.ParamMetadata> transition_params = new List<REP_Models.ParamMetadata>();
        Dictionary<string, string> linked_param = new Dictionary<string, string>();

        if (AppCode == "SIB")
        {
            transition_params = SModule.GetTransitionParams(parent, child);
        }  
        else if (AppCode == "WFL")
        {
            transition_params = WModule.GetTransitionParams(parent, child);
        }
        else
        {
            string trans_params = await _repService.GetTransitionParamsAsync(id_trans);
            if (trans_params != null)
            {
                transition_params = JsonSerializer.Deserialize<List<REP_Models.ParamMetadata>>(trans_params);
            }
        }

        foreach (var param in transition_params.Where(x => (x.LINKEDCOLUMNCODE != null || x.LINKEDPARAMCODE != null) && !(x.IS_SYSTEM == 1 && x.TYPE_SYSTEM == "CNT_PAGE")))
        {
            linked_param.Add(param.LINKEDCOLUMNCODE ?? param.LINKEDPARAMCODE, param.CODE);

        }
        transition_params.RemoveAll(x => (x.LINKEDCOLUMNCODE != null || x.LINKEDPARAMCODE != null) && !(x.IS_SYSTEM == 1 && x.TYPE_SYSTEM == "CNT_PAGE"));


        return (transition_params, linked_param);
    }

    private async Task AddTab(string namegrid, string codeobj, string nameobj, long id_trans, bool haveparam, string visibility_formula, int kind, TabPageKind pagekind)
    {

        Dictionary<string, string> linked_param = new Dictionary<string, string>();
        List<REP_Models.ParamMetadata> transition_params = new List<REP_Models.ParamMetadata>();
        (transition_params, linked_param) = await GetTransitionParam(repositoryInfo.AppCode, Tab.CodeObj, codeobj, id_trans);

        TabDataPrm tbPrm = new TabDataPrm { namegrid = namegrid, codeobj = codeobj, nameobj = nameobj, parentTab = Tab, 
                                            transitionParams = transition_params, linkedParam = linked_param, 
                                            visibilityFormula = visibility_formula,
                                            pagekind=TabPageKind.Standart,
                                            lstpage=String.Empty
                                           };


        var prm = transition_params.FirstOrDefault(x => x.IS_SYSTEM == 1 && x.TYPE_SYSTEM == "CNT_PAGE", null);

        if (prm!=null)
        {
            // Проверяем наличие элементов в списке
            if (selectedItems.Count > 0 && selectedItems[0].ContainsKey(prm.LINKEDCOLUMNCODE))
            {
                tbPrm.pagekind = TabPageKind.CntPage;
                tbPrm.lstpage = selectedItems[0][prm.LINKEDCOLUMNCODE].ToString();

            }
            else
            {               
                return;

            }
        }
        else 
        if (kind==2)
        {
            tbPrm.pagekind = TabPageKind.Modal;

        }
        else
        {
            tbPrm.pagekind = pagekind;
        }
        TabService.AddBottomTab(tbPrm);
    }

    private async Task<object> GetDefaultVal(string defval, string datatype)
    {
        object val;
        string query = "select " + defval + " from dual";
        Dictionary<string, object> data = new Dictionary<string, object>();

        await dbService.GetDataSingle(query, data, null, "1=1");
        val = data.FirstOrDefault().Value;
        if (datatype == "DATE")
        {
            val = DateTime.ParseExact(val.ToString(), "yyyyMMdd", CultureInfo.InvariantCulture).ToString("dd.MM.yyyy");

        }
        if (datatype == "NUMBER")
        {
            val = Convert.ToDecimal(val);
        }
        return val;
    }

    // Displaying page @context.CurrentPage of @context.NumberOfPages <b>(total @context.TotalCount records)</b>

    /*для ревреша*/
    private async Task ReloadAsync()
    {
        is_request = true;
        //await LoadDataAsync(Reloadargs);
        await LoadTmpBegin();
        is_request = false;
        StateHasChanged();
    }

    private bool is_load = false;
    private async Task LoadDataBegin()
    {
        if (repositoryInfo.Param.Where(x => x.HIDDENVALUE == 0).Count() == 0 ||
            EmptyText == "Переход недоступен" ||
            EmptyText == "Не введены связанные значения")
        {
            return;
        }

        var (formValues, formRes) = await FiltrRow();

        if (formRes)
        {
            globalFilter.Clear();



            foreach (var kvp in formValues)
            {
                string paramName = kvp.Key;
                object paramValue = kvp.Value;
                var meta = repositoryInfo.Param.First(x => x.CODE == kvp.Key);
                globalFilter.Add(kvp.Key, new Rep_Param(meta.ISMANDATORY, meta.DATATYPE, ParameterDirection.Input, meta.DATALENGTH,
                                                        meta.DATAPRECISION, kvp.Value)
                                );
            }
            if (Tab.Kind==TabPageKind.Modal)
            {
                GridService.CopyDictionaries(OldParams, Tab.Prm);                
            }
            is_request = true;
            //? isFiltering = true;

            EmptyText = "Нет записей для отображения";
            Reloadargs.Skip=0;
            Reloadargs.Top = pageDefault;
            await LoadTmpBegin();
            //?? await LoadDataAsync(Reloadargs);
            is_request = false;
        }
        //?  DialogService.Close(true);
    }


    private bool FilterClearDataAsync_run = false;
    private bool FilterClearDataAsync_w = false;
    private async Task colFilterClear(RadzenDataGridColumn<IDictionary<string, object>> col)
    {
        col.ClearFilters();

        if (col.FilterPropertyType == typeof(String))
        {
            col.SetFilterOperator(FilterOperator.Contains);
        }
        else
        {
            col.SetFilterOperator(FilterOperator.Equals);
        }
    }
    private bool FilterAply = false;
    private async Task FilterAplyAsync()
    {
        FilterAply = true;
        await FilterReloadDataAsync(false, null, null);
        FilterAply = false;
    }

    private bool FilterAplyTmp = false;
    private async Task FilterAplyAsyncTmp(CancellationToken token)
    {
        FilterAplyTmp=true;
      /*  if (Tab.load_state==2)
        {
            TabService.OnChangeLoadState(0, Tab);
        }*/
        await FilterReloadDataAsync(false, null, null);
        FilterAplyTmp = false;

    }
    private async Task FilterAplyAsyncTmp1()
    {
        FilterAplyTmp = true;
        if (Tab.load_state == 2)
        {
            TabService.OnChangeLoadState(0, Tab);
        }
        await FilterReloadDataAsync(false, null, null);
        FilterAplyTmp = false;

    }
    private async Task FilterClearDataAsync()
    {
        FilterClearDataAsync_w = true;
        FilterClearDataAsync_run = true;

        foreach(var col in grid.ColumnsCollection)
        {

            await colFilterClear(col);
        }

        foreach (var sel in SprSelLongDict)
        {
            sel.Value.ValPrm = null;
            dataDict[sel.Key] = null;
            SprLongCountDict[sel.Key] = 0;
            SprLongFiltrDict[sel.Key] = "";
        }

        foreach (var sel in SprSelShotDict)
        {
            sel.Value.ValPrm = null;            
        }

        foreach (var key in FilterCheckBox.Keys)
        {
            FilterCheckBox[key] = null;
        }
        Reloadargs.Filters = Enumerable.Empty<FilterDescriptor>();
        FilterClearDataAsync_run = false;

        await FilterReloadDataAsync(false, null, null);

        FilterClearDataAsync_w=false;


    }


    void IDisposable.Dispose()
    {
        try
        {



            if (_mainCts != null && !_mainCts.IsCancellationRequested)
            {
                _mainCts.Cancel(); // Отменяем асинхронную операцию
            }

            if (!string.IsNullOrEmpty(name_tmptab))
            {
                string dropTableQuery = $"BEGIN BEGIN EXECUTE IMMEDIATE 'DROP TABLE {name_tmptab}'; EXCEPTION WHEN OTHERS THEN NULL; END; END;";
                var dropResult = dbService.ExecuteData(dropTableQuery);
            }
        }
        catch(Exception ee)
        {
            //была ошибка в админке!
            var err = ee.Message;
        }
    }
    protected async Task<RepositoryInfo> GetRepositoryInfo(string CodeObj, string? AppCode)

    {
        RepositoryInfo rep = new RepositoryInfo();
        if (AppCode == "SIB")
        {
            rep = SModule.GetRepositoryInfo(CodeObj);
        }
        else if (CodeObj == "WTASK" || CodeObj == "WWORKLET" || CodeObj == "WWORKFLOW" || CodeObj == "WACTIVATION" || CodeObj == "WACTIVATION_DET")
        {
            rep = WModule.GetRepositoryInfo(CodeObj);
        }
        else
        { 
            rep.MenuGrid = Enumerable.Empty<EWA.Models.REP_Models.GRID_Menu>();
            rep = await _repService.GetRepositoryInfo(Security.User,CodeObj);
            rep.MenuGrid = await MenuGrid.GetMenuData(Security.User,CodeObj);

        }

        if (rep.Column.Count(x => x.NAME == "DUMMY") > 0)
        {
            rep.Dummy = true;
        }
        if (rep.AppCode == "SIB" || rep.AppCode == "WFL")
        {
            rep.is_tempory = false;           
        }
        else 
        {
            rep.is_tempory = true;
        }

        return rep;

    }

    protected async Task<(RepositoryInfo, Dictionary<string, Rep_Param>)> ChangeRepositoryInfo(RepositoryInfo _rep, Dictionary<string, string> _linkedParam, List<ParamMetadata> _transitionParams, Dictionary<string, Rep_Param> _globalFilter)
    {
        for (int j = 0; j < _rep.Param.Count; j++)
        {
            if (_linkedParam.Any(x => x.Value == _rep.Param[j].CODE))
            {
                _rep.Param[j].HIDDENVALUE = 1;
            }

            if (_transitionParams.Any(x => x.CODE == _rep.Param[j].CODE))
            {
                _rep.Param[j] = _transitionParams.First(x => x.CODE == _rep.Param[j].CODE);

            }

        }

        foreach (var prm in _rep.Param)
        {
            object val = null;
            if (prm.DEFAULTVALUE != null)
            {
                if (prm.DEFAULTVALUE.ToString().Substring(0, 1) == "=")
                {
                    val = await GetDefaultVal(prm.DEFAULTVALUE.ToString().Substring(1), prm.DATATYPE);
                }
                else
                {
                    val = prm.DEFAULTVALUE;
                    if (prm.DATATYPE == "NUMBER")
                    {
                        val = Convert.ToDecimal(val);
                    }

                }

            }

            _globalFilter.Add(prm.CODE, new Rep_Param(prm.ISMANDATORY, prm.DATATYPE, ParameterDirection.Input, prm.DATALENGTH,
                                                          prm.DATAPRECISION, val));


        }


        return (_rep, _globalFilter);
    }

    protected async Task<(Dictionary<string, IEnumerable<REP_Models.SPRShort>>, Dictionary<string, EWA.Models.REP_Models.Rep_SprLongFiltrMeta>)> GetSpr(RepositoryInfo _rep)
    {
        Dictionary<string, IEnumerable<REP_Models.SPRShort>> _SprShortDict = new();
        Dictionary<string, EWA.Models.REP_Models.Rep_SprLongFiltrMeta> _SprLongDict = new();

        foreach ((string dimcode, string param, string datatype) in _rep.Column.Where(x => x.DIMCODE != null).Select(x => (x.DIMCODE, x.CODE, x.DATATYPE)).Concat(_rep.Param.Where(x => x.DIMCODE != null).Select(x => (x.DIMCODE, x.CODE, x.DATATYPE))))
        {

            if (_SprShortDict.ContainsKey(dimcode) == false && _SprLongDict.ContainsKey(dimcode) == false)
            {
                if (_rep.AppCode == "SIB")
                {
                    var _sprMetadata = await SModule.GetSPRData(dimcode);
                    _SprShortDict.Add(dimcode, _sprMetadata);

                }
                else if (_rep.AppCode == "WFL")
                {
                    var _sprMetadata = await WModule.GetSPRData(dimcode);
                    _SprShortDict.Add(dimcode, _sprMetadata);
                }
                else
                {
                    var (_sprMetadata, _isShortDim, _SprLongFiltrMeta) = await _getspr.GetSPRDataAsync1(dimcode, _rep.Dbname_Query, datatype);
                    if (_isShortDim)
                    {
                        _SprShortDict.Add(dimcode, _sprMetadata);
                    }
                    else
                    {
                        _SprLongDict.Add(dimcode, _SprLongFiltrMeta);
                    }
                }
            }

        }

        return (_SprShortDict, _SprLongDict);

    }


    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        Console.WriteLine("OnInitializedAsync 1" + Tab.CodeObj);

        repositoryInfo= await GetRepositoryInfo(Tab.CodeObj, Tab.Main_App_Code);
        if (repositoryInfo.TypeObject == "GET_PROCEDURE")
        {
            repositoryInfo.Query = await _repService.GenSqrt(Tab.CodeObj, repositoryInfo);
        }


        dbService = new DBService(repositoryInfo.Dbname_Query, Security);



        (repositoryInfo, globalFilter) = await ChangeRepositoryInfo(repositoryInfo, Tab.LinkedParam, Tab.TransitionParams, globalFilter);

        foreach (string param in repositoryInfo.Column.Where(x => x.DOMAINCODE == "Bool").Select(x => x.CODE).Concat(repositoryInfo.Param.Where(x => x.DOMAINCODE == "Bool").Select(x => x.CODE)))
        {

            FilterCheckBox.Add(param, null);
        }

        (SprShortDict, SprLongDict) = await GetSpr(repositoryInfo);


        foreach ((string dimcode, string param) in repositoryInfo.Column.Where(x => x.DIMCODE != null).Select(x => (x.DIMCODE, x.CODE)).Concat(repositoryInfo.Param.Where(x => x.DIMCODE != null).Select(x => (x.DIMCODE, x.CODE))))
        {
            if (SprLongDict.ContainsKey(dimcode))
            {

                SprSelLongDict.Add(param, new REP_Models.Rep_SprLongSelDict { KeySprLongDict = dimcode, ValPrm = null });
                gridDict.Add(param, new RadzenDropDownDataGrid<IDictionary<string, object>>());
                IEnumerable<IDictionary<string, object>> data; //= Enumerable.Empty<IDictionary<string, object>>();
                dataDict.Add(param, null);
                SprLongCountDict.Add(param, 0);
                SprLongFiltrDict.Add(param, "");
            }
            else
            {
                SprSelShotDict.Add(param, new EWA.Models.REP_Models.Rep_SprShortSelDict { KeySprShortDict = dimcode, ValPrm = null });
            }
        }

        if (repositoryInfo.is_tempory)
        {   
            nextValue = await Seq.GetNextValueAsync("EWA_ROW_SEQ");
            name_tmptab = "TMP_EWA_" + nextValue;
            chng_query = $"SELECT * FROM {name_tmptab}";
            _PublicParams._name_tmptab = name_tmptab;
            Console.WriteLine($"Временная таблица: {name_tmptab}");
            var TemporyTable = await dbService.BuildTemporyTable(name_tmptab, repositoryInfo.Column);
            var result_create = await dbService.ExecuteData(TemporyTable.Item1);
            Console.WriteLine($"Временная таблица создана: {name_tmptab}  итог: {result_create.ToString()}");

            if (repositoryInfo.TypeObject != "GET_PROCEDURE")
            {
                repositoryInfo.Query = TemporyTable.Item3 + " from (" + repositoryInfo.Query + "\r\n)";
            }
            colInfo_str = TemporyTable.Item2;
        }
        else
        {
            chng_query = repositoryInfo.Query;
        }


        _PublicParams._dbname = repositoryInfo.Dbname_Query;
        _PublicParams._is_tempory = repositoryInfo.is_tempory;
        _PublicParams._query = repositoryInfo.Query;
        _PublicParams._metadata_param = repositoryInfo.Param;
        _PublicParams._SprShortDict = SprShortDict;
        _PublicParams._SprSelShortDict = SprSelShotDict;
        _PublicParams._SprLongDict = SprLongDict;
        _PublicParams._SprSelLongDict = SprSelLongDict;
        _PublicParams._form_code = Tab.CodeObj;
        _PublicParams._cfg_data = repositoryInfo.Cfg;
    }
    int h = 0;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            is_new = false;
        }

        if (h == 1)
        {
            Tab.is_complete = true;
            h = 3;
            Console.WriteLine("OnAfterRenderAsync"+Tab.CodeObj);
            await OnGlobalFiltrExt();

            foreach(var m in repositoryInfo.MenuGrid.Where(x=>x.AUTO_MODE==1))
            {
                await AddTab("EditGrid", m.CODE_OBJECT, m.NAME_OBJECT, m.ID, true, m.VISIBILITYFORMULA,
                                m.KIND, TabPageKind.AutoMode);
            }


        }
    }

    private async Task OnGlobalFiltrExt()
    {

        if (repositoryInfo.Param.Count(x => x.ISMANDATORY == 1 && x.DEFAULTVALUE == null && x.HIDDENVALUE == 0) > 0 &&
            Tab.ParentTab.CodeObj != "head")
        {
            Console.WriteLine("OnGlobalFiltrExt" + Tab.CodeObj);
            is_request = false;          
            await LoadDataBegin();



        }

        if (Tab.ParentTab.CodeObj == "head")
        {
            foreach (var prm in globalFilter.Where(x => x.Value.Val == null))
            {
                if (repositoryInfo.Param.Any(x => x.CODE == prm.Key && x.HIDDENVALUE == 0))
                {
                    is_request = true;          
                    await LoadDataBegin();
                    is_request = false;
                    return;

                }
            }
        }

    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        Console.WriteLine("OnParametersSetAsync 1" + Tab.CodeObj);
        EmptyText = "Нет записей для отображения";

        if (!userChngGridHeight)
        {
            if (Tab.CountLvl > 3)
            {
                gridHeight = 60 / 3;
            }
            else if (Tab.CountLvl == 2)
            {
                gridHeight = 56 / 2;
            }
        }
        if (isLoading)
        {

            return;
        }
        bool rez = false;


        checkLoad(Tab, ref rez);
        if (rez)
        {
            Tab.isLoading = true;
            isLoading = true;
            StateHasChanged();
        }

        while (rez == true)
        {
            rez = false;
            checkLoad(Tab, ref rez);
            await Task.Delay(100);
        }

        if  (!Tab.Show)
        {
            if (_mainCts != null && !_mainCts.IsCancellationRequested)
            {
                _mainCts.Cancel(); 
            }
        }
        Tab.isLoading = false;
        isLoading = false;

        if (Tab.IsSelect)
        {
            foreach (var kvp in Tab.Prm)
            {
                globalFilter[kvp.Key].Val = kvp.Value;

            }

            if (!GridService.MenyCheck(Tab.VisibilityFormula, Tab.ParentTab.RowData, Tab.ParentTab.ParamData))
            {
                Console.WriteLine("OnParametersSetAsync 2" + Tab.CodeObj);

                EmptyText = "Переход недоступен";
                lock (_syncLock)
                {
                    count = 0;
                    total_count = 0;
                }
                queryResults = Enumerable.Empty<IDictionary<string, object>>();

                OldParams.Clear();
                selectedItems.Clear();
                is_new = false;
                isInit = false;
                Tab.isLoad = false;
                if (h == 0)
                { h = 1; }
                return;
            }

            if (Tab.Prm.Count(x => x.Value == null) > 0)
            {
                Console.WriteLine("OnParametersSetAsync 3" + Tab.CodeObj);
                var items = new List<IDictionary<string, object>>();
                EmptyText = "Не введены связанные значения";
                lock (_syncLock)
                {
                    count = 0;
                    total_count = 0;
                }
                queryResults = Enumerable.Empty<IDictionary<string, object>>();

                OldParams.Clear();
                selectedItems.Clear();
                is_new = false;
                isInit = false;
                Tab.isLoad = false;
                if (h == 0)
                { h = 1; }
                return;
            }          


            foreach (var m in repositoryInfo.Param.Where(x => x.ISMANDATORY == 1))
            {
                if (globalFilter.Where(x => x.Value.Val == null).Count(x => x.Key == m.CODE) > 0)
                {
                    Console.WriteLine("OnParametersSetAsync 4" + Tab.CodeObj);
                    var items = new List<IDictionary<string, object>>();
                    EmptyText = "Не введены обязательные параметры";
                    lock (_syncLock)
                    {
                        count = 0;
                        total_count = 0;
                    }
                    queryResults = Enumerable.Empty<IDictionary<string, object>>();

                    OldParams.Clear();
                    selectedItems.Clear();
                    is_new = false;
                    isInit = false;
                    Tab.isLoad = false;
                    if (h ==0)
                    { h = 1; }
                    return;

                }
            }

            if (isInit && Tab.ParentTab.CodeObj == "head")
            {
                foreach (var prm in globalFilter.Where(x => x.Value.Val == null))
                {
                    if (repositoryInfo.Param.Any(x => x.CODE == prm.Key && x.HIDDENVALUE == 0))
                    {
                        var items = new List<IDictionary<string, object>>();
                        EmptyText = "Введите параметры";
                        Console.WriteLine("OnParametersSetAsync 5" + Tab.CodeObj);
                        count = 0;
                        total_count = 0;
                        queryResults = Enumerable.Empty<IDictionary<string, object>>();

                        OldParams.Clear();
                        selectedItems.Clear();
                        is_new = false;
                        isInit = false;
                        Tab.isLoad = false;
                        if (h == 0)
                        { h = 1; }
                        return;

                    }
                }
            }


            if (!GridService.AreDictionariesEqual(Tab.Prm, OldParams) || (isInit && Tab.TransitionParams.Count == 0))
            {
                GridService.CopyDictionaries(OldParams, Tab.Prm);


                is_request = true;
                ReloadPage = "-1;-1;-1;";                  
                filterSqlOld = "";
                Reloadargs.Skip = 0;
                Reloadargs.Top = pageDefault;
                //await LoadDataAsync(Reloadargs);
                isInit = false;
                await LoadTmpBegin();
                is_request = false;
                Tab.isLoad = false;

            }
            Tab.isLoad = false;

            if (h == 0)
            { Console.WriteLine("OnParametersSetAsync 8" + Tab.CodeObj);
                h = 1; }
            Console.WriteLine("OnParametersSetAsync 9" + Tab.CodeObj);

        }


    }

    private void checkLoad(TabData ff, ref bool rez)
    {

        foreach (var tt in ff.SubTabs.Where(x => x.Value.Show && x.Value.Kind!=TabPageKind.Modal))
        {           

            if (tt.Value.isLoad && tt.Value.IsSelect || tt.Value.isLoading)
            {
                rez=true;
                return;
            }

            checkLoad(tt.Value, ref rez);
            if (rez)
            {
                return;
            }

        }                
    }
    private bool is_requestRun = false;
    private string ReloadPage = "-1;-1;-1;";
    private LoadDataArgs Reloadargs=new LoadDataArgs();



    private string orderByOld;
    private string filterSqlOld;
    private int skip = 0;
    private int take = 50;
    private int pageDefault = 50;

    private string colInfo_str = "";
    Dictionary<string, Rep_Param> filterprm = new Dictionary<string, Rep_Param>();

    private async Task OnFilterCleared(DataGridColumnFilterEventArgs<IDictionary<string, object>> args)
    {      
        await colFilterClear(args.Column);
    }

    


    private async Task<(int _total_count, int _status, string _errorMessage)> FillTempTable(string count_query, CancellationToken token,
    TaskCompletionSource<bool> tcs)
    {

        if (repositoryInfo.is_tempory)
        {
            var insertResult = await dbService.ExecuteDataTempory1(hint, name_tmptab, repositoryInfo.Query,
                                                                         repositoryInfo.TypeObject, globalFilter, colInfo_str,
                                                                         token);

            if (!insertResult.res)
            {
                if (insertResult.errorMessage != null)
                {

                    string err_title = "Ошибка ";
                    if (insertResult.errorMessage == "Текущая операция отменена")
                    {
                        err_title = "Внимание ";
                    }

                    await DialogService.OpenAsync<ErrorGrid>(err_title,
                                                    new Dictionary<string, object> { { "_errorMessage", insertResult.errorMessage } },
                                                    new DialogOptions { Draggable = true, Resizable = true });



                }
                if (!token.IsCancellationRequested)
                {
                    isLoading = false;
                    TabService.ChangeLoadState(0, Tab);
                }
                tcs.TrySetResult(true);
                return (total_count, -1, insertResult.errorMessage);
            }

            var rez = await dbService.GetDataSimple(count_query, token);

            if (rez.ErrorMessage == null)
            {
                total_count = Convert.ToInt32(rez.Items.First()["CNT"]);
            }
            else
            {
                tcs.TrySetResult(true);
                return (total_count, -1, rez.ErrorMessage);
            }

        }
        await FilterAplyAsyncTmp(token);
        
        if (!token.IsCancellationRequested)
        {
            TabService.ChangeLoadState(0, Tab);
            is_requestRun = true;
        }
        tcs.TrySetResult(true);

        return (total_count, 1, null);
    }


    private async Task<int> ScanFirstPage(string countQuery, CancellationToken token, Task<bool> FillTempTableCompleted)
    {
        int gCount = 0;

        while (gCount < 50 && !token.IsCancellationRequested &&
               !FillTempTableCompleted.IsCompleted)
        {            

            var result = await dbService.GetDataSimple(countQuery, token);

            if (result.ErrorMessage != null)
            {
                // Логируем ошибку или выводим предупреждение
                Console.WriteLine($"Ошибка при получении данных: {result.ErrorMessage}");
                continue;
            }
            else
            {
                gCount = Convert.ToInt32(result.Items.First()["CNT"]);
            }

            await Task.Delay(1000, token); 
        }


        return gCount;
    }

    private async Task SelCnTempTable(string count_query, CancellationToken token, Task<bool> FillTempTableCompleted)
    {
        int count1 = 0;
        int count1_prev = 0;
        int total_count1 = 0;
        int total_count1_prev = 0;

        while (!token.IsCancellationRequested &&
               !FillTempTableCompleted.IsCompleted)
        {
            var rez1 = await dbService.GetDataSimple(count_query, token, filterprm, filterSql_w);
            if (rez1.ErrorMessage == null)
            {
                count1 = Convert.ToInt32(rez1.Items.First()["CNT"]);
            }
            var rez = await dbService.GetDataSimple(count_query, token);

            if (rez.ErrorMessage == null)
            {
                total_count1 = Convert.ToInt32(rez.Items.First()["CNT"]);
            }

            if (total_count1 != total_count1_prev || count1 != count1_prev)
            {
                total_count1_prev = total_count1;
                count1_prev = count1;
                lock (_syncLock)
                {
                    total_count = total_count1;
                    count = count1;
                    StateHasChanged();
                }

            }

            await Task.Delay(5 * 1000, token);
        }


    }

    
    private  CancellationTokenSource _mainCts = new CancellationTokenSource();
    private async Task ClearData()
    {
        is_request = false;
        is_requestRun = false;
        isLoading = true;
        total_count = 0;
        count = 0;
        EmptyText = "Нет записей для отображения";
        queryResults = Enumerable.Empty<IDictionary<string, object>>();
    }

    private async Task LoadTmpBegin()
    {

        _mainCts.Cancel();

        _mainCts = new CancellationTokenSource();
        // Новый токен для текущей сессии
        var localCts = CancellationTokenSource.CreateLinkedTokenSource(_mainCts.Token);

        var token = localCts.Token;

        var dropResult = await dbService.ExecuteData($"truncate TABLE {name_tmptab}");
        await ClearData();

        is_request = false;
        is_requestRun = false;
        isLoading = true;
        total_count = 0;
        count = 0;
        EmptyText = "Нет записей для отображения";
        queryResults = Enumerable.Empty<IDictionary<string, object>>();
        StateHasChanged();
        string count_query = $"SELECT count(1) as CNT FROM {name_tmptab}";
        TabService.ChangeLoadState(1, Tab);

        var FillTempTable_Comp = new TaskCompletionSource<bool>();

        
        var FillTempTableRez = FillTempTable(count_query, token, FillTempTable_Comp);


        if (repositoryInfo.TypeObject != "GET_PROCEDURE")
        {
            var ScanFirstPageRez = await ScanFirstPage(count_query, token, FillTempTable_Comp.Task);


            if (!FillTempTableRez.IsCompleted && !token.IsCancellationRequested)
            {
                count = ScanFirstPageRez;
                // total_count = ScanFirstPageRez;
                TabService.ChangeLoadState(2, Tab);
                await FilterAplyAsyncTmp(token);
                is_requestRun = true;
            }

            var SelCnTempTableRez = SelCnTempTable(count_query, token, FillTempTable_Comp.Task);
        }
           // await Task.WhenAll(FillTempTableRez, SelCnTempTableRez);

           
        

    }
  
    private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1); 

    private async Task RunChild(CancellationToken cancellationToken)
    {
        bool entered = false;
        try
        {
            await _semaphore.WaitAsync(cancellationToken); // Ожидаем захвата семафора
            entered = true;

            // Выполняем тело метода Child1
            await FilterAplyAsyncTmp(cancellationToken);
        }
        catch (OperationCanceledException)
        {
            Console.WriteLine("Процесс отменён.");
        }
        catch (Exception ex)
        {
            Console.WriteLine("Ошибка выполнения Child1: " + ex.Message);
        }
        finally
        {
            if (entered)
            {
                _semaphore.Release(); // Освобождаем семафор после завершения
            }
        }
    }

    private readonly object _syncLock = new object();
    private string filterSql_w = "";
    private async Task LoadDataAsync(LoadDataArgs args)
    {
        var localCts = CancellationTokenSource.CreateLinkedTokenSource(_mainCts.Token);

        var token = localCts.Token;

        bool error_tt = false;
        if (FilterClearDataAsync_run)
        {
            return;
        }
        string orderBy="";
        string filterSql="";
        orderBy = args.OrderBy;

        /*признак пересчета локального фильтра*/
        if (isFiltering || is_request || FilterClearDataAsync_w || FilterAply || FilterAplyTmp)
        {
            filterprm.Clear();
            (filterprm, filterSql_w, filterSql) = await GridService.ConvertFilterDescriptorToSql(args.Filters, repositoryInfo.Column);
            Console.WriteLine("LoadDataAsync 2" + Tab.CodeObj);

        }           
        Reloadargs = args;


        if (EmptyText == "Переход недоступен" || EmptyText == "Не введены связанные значения" || EmptyText == "Не введены обязательные параметры")
        {   
            Console.WriteLine("LoadDataAsync 3" + Tab.CodeObj);
            return;
        }

        skip = args.Skip ?? 0;
        take = args.Top ?? pageDefault; 
        string curReloadPage = skip.ToString() + ";" + take.ToString() + ";" + orderBy + ";";

        if ((isFiltering || FilterClearDataAsync_w ||  FilterAply) && filterSqlOld != filterSql ||
             is_requestRun && ReloadPage != curReloadPage && !FilterClearDataAsync_w || 
             is_request || FilterAplyTmp)
        {
            if ((FilterClearDataAsync_w || isFiltering || FilterAply) && filterSqlOld != filterSql)
            {
                ReloadPage = "-1;-1;-1;";
                Reloadargs.Skip = 0;
                Reloadargs.Top = pageDefault;
                args.Skip = 0;
                args.Top = pageDefault;
            }
            pageDefault = args.Top ?? pageDefault;
            skip = args.Skip ?? 0;
            take = pageDefault;

            
            if (skip==0 && grid!=null)
            {
                grid.CurrentPage = 0;
            }

            orderByOld = orderBy;
            if (isFiltering || is_request || FilterClearDataAsync_w || FilterAply || FilterAplyTmp)
            {
                filterSqlOld = filterSql;

            }

            ReloadPage = curReloadPage;           
            var timequery_s = DateTime.Now;
            if (Tab.Kind!=TabPageKind.Modal)
            {
                Console.WriteLine("LoadDataAsync 5" + Tab.CodeObj);
                Tab.isLoading = true;

            }
            isLoading = true;
            selectedItems.Clear();

            if (Tab.Kind != TabPageKind.Modal)
            {
                TabService.ChangeSelectedRow(null, globalFilter, Tab);
                StateHasChanged();
            }
            Console.WriteLine("LoadDataAsync 6" + Tab.CodeObj);
            if (is_request)
            {
                is_requestRun = true;
                Console.WriteLine("LoadDataAsync 7" + Tab.CodeObj);
            }

           


            Console.WriteLine("LoadDataAsync 8" + Tab.CodeObj);
            LogInfo lg = new LogInfo
                {
                    _globalFilter = globalFilter,
                    _dbname = GetDataSource(repositoryInfo.Dbname_Query),
                    _query = chng_query,
                    _is_tempory = repositoryInfo.is_tempory,
                    _name_tmptab = name_tmptab,
                    _skip = skip, _take = take, 
                    _filter = filterSql, _orderBy = orderBy
                };

            string jsonString = GetLogJSon(lg);

            bool success = false;
            string errorMessage = "";
            decimal logid = 0;

            (success, errorMessage, logid)=await _logService.LogI(Security.User.CODE, repositoryInfo.AppCode, Tab.Index.ToString(), 
                                                                 Tab.PathIndex, "Q", jsonString, Tab.CodeObj, Tab.Main_App_Code, Security.User.SessionId, Security.User.SessionInfo);

            var result = new PaginationResult
                {
                    Items = Enumerable.Empty<IDictionary<string, object>>(),
                    Count = 0,
                    ErrorMessage = ""
                };

            if(error_tt)
            {
                Tab.isLoading = false;
                isLoading = false;
               
                is_request = false;
                error_tt=false;              
                return;
            }
           

            result = await dbService.GetDataPagination2(
            baseSql: chng_query,
            skip: skip,
            take: take,
            parameters: globalFilter,
            filterprm: filterprm,
            filter: filterSql_w,           
            orderBy: orderBy,
            cancellation_Token: token);

            if (string.IsNullOrEmpty(result.ErrorMessage))
            {              
                TimeSpan timequery = DateTime.Now - timequery_s;
                totalTime=" (" + $"{timequery.TotalSeconds:F2} сек." + ")";
                lock (_syncLock)
                {
                    queryResults = result.Items;

                    //?  count = result.Count;

                    selectedItems.Clear();
                }
                if (Tab.Kind != TabPageKind.Modal)
                {
                    TabService.ChangeSelectedRow(null, globalFilter, Tab);
                }
                (success, errorMessage) = await _logService.LogS(logid, jsonString);
               
                is_load = true;
                
            }
            else
            {
                (success, errorMessage) = await _logService.LogE(logid, result.ErrorMessage);

                await DialogService.OpenAsync<ErrorGrid>("Ошибка получения страницы",
                                 new Dictionary<string, object> { { "_errorMessage", result.ErrorMessage } },
                                 new DialogOptions { Draggable = true, Resizable = true }
                
               );
              

            }                                


            Tab.isLoading = false;
            isLoading = false;
           
            count = result.Count;
            if (!repositoryInfo.is_tempory || total_count<result.Count)
            {
                total_count = result.Count;
            }
           
            StateHasChanged();
           
            is_request = false;


        }
    }
    //для экспорта
    private async Task ExportData(RadzenProfileMenuItem args)
    {
        ExportType exportType = ExportType.None_Type;
        ExportScope scope = ExportScope.None_Type;
        if (Enum.TryParse<ExportGrid>(args.Value, out var action))
        {
            switch (action)
            {
                case ExportGrid.CSV_All:
                    exportType = ExportType.CSV;
                    scope = ExportScope.All;
                    break;
                case ExportGrid.CSV_Cur:
                    exportType = ExportType.CSV;
                    scope = ExportScope.CurrentPage;
                    break;
                case ExportGrid.XLS_All:
                    exportType = ExportType.XLSX;
                    scope = ExportScope.All;
                    break;
                case ExportGrid.XLS_Cur:
                    exportType = ExportType.XLSX;
                    scope = ExportScope.CurrentPage;
                    break;
            }
        }
        isLoading = true;

        LogInfo lg = new LogInfo
                {
                    _globalFilter = globalFilter,
                    _dbname = GetDataSource(repositoryInfo.Dbname_Query),
                    _query = chng_query,
                _is_tempory = repositoryInfo.is_tempory,
                    _name_tmptab = name_tmptab,
                    _skip = skip, _take = take, 
                    _filter = filterSqlOld, _orderBy = orderByOld
                };

        string jsonString = GetLogJSon(lg);

        bool success = false;
        string errorMessage = "";
        decimal logid = 0;
        string operType=string.Empty;

        if (exportType == ExportType.CSV && scope == ExportScope.All)
        {
            operType = "CSV";
        }
        if (exportType == ExportType.CSV && scope == ExportScope.CurrentPage)
        {
            operType = "CSVPG";
        }
        if (exportType == ExportType.XLSX && scope == ExportScope.All)
        {
            operType = "XLS";
        }
        if (exportType == ExportType.XLSX && scope == ExportScope.CurrentPage)
        {
            operType = "XLSPG";
        }        


        (success, errorMessage, logid)=await _logService.LogI(Security.User.CODE, repositoryInfo.AppCode, Tab.Index.ToString(), 
                                                     Tab.PathIndex, operType, jsonString, Tab.CodeObj, Tab.Main_App_Code, Security.User.SessionId, Security.User.SessionInfo);



        await Task.Run(async () =>
        {
            try
            {
                if (count == 0)
                {
                    //await DialogService.Alert("Нет данных для экспорта", "", new AlertOptions() { OkButtonText = "OK" });
                    return;
                }

                string filterSql_w1 = string.Empty;
                string filterSql = string.Empty;
                const int maxRowsPerSheet = 1048575; // max - 1  для заголовка
                filterprm.Clear();
                (filterprm, filterSql_w1, filterSql) =  await GridService.ConvertFilterDescriptorToSql(grid.Query.Filters, repositoryInfo.Column);
                Stream stream;
                string fileExtension;
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string fileName;

                switch (exportType)
                {
                    case ExportType.XLSX:
                        if (scope == ExportScope.All)
                        {
                            stream = await _excelService.ExportGridToExcel(
                                repositoryInfo.Column,
                                repositoryInfo.Dbname_Query,
                                chng_query,
                                count,
                                globalFilter,
                                filterSql,
                                grid.Query.OrderBy);
                            fileName = $"{repositoryInfo.Title}_{timestamp}.xlsx";
                        }
                        else
                        {
                            stream = await _excelService.ExportDataToExcel(repositoryInfo.Column, queryResults);
                            fileName = $"{repositoryInfo.Title}_{timestamp}_Page.xlsx";
                        }
                        break;
                    case ExportType.CSV:
                        if (scope == ExportScope.All)
                        {
                            stream = await _excelService.ExportGridToCSV(
                                repositoryInfo.Column,
                                repositoryInfo.Dbname_Query,
                                chng_query,
                                count,
                                globalFilter,
                                filterSql,
                                grid.Query.OrderBy);
                            fileExtension = count > maxRowsPerSheet ? "zip" : "csv";
                            fileName = $"{repositoryInfo.Title}_{timestamp}.{fileExtension}";
                        }
                        else
                        {
                            stream = await _excelService.ExportDataToCSV(repositoryInfo.Column, queryResults);
                            fileName = $"{repositoryInfo.Title}_{timestamp}_Page.csv";
                        }
                        break;
                    default:
                        throw new ArgumentException("Неподдерживаемый тип экспорта");
                }

                using var streamRef = new DotNetStreamReference(stream: stream);
                await JSRuntime.InvokeVoidAsync("downloadFileFromStream", fileName, streamRef);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Export error: {ex.Message}");
                (success, errorMessage) = await _logService.LogE(logid, ex.Message);

                await DialogService.Alert($"Ошибка при экспорте: {ex.Message}", "Ошибка", 
                        new AlertOptions { Draggable = true, Resizable = true }
                );
            }
            finally
            {
                (success, errorMessage) = await _logService.LogS(logid, jsonString);
                isLoading = false;
            }
        });
    }
    //Для внешних кнопок
    private async Task ReloadDef()
    {
        ReloadPage = "-1;-1;-1;";
        filterSqlOld = "";
        Reloadargs.Skip = 0;
        Reloadargs.Top = pageDefault;
        await LoadDataAsync(Reloadargs);
        is_request = false;
        Tab.isLoad = false;
    }
    private void OnQueryResultsChanged((IEnumerable<IDictionary<string, object>>, int) data)
    {
        queryResults = data.Item1;
        count = data.Item2;
        StateHasChanged();
    }
    //Для переходов
    private bool check_linked_services()
    {
        bool rez = false;
        Dictionary<string, object> row = new Dictionary<string, object>();
        if (selectedItems.Count() > 0)
        {
            foreach (var r in selectedItems[0])
            {
                row.Add(r.Key, r.Value);
            }
        }
        check_linked_servicesR("MAIN", ref rez, row);
        return !rez;
    }
    private void check_linked_servicesR(string code_parent, ref bool rez, Dictionary<string, object> row)
    {
        foreach (var mm in repositoryInfo.MenuGrid.Where(m => m.CODE_PARENT == code_parent/*"MAIN"*/))
        {
            var children = repositoryInfo.MenuGrid.Where(m => m.CODE_PARENT == mm.CODE_CHILD).ToList();
            if (children.Any())
            {
                check_linked_servicesR(mm.CODE_CHILD, ref rez, row);
                if (rez)
                {
                    return;
                }
            }
            else
            {
                @if (GridService.MenyCheck(mm.VISIBILITYFORMULA, row, globalFilter))
                {
                    rez = true;
                    return;
                }
            }
        }
    }
    void OnExpandM(TreeExpandEventArgs args)
    {
        if (args.Value is EWA.Models.REP_Models.MenuInfo singleMenu)
        {
            var id = $"{singleMenu.CODE_OBJECT}#{singleMenu.CODE_PARENT}";
            expandedState[id] = true;
        }
    }
    void OnCollapseM(TreeEventArgs args)
    {
        if (args.Value is EWA.Models.REP_Models.MenuInfo singleMenu)
        {
            var id = $"{singleMenu.CODE_OBJECT}#{singleMenu.CODE_PARENT}";
            expandedState[id] = false;
        }
    }
    private RadzenTree tree;

    async Task OnChangeM(TreeEventArgs args)
    {

        var selectedItems = args.Value as EWA.Models.REP_Models.GRID_Menu;
        Console.WriteLine("OnChangeM" + Tab.CodeObj);
        if (selectedItems != null)
        {
             

            var typeObject = selectedItems.TYPE_OBJECT;
            if (typeObject != "FOLDER")
            {
                while (Tab.SubTabs.Count(x => !x.Value.is_complete) > 0)
                {
                    selectedItems = null;
                    tree.ClearSelection();
                  // StateHasChanged();
                    return;
                    await Task.Delay(50);
                }
                if (typeObject != "REPORT_TEMPLATE")
                {

                    await AddTab("EditGrid", selectedItems.CODE_OBJECT, selectedItems.NAME_OBJECT, selectedItems.ID, true, selectedItems.VISIBILITYFORMULA,
                                 selectedItems.KIND, TabPageKind.Standart);

                }
                else
                {
                    await AddTab("FReport", selectedItems.CODE_OBJECT, selectedItems.NAME_OBJECT, selectedItems.ID, true, selectedItems.VISIBILITYFORMULA,
                             selectedItems.KIND, TabPageKind.Standart);
                }
            }
            else
            {
                var id = $"{selectedItems.CODE_OBJECT}#{selectedItems.CODE_PARENT}";
                if (!expandedState.TryGetValue(id, out var current))
                {
                    current = false;
                }
                expandedState[id] = !current;
            }
            selectedItems = null;
            tree.ClearSelection();
            StateHasChanged();
        }
    }
    RenderFragment BuildTreeItem(EWA.Models.REP_Models.GRID_Menu item) => __builder =>
    {
        var children = repositoryInfo.MenuGrid.Where(m => m.CODE_PARENT == item.CODE_CHILD && item.AUTO_MODE != 1).ToList();
        Dictionary<string, object> row = new Dictionary<string, object>();
        if (selectedItems.Count() > 0)
        {
            foreach (var r in selectedItems[0])
            {
                row.Add(r.Key, r.Value);
            }
        }
        //bool isExpanded = expandedState.ContainsKey(item.CODE_CHILD) && expandedState[item.CODE_CHILD];
        bool isExpanded = expandedState.ContainsKey($"{item.CODE_OBJECT}#{item.CODE_PARENT}") && expandedState[$"{item.CODE_OBJECT}#{item.CODE_PARENT}"];
        if (children.Any())
        {
            <RadzenTreeItem Text="@item.NAME_OBJECT" Value="@item" Expanded="@isExpanded" >
                <Template>
                    <RadzenIcon Icon="@MenuGrid.GetIconForType(item.TYPE_OBJECT)" style="width: 20px; margin-right: 6px;" />
                    <span style="white-space: normal; word-wrap: break-word; overflow-wrap: break-word;">
                        <b>@context.Text</b>
                    </span>
                </Template>
                <ChildContent>
                    @foreach (var child in children.Where(x=>x.AUTO_MODE!=1))
                    {
                        @if (GridService.MenyCheck(child.VISIBILITYFORMULA, row, globalFilter))
                        {
                            @BuildTreeItem(child)
                        }   
                    }
                </ChildContent>
            </RadzenTreeItem>
        }
        else
        {
            @if (GridService.MenyCheck(item.VISIBILITYFORMULA, row, globalFilter))
            {
                <RadzenTreeItem Text="@item.NAME_OBJECT" Value="@item">
                    <Template>
                        <RadzenIcon Icon="@MenuGrid.GetIconForType(item.TYPE_OBJECT)" style="width: 20px; margin-right: 6px;" />
                        <span style="white-space: normal; word-wrap: break-word; overflow-wrap: break-word;">
                            @context.Text
                        </span>
                    </Template>
                </RadzenTreeItem>
            }
        }
    };
}

