﻿@page "/reps"

@rendermode InteractiveServer
@inject EWA.Services.RepService.ClipboardService ClipboardService
@inject RepService _repService
@inject AMLService _amlService
@inject ContextMenuService ContextMenuService
@inject DialogService DialogService
@inject IJSRuntime JSRuntime

@using System.Collections.Generic
@using System.Data
@using Microsoft.Extensions.Configuration
@using System.Text.Json
@using Radzen.Blazor
@using Oracle.ManagedDataAccess.Client
@using Radzen
@using EWA.Services
@using EWA.Controllers
@using EWA.Models
@using EWA.Components.Pages
@using System.Net.Http
@using System.Reflection
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Mvc
@using System.Globalization

@if (objects == null && SelectedObject == null)
{
    <p><em>Loading...</em></p>
}
else
{
    <RadzenRow Gap="1rem">
        <RadzenColumn Size="12" SizeMD="6">
            <RadzenDataGrid @ref="objGrid"
                            TItem="Object"
                            Density="Density.Compact"
                            Data="@objects"
                            Style="width:100%; height:420px;"
                            ColumnWidth="200px"
                            AllowFiltering="true"
                            AllowPaging="true" PageSize="10" AllowSorting="true"
                            @bind-Value="@SelectedObject">
                <HeaderTemplate>
                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween">
                        <RadzenStack Orientation="Orientation.Horizontal" Gap="0rem">
                            <RadzenColumn>
                                <RadzenButton Icon="Add"
                                              Variant="Variant.Text"
                                              ButtonStyle="ButtonStyle.Base" />
                            </RadzenColumn>
                            <RadzenColumn>
                                <RadzenButton Icon="Edit"
                                              Variant="Variant.Text"
                                              ButtonStyle="ButtonStyle.Base" />
                            </RadzenColumn>
                            <RadzenColumn>
                                <RadzenButton Icon="refresh"
                                              Click="@LoadDataGrid"
                                              Variant="Variant.Text"
                                              ButtonStyle="ButtonStyle.Base" />
                            </RadzenColumn>
                        </RadzenStack>
                    </RadzenStack>
                </HeaderTemplate>
                <Columns>
                    <RadzenDataGridColumn Property="Code" Title="Код объекта" Width="120px" />
                    <RadzenDataGridColumn Property="Name" Title="Наименование объекта" Width="200px" />
                    <RadzenDataGridColumn Property="Type" Title="Тип объекта" Width="200px" />
                </Columns>
            </RadzenDataGrid>
        </RadzenColumn>
        <RadzenColumn Size="12" SizeMD="6">
            <RadzenCard Variant="Variant.Outlined" Style="height: 100%;">
                <RadzenStack Gap="1rem">
                    <RadzenCard Variant="Variant.Text" class="rz-background-color-primary-lighter rz-color-on-primary-lighter">
                        Объект:
                        <b>@SelectedObject.FirstOrDefault()?.Name</b>
                    </RadzenCard>
                    <RadzenTabs>
                        <Tabs>
                            <RadzenTabsItem Text="Запросы">
                                <RadzenDataGrid AllowFiltering="true" AllowPaging="true" AllowSorting="true" Data="@(SelectedObject.FirstOrDefault()?.OQuery)">
                                    <Columns>
                                        <RadzenDataGridColumn Property="SQL_Query" Title="Запрос выборки" />
                                        <RadzenDataGridColumn Property="UPD_Query" Title="Запрос для обновления" />
                                        <RadzenDataGridColumn Property="INS_Query" Title="Запрос для вставки" />
                                        <RadzenDataGridColumn Property="DEL_Query" Title="Запрос для удаления" />
                                    </Columns>
                                </RadzenDataGrid>
                            </RadzenTabsItem>
                            <RadzenTabsItem Text="Список колонок">
                                <RadzenDataGrid AllowFiltering="true" AllowPaging="true" AllowSorting="true" Data="@(SelectedObject.FirstOrDefault()?.OColums)">
                                    <Columns>
                                        <RadzenDataGridColumn Property="Code_column" Title="Код колонки" />
                                        <RadzenDataGridColumn Property="Name_column" Title="Наименование колонки" />
                                        <RadzenDataGridColumn Property="Type_column" Title="Тип колонки" />
                                    </Columns>
                                </RadzenDataGrid>
                            </RadzenTabsItem>
                            <RadzenTabsItem Text="Список параметров">
                                <RadzenDataGrid AllowFiltering="true" AllowPaging="true" AllowSorting="true" Data="@(SelectedObject.FirstOrDefault()?.OParametrs)">
                                    <Columns>
                                        <RadzenDataGridColumn Property="Code_par" Title="Код параметра" />
                                        <RadzenDataGridColumn Property="Name_par" Title="Наименование параметра" />
                                        <RadzenDataGridColumn Property="Type_par" Title="Тип параметра" />
                                    </Columns>
                                </RadzenDataGrid>
                            </RadzenTabsItem>

                        </Tabs>
                    </RadzenTabs>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
    </RadzenRow>
}

@code {

    public class Object
    {
        public int ID { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public List<Object_query> OQuery { get; set; } = new List<Object_query>();
        public List<Object_metacol> OColums { get; set; } = new List<Object_metacol>();
        public List<Object_metapar> OParametrs { get; set; } = new List<Object_metapar>();
    }

    public class Object_query
    {
        public string SQL_Query { get; set; }
        public string UPD_Query { get; set; }
        public string INS_Query { get; set; }
        public string DEL_Query { get; set; }
    }
    public class Object_metacol
    {
        public string Code_column { get; set; }
        public string Name_column { get; set; }
        public string Type_column { get; set; }

    }
    public class Object_metapar
    {
        public string Code_par { get; set; }
        public string Name_par { get; set; }
        public string Type_par { get; set; }

    }

    [Inject]
    private EWA.Services.AMLService _amlserv { get; set; }

    RadzenDataGrid<Object> objGrid;
    IList<Object> selectedItems = new List<Object>();
    IList<Object> SelectedObject { get; set; }
    IList<Object> objects = new List<Object>();
    //IQueryable<Object> objects = new Queryable<Object>();

    protected async Task LoadDataGrid()
    {
        /*var result = await _amlserv.GetDataOBJ();

        if (result != null)
        {
            foreach (var item in result)
            {
                List<REP_Models.ColumnMetadata> col_jsn = new List<REP_Models.ColumnMetadata>();
                List<REP_Models.ParamMetadata> param_jsn = new List<REP_Models.ParamMetadata>();
                List<Object_metacol> m_cols;
                List<Object_metapar> m_param;
                var type = item.Value.type;
                var id = item.Value.id;
                var code = item.Value.code;
                var name = item.Value.name;
                var coldata = item.Value.cols;
                var pardata = item.Value.param;
                var sqlq = item.Value.sqlq;
                var sqli = item.Value.sqlins;
                var sqlu = item.Value.sqlupd;
                var sqld = item.Value.sqldel;



                if (coldata != null)
                {
                    col_jsn = JsonSerializer.Deserialize<List<REP_Models.ColumnMetadata>>(coldata);
                }
                if (pardata != null)
                {
                    param_jsn = JsonSerializer.Deserialize<List<REP_Models.ParamMetadata>>(pardata);
                }
                m_cols = col_jsn.Select(col => new Object_metacol
                    {
                        Code_column = col.CODE,
                        Name_column = col.NAME,
                        Type_column = col.DATATYPE
                    }).ToList();
                m_param = param_jsn.Select(col => new Object_metapar
                    {
                        Code_par = col.CODE,
                        Name_par = col.NAME,
                        Type_par = col.DATATYPE
                    }).ToList();

                Object temp_obj = new Object
                    {
                        ID = id,
                        Code = code,
                        Name = name,
                        Type = type,
                        OColums = m_cols,
                        OParametrs = m_param,
                        OQuery = new List<Object_query>()
                    };

                Object_query tempq = new Object_query
                    {
                        SQL_Query = sqlq,
                        INS_Query = sqli,
                        UPD_Query = sqlu,
                        DEL_Query = sqld
                    };
                temp_obj.OQuery.Add(tempq);

                objects.Add(temp_obj);


            }
            SelectedObject = new List<Object>() { objects.FirstOrDefault() };

        }
        */
        await objGrid.Reload();
        StateHasChanged();

    }


    protected override async Task OnInitializedAsync()
    {
        //await base.OnInitializedAsync();
        //await LoadDataGrid();

        objects = new List<Object>();
        SelectedObject = new List<Object>();


        //SelectedObject = new List<Object>() { objects.FirstOrDefault() };
        //await objGrid.Reload();
        StateHasChanged();
    }

    void OnCellContextMenu(Object args)
    {
        //var columnName = args.OColums;
        //var columnCode = args.Column.Property;
        //var columnValue = args.Data.ContainsKey(columnCode) ? args.Data[columnCode] : "что-то пошло не так";

        /* var contextMenuItems = new List<ContextMenuItem>
         {

         new ContextMenuItem() {
             Text = "Посмотреть детально",
             Value = new { Key = 2, ColumnName = columnName, ColumnValue = columnValue },
             Icon = "visibility"},
         new ContextMenuItem() {
             Text = "Копирвать",
             Value = new { Key = 3, ColumnName = columnName, ColumnValue = columnValue },
             Icon = "visibility"}
         };*/

        //ContextMenuService.Open(args, contextMenuItems, OnMenuItemClick);
    }

    void OnMenuItemClick(MenuItemEventArgs args)
    {
        var conData = args.Value;
        if (conData != null)
        {
            var properties = conData.GetType().GetProperties();
            var keyProperty = properties.FirstOrDefault(p => p.Name == "Key");
            if (keyProperty != null)
            {
                var keyValue = keyProperty.GetValue(conData);
                if (keyValue.ToString() == "2")
                {
                    var columnNameProp = properties.FirstOrDefault(p => p.Name == "ColumnName");
                    var columnValueProp = properties.FirstOrDefault(p => p.Name == "ColumnValue");
                    var columnName = columnNameProp.GetValue(conData);
                    var columnValue = columnValueProp.GetValue(conData);
                    DialogService.Open<GLOBAL.ColumnDetail>("Детально", new Dictionary<string, object>
                    {    { "ColumnName", columnName }, { "Value", columnValue }});
                }
                else if (keyValue.ToString() == "3")
                {
                    var columnValueProp = properties.FirstOrDefault(p => p.Name == "ColumnValue");
                    var columnValue = columnValueProp.GetValue(conData);
                    ClipboardService.WriteTextAsync(columnValue.ToString());
                }
            }
        }

    }
}
