﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" DoublePass="true" ReportInfo.Created="08/27/2025 18:19:19" ReportInfo.Modified="09/02/2025 14:10:36" ReportInfo.CreatorVersion="2025.2.0.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    private void MasterData1_AfterPrint(object sender, EventArgs e)
    {
      DataSourceBase dataset = Report.GetDataSource(&quot;ANK_IP2&quot;);
      if (dataset.RowCount == 0)
      {
        Memo20.Text = &quot;Нет&quot;;
      }
    }

    private void CurAccountValueMonth_AfterPrint(object sender, EventArgs e) 
    {
      DataSourceBase dataset = Report.GetDataSource(&quot;CurAccountValueMonth&quot;);
      if (dataset.RowCount == 0)
      {
        Memo134.Text = &quot;Нет&quot;;
      }
    } 
  }
}
</ScriptText>
  <Dictionary>
    <OracleDataConnection Name="smdev" ConnectionString="rijcmlqcqSAZRx+/kmWTGGD9Hx6z1ravUjPQ4Y+NahKxl6jwchlnOv2XzTWJBJGc/Dj7cULc0XCc2lGVsd8b+mImXS/qcCp6b+6gKWX53h2RmJm9rU=">
      <ProcedureDataSource Name="Служебная информация(SuspTransactions)" DataType="System.Int32" Enabled="true" TableName="Служебная информация(SuspTransactions)" SelectCommand="ANKENP_ANK_SERVICEINFO_SUSPTRN">
        <Column Name="CARD_CREATE_DATE" DataType="System.String"/>
        <Column Name="SUM_CURR" DataType="System.String"/>
        <Column Name="OPERATION_FORM_CODE" DataType="System.String"/>
        <Column Name="SUSPICION_CODE" DataType="System.String"/>
        <Column Name="CARD_NUM_DATE" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="Служебная информация(FinTransactions)" DataType="System.Int32" Enabled="true" TableName="Служебная информация(FinTransactions)" SelectCommand="ANKENP_ANK_SERVICEINFO_FINTRNS">
        <Column Name="FINTRANSACTIONS" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="Служебная информация" DataType="System.Int32" Enabled="true" TableName="Служебная информация" SelectCommand="ANKENP_ANK_SERVICEINFO">
        <Column Name="CLIENTSERVICEVALUE" DataType="System.String"/>
        <Column Name="CLIENTFURTHERACTIONS" DataType="System.String"/>
        <Column Name="TYPECLIENTFURTHERACTIONS" DataType="System.String"/>
        <Column Name="STANDARDCLIENTFURTHERACTIONS" DataType="System.String"/>
        <Column Name="OTHERCLIENTFURTHERACTIONS" DataType="System.String"/>
        <Column Name="ZAPROSCLIENTACTIONSRISK" DataType="System.String"/>
        <Column Name="FINTRCLIENTACTIONSRISK" DataType="System.String"/>
        <Column Name="OTKAZFINTRANSCLIENTACTIONSRISK" DataType="System.String"/>
        <Column Name="OTKAZDOGCLIENTACTIONSRISK" DataType="System.String"/>
        <Column Name="OTKAZDBOCLIENTACTIONSRISK" DataType="System.String"/>
        <Column Name="OTHERCLIENTACTIONSRISK" DataType="System.String"/>
        <Column Name="CLIENTFURTHERVERIFICATION" DataType="System.String"/>
        <Column Name="STOPOPERPOST" DataType="System.String"/>
        <Column Name="DOVEROPER" DataType="System.String"/>
        <Column Name="NEGATIVGOODWILL" DataType="System.String"/>
        <Column Name="ASSOCIATEDNEGATIVGOODWILL" DataType="System.String"/>
        <Column Name="CLIENTIDENTRESULTS" DataType="System.String"/>
        <Column Name="DATAINFOCLIENTIDENTRESULTS" DataType="System.String"/>
        <Column Name="ACTIVECLIENTIDENTRESULTS" DataType="System.String"/>
        <Column Name="NOCONTACTCLIENTIDENTRESULTS" DataType="System.String"/>
        <Column Name="NOACTIVECLIENTIDENTRESULTS" DataType="System.String"/>
        <Column Name="SPRAVKACLIENTIDENTRESULTS" DataType="System.String"/>
        <Column Name="OTHERCLIENTIDENTRESULTS" DataType="System.String"/>
        <Column Name="CLIENTRESULTS" DataType="System.String"/>
        <Column Name="SIGNFATCA" DataType="System.String"/>
        <Column Name="TERRORPEPSANCTION" DataType="System.String"/>
        <Column Name="CLIENTTOTALRISK" DataType="System.String"/>
        <Column Name="TOTALRISKFACTORS" DataType="System.String"/>
        <Column Name="CLIENTPROFILERISK" DataType="System.String"/>
        <Column Name="PROFILERISKFACTORS" DataType="System.String"/>
        <Column Name="CLIENTOPERRISK" DataType="System.String"/>
        <Column Name="OPERRISKFACTORS" DataType="System.String"/>
        <Column Name="CLIENTGEORISK" DataType="System.String"/>
        <Column Name="GEORISKFACTORS" DataType="System.String"/>
        <Column Name="DT_FILL" DataType="System.String"/>
        <Column Name="FIO" DataType="System.String"/>
        <Column Name="UPDATEDT" DataType="System.String"/>
        <Column Name="USERLOG" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
        <ProcedureParameter Name="P_USERLOG" DataType="126" Expression="[P_USERLOG]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="PartnerCompanies" DataType="System.Int32" Enabled="true" TableName="PartnerCompanies" SelectCommand="ANKENP_ANK_PC">
        <Column Name="NAME" DataType="System.String"/>
        <Column Name="UNP" DataType="System.String"/>
        <Column Name="REGPLACE" DataType="System.String"/>
        <Column Name="GROUND" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
        <ProcedureParameter Name="P_USERLOG" DataType="126" Expression="[P_USERLOG]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="CurAccountValueMonth" DataType="System.Int32" Enabled="true" TableName="CurAccountValueMonth" SelectCommand="ANKENP_ANK_AVG_MONT_TURNOVER">
        <Column Name="CUR" DataType="System.String"/>
        <Column Name="DEBT" DataType="System.String"/>
        <Column Name="DEBT_NAL" DataType="System.String"/>
        <Column Name="CRED" DataType="System.String"/>
        <Column Name="CRED_NAL" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="ANK_IP1" DataType="System.Int32" Enabled="true" TableName="ANK_IP1" SelectCommand="ANKENP_ANK_BASIC">
        <Column Name="JURIDIC_PERSON_NAME" DataType="System.String"/>
        <Column Name="PREV_SURNAME" DataType="System.String"/>
        <Column Name="CITIZENSHIP" DataType="System.String"/>
        <Column Name="RESIDENCEPLACE" DataType="System.String"/>
        <Column Name="CORRESPONDENCEADDRESS" DataType="System.String"/>
        <Column Name="BIRTHDATE" DataType="System.String"/>
        <Column Name="BIRTHPLACE" DataType="System.String"/>
        <Column Name="ENTRYVISAINFO" DataType="System.String"/>
        <Column Name="DOC" DataType="System.String"/>
        <Column Name="PERSONALNUMBER" DataType="System.String"/>
        <Column Name="REGISTRATIONAUTHORITY" DataType="System.String"/>
        <Column Name="REGISTRATIONINFOLAST" DataType="System.String"/>
        <Column Name="PAYNUMBER" DataType="System.String"/>
        <Column Name="TAXOFFICENAME" DataType="System.String"/>
        <Column Name="ANNUALREVENUE" DataType="System.String"/>
        <Column Name="CONTACTPHONE" DataType="System.String"/>
        <Column Name="EMAIL" DataType="System.String"/>
        <Column Name="WEBSITE" DataType="System.String"/>
        <Column Name="OKVED" DataType="System.String"/>
        <Column Name="OTHERBANKSACCOUNTS" DataType="System.String"/>
        <Column Name="OTHERBANKSACCOUNTSSFM" DataType="System.String"/>
        <Column Name="PURPOSEOFESTABLISHINGRELATIONS" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
        <ProcedureParameter Name="P_USERLOG" DataType="126" Expression="[P_USERLOG]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="ANK_IP2" DataType="System.Int32" Enabled="true" TableName="ANK_IP2" SelectCommand="ANKENP_ANK_BASIC_CONTRACTS">
        <Column Name="CONTRACTCREDITACCOUNTTYPE" DataType="System.String"/>
        <Column Name="CONTRACTSALARYACCOUNTTYPE" DataType="System.String"/>
        <Column Name="CONTRACTDEPOSITACCOUNTTYPE" DataType="System.String"/>
        <Column Name="CONTRACTEMONEYACCOUNTTYPE" DataType="System.String"/>
        <Column Name="CONTRACTACCOUNTTYPE" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="ANK_IP9" DataType="System.Int32" Enabled="true" TableName="ANK_IP9" SelectCommand="ANKENP_ANK_FIN_INSTITUT_R">
        <Column Name="NAME" DataType="System.String"/>
        <Column Name="REGISTRATIONINFO" DataType="System.String"/>
        <Column Name="LOCATION" DataType="System.String"/>
        <Column Name="PAYNUMBER" DataType="System.String"/>
        <Column Name="GROUND" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
        <ProcedureParameter Name="P_USERLOG" DataType="126" Expression="[P_USERLOG]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="ANK_IP8" DataType="System.Int32" Enabled="true" TableName="ANK_IP8" SelectCommand="ANKENP_ANK_INFORM_FL_R">
        <Column Name="NAME" DataType="System.String"/>
        <Column Name="CITIZENSHIP" DataType="System.String"/>
        <Column Name="BIRTH" DataType="System.String"/>
        <Column Name="REGISTRATIONPLACE" DataType="System.String"/>
        <Column Name="DOC" DataType="System.String"/>
        <Column Name="PAYNUMBER" DataType="System.String"/>
        <Column Name="GROUND" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
        <ProcedureParameter Name="P_USERLOG" DataType="126" Expression="[P_USERLOG]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="ANK_IP5" DataType="System.Int32" Enabled="true" TableName="ANK_IP5" SelectCommand="ANKENP_ANK_RELATED_PERSONS">
        <Column Name="CONTRACTORSJUR" DataType="System.String"/>
        <Column Name="CONNECTEDANDPARTNERORG" DataType="System.String"/>
        <Column Name="INFOREPRESENTATIVE" DataType="System.String"/>
        <Column Name="INFOBENEFITPRIOBRETATEL" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
        <ProcedureParameter Name="P_USERLOG" DataType="126" Expression="[P_USERLOG]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="ANK_IP4" DataType="System.Int32" Enabled="true" TableName="ANK_IP4" SelectCommand="ANKENP_ANK_FIN_INSTITUT_CO">
        <Column Name="NAME" DataType="System.String"/>
        <Column Name="REGISTRATIONINFO" DataType="System.String"/>
        <Column Name="LOCATION" DataType="System.String"/>
        <Column Name="PAYNUMBER" DataType="System.String"/>
        <Column Name="GROUND" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
        <ProcedureParameter Name="P_USERLOG" DataType="126" Expression="[P_USERLOG]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="ANK_IP3" DataType="System.Int32" Enabled="true" TableName="ANK_IP3" SelectCommand="ANKENP_ANK_INFORM_FL_CO">
        <Column Name="NAME" DataType="System.String"/>
        <Column Name="CITIZENSHIP" DataType="System.String"/>
        <Column Name="BIRTH" DataType="System.String"/>
        <Column Name="RESIDENCE" DataType="System.String"/>
        <Column Name="DOC" DataType="System.String"/>
        <Column Name="REGISTRATIONINFO" DataType="System.String"/>
        <Column Name="PAYNUMBER" DataType="System.String"/>
        <Column Name="GROUND" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
        <ProcedureParameter Name="P_USERLOG" DataType="126" Expression="[P_USERLOG]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="ANK_IP11" DataType="System.Int32" Enabled="true" TableName="ANK_IP11" SelectCommand="ANKENP_ANK_FIN_INSTITUT_BP">
        <Column Name="NAME" DataType="System.String"/>
        <Column Name="REGISTRATIONINFO" DataType="System.String"/>
        <Column Name="LOCATION" DataType="System.String"/>
        <Column Name="PAYNUMBER" DataType="System.String"/>
        <Column Name="GROUND" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
        <ProcedureParameter Name="P_USERLOG" DataType="126" Expression="[P_USERLOG]" Direction="Input"/>
      </ProcedureDataSource>
      <ProcedureDataSource Name="ANK_IP10" DataType="System.Int32" Enabled="true" TableName="ANK_IP10" SelectCommand="ANKENP_ANK_INFORM_FL_BP">
        <Column Name="NAME" DataType="System.String"/>
        <Column Name="CITIZENSHIP" DataType="System.String"/>
        <Column Name="BIRTH" DataType="System.String"/>
        <Column Name="REGISTRATIONPLACE" DataType="System.String"/>
        <Column Name="DOC" DataType="System.String"/>
        <Column Name="REGISTRATIONINFO" DataType="System.String"/>
        <Column Name="PAYNUMBER" DataType="System.String"/>
        <Column Name="GROUND" DataType="System.String"/>
        <Column Name="P_OUT" DataType="FastReport.Variant"/>
        <ProcedureParameter Name="P_OUT" DataType="121" Direction="Output"/>
        <ProcedureParameter Name="P_DATE" DataType="106" Expression="[P_DATE]" Direction="Input"/>
        <ProcedureParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[P_CODE_SUBJECT]" Direction="Input"/>
        <ProcedureParameter Name="P_USERLOG" DataType="126" Expression="[P_USERLOG]" Direction="Input"/>
      </ProcedureDataSource>
      <TableDataSource Name="ankjp_one_value" DataType="System.Int32" Enabled="true" SelectCommand="select&#13;&#10;  dwh.ANK_ENP.GET_UPD_ANK (:P_CODE_SUBJECT, :p_date) as UPD_ANK&#13;&#10;from DUAL&#13;&#10;">
        <Column Name="UPD_ANK" DataType="System.String"/>
        <CommandParameter Name="P_CODE_SUBJECT" DataType="126" Expression="[p_code_subject]"/>
        <CommandParameter Name="p_date" DataType="106" Expression="[p_date]"/>
      </TableDataSource>
      <TableDataSource Name="IDC_u_d" DataType="System.Int32" Enabled="true" SelectCommand="select trunc(sysdate) as curr_date, SYS_CONTEXT('DWH', 'USER') user_name from dual">
        <Column Name="CURR_DATE" DataType="System.DateTime" Format="Date"/>
        <Column Name="USER_NAME" DataType="System.String"/>
      </TableDataSource>
    </OracleDataConnection>
    <Parameter Name="p_date" DataType="System.DateTime" Expression="DateTime.Today"/>
    <Parameter Name="p_code_subject" DataType="System.String" Expression="&quot;ISPE61111331&quot;"/>
    <Parameter Name="p_userlog" DataType="System.String" Expression="&quot;DWH&quot;"/>
  </Dictionary>
  <ReportPage Name="Page1" Landscape="true" PaperWidth="297" PaperHeight="210" Watermark.Font="Arial, 60pt">
    <DataBand Name="MasterData1" Top="122.96" Width="1047.06" Height="555.59" CanGrow="true" CanShrink="true" CanBreak="true" DataSource="ANK_IP1">
      <TextObject Name="Memo5" Left="3.78" Width="1039.37" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;1. Фамилия, Собственное имя, Отчество: &lt;/b&gt; [ANK_IP1.JURIDIC_PERSON_NAME]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo3" Left="3.78" Top="26.46" Width="1039.37" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;2. Предыдущие фамилии (при регистрации рождения, заключения брака(ов) и др.): &lt;/b&gt; [ANK_IP1.PREV_SURNAME]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo4" Left="3.78" Top="52.91" Width="1039.37" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;3. Гражданство: &lt;/b&gt; [ANK_IP1.CITIZENSHIP]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo14" Left="3.78" Top="78.11" Width="1039.37" Height="21.8" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;4. Место жительства и (или) место пребывания: &lt;/b&gt; [ANK_IP1.RESIDENCEPLACE]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo13" Left="3.78" Top="132.28" Width="1039.37" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;5. Дата рождения: &lt;/b&gt; [ANK_IP1.BIRTHDATE]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo12" Left="3.78" Top="158.74" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;6. Место рождения: &lt;/b&gt; [ANK_IP1.BIRTHPLACE]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo6" Left="3.78" Top="185.2" Width="1039.37" Height="37.8" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;7. Сведения о въездной визе (для лиц, не являющихся гражданами Республики Беларусь, если международным договором не предусмотрен безвизовый въезд на территорию Республики Беларусь), в том числе срок , на который выдана виза: &lt;/b&gt; [ANK_IP1.ENTRYVISAINFO]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo24" Left="3.78" Top="227.09" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="8. Реквизиты документа, удостоверяющего личность (наименование, серия и номер документа, кем и когда он выдан, срок действия данного документа):" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo7" Left="3.78" Top="279.69" Width="1039.37" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;9. Идентификационный номер: &lt;/b&gt;[ANK_IP1.PERSONALNUMBER]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo23" Left="3.78" Top="249.77" Width="1039.37" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[ANK_IP1.DOC]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo15" Left="3.78" Top="381.73" Width="1039.37" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;11. Учетный номер плательщика: &lt;/b&gt; [ANK_IP1.PAYNUMBER]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo17" Left="3.78" Top="468.66" Width="1039.37" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;13. Контактные телефоны (домашний/мобильный): &lt;/b&gt; [ANK_IP1.CONTACTPHONE]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo18" Left="3.78" Top="498.9" Width="1039.37" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;14. Адрес электронной почты: &lt;/b&gt; [ANK_IP1.EMAIL]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo19" Left="3.78" Top="529.13" Width="1039.37" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;15. Адрес сайта в Интернете: &lt;/b&gt; [ANK_IP1.WEBSITE]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo585" Left="3.78" Top="105.83" Width="1043.15" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;4.1. Адрес для направления корреспонденции: &lt;/b&gt; [ANK_IP1.CORRESPONDENCEADDRESS]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo586" Left="3.78" Top="411.97" Width="1043.15" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;11.1. Наименование и номер налоговой инспекции: &lt;/b&gt; [ANK_IP1.TAXOFFICENAME]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo587" Left="3.78" Top="442.21" Width="1043.15" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;12. Объем выручки за предыдущий отчетный год в эквиваленте долл. США, рассчитанном по среднему курсу НБ РБ: &lt;/b&gt; [ANK_IP1.ANNUALREVENUE]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo67" Left="3.78" Top="355.28" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;10.2. Сведения о последней регистрации изменений и дополнений:&lt;/b&gt; [ANK_IP1.REGISTRATIONINFOLAST]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo68" Left="3.78" Top="306.14" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" Text="&lt;b&gt;10. Сведения о государственной регистрации (регистрационный номер, дата регистрации, наименование регистрирующего органа):&lt;/b&gt;" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <TextObject Name="Memo16" Left="3.78" Top="328.82" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;10.1. Сведения о регистрации: &lt;/b&gt;[ANK_IP1.REGISTRATIONAUTHORITY]" Padding="2, 1, 2, 1" Font="Arial, 10pt" TextRenderType="HtmlParagraph"/>
      <DataHeaderBand Name="Header2" Width="1047.06" Height="118.96" CanGrow="true">
        <PictureObject Name="Picture1" Left="389.29" Width="268.35" Height="105.83" Fill.Color="White" ShiftMode="WhenOverlapped" ImageFormat="Bmp" Image="Qk0WOgEAAAAAADYAAAAoAAAA+AAAAGwAAAABABgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7/AAAADwwPEA4QEA4QEA0QAAAAuri6////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////AAAAAAAAAAAAAAAAAAAAAAAAjIuM////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////GxobAAAABAIEBAIEBAIEAAAAZ2Zn////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////QkBCAAAABAIEBAIEBAIEAAAAQT9B//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7///////////////////////////////////////////////////7/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////Z2dnAAAABAIEBAIEBAIEAAAAGRgZ//////////////////////////////////////////////////////7/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////0M7QhIKEWlhaREJEPTw9Q0JDV1VXb25vkpGSurm67ezt//////7/////////9/X3eHd4PTw9LCssNzU3W1lbjIuMoaChn56fnpyenJucm5mbkI+Q8fDx7OrsZWRlPz0/GxkbAQABAAAAAAAAAgACGRcZS0pLlJOU////////////////wcDBkpGSmZiZmZiZmZiZlpWWo6Gj////////////trW2lpSWmZiZmZiZmZiZlpWWurm6mZeZAAAABAIEBAIEBAIEAQABAAAA////trS2UU9RExITAQABIB4gfXx9////////////////////////////////19fXjIqMZ2dnYV9hcG9wkpGSxcTF////////////////////7u7uiYiJmZiZmZiZmZiZlpSWsrGy////////////rKusl5WXmZiZmZiZmZiZkY+R6Ofo////////6efpkI6QmZiZmZiZmZiZlJSUtLO0////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////n52fpaOlpaOlpaOlpaSlpaOlpaSlpaOlpaOlpaOlpaSlpaOlpaSlpaOlpaOlpaOlpaSlpaOlpaSlpaOlpaOlpaOlpaSlpaOlpaSlpaOlpaOlpaOlpaSlpaOlpaSlpaOlpaOlpaOlpaSlpaOlpaSlpaOlpaOlpaOlpaSlpaOlpaSlpaOlpaOlpaOlpqSmpaOlo6GjysfK////////////393fBAIEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////////////trS2AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAt7e3ysvKAAAAAAAAAAAAAAAAAgACAgACAAAAAAAAAAAAAAAAAAAAeHZ4////////fXx9AAAAAAAAAAAAAAAAAAAAAAAA////////////ZmRmAAAAAAAAAAAAAAAAAAAADgwO0c/RAAAABAIEBAIEBAIEAwEDAgECBwYHAAAAAAAAAAAAAAAAAAAAAAAAAAAA6unq//////////7/////0tHSDgwOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYF9g////////////////AAAAAAAAAAAAAAAAAAAAAAAA////////////AAAAAAAAAAAAAAAAAAAAAAAAODY4////////wcHBAAAAAAAAAAAAAAAAAAAAZmVm////qKWor62vsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wsK2wsK6wq6mr0c3R//////////////7/////lZKVe3l7fn1+f3x/fnx+f31/fnx+f3x/fn1+f3x/fnx+f3x/fnx+f3x/fn1+f3x/fnx+f3x/fnx+f3x/fn1+f3x/fnx+f3x/fnx+f3x/fn1+f3x/fnx+f3x/fnx+f3x/fn1+f3x/fnx+f3x/fnx+f3x/fn1+f3x/fnx+f3x/fnx+f3x/f31/fnx+fnx+f3x/enh6ko+S////////////AAAAAAAAAwEDBAIEBAIEBAIEAQABAAAAAAAAAAAAAAAAAwEDAAAA1tXW////////AAAAAgECAwEDBAIEBAIEAwEDAAAAAAAAAAAAAwIDBAEEBAIEBAIEAAAAkpKS3NzcAAAAAAAAAAAAAAAAAAAAAAAAAAAABAIEAwEDBAIEAgECAAAAcnFy////sK+wAAAABAIEBAIEBAIEAwEDAAAA////////////mJeYAAAABAIEBAIEBAIEAgACAAAA7u3uAAAABAIEBAIEBAIEBAEEAwEDAQABBAEEBAEEBAEEBAIEBAIEAwEDAAAAAAAA////////////q6mrAAAAAAAAAwEDBAEEBAIEBAIEBAIEBAIEAwEDAgACAAAAAAAA2NbY////////nJucAAAABAIEAwEDBAIEAAAApaSl////////AAAAAwEDBAIEBAIEBAEEAwIDAAAA4N/g////vr2+AAAABAIEBAIEBAIEAAAAoqKi//7/cW9xfnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fnx+fnt+fXx9fnt+fXx9fnt+fXx9fnt+fXx9fnt+fXx9fnt+fXx9fnt+fXx9fnt+fXx9fnx+fXx9fnt+fXx9fnx+fXx9fnt+fXx9fnx+fXx9fnt+fXx9fnx+fXx9fnt+fXx9fnx+fXx9fnt+fXx9fnx+fXx9fnt+fXx9f3x/bmxu7ezt////////////////////vLm8e3p7hIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGChIGEgoGCg4GDeHZ4////////////AAAAAwEDBAEEBAIEBAIEAQABEhASfHx8eHd4TU1NDQwNAAAAAAAAgoGC////zc3NAAAAAwEDBAIEBAIEAwEDAAAAgoKCf39/ERARAAAAAwEDBAIEBAIEAAAAa2pr4+PjAAAADg0OZ2dnq6qr09LT5eTlWlpaAAAAAwEDBAEEAwEDAQABAAAA////1tXWAAAABAIEBAIEBAIEAwEDAAAA6ejp////////vby9AAAABAEEBAIEBAIEAwEDAAAA/v3+AAAAAwEDBAIEBAIEBAIEBAEEBAEEAAAAAAAAAAAAAwEDBAEEBAIEAwEDAAAAWFdY////9vT2AAAAAgACAwEDBAIEBAEEAwEDAAAAAAAAAQABBAEEAwEDAwEDAAAAAAAA6unq////////AAAAAQABBAEEAwEDAAAAJyYn////3NvcAAAABAIEBAIEBAIEBAIEBAEEAAAANzU3////uLi4AAAABAIEBAIEAwEDAAAA1NTU4+Ljend6g4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEf35/kY+R////////////////////////2tnaeXd5g4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDhIGEg4GDd3V37ezt////////IB4gAAAABAEEBAIEBAIEAAAAOTc5////////////////////n56feHZ4////+vn6AAAAAwEDAwEDBAIEAwEDAAAAube5////m5mbAAAAAwEDBAIEBAIEAAAAQkBC////4uLi////////////////////////NjU2AAAAAwEDBAEEAwEDAAAA////+/v7AAAAAwEDBAIEBAIEBAIEAAAAwsHC////////5eXlAAAAAwEDBAIEBAIEAwEDAAAA7OvsAAAAAQABBAIEBAIEBAIEBAEEAAAAUVBRsrGyBgUGAAAAAwIDBAEEBAEEAgECAAAA////bWttAAAABAIEAwEDBAEEAwIDAAAAZGJklZSVAAAAAQABBAEEAwEDBAIEAAAAFBMU////////fn1+AAAABAEEBAIEAwEDAAAA/Pz8raytAAAABAEEBAIEBAIEBAIEBAEEAwIDAAAA6Ojoube5AAAABAIEBAIEAwEDAAAA////xcTFfHp8hIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDhIKEg4KDend61NHU/////////////////////////Pv8bmxue3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7e3l7cnByx8XH////////0M/QAAAAAQABBAEEBAEEBAEEAAAAAAAAAAAAFxYXWlhatbS1//////////7/////Pz8/AAAAAwEDBAEEAwIDBAMEAAAAAAAALi0uAAAABAEEBAIEBAIEAAAAGBYY////////////////////////////////IB4gAAAABAEEAwEDAAAAEQ8R////////AAAAAgACBAIEBAIEBAIEAAAAnZyd////////////AAAAAwEDBAIEBAIEBAEEAAAAzc3NHx4fAAAABAEEBAIEBAIEAAAABQMF////////+fj5AAAAAwEDAwEDBAEEBAIEAAAA29vbLy0vAAAAAwEDBAIEBAEEAAAALy0v////////4+LjAAAAAwEDBAIEAwEDAwEDAAAAw8HD////////AAAAAgACBAIEAwEDAAAAi4mLhYSFAAAABAIEBAIEBAEEBAEEBAEEAwIDAAAAQT9Bx8bHAAAABAIEBAIEAgACAAAA////oqCidXN1enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7eXh5e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6e3h7enh6end6dHN0////////////////////////////////wsDCxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxsTGxcPFycfJ8vDy////////////vLu8AAAAAAAAAAAAAgACBAEEAwEDAwEDAAAAAAAAAAAAAAAAp6Sn////////////Hx4fAAAAAAAAAAAAAwEDAwEDAgACAAAABAEEBAIEBAIEBAIEAQABAAAA////////////////////////6unqHRsdAAAAAwEDAwIDAAAARkVG/vz+////////DAsMAAAABAIEBAIEBAIEAAAAaWhp////////////AAAAAQABBAIEBAIEBAEEAAAAo6KjSUdJAAAABAIEBAIEBAIEAAAAOjk6////////////GRgZAAAABAIEBAIEBAEEAAAAs7KzLCosAAAABAIEBAIEBAIEAAAAdXR1////////////HRwdAAAABAIEBAIEBAIEAAAAaGho////////XFtcAAAAAwEDBAIEAAAAGhcaVlVWAAAABAIEBAEEAwIDAAAAAwEDBAEEAwEDAAAAe3l7AAAABAIEBAEEAAAAIB8g////1dPVzszOz83Pzs3Ozs3Oz87Pz87Pzs3Oz83Pzs3Ozs3Oz87Pz87Pzs3Oz83Pzs3Ozs3Oz87Pzs7Ozs3Oz83Pzs3Ozs3Oz87Pz87Pzs3Oz83Pzs3Oz83Pz87Pz87Pzs3Oz87Pzs3Oz83Pz87Pz87Pzs3Oz87Pzs3Oz87Pz87Pz87Pzs3Oz87Pzs3Oz87Pz87Pz87Pzs3O0M7Qzs3Oz87Pz87Pz87Pzs3Oz87Pzs3Oz87Pz87Pz87Pzs3Oz87PzczN397f////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////19fXDw0PkI6Q/fz9////////////jo2OOTc5AAAAAAAAAAAAAAAABAIEAwEDAwIDAQABAAAAi4mL////////////xsbGWFdYEhASAAAAAAAAAAAAAAAAAwEDBAIEBAIEBAIEAwEDAAAA//////////////7/////////////pKOkAAAAAwEDAwEDAQABVFNU////////////NTM1AAAABAIEBAIEBAIEAQABAAAA////////////BAMEAQABBAIEBAIEBAIEAAAAeXd5c3NzAAAABAIEBAIEBAIEAAAAJCEk////////////EhESAAAABAIEBAIEBAIEAAAApKOkX15fAAAABAEEBAIEBAIEAAAAMC4w////////////JSMlAAAABAIEBAIEBAEEAAAAQ0JD////////7+7vAAAABAIEAwEDBAEEAAAAAAAAAwEDBAIEAwEDAAAAaWhpAAAAAwIDAwEDAwEDAAAAAwEDBAIEAwEDAAAAU1JT////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////4+Pj29rb4uHi8vHy////////////////////////////////////////////////////////////////zs3OAAAAAAAAAAAAIiEij42P////////////////+Pf4vb29c3JzAAAAAwEDBAEEAwEDAwADAAAA9/b3////////wb/B////////////9PT0yMfIaGdoAAAAAwEDBAIEBAIEBAIEAAAA5uXmy8nL////////////////////////X15fAAAABAEEAwEDAAAAAQAB////////XFpcAAAABAIEBAIEBAIEBAEEAAAAAAAAjYyNUlFSAAAAAwEDBAIEBAIEBAEEAAAATktOnZ2dAAAABAEEBAIEBAIEAQABAQAB////////iYaJAAAABAIEAwEDBAIEAwEDAAAAt7a3y8rLAAAAAwEDBAIEBAIEAwEDAAAAnJqc////9fP1AAAAAwEDBAIEBAIEBAEEAAAAS0pL////////////Ozo7AAAABAEEBAIEBAEEAwEDBAIEBAIEAgACAAAA////AAAAAQABBAEEAwEDAwEDBAIEBAIEBAEEAAAAZmVmz87PwsHC0dDR9fT1//////////////////////////////////7//////v3+q6mrjIuMioiKoqGizMvM+fn5/////////////f39/Pz8+fj5////9/b3+fj5+vn6+vn6+vn6+vn69/b3/////////////fz99fP1+vn6+vn6+vn6+vn6+Pf4////+vj6+Pf4+vn6+vn6+vn6+vn6+Pf4////////////////////+Pj4+vn6+vn6+vn6+vn6+fj58/Lz////////////////////////////////////////////AAAAAAAAAwEDAwEDAwEDAwEDAwEDAwEDAwEDAwEDAwEDAwADBQMFJCIkdnV2//////////7/////////////////19bXTUpNAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFhQW1dXV////0M7QAAAAAwEDAwEDAwEDAwEDAAAAzszO////////////w8LDAAAABAEEAwEDAAAAAAAAAAAAHx0fcnBysrGy5eTl/v7+29jbAAAAAgACBAIEBAIEAwEDAAAAgH+A////////BQMFAAAAPTs9g4KDuLa41tXWn5+fAAAAAwEDBAIEBAIEBAEEAAAAzs3OKSgpAAAAOTc5d3Z3oqGit7a3srCyDw4PAAAABAIEBAEEAwIDAAAApKKk////gICAAAAABAIEBAIEBAIEBAIEBAEEAQABAAAAAAAAAwIDBAEEBAIEBAIEAwEDAAAAKSgpxsbGAAAABAEEBAIEBAIEAwEDAQABAAAAAAAAAAAAAwIDAwEDBAIEAwEDBAMEAAAA+Pf4////ISAhAAAAAwEDBAEEBAIEAwIDAAAAEA4QAAAAAQABBAIEBAIEBAIEAwEDAAAAkpKS////////////09LTAAAAAwEDBAIEBAIEBAIEBAIEBAEEAAAALSst////d3Z3AAAAAwEDBAIEBAIEBAIEBAIEBAEEAwIDAAAAAAAAAAAAAAAAAAAAAAAAOzk7pqWm//////////7/////////////b25vAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAe3l7a2prAAAAAAAAAAAAAAAAAAAAAAAAycfJ////////////AAAAAAAAAAAAAAAAAAAAAAAAEA4Qz87PAAAAAAAAAAAAAAAAAAAAAAAAdHJ0////////////aWdpAAAAAAAAAAAAAAAAAAAAAAAAODY4////////////////////////////////////////////VVRVAAAAAQABAAAAAQABAAAAAQABAAAAAQABAAAAAQABAAAAAAAAAAAAAAAAAAAAjoyO////////////////aWZpAAAAAAAAAQABAwEDBAEEAwIDBAIEAwEDBAEEAQABAAAATEpM////////bGtsAAAAAQABAAAAAQABAAAACAcI////////////tra2AAAABAIEBAIEBAIEBAEEAwEDAAAAAAAAAAAAAAAAAAAAAAAAAwEDAwEDBAIEBAEEBAIEAAAAXVxd////////amhqAAAAAAAAAAAAAAAAAAAAAAAABQMFAwEDBAIEBAEEAwEDAAAAuLa4kpGSAAAAAAAAAAAAAAAAAAAAAAAAAQABAwEDBAIEBAEEAwIDAAAAm5qb////qKeoAAAABAIEBAIEBAIEAwEDAwEDAwIDBAEEBAEEBAEEBAIEBAIEBAIEBAEEAAAAPz0/6enpAAAABAEEBAIEBAIEBAIEAwEDAwEDAwEDAwEDBAEEBAIEBAEEAwIDAAAARERE////////////AAAAAAAAAwIDAwEDBAEEAwEDAAAAAgACBAIEBAIEBAIEAwEDAAAAAAAA////////////////////HRsdAAAABAEEBAIEBAIEBAIEBAEEAAAAeHd4////////AAAAAgACBAIEBAIEBAIEBAIEBAIEBAEEAwEDBAIEBAIEBAEEBAEEAgECAAAAAAAAAAAAtbS1////////////ubi5AAAAAwEDBAIEBAIEBAIEBAEEAwEDAgACAwIDAwEDBAEEAwEDBAEEAAAAFBQU8vHyAAAAAwEDBAIEAwIDBAIEAAAAaGdo////////////Q0FDAAAABAIEAwEDBAIEAwEDAAAA////AAAAAgACBAIEAwIDBAIEAAAAFhUW//////7/////LSstAAAABAEEAwIDBAIEAwIDAAAAtLS0////////////////////////////////////////////uLe4AAAAAwEDBAIEAwIDBAIEAwIDBAIEAwIDBAIEAwIDBAIEAwIDBAEEAwEDAwADAAAAXVxd////////sbGxAAAAAwEDBAEEAwEDBAIEAwEDBAEEAAAAAAAAAAAAAwEDAgECAAAA////////////Dw8PAAAABAEEBAIEAwEDAAAAhYSF////////rKysAAAABAIEBAIEBAIEBAEEAwEDAgACAwEDBAEEBAEEAwEDAwEDAwEDBAEEBAEEAwEDAAAAAAAA4eDh////////raytAAAAAAAAAAAAAgACAwEDAwEDAwEDBAEEAwEDAAAAAAAAAAAA////2NfYAAAAAAAAAAAAAgACAwEDAwEDAwEDBAEEAwEDAQABAAAAAAAA////////zs3OAAAABAEEBAEEAwEDBAEEBQQFAAAAAAAAAwEDBAEEBAEEBAIEAwEDAgACAAAAv76/////AAAAAAAAAQABAwEDBAEEBAEEBAIEBAIEAwEDAwEDAQABAAAAAAAALS0t////////////////+vn6CgoKAAAAAQABAwEDBAEEBAIEBAIEBAEEBAEEAwEDAAAAAAAA2NfY////////////////////tLO0AAAAAwIDBAEEBAEEBAEEAwEDAAAAwL/A////////VlVWAAAAAwEDAwEDBAIEBAIEBAIEBAIEBAEEBAEEAwEDAwEDAwEDBAEEBAEEAwEDAAAAAAAAXFtc////////bWxtAAAABAIEBAIEBAIEBAIEAwEDAAAAAAAAAAAAAwEDAwEDBAIEAwIDBAEEAAAA////AAAAAgACAwIDBAIEAwIDAAAABwYH////////////oqKiAAAAAwEDBAIEAwIDBAIEAAAArKusTUxNAAAAAwIDBAIEAwIDBAEEAAAA+vn6////////AAAAAwADAwIDBAIEAwIDAwEDAAAA////////////////////////////////////////////////////AAAAAwADAwIDBAIEAwIDBAIEAwIDAwEDAAAAAAAAAwEDBAIEAwIDBAIEAwEDBQIFAAAAnZud////R0ZHAAAABAEEAwIDBAIEAwEDBQQFAAAACAYIIiAiDgsOAAAAAAAAAAAAh4eH////////7u3uAAAAAgACBAIEAwEDAgACAAAA/vz+////oqCiAAAABAIEBAIEBAEEAwIDAgACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMDAw5OPk////////////+vn6X15fOjk6EhASAAAAAAAAAAAAAAAAAAAAAAAAFhUWfHp8////////////aWlpQUBBFxUXAAAAAAAAAAAAAAAAAAAAAAAAAAAATEtM8fDx////////8/LzAAAAAAAAAAAAAAAAAAAAAAAAz87PGhkaAAAAAAAAAAAAAAAAAAAAAAAAmpia////////NjQ2HhweAgACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAASEdIwL/A////////////////////////////i4qLAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALy0v8vHy//////7/////////////////////AAAAAAAAAAAAAAAAAAAAAAAAAAAA////////////7OvsAAAAAAAAAAAAAwEDBAIEBAIEBAEEBAIEAwIDAAAAAAAAAAAAAQABBAEEBAEEAwIDAwIDAAAAVFJU////oaChAAAAAwEDBAIEBAIEBAEEAAAAwL/A////sbCxAAAAAwIDAwEDBAIEAwEDAAAApqWmX15fAAAABAEEAwIDBAIEAwEDAAAA6Obo////////////AAAABAIEAwEDBAIEAwIDAAAAQUBBurm6AAAABAEEAwIDBAIEAwEDAAAAl5aX////397fAAAAAwIDBAIEAwIDBAEEAAAAODc4////////////////////////////////////////////////////ODc4AAAABAIEAwIDBAIEAwIDAwEDAgECMTAxFhUWAAAAAAAABAIEAwIDBAIEAwEDAgACAAAA////Ojk6AAAAAwIDBAIEAwIDAwEDAAAA1NLU////////////////4uLilZOVVlVW////////////i4qLAAAABAEEAwIDAwEDAAAAPDo8////lZWVAAAABAIEBAIEBAEEAAAADw4P////////1tXWt7W3oZ+hlpWWnZydrKusz87P////////////////////////////////////////////////+Pf48PDw+/r7////////////////////////////////////////////+vr68fDx9/f3////////////////////////////////////////////////////////////8e/xpqamhYOFhYWFsrGy////////////////////////////////////8PDw4+Pj4ODg6+vr////////////////////////////////////////////////////0tLSn56fenp6amlqdHN0nZyd9fT1////////////////////////////////////////////////////////////////////////////////////////EhESAAAABAIEBAIEBAIEAwEDAAAAhIOE////raytAAAAAQABBAIEBAEEBAEEAwIDAAAApqWm////AAAAAAAAAwEDBAIEBAIEAQABAAAAeXh58vHyIiAiAAAABAEEAwIDBAEEAAAAOTc5ysnKAAAAAwEDBAIEAwIDBAEEAAAAjoyO////////////MTAxAAAABAIEAwIDBAIEAgACAAAA////AAAAAwEDBAEEAwIDBAIEAAAAODY4////mZiZAAAABAEEAwIDBAIEAwEDAAAAmZiZ////////////////////////////////////////////////////mJeYAAAAAwEDBAIEAwIDBAIEAwEDAAAA////////+/n7EA4QAAAABAIEAwIDBAIEAwEDAAAAnZydbm1uAAAABAEEAwIDBAIEAQABDAoM4eHhyMfIx8fHyMfIyMjIz87P1dXV2djZw8LD7+7v////////IyMjAAAAAwEDBAEEAwIDAAAA0tDSlZOVAAAABAIEBAIEBAEEAAAAKCYo////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////ExETAAAABAIEBAIEBAEEAwEDAAAA////////////29nbAAAAAwEDBAIEBAIEBAEEAAAAAAAA////1dLVAAAAAAAAAwEDBAIEBAIEAQABAAAAAAAAAAAAAwEDAwIDBAIEAwIDAwEDAAAA////AAAAAwEDAwEDBAIEAwIDAwEDAAAAAAAAAAAAAAAAAwIDAwEDAwEDBAIEAwIDBAEEAAAA0c/RLCssAAAAAwIDBAIEAwIDAwADAAAA////VlVWAAAAAwEDBAIEAwIDBAEEAAAA/fz9////////////////////////////////////////////////////+vn6AAAABAIEAwEDBAIEAwIDBAEEAAAArayt////////oJ+gAAAAAwEDBAIEAwIDBAEEAAAAQT9BxMTEAAAAAwIDBAIEAwIDBAEEAQABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAATkxO////////////AAAAAgACAwEDBAIEAAAAEA4QlZSVAAAABAIEBAIEBAEEAAAARkVG////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////VlRWAAAABAIEBAIEBAIEAwEDAAAA1tXW//////7/////MC4wAAAABAIEBAIEBAIEAwEDAAAApKOk////5uTmFxcXAAAAAAAAAAAAAAAAAgACBAEEAwEDAwIDBAIEAwIDBAIEAwEDAAAAx8bHPDs8AAAABAEEAwIDBAIEAwIDAwEDAQABAgACAQABAwIDAwEDBAIEAwIDBAIEAwEDAAAAZWVllJSUAAAABAIEAwIDBAIEAwEDAAAA0c/REA8QAAAABAEEAwIDBAIEAAAAJSQl////////////////////////////////////////////////////////////FxYXAAAABAEEAwIDBAIEAwEDAAAALS4txsXGv7+/Dw4PAAAABAEEAwIDBAIEAwIDAAAAFhQW////AAAAAgACAwIDBAIEAwIDBAEEAAAAAAAAAAAAAAAAAgECBAEEAwIDBAIEAQABAAAA////////////qaipAAAAAwEDBAEEBAEEAAAABwYHAgACBAIEBAIEBAEEAAAAYmJi////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////tbW1AAAABAEEBAIEBAIEBAEEAAAAQD9A////////////dXR1AAAABAEEBAIEBAIEBAEEAAAAQkJC////////////8vHyjo6OTUtNGRcZAAAAAAAAAAAAAAAAAwIDBAIEAwIDBAIEAAAAXVxdpaWlAAAAAwEDBAIEAwIDBAIEAwEDAwEDAAAAAQABAAAAAgACAwEDBAIEAwIDBAEEAQABAAAA////AAAAAwEDBAIEAwIDBAIEAAAAbmxuQ0NDAAAAAwEDBAIEAwIDAAAAFxYX////////////////////////////////////////////////////////////eXl5AAAAAwEDBAIEAwIDBAIEAwEDAAAAAAAAAAAAAAAABAEEAwIDBAIEAwIDBAEEAAAALy8v////ZWVlAAAABAEEAwEDBAIEAAAAMC4w////////////AAAAAgECBAEEAwIDBAEEAAAA9fT1//////7/////PTs9AAAAAwIDBAEEBAIEAgACBAEEBAIEBAIEBAEEAAAAgYCB////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////AAAAAgECBAEEBAIEBAEEAwIDAAAAn52f////////S0lLAAAAAwEDBAIEBAIEBAEEAAAAEA0Q////////+/v7jIuM////////////////////ysjKERARAQABAwIDBAIEAwEDAgACAAAA////AAAABAEEAwIDBAIEAwEDAwEDBwYHBQIFAAAAAgACAAAAAgACAwEDBAIEAwEDBAIEAAAA9fT1CAcIAQABAwEDBAIEAwIDAQABAAAA////AwIDAAAAAwEDBAIEAwEDAAAAHBsc////////////////////////////////////////////////////////2dnZAAAABAEEAwIDBAIEAwIDBAIEAwIDBAIEAwEDBAEEAwIDBAIEAwIDBAEEAwEDAAAAo6Gj////////AAAAAAAABAIEAwEDBAEEAAAASUZJ8fDxtrO2AAAABAEEAwEDBAIEAwEDAAAA+/v7////////////////AAAAAQABAwEDBAEEBAEEBAIEBAIEBAIEBAIEAAAAn56f////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////NjQ2AAAABAEEBAIEBAIEAwEDAwMDAAAAKykrWFdYAAAAAwEDBAEEBAIEBAIEAwEDAAAAGhga//////7/////Hx4fAAAACwkLVFNUi4mLra2ttbO1HRsdAAAABAIEAwIDBAIEAwEDAAAA6OjoGhkaAAAABAIEAwIDBAEEAQABAQAB////////////4uHiAAAABAEEAwIDBAIEAwEDAAAAiIeIdHF0AAAABAEEAwIDBAIEAwEDAAAA3t/e/fv9AAAAAgACAwEDBAEEBAMEAAAALy0v////////////////////////////////////////////////////////AAAAAAAABAIEAwIDBAIEAwIDBAIEAwEDAAAAAAAAAQABAAAAAAAAAAAAAAAAUFBQ////////////0tDSAAAAAAAABAEEAwEDBAEEAAAAAAAAAAAABAEEAwEDBAIEAwEDAAAAEA4Q////////////////////w8LDAAAAAwEDBAIEBAIEBAIEBAIEBAIEBAIEAAAAvLy8////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////l5SXAAAABAEEBAIEBAIEAwEDAwEDAwEDAAAAAAAAAwEDBAEEBAIEBAIEBAIEBAEEAAAAUE5Q////////////ubm5AAAAAAAAAAAAAAAAAAAAAAAAAAAABAIEAwIDBAIEAwEDBAEEAAAAmJeYfn1+AAAAAwIDBAIEAwEDBAIEAAAA09LT////////////AAAAAAAABAIEAwIDBAIEAAAAHRwd3t3eAAAAAwEDBAIEAwIDBAIEAAAAeXh5////wcDBAAAABAIEAwEDBAEEAwEDAAAARkVG////////////////////////////////////////////////////WVdZAAAAAwEDBAIEAwIDBAIEAwEDAAAAFRQVCAYIAgACAAAAEhASSUdJw8LD/////v7+//////7/////7u3uEhESAAAAAAAAAwIDBAEEAwEDBAEEAwIDBAIEAwEDAAAAAAAA19bX////////////////////////WFZYAAAAAwEDBAIEBAIEBAIEBAIEBAEEAAAA2djZ//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////9vX2AAAAAwEDBAIEAwEDAQABAAAAAwADBAIEBAIEBAIEBAIEBAIEBAIEBAEEAwEDAAAA1NTU////////////////AAAAAAAAAAAAAwEDBAEEBAIEBAIEAwIDBAIEAwEDAwEDAAAAAAAA397f2NfYAAAABAEEAwIDBAEEAwIDAAAAb29v////////////VlZWAAAAAwIDBAIEAwIDBAEEAAAA////AAAAAwADAwIDBAIEAwIDAAAAFBQU////////f39/AAAABAIEAwEDBAEEAwIDAAAAZWNl////////////////////////////////////////////////uLi4AAAABAEEAwIDBAIEAwIDBAEEAAAA////////////////////////////////////////////////////////srKyKyorAAAAAAAAAAAAAAAAAAAAAAAAAAAAGRgZ3t3e/////v7+////////////////////////AQABAAAAAAAAAAAAAAAAAAAAAAAAAAAA9vb2////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////9vX27evt4uHi393f29rb3Nvc393f4+Hj6+nr8vLy/fz9/////////////////////////////////////////////////////////////////////////Pv8+Pf4+vn6////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////GBYYAAAABAEEAQABGxsbQ0JDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAjo2O////////////////////pqWmMzMzCwkLAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKygry8rL////////AAAAAAAAAAAAAAAAAAAAAAAAAAAA////////////tbS1AAAAAAAAAAAAAAAAAAAAAAAAmZeZUE5QAAAABAIEAwIDBAIEAwEDAAAA+Pf4////////Ojk6AAAAAAAAAAAAAAAAAAAAAAAAaGZo////////////////////////////////////////////////AAAAAgACBAIEAwIDBAIEAwEDAgACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZmRm////////////////////////////////wsHClJOUgoCChoaGoJ+g6Ojo////////////////////////////////////////5OLkmpqaoaGhoqGioqGioqGioqGilZWV////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////2tjatrW2mZeZfnt+c3FzdnR2d3V3d3Z3eHZ4eHZ4eHZ4eXd5eHZ4d3V3dnR2dXN1c3JzfHp8i4mLmpmarKqsvr2++/n7////////wL7At7W3rKqsnJqckI2QgX+BdnR2dXN1dXN1dnR2dnN2dXN1dXN1dHJ0dnR2hYKFl5WXrqyuyMXI5ePl////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////mZeZAAAAAwIDAgECAAAAycjJ////5uXmoqKic3JzU1JTRENEVFNUgoKC9/b3////////////////////////////////////////+/v74ODgysrKw8PDysnK4eHh////////////////////7Ovs4uLi4+Pj4+Pj4+Pj4+Pj29rb////////////////39/f4+Tj4+Pj4+Tj4+Pj4uHi9PP0paOlAAAAAwIDBAIEAwEDBAIEAAAAl5aX////////////39/f5OPk5OPk5OPk5OPk5eTl0M7Q////////////////////////////////////////////////OTc5AAAAAwIDBAIEAwIDBAIEAwEDAwADAQABAgACAQABAgACAQABAgACAAAABgQG//////////////////////////7///////////////////////////////////////////////////////////////////7/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////2dfZm5mbdHJ0enh6fHl8fXt9fXx9fXt9fHp8enh6enh6eHZ4f3x/g4GDhIKEhIKEhIKEhIKEhIGEgoGCg4CDgH6AgH2Afnx+eXd5c3Jzk5CTenh6e3h7fXp9fXx9f31/gH6AgoCChIKEg4GDhIKEg4GDg4GDfXt9enh6e3h7enh6end6eXd5eHZ4eXd5eHZ4dHJ0j42Puri66unq////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////CAYIAAAABAIEAwIDAAAABgUGeHd4x8XH+vr6////////////////+vr6vby9x8XH//////////////////////////////////////////////////////////////7///////////7///////////////////////////////////////7/////////////////////////////////////AAAABAEEAwIDBAIEAwEDAAAAODU4//////////////////////////////////////////7/////////////////////////////////////////////mZiZAAAABAIEAwIDBAIEAwIDBAIEAwEDBAIEAwIDBAIEAwIDBAIEAwIDBAEEAAAA5+bn////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////19XXgH6Ad3V3fXt9e3l7dnV2dnN2jImMoqCisrGywL/AzcrN1NPU5ubmube5fn1+hIKEg4GDhIGEhIKEg4GDg4GDg4GDg4GDg4GDfHp8dnR2lZOVoZ6hlJGUdHN0f31/g4GDg4GDg4GDg4GDg4GDg4GDg4GDg4GDgH6Az83P4uHi0dDRy8nLvby9r66vnpyehYOFdXJ1d3V3eXd5enh6c3JzhYKF5OLk//////7/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7OvsAAAAAAAAAwEDAwEDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAJiUm////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////IiAiAAAABAIEAwIDBAIEAgACAAAA////////////////////////////////////////////////////////////////////////////////////////+vn6AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAf31/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////uba5cnByfHt8dnR2kY+RxcPF7evt////////////////////////////sK6we3h7g4GDg4GDgoCCfHp8e3l7g4GDg4GDhIKEgn+Cc3FztbK1/////////////////v7+oZ6hdnR2goCChIKEgn+CfHl8fHl8goCCg4GDg4CDenh6ysjK////////////////////////////4+PjvLq8hYOFd3R3fnx+cnByura6////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7+3vCAgIAAAAAAAAAwEDBAEEBAEEAwEDAwIDAwEDAwEDAwEDAwIDAwEDAAAA////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////fn1+AAAAAAAAAAAAAAAAAAAAAAAArq2u////////////////////////////////////////////////////////////////////////////////////////RURFMzEzNTQ1NTQ1NTQ1NTQ1NTQ1NTQ1NTQ1NTQ1NTQ1NTQ1NTQ1NTQ1Ly4vWFdY////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////9vX2c3Jzd3V3n5yf9vX2////////////////////////////////8vLyf31/fHp8g4KDfXt9dnN2ioeKwL/A29nbfnx+g4GDgX+Bend66+nr////////////////////////////ycfJdXJ1gn+CgoCC4d7huLe4hIGEd3V3f3x/goCCdnR2kY+R////////////////////////////////////5uXmjImMe3p7c3Fzy8nL////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////k5GTAAAAAAAAAAAAAAAAAAAAAgACAwIDBAEEAwEDBAEEBAEEAAAArq2u////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////6enpbGtsd3Z3d3d3d3Z3d3d3cXBxqqmq////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////vLq8c3Jzvbq9////////////////////////////////////1tPWc3BzgX+Bf31/c3FzqKWo7+7v////////kI2QgH6AgoCCe3h79/b3////////////////////////////////////wb/BeXd5fXx9op+i////////5+XnnZudc3Jzgn+CfXx9e3l78e/x////////////////////////////////////npuefnx+gH6A////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////ysnKd3Z3PDs8Dw4PAAAAAAAAAAAAAAAAAAAAAAAAAAAAPTs9////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////rautcm9y8O7w////////////////////////////////ycbJcnBygoCCdnN2mZeZ+vn6////////////zcvNd3V3g4CDdXJ18vHy////////////////////////////////////////////lpSWf3x/dXN14t/i////////////8O7wkI6QeXZ5gX+BdHJ05uXm////////////////////////////////zcvNenh6eXV5////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////9vX25ubm2dnZzMzMvLu8n52f////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////1NLUcnFyiIWI5uPm////////////////////////xMDEdHJ0gX+BeHZ42NbY////////////////////g4GDgX+BdnR21dLV////////////////////////////////////////////////+ff5dXN1f31/kpCS////////////////////yMfIdHJ0gX+Bc3Bz5OLk////////////////////////09HTfnt+e3l7nJqc////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////o6Cjc3JzdXJ1fnt+o6Gjuri6ysjK1dPVube5dnN2fHt8h4SH//3/////////////////////3tzed3V3fnx+n52f////////////////////////////////////////////////////////q6irfXx9dXN18fHx////////////////////9PP0f31/fnx+dXN1zcvN09HTx8THtbO1nJqceHZ4eXZ5eXh5g4CD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////09DTjImMc3FzdXN1dHJ0dHJ0eXh5fXt9e3p7kY6R////////////////////////////p6WnfXt9eHV48/Lz////////////////////////////////////////////////////////8/Lzd3V3fHp8u7m7////////////////////////////hYOFf31/enh6eHZ4dXN1d3V3eHZ4eHZ4d3V3tLK0//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7/4eDh09HTxcLFe3l7fnx+jouO////////////////////////////////f31/gX+BdnV2////////////////////////////////////////////////////////////////e3l7gH6AkI2Q////////////////////////////////gn+CgH6Ai4iLwsDCs7Czv76/3Nvc//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7/////////////tbO1fXt9fXt9////////////////////////////////8/LzdXN1gX+Bi4iL////////////////////////////////////////////////////////////////lJKUf31/eHZ4////////////////////////////////+ff5d3V3eHZ42NXY////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////dHJ0dnN23t3e////////////////////////////////1NLUend6fXt9srCy//////////////////////////////////////////////////7/////////////vLq8fHp8eHV45uXm////////////////////////////////y8jLd3V3gH6A////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////4eDhdXN1k5CT////////////////////////////////////uri6fHt8end63Nvc////zMnMjouO////////////////////////////////////////xsPG6+nr////5eTleXd5fHl8zMrM////////////////////////////////////g4GDdHJ05eTl////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////0tDScm9yz8zP////////////////////////////////////o6Gjf3x/eXd58O3wjIqMcG1woJ2g////////////////////////////////////////kI2QcnBynpue3Nrce3l7fXp9t7S3////////////////////////////////////vLm8c3Fzw8HD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////0tDSb21v5uXm////////////////////////////////////kY+RgH6Ag4GDfHl8c3FzkY+R/////////////////////////////////////////////Pz8gX6BeXh5fXp9hIOEf31/paSl////////////////////////////////////0tHSc3JzuLW4////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7OrsbGps0s/S////////////////////////////////////gX6BgoCCgX+BjIqM1NPU////////////////////////////////////////////////////////ko6Sf31/g4KDgX6BkY+R////////////////////////////////////vLq8dHJ0x8bH////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////iYaJd3V38fDx/////////////////////fz9yMbIfnx+fHt8hIKEfnx+rKqs////////////////////////////////////////////////////////////////d3R3g4KDhYOFenh6h4SH0M7Q////////////////////////4+HjeXd5cnBy9/b3/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////v3+ioiKZmRmiIaIp6SnqqeqmZeZfHl8Z2VncG5wmZaZnpyef31/goCCiIaI////////////////////////////////////////////////////////////6urqeHV4g4GDgH6AnZudf31/bmxubmxuhoOGo6CjsK+wq6mriYeJc3FzcG5wwsDC////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////6OboxMLEtLO0trS2xMLE3Nvc+Pf4////////////eXZ5goCCdnR28vHy////////////////////////////////////////////////////////vby9fHp8g4GDdnR29/b3////3dzduri6mJaYfnx+cG1wb21vfnx+oJ2g5+Xn////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////r6yvfXt9fXt9rqyu////////////////////////////////////////////////////////ioiKgH6AgH6AjouO////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////9fT1dXN1g4GDdnN2/Pv8////////////////////////////////////////////////6unqdXR1hIKEeHV40tHS////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////npyef31/fnx+mpia////////////////////////////////////////////////npuef31/gn+ChIKE////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7uzudHF0hIGEdnN20M7Q////////////////////////////////////////6ejpdXJ1goCCdHF04+Hj////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////qqeqe3h7g4GDdXN18fDx////////////////////////////////////goCCgX+BeXh5sK6w////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////goCCf31/goCCe3d7+fj5////////////////////////////m5ibfHp8e3l7mZaZ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////+vj6enh6fnx+gX+BeXZ56efp////////////////////l5WXeXh5d3V3oZ6h//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////3/jouOeHZ4fn1+dHF0rqyu7+7v7Ovsv76/d3V3dnR2d3R3zcvN////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////1dTVlJKUdHJ0cG5wbGlsa2lrbGtsi4mLxMLE//////////7/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////7evt4+Hj7Ovs////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////"/>
        <TextObject Name="Memo1" Left="437.19" Top="68.71" Width="248.99" Height="22.46" ShiftMode="WhenOverlapped" Text="ОАО &quot;БЕЛГАЗПРОМБАНК&quot;" Padding="2, 1, 2, 1" Font="Arial, 9.75pt, style=Bold"/>
        <TextObject Name="Memo2" Left="299.89" Top="94.5" Width="477.15" Height="20.46" ShiftMode="WhenOverlapped" Text="ВОПРОСНИК КЛИЕНТА - ИНДИВИДУАЛЬНОГО ПРЕДПРИНИМАТЕЛЯ р" Padding="2, 1, 2, 1" Font="Arial, 9.75pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData13" Top="682.55" Width="1047.06" Height="49.13" CanGrow="true" CanShrink="true">
      <TextObject Name="Memo8" Left="3.78" Width="1039.37" Height="22.68" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="16. Вид договорных отношений с ОАО «Белгазпромбанк» (наименование договора, тип и номер счета):" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo20" Left="3.78" Top="26.46" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" BeforePrintEvent="MasterData1_AfterPrint" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
    </DataBand>
    <DataBand Name="MasterData5" Top="774.71" Width="1047.06" Height="18.9" CanGrow="true" CanShrink="true" DataSource="ANK_IP2">
      <TextObject Name="Memo69" Width="207.87" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP2.CONTRACTCREDITACCOUNTTYPE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo70" Left="207.87" Width="196.54" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP2.CONTRACTSALARYACCOUNTTYPE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo71" Left="404.41" Width="185.2" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP2.CONTRACTDEPOSITACCOUNTTYPE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo72" Left="589.61" Width="200.32" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP2.CONTRACTEMONEYACCOUNTTYPE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo73" Left="789.92" Width="257.01" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP2.CONTRACTACCOUNTTYPE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header8" Top="735.68" Width="1047.06" Height="35.03" CanGrow="true" CanShrink="true">
        <TextObject Name="Memo135" Left="789.92" Width="257.01" Height="35.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Прочие договора" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo137" Left="404.41" Width="185.2" Height="35.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Текущие/вкладные счета клиента" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo138" Left="589.61" Width="200.32" Height="35.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Договора электронные деньги" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo139" Width="207.87" Height="35.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Кредиты клиента" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo140" Left="207.87" Width="196.54" Height="35.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Зарплатный проект" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData7" Top="797.61" Width="1047.06" Height="257.01" CanGrow="true" CanShrink="true" DataSource="ANK_IP1">
      <TextObject Name="Memo89" Left="3.78" Top="117.17" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[ANK_IP1.OTHERBANKSACCOUNTS]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo90" Left="3.78" Top="71.81" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="18. Сведения о счетах, открытых в других банках (номер и тип счета, наименование учреждения банка): " Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo10" Left="3.78" Top="49.13" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[ANK_IP1.OKVED]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo136" Left="3.78" Top="185.2" Width="1039.37" Height="37.8" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="19. Сведения о постоянных контрагентах:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo141" Left="3.78" Top="226.77" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[ANK_IP5.CONTRACTORSJUR]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo58" Left="3.78" Top="94.49" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="18.1. Счета в других банках по информации, предоставленной клиентом: " Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo59" Left="3.78" Top="139.84" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="18.2. Счета в других банках, установленные исходя из осуществляемых финансовых операций: " Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo60" Left="3.78" Top="162.52" Width="1039.37" Height="18.9" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="[ANK_IP1.OTHERBANKSACCOUNTSSFM]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo22" Left="3.78" Top="3.78" Width="1039.37" Height="41.57" CanGrow="true" CanShrink="true" ShiftMode="WhenOverlapped" Text="&lt;b&gt;17. Виды предпринимательской деятельности (допускается указание кода ОКЭД из общегосударственного справочника видов экономической деятельности&lt;/b&gt;  &lt;b&gt;&lt;i&gt;ОКРБ 005-2011, действующего с 01.01.2016)&lt;/b&gt;&lt;/i&gt; &lt;b&gt;или словесное описание основного вида экономической деятельности): &lt;/b&gt; " Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold" TextRenderType="HtmlParagraph"/>
    </DataBand>
    <DataBand Name="MasterData16" Top="1084.65" Width="1047.06" Height="22.68" CanGrow="true" CanShrink="true" DataSource="PartnerCompanies">
      <TextObject Name="Memo142" Width="298.58" Height="22.68" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[PartnerCompanies.NAME]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo143" Left="298.58" Width="370.39" Height="22.68" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[PartnerCompanies.UNP]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo144" Left="668.98" Width="377.95" Height="22.68" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[PartnerCompanies.REGPLACE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header9" Top="1058.62" Width="1047.06" Height="22.03" CanGrow="true" CanShrink="true">
        <TextObject Name="Memo149" Left="668.98" Width="377.95" Height="22.03" Border.Lines="All" GrowToBottom="true" Text="Местонахождение" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo150" Width="298.58" Height="22.03" Border.Lines="All" GrowToBottom="true" Text="Наименование контрагента" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo151" Left="298.58" Width="370.39" Height="22.03" Border.Lines="All" GrowToBottom="true" Text="УНП" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData14" Top="1111.33" Width="1047.06" Height="75.59" CanGrow="true" CanShrink="true">
      <TextObject Name="Memo84" Left="3.78" Top="3.78" Width="1031.81" Height="18.9" CanGrow="true" ShiftMode="WhenOverlapped" Text="20. Предполагаемые среднемесячные обороты по счету, в том числе оборот наличных денежных средств (в разрезе видов валют):" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Memo134" Left="3.78" Top="49.13" Width="1031.81" Height="18.9" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" BeforePrintEvent="CurAccountValueMonth_AfterPrint" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo65" Left="49.13" Top="22.68" Width="835.82" Height="18.9" CanGrow="true" ShiftMode="WhenOverlapped" Text="(с 01.07.2016 обороты по счету в белорусских рублях указываются с учетом деноминации официальной денежной единицы Республики Беларусь (в BYN)):" Padding="2, 1, 2, 1" Font="Arial, 8pt, style=Italic"/>
    </DataBand>
    <DataBand Name="MasterData15" Top="1246.95" Width="1047.06" Height="26.46" CanGrow="true" CanShrink="true" DataSource="CurAccountValueMonth">
      <TextObject Name="Memo91" Width="207.87" Height="26.46" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[CurAccountValueMonth.CUR]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo92" Left="207.87" Width="196.54" Height="26.46" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[CurAccountValueMonth.DEBT]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo131" Left="404.41" Width="185.2" Height="26.46" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[CurAccountValueMonth.DEBT_NAL]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo132" Left="589.61" Width="200.32" Height="26.46" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[CurAccountValueMonth.CRED]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo133" Left="789.92" Width="257.01" Height="26.46" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[CurAccountValueMonth.CRED_NAL]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header7" Top="1190.92" Width="1047.06" Height="52.03" CanGrow="true" CanShrink="true">
        <TextObject Name="Memo11" Left="789.92" Width="257.01" Height="52.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Оборот наличных денежных средств по кредиту" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo35" Left="404.41" Width="185.2" Height="52.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="В том числе оборот наличных денежных средств по дебету" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo82" Left="589.61" Width="200.32" Height="52.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Общий оборот по кредиту" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo83" Width="207.87" Height="52.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Наименование валюты" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo85" Left="207.87" Width="196.54" Height="52.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Общий оборот по дебету" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData2" Top="1325.22" Width="1047.06" Height="18.9" CanGrow="true" CanShrink="true" DataSource="ANK_IP5">
      <TextObject Name="Memo37" Left="3.78" Width="1039.37" Height="18.9" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP5.CONNECTEDANDPARTNERORG]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header4" Top="1277.41" Width="1047.06" Height="43.81" CanGrow="true">
        <TextObject Name="Memo36" Left="3.78" Width="1039.37" Height="35.03" ShiftMode="WhenOverlapped" Text="21. Сведения о лицах, способных прямо или косвенно (через иных лиц) определять (оказывать влияние на принятие) решения индивидуального предпринимателя, о лицах, на принятие решений которыми индивидуальный предприниматель оказывает такое влияние:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData3" Top="1499.52" Width="1047.06" Height="26.46" CanGrow="true" CanShrink="true" DataSource="ANK_IP3">
      <TextObject Name="Memo39" Width="102.05" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP3.NAME]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo41" Left="102.05" Width="97.71" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP3.CITIZENSHIP]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo43" Left="199.76" Width="94.49" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP3.BIRTH]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo45" Left="294.24" Width="121.5" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP3.RESIDENCE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo47" Left="415.75" Width="162.52" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP3.DOC]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo49" Left="578.27" Width="204.09" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP3.REGISTRATIONINFO]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo51" Left="782.36" Width="94.49" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP3.PAYNUMBER]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo76" Left="876.85" Width="170.08" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP3.GROUND]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header1" Top="1348.12" Width="1047.06" Height="147.4" CanGrow="true" CanShrink="true">
        <TextObject Name="Memo38" Left="3.78" Width="1031.81" Height="18.9" ShiftMode="WhenOverlapped" Text="21.1.  Сведения о физических лицах/индивидуальных предпринимателях:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo40" Top="22.68" Width="102.05" Height="124.72" Border.Lines="All" ShiftMode="WhenOverlapped" Text="Ф.И.О." Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo42" Left="102.05" Top="22.68" Width="97.71" Height="124.72" Border.Lines="All" ShiftMode="WhenOverlapped" Text="Гражданство" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo44" Left="199.76" Top="22.68" Width="94.49" Height="124.72" Border.Lines="All" ShiftMode="WhenOverlapped" Text="Дата и место рождения" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo46" Left="294.24" Top="22.68" Width="121.5" Height="124.72" Border.Lines="All" ShiftMode="WhenOverlapped" Text="Место жительства (регистрация)" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo48" Left="415.75" Top="22.68" Width="162.52" Height="124.72" Border.Lines="All" ShiftMode="WhenOverlapped" Text="Реквизиты документа, удостоверяющего личность: наименование, серия и номер документа, кем и когда выдан, идентификационный номер (при наличии)" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo50" Left="578.27" Top="22.68" Width="204.09" Height="124.72" Border.Lines="All" ShiftMode="WhenOverlapped" Text="Регистрационный номер и дата регистрации индивидуального предпринимателя, наименование регистрирующего органа (для индивидуальных предпринимателей)" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo52" Left="782.36" Top="22.68" Width="94.49" Height="124.72" Border.Lines="All" ShiftMode="WhenOverlapped" Text="УНП" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo75" Left="876.85" Top="22.68" Width="170.08" Height="124.72" Border.Lines="All" ShiftMode="WhenOverlapped" Text="Иные сведения" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData4" Top="1632.25" Width="1047.06" Height="18.9" CanGrow="true" CanShrink="true" DataSource="ANK_IP4">
      <TextObject Name="Memo64" Left="653.48" Width="216.32" Height="18.9" Border.Lines="Right, Top, Bottom" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP4.PAYNUMBER]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo61" Width="156.47" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP4.NAME]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo62" Left="156.47" Width="278.17" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP4.REGISTRATIONINFO]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo63" Left="434.65" Width="218.21" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP4.LOCATION]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo86" Left="870.43" Width="175.53" Height="18.9" Border.Lines="Right, Top, Bottom" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP4.GROUND]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header3" Top="1529.98" Width="1047.06" Height="98.27" CanGrow="true">
        <TextObject Name="Memo53" Left="3.78" Top="7.56" Width="1031.81" Height="18.9" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="21.2. Сведения об организациях/финансовых институтах:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo54" Top="30.24" Width="155.53" Height="68.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Наименование" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo55" Left="156.47" Top="30.24" Width="278.17" Height="68.03" Border.Lines="Right, Top, Bottom" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Регистрационный номер и дата регистрации организации, наименование регистрирующего органа" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo56" Left="434.65" Top="30.24" Width="218.21" Height="68.03" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Место нахождения (юридический адрес)" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo57" Left="653.55" Top="30.24" Width="216.32" Height="68.03" Border.Lines="Right, Top, Bottom" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="УНП" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo81" Left="870.43" Top="30.24" Width="175.53" Height="68.03" Border.Lines="Right, Top, Bottom" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Иные сведения" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData17" Top="1680.18" Width="1047.06" Height="23.79" CanGrow="true" CanShrink="true" CanBreak="true" DataSource="ANK_IP5">
      <TextObject Name="Memo169" Left="3.78" Top="1.89" Width="1032.39" Height="18.9" CanGrow="true" CanShrink="true" Text="[ANK_IP5.INFOREPRESENTATIVE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header12" Top="1655.15" Width="1047.06" Height="21.03" CanGrow="true">
        <TextObject Name="Memo124" Left="3.78" Width="1027.81" Height="20.03" ShiftMode="WhenOverlapped" Text="22. Сведения о представителях: " Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData11" Top="1837.93" Width="1047.06" Height="22.9" CanGrow="true" CanShrink="true" DataSource="ANK_IP8">
      <TextObject Name="Memo111" Width="101.05" Height="18.9" Border.Lines="Left, Top, Bottom" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP8.NAME]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo112" Left="102.05" Width="112.39" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP8.CITIZENSHIP]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo113" Left="215.43" Width="113.39" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP8.BIRTH]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo114" Left="328.82" Width="132.28" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP8.REGISTRATIONPLACE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo115" Left="461.1" Width="253.23" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP8.DOC]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo116" Left="714.33" Width="153.96" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP8.PAYNUMBER]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo88" Left="869.29" Width="176.64" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP8.GROUND]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header10" Top="1707.97" Width="1047.06" Height="125.96" CanGrow="true" CanShrink="true">
        <TextObject Name="Memo103" Left="3.78" Top="6.56" Width="1031.81" Height="18.9" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="22.1. Сведения о физических лицах/индивидуальных предпринимателях:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo104" Top="30.24" Width="102.05" Height="95.72" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Ф.И.О." Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo105" Left="102.05" Top="30.24" Width="112.39" Height="95.72" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Гражданство" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo106" Left="215.43" Top="30.24" Width="113.39" Height="95.72" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Дата и место рождения" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo107" Left="328.82" Top="30.24" Width="132.28" Height="95.72" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Место жительства (регистрация)" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo108" Left="461.1" Top="30.24" Width="253.4" Height="95.72" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Реквизиты документа, удостоверяющего личность: наименование, серия и номер документа, кем и когда выдан, идентификационный номер (при наличии)" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo109" Left="714.5" Top="30.24" Width="153.96" Height="95.72" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="УНП" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo87" Left="869.29" Top="30.24" Width="176.64" Height="95.72" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Иные сведения" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData12" Top="1952.96" Width="1047.06" Height="22.68" CanGrow="true" CanShrink="true" DataSource="ANK_IP9">
      <TextObject Name="Memo120" Width="222.99" Height="22.68" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP9.NAME]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo121" Left="222.99" Width="308.41" Height="22.68" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP9.REGISTRATIONINFO]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo122" Left="531.4" Width="226.02" Height="22.68" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP9.LOCATION]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo123" Left="757.42" Width="129.64" Height="22.68" Border.Lines="Left, Top, Bottom" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP9.PAYNUMBER]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo95" Left="888.19" Width="157.87" Height="22.68" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP9.GROUND]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header11" Top="1864.83" Width="1047.06" Height="84.13" CanGrow="true">
        <TextObject Name="Memo118" Top="28.1" Width="221.99" Height="56.03" Border.Lines="All" GrowToBottom="true" Text="Наименование" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo125" Left="3.78" Width="1031.81" Height="18.9" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="22.2. Сведения об организациях/финансовых институтах:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo74" Left="223" Top="27.97" Width="307.57" Height="56.03" Border.Lines="Left, Top, Bottom" Text="Регистрационный номер и дата регистрации, наименование регистрирующего органа" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo77" Left="531.4" Top="27.97" Width="225.17" Height="56.03" Border.Lines="Left, Top, Bottom" Text="Место нахождения (юридический адрес)" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo78" Left="757.42" Top="28" Width="128.64" Height="56.03" Border.Lines="Left, Top, Bottom" Text="УНП" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo93" Left="888.19" Top="27.97" Width="157.87" Height="56.03" Border.Lines="All" Text="Иные сведения" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData18" Top="2005.67" Width="1047.06" Height="27.67" CanGrow="true" CanShrink="true" DataSource="ANK_IP5">
      <TextObject Name="Memo9" Left="3.78" Top="4.99" Width="1034.47" Height="18.9" CanGrow="true" CanShrink="true" Text="[ANK_IP5.INFOBENEFITPRIOBRETATEL]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header13" Top="1979.64" Width="1047.06" Height="22.03" CanGrow="true">
        <TextObject Name="Memo126" Left="3.78" Width="1030.81" Height="20.03" ShiftMode="WhenOverlapped" Text="23. Сведения о выгодоприобретателях:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData20" Top="2166.07" Width="1047.06" Height="35.24" CanGrow="true" CanShrink="true" DataSource="ANK_IP10">
      <TextObject Name="Memo168" Left="763.09" Width="143.24" Height="34.57" Border.Lines="Left, Top, Bottom" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP10.PAYNUMBER]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo162" Width="102.05" Height="34.57" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP10.NAME]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo164" Left="102.05" Width="112.39" Height="34.57" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP10.CITIZENSHIP]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo165" Left="215.43" Width="158.74" Height="34.57" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP10.BIRTH]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo166" Left="374.17" Width="166.3" Height="34.57" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP10.REGISTRATIONPLACE]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo167" Left="540.47" Width="222.17" Height="34.57" Border.Lines="Left, Top, Bottom" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP10.DOC]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo98" Left="907.09" Width="137.46" Height="34.57" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP10.GROUND]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header14" Top="2037.34" Width="1047.06" Height="124.73" CanGrow="true" CanShrink="true">
        <TextObject Name="Memo127" Left="3.78" Top="2.78" Width="1031.81" Height="18.9" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="23.1. Сведения о физических лицах/индивидуальных предпринимателях:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo128" Top="30.24" Width="102.05" Height="94.49" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Ф.И.О." Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo129" Left="102.05" Top="30.24" Width="112.39" Height="94.49" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Гражданство" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo130" Left="215.43" Top="30.24" Width="158.74" Height="94.49" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Дата и место рождения" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo146" Left="374.17" Top="30.24" Width="166.3" Height="94.49" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Место жительства (регистрация)" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo148" Left="540.47" Top="30.24" Width="222.17" Height="94.49" Border.Lines="Left, Top, Bottom" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Реквизиты документа, удостоверяющего личность: наименование, серия и номер документа, кем и когда выдан, идентификационный номер (при наличии)" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo96" Left="763.46" Top="30.24" Width="143.62" Height="94.49" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="УНП" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo97" Left="907.09" Top="30.24" Width="137.84" Height="94.49" Border.Lines="All" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="Иные сведения" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData19" Top="2302.66" Width="1047.06" Height="24.66" CanGrow="true" CanShrink="true" DataSource="ANK_IP11">
      <TextObject Name="Memo158" Width="222.99" Height="23.91" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP11.NAME]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo159" Left="222.99" Width="304.63" Height="23.91" Border.Lines="All" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP11.REGISTRATIONINFO]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo160" Left="527.62" Width="154.2" Height="23.91" Border.Lines="Left, Top, Bottom" CanGrow="true" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="[ANK_IP11.LOCATION]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo110" Left="682.58" Width="185.95" Height="23.91" Border.Lines="All" CanGrow="true" GrowToBottom="true" Text="[ANK_IP11.PAYNUMBER]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo100" Left="869.29" Width="176.02" Height="23.91" Border.Lines="Right, Top, Bottom" CanGrow="true" GrowToBottom="true" Text="[ANK_IP11.GROUND]" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <DataHeaderBand Name="Header15" Top="2205.31" Width="1047.06" Height="93.35" CanGrow="true">
        <TextObject Name="Memo155" Top="37.8" Width="222.99" Height="55.03" Border.Lines="All" GrowToBottom="true" Text="Наименование" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo157" Left="3.78" Top="4.41" Width="1031.81" Height="18.9" GrowToBottom="true" ShiftMode="WhenOverlapped" Text="23.2. Сведения об организациях/финансовых институтах:" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo79" Left="222.99" Top="37.8" Width="303.8" Height="55.03" Border.Lines="All" Text="Регистрационный номер и дата регистрации, наименование регистрирующего органа" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo80" Left="682.58" Top="37.8" Width="185.68" Height="55.03" Border.Lines="Left, Top, Bottom" Text="УНП" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo94" Left="527.62" Top="37.8" Width="154.35" Height="55.03" Border.Lines="Top, Bottom" Text="Место нахождения (юридический адрес)" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Memo99" Left="869.29" Top="37.8" Width="176.12" Height="55.03" Border.Lines="All" Text="Иные сведения" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <DataBand Name="MasterData43" Top="2331.32" Width="1047.06" Height="464.88" CanGrow="true" CanBreak="true" KeepChild="true" DataSource="IDC_u_d">
      <TextObject Name="Memo21" Left="18.9" Top="377.95" Width="518.9" Height="18.9" CanGrow="true" Text="Дата печати вопросника из электронной базы данных анкет клиентов (ЕРК) " Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Italic"/>
      <TextObject Name="Memo25" Left="18.9" Top="196.54" Width="341.6" Height="18.9" CanGrow="true" Text="Ф.И.О. и должность лица, подписавшего вопросник:" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo26" Left="710.55" Top="196.54" Width="121.39" Height="18.9" CanGrow="true" Text="Дата заполнения:" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo27" Left="40.44" Top="113.39" Width="991.5" Height="17.9" CanGrow="true" Text="Есть изменения в пунктах вопросника: _______________________________________________________________________________________________,&#13;&#10;  " Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo28" Left="40.31" Top="68.03" Width="991.5" Height="34.02" CanGrow="true" Text="ВСЕ пункты вопросника содержат актуальные сведения (документы и сведения, указанные в настоящем вопроснике, являются действительными)." Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo29" Left="18.9" Top="16.38" Width="274.21" Height="18.9" CanGrow="true" Text="Настоящим подтверждаю:" Padding="2, 1, 2, 1" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Memo30" Left="18.9" Top="36.54" Width="384.25" Height="15.12" CanGrow="true" Text="(выбрать один из предлагаемых вариантов, заполняется клиентом)" Padding="2, 1, 2, 1" Font="Arial, 7pt"/>
      <ShapeObject Name="Shape3" Left="18.9" Top="71.81" Width="18.9" Height="18.9"/>
      <ShapeObject Name="Shape5" Left="18.9" Top="113.39" Width="18.9" Height="18.9"/>
      <LineObject Name="Line7" Left="22.68" Top="234.33" Width="317.48"/>
      <LineObject Name="Line8" Left="778.8" Top="234.33" Width="261.79"/>
      <TextObject Name="Memo31" Left="710.55" Top="219.21" Width="67.47" Height="18.9" CanGrow="true" Text="Подпись:" Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <LineObject Name="Line9" Left="832.73" Top="211.65" Width="207.87"/>
      <TextObject Name="Memo32" Left="538.58" Top="377.95" Width="222.99" Height="18.9" CanGrow="true" Text="[IDC_u_d.CURR_DATE]" Padding="2, 1, 2, 1" Format="Date" Format.Format="d" Font="Arial, 10pt"/>
      <TextObject Name="Memo33" Left="498.9" Top="131.28" Width="297.32" Height="13.12" CanGrow="true" Text="(указать номера пунктов)" Padding="2, 1, 2, 1" Font="Arial, 7pt"/>
      <TextObject Name="Memo34" Left="19.9" Top="144.62" Width="1012.91" Height="18.9" CanGrow="true" Text="остальные пункты вопросника содержат актуальные сведения. Информационное письмо (документы) с изменениями прилагаются." Padding="2, 1, 2, 1" Font="Arial, 10pt"/>
      <TextObject Name="Memo201" Left="18.9" Top="238.11" Width="123.35" Height="13.34" CanGrow="true" Text="(заполняется клиентом)" Padding="2, 1, 2, 1" Font="Arial, 7pt"/>
      <TextObject Name="Memo203" Left="18.9" Top="291.02" Width="1012.91" Height="18.9" CanGrow="true" Text="*Служебная информация (заполняется работником ОАО «Белгазпромбанк»):" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Italic"/>
      <TextObject Name="Memo226" Left="40.32" Top="321.26" Width="991.5" Height="17.9" CanGrow="true" Text="Вопросник получен в рамках рассмотрения заявки на осуществление кредитной операции" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Italic"/>
      <ShapeObject Name="Shape6" Left="18.9" Top="321.26" Width="18.9" Height="18.9"/>
      <TextObject Name="Memo227" Left="185.2" Top="339.38" Width="282.09" Height="11.34" CanGrow="true" Text="(ставится отметка работником ОАО «Белгазпромбанк»)" Padding="2, 1, 2, 1" Font="Arial, 7pt, style=Italic"/>
      <TextObject Name="Memo66" Left="15.12" Top="423.31" Width="498.9" Height="18.9" CanGrow="true" Text="[ankjp_one_value.UPD_ANK]" Padding="2, 1, 2, 1" Font="Arial, 10pt, style=Bold"/>
    </DataBand>
  </ReportPage>
</Report>
