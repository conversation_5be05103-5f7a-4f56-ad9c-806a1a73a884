﻿using DocumentFormat.OpenXml.Drawing.Charts;
using DocumentFormat.OpenXml.Office2010.Excel;
using EWA.Models;
using System.Configuration;
using System.Reflection;
using static EWA.Services.SIBService;
using static EWA.Models.REP_Models;
using static EWA.Models.WFL_Models;

namespace EWA.Services
{
    public class WFLModule
    {
        private readonly IConfiguration _configuration;
        private readonly ConnectionStringProvider _ConService;
        public WFLModule(IConfiguration configuration, ConnectionStringProvider ConService)
        {
            _configuration = configuration;
            _ConService = ConService;
            dbService = new DBService(_ConService.GetConnectionString("EWAREP"));
        }
        private DBService dbService;

        public static readonly Dictionary<string, WFL_Param> WFLParam = new Dictionary<string, WFL_Param>
        {
            ["DTS"] = new WFL_Param
            {
                Code = "DTS",
                Name = "Дата с...",
                DataType = "Date"
            },
            ["DTE"] = new WFL_Param
            {
                Code = "DTE",
                Name = "Дата по...",
                DataType = "Date"
            },
            ["ACTIVE_MODE"] = new WFL_Param
            {
                Code = "ACTIVE_MODE",
                Name = "Тип активации",
                DataType = "Bool",
                Options = new Dictionary<string, string>
                {
                    ["SINGLE"] = "Только один действующий",
                    ["MULTI"] = "Несколько одновременно"
                }

            },
            ["TYPE_WFL"] = new WFL_Param
            {
                Code = "TYPE_WFL",
                Name = "Тип процесса",
                DataType = "String",
                Options = new Dictionary<string, string>
                {
                    ["CALC_AML"] = "Расчет признаков",
                    ["LOAD_DL"] = "Загрузка детального слоя"
                }
            }
        };
        static string _typeobj = "WFLTables";
        readonly string _namespace = "EWA.Services.WFLModule+";
        public static readonly List<WFLRepData> RepRegistory = new()
        {
                new WFLRepData
                {
                    Alias = "WTASK",
                    Code = "WFL_TASK",
                    Methods = new List<string> { "GetSqlQuery", "GetActions", "GetActQuery", "GetObjInfo","GetColumnMetadata","GetParamMetadata" }
                },
                new WFLRepData
                {
                    Alias = "WWORKLET",
                    Code = "WFL_WORKLET",
                    Methods = new List<string> { "GetCfgData", "GetSqlQuery", "GetActions", "GetActQuery", "GetObjInfo", "GetColumnMetadata","GetParamMetadata" }
                },
                new WFLRepData
                {
                    Alias = "WWORKFLOW",
                    Code = "WFL_WORKFLOW",
                    Methods = new List<string> { "GetMenudata", "GetTransitionParams","GetCfgData", "GetSqlQuery", "GetActions", "GetActQuery", "GetObjInfo", "GetColumnMetadata"}
                },
                new WFLRepData
                {
                    Alias = "WACTIVATION",
                    Code = "WFL_ACTIVATION",
                    Methods = new List<string> { "GetMenudata", "GetTransitionParams","GetParamMetadata", "GetTransitionParams", "GetSqlQuery", "GetObjInfo","GetActions","GetColumnMetadata","GetMenudata","GetParamMetadata","GetActQuery" }
                },
                
                new WFLRepData
                {
                    Alias = "WACTIVATION_DET",
                    Code = "WFL_ACTIVATION_DET",
                    Methods = new List<string> {"GetParamMetadata", "GetTransitionParams", "GetSqlQuery", "GetObjInfo","GetActions","GetColumnMetadata","GetMenudata","GetParamMetadata","GetActQuery" }
                },
                new WFLRepData
                {
                    Alias = "WFL_DIM_LISTSUSP",
                    Code = "W_DIM_LISTSUSP",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo", "GetColumnMetadata" }
                },
                new WFLRepData
                {
                    Alias = "WFL_DIM_LISTOBJ",
                    Code = "W_DIM_LISTOBJ",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo", "GetColumnMetadata" }
                },
                new WFLRepData
                {
                    Alias = "WFL_DIM_TYPE_OBJ",
                    Code = "W_DIM_TYPE_OBJ",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo", "GetColumnMetadata" }
                }
        };
        public async Task<IEnumerable<SPRShort>> GetSPRData(string code_spr)
        {
            IEnumerable<SPRShort> _sprMetadata;
            REP_Models.IMetadataObject metaObject = null;
            RepositoryInfo repositoryInfo = new RepositoryInfo();
            string query = string.Empty;
            string param_meta = string.Empty;
            List<REP_Models.ParamMetadata> _metadata_param = new List<REP_Models.ParamMetadata>();
            List<REP_Models.ColumnMetadata> _metadata_column = new List<REP_Models.ColumnMetadata>();

            _sprMetadata = Enumerable.Empty<SPRShort>();
            repositoryInfo = GetRepositoryInfo(code_spr);
            query = repositoryInfo.Query;
            _metadata_param = repositoryInfo.Param;
            _metadata_column = repositoryInfo.Column;
            var parameters = new Dictionary<string, object>();
            foreach (var kvp in _metadata_param.Where(x => x.CODE != "p_NameFilter"))
            {
                string paramName = kvp.CODE;
                object paramValue = null;

                parameters.Add(paramName, paramValue);
            }
            var (items, errorMessage) = await dbService.GetDataSPRShort(query, parameters);

            _sprMetadata = items;
            return (_sprMetadata);
        }
        public static Task<IEnumerable<MenuInfo>> GetMenuData()
        {

            var menuInfos = new List<MenuInfo>
                {
                    new MenuInfo { CODE_CHILD = "FWORKF", CODE_PARENT = "MAIN", CODE_OBJECT = "FWORKF", NAME_OBJECT = "Настройка заданий", TYPE_OBJECT = "FOLDER" },
                    new MenuInfo { CODE_CHILD = "FWORKA", CODE_PARENT = "MAIN", CODE_OBJECT = "FWORKA", NAME_OBJECT = "Выполнение заданий", TYPE_OBJECT = "FOLDER" },
                    new MenuInfo { CODE_CHILD = "WTASK", CODE_PARENT = "FWORKF", CODE_OBJECT = "WTASK", NAME_OBJECT = "Задачи", TYPE_OBJECT = _typeobj },
                    new MenuInfo { CODE_CHILD = "WWORKLET", CODE_PARENT = "FWORKF", CODE_OBJECT = "WWORKLET", NAME_OBJECT = "Задания", TYPE_OBJECT = _typeobj },
                    new MenuInfo { CODE_CHILD = "WWORKFLOW", CODE_PARENT = "FWORKF", CODE_OBJECT = "WWORKFLOW", NAME_OBJECT = "Процессы", TYPE_OBJECT = _typeobj },
                    new MenuInfo { CODE_CHILD = "WACTIVATION", CODE_PARENT = "FWORKA", CODE_OBJECT = "WACTIVATION", NAME_OBJECT = "Журнал выполнения заданий", TYPE_OBJECT = _typeobj }
                };
            return Task.FromResult(menuInfos.AsEnumerable());
        }
        public RepositoryInfo GetRepositoryInfo(string code)
        {
            RepositoryInfo rep = new RepositoryInfo();
            rep.Query = GetSqlQuery(code);
            rep.Code = code;
            rep.Cfg = GetCfgData(code);
            (rep.Title, rep.TypeObject) = GetObjInfo(code);
            (rep.ColAdd, rep.ColUpd, rep.ColDel) = GetActions(code);
            rep.Column = GetColumnMetadata(code);
            rep.Param = GetParamMetadata(code);
            (rep.AddSql, rep.UpdSql, rep.DelSql) = GetActQuery(code);
            //rep.Dbname_Query = _configuration.GetConnectionString("Rep_Connection");
            rep.Dbname_Query = _ConService.GetConnectionString("EWAREP");
            rep.MenuGrid = GetMenudata(code);
            rep.AppCode = "WFL";

            return rep;

        }

        public (string sqlA, string sqlE, string slqD) GetActQuery(string code)
        {
            (string sqlA, string sqlE, string sqlD) def_val = (null, null, null);
            var meta = RepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetActQuery"))
                return def_val;

            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetActQuery", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;

            var result = method.Invoke(null, null);

            return result is ValueTuple<string, string, string> tupleResult
                ? tupleResult
                : def_val;
        }
        public List<REP_Models.CfgData> GetCfgData(string code)
        {
            List<REP_Models.CfgData> def_val = new List<REP_Models.CfgData>{
                new REP_Models.CfgData
                {
                    CODE = "NO_DATA",
                    NAME = "Пустышка",
                    VALUE = "NO_DATA"
                }
            };

            var meta = RepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetCfgData"))
                return def_val;
            
            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetCfgData", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;

            var result = method.Invoke(null, null);
            return result as List<REP_Models.CfgData> ?? def_val;

        }

        public string GetSqlQuery(string code)
        {
            string def_val = "SELECT 'NO DATA' as COLUM from DUAL";

            var meta = RepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetSqlQuery"))
                return def_val;
            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetSqlQuery", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;

            var result = method.Invoke(null, null);
            return result as string ?? def_val;
        }
        public (string title, string typeobj) GetObjInfo(string code)
        {
            (string title, string typeobj) def_val = ("No Data", "Undef");

            var meta = RepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetObjInfo"))
                return def_val;
            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetObjInfo", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;

            var result = method.Invoke(null, null);
            return result as (string, string)? ?? def_val;
        }

        public (bool actA, bool actE, bool actD) GetActions(string code)
        {
            (bool actA, bool actE, bool actD) def_val = (false, false, false);

            var meta = RepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetActions"))
                return def_val;
            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetActions", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;

            var result = method.Invoke(null, null);
            return result as (bool, bool, bool)? ?? def_val;

        }
        public List<REP_Models.ColumnMetadata> GetColumnMetadata(string code)
        {

            List<REP_Models.ColumnMetadata> def_val = new List<REP_Models.ColumnMetadata>{
                new REP_Models.ColumnMetadata
                {
                    CODE = "COLUM",
                    NAME = "COLUM",
                    DATATYPE = "VARCHAR2",
                    DATALENGTH = 30,
                    COL_REF = null,
                    COL_REF_DATA = null,
                    IS_PK = 0
                }
            };


            var meta = RepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetColumnMetadata"))
                return def_val;
            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetColumnMetadata", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;

            var result = method.Invoke(null, null);
            
            if (result is List<REP_Models.ColumnMetadata> list)
            {
                /*list.Add(new ColumnMetadata
                {
                    CODE = "ERRORINFO",
                    DATATYPE = "VARCHAR2",
                    DATALENGTH = 4000,
                    NAME = "Предупреждение",
                    VIEWVISIBLE = 1
                });
                */
                return list;
            }
            return def_val;
        }

        public IEnumerable<REP_Models.GRID_Menu> GetMenudata(string code)
        {
            IEnumerable<GRID_Menu> def_val = Enumerable.Empty<REP_Models.GRID_Menu>();

            var meta = RepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetMenudata"))
                return def_val;
            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetMenudata", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;

            var result = method.Invoke(null, null);
            return result as IEnumerable<GRID_Menu> ?? def_val;
        }


        public List<REP_Models.ParamMetadata> GetParamMetadata(string code)
        {

            List<REP_Models.ParamMetadata> def_val = new List<REP_Models.ParamMetadata>();

            var meta = RepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetParamMetadata"))
                return def_val;
            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetParamMetadata", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;

            var result = method.Invoke(null, null);
            return result as List<REP_Models.ParamMetadata> ?? def_val;

        }
        public List<REP_Models.ParamMetadata> GetTransitionParams(string code, string code_child)
        {
            List<REP_Models.ParamMetadata> def_val = new List<REP_Models.ParamMetadata>();

            var meta = RepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetTransitionParams"))
                return def_val;
            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetTransitionParams", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;

            var result = method.Invoke(null, [code_child]);
            return result as List<REP_Models.ParamMetadata> ?? def_val;

        }

        public static class WFL_TASK
        {
            public static string GetSqlQuery()
            {
                return "select wt.ID, wt.CODE,wt.NAME,wt.TYPE_OBJ,wtt.name as TYPE_OBJ#N,wt.CODE_OBJ,wto.NAME as CODE_OBJ#N,wt.action_sql as ACTSQL" + Environment.NewLine
                      + "  from wrfl_task wt" + Environment.NewLine
                      + "  left join wrfl_task_type wtt on wtt.code = wt.TYPE_OBJ" + Environment.NewLine
                      + "  left join WRFL_LIST_OBJ_VIEW wto on wto.code = wt.CODE_OBJ" + Environment.NewLine
                      + " where :P_TYPE_OBJ is null or :P_TYPE_OBJ = wt.TYPE_OBJ" + Environment.NewLine
                      + " order by wt.CODE";
            }
            public static (string sqlA, string sqlE, string slqD) GetActQuery()
            {
                return ("insert into wrfl_task(id,code,name,type_obj,code_obj,action_sql)" + Environment.NewLine
                       + "values(ewa_row_seq.nextval,'s_'||:IN_CODE, :IN_NAME, :IN_TYPE_OBJ, :IN_CODE_OBJ, :IN_ACTSQL)" + Environment.NewLine
                       + "RETURNING id INTO :OUT_ID",
                        "UPDATE wrfl_task" + Environment.NewLine
                       + "   SET name = :IN_name," + Environment.NewLine
                       + "       type_obj = :IN_type_obj," + Environment.NewLine
                       + "       code_obj = :IN_code_obj," + Environment.NewLine
                       + "     action_sql = :IN_ACTSQL" + Environment.NewLine
                       + "   WHERE id = :IN_ID",
                        "Delete from wrfl_task where Id = :IN_ID");
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Задачи", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (true, true, false);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 32, IS_PK = 1 },
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "Код задачи", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1, INSERTABLE = 1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "Наименование задачи", DATATYPE = "VARCHAR2", DATALENGTH = 1000, VIEWVISIBLE =1, INSERTABLE = 1, EDITABLE = 1},
                new REP_Models.ColumnMetadata { CODE = "TYPE_OBJ", NAME = "Тип объекта", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1, INSERTABLE = 1, EDITABLE = 1,DOMAINCODE ="CODE", DIMCODE = "WFL_DIM_TYPE_OBJ",DIMNAME = "Тип объекта узла"},
                new REP_Models.ColumnMetadata { CODE = "TYPE_OBJ#N", NAME = "Тип объекта", DATATYPE = "VARCHAR2", DOMAINCODE = "Name", DATALENGTH = 1000, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE_OBJ", NAME = "Код объекта", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1, INSERTABLE = 1, EDITABLE = 1,DOMAINCODE ="CODE",DIMCODE = "WFL_DIM_LISTOBJ",DIMNAME = "Код объекта узла"},
                new REP_Models.ColumnMetadata { CODE = "CODE_OBJ#N", NAME = "Код объекта", DATATYPE = "VARCHAR2", DOMAINCODE = "Name", DATALENGTH = 1000, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "ACTSQL", NAME = "Исполняемый запрос", DATATYPE = "CLOB", VIEWVISIBLE =1, INSERTABLE = 1, EDITABLE = 1}
                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>
                {
                    new REP_Models.ParamMetadata { CODE = "P_TYPE_OBJ", NAME = "Тип объекта", DOMAINCODE ="Code", DIMCODE = "WFL_DIM_TYPE_OBJ",DIMNAME = "Тип объекта узла", DATATYPE = "VARCHAR2", DATALENGTH = 30}
                };
            }

        }


        public static class WFL_WORKLET
        {
            public static string GetSqlQuery()
            {
                return "select wt.id, wt.code, wt.name, wt.type_obj,wtt.name as TYPE_OBJ#N, wt.code_obj,wto.NAME as CODE_OBJ#N, wt.list_task, wt.lnk_task " + Environment.NewLine
                      + "  from wrfl_worklet wt" + Environment.NewLine
                      + "  left join wrfl_task_type wtt on wtt.code = wt.TYPE_OBJ" + Environment.NewLine
                      + "  left join wrfl_list_susp_view wto on wto.code = wt.CODE_OBJ" + Environment.NewLine
                      + " where :P_TYPE_OBJ is null or :P_TYPE_OBJ = TYPE_OBJ" + Environment.NewLine
                      + " order by CODE";
            }
            public static (string sqlA, string sqlE, string slqD) GetActQuery()
            {
                return ("insert into wrfl_worklet(id,code,name,type_obj,code_obj,list_task,lnk_task)" + Environment.NewLine
                       +"values(ewa_row_seq.nextval, 'wl_'||:IN_CODE, :IN_NAME,:IN_TYPE_OBJ,:IN_CODE_OBJ, :IN_LIST_TASK, :IN_LNK_TASK)"+Environment.NewLine
                       + "RETURNING id INTO :OUT_ID",
                        "UPDATE wrfl_worklet" + Environment.NewLine
                       +"   SET name = :IN_name," + Environment.NewLine
                       +"       type_obj = :IN_type_obj," + Environment.NewLine
                       +"       code_obj = :IN_code_obj," + Environment.NewLine
                       +"     list_task = :IN_LIST_TASK," + Environment.NewLine
                       +"     lnk_task = :IN_LNK_TASK" + Environment.NewLine
                       +"   WHERE id = :IN_ID",
                        "Delete from wrfl_worklet where Id = :IN_ID");
            }
            public static List<REP_Models.CfgData> GetCfgData()
            {
                return new List<REP_Models.CfgData>
                {
                new REP_Models.CfgData {CODE = "USE_FORM", NAME = "Использовать форму", VALUE = "WFLForm"}
                };
            }

            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Задания", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (true, true, false);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 32, IS_PK = 1 },
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "Код задания", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1, INSERTABLE = 1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "Наименование задания", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1, INSERTABLE = 1, EDITABLE = 1},
                new REP_Models.ColumnMetadata { CODE = "TYPE_OBJ", NAME = "Тип объекта", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =0, INSERTABLE = 1, EDITABLE = 1,DOMAINCODE ="CODE", DIMCODE = "WFL_DIM_TYPE_OBJ",DIMNAME = "Тип объекта"},
                new REP_Models.ColumnMetadata { CODE = "TYPE_OBJ#N", NAME = "Тип объекта", DATATYPE = "VARCHAR2", DOMAINCODE = "Name", DATALENGTH = 1000, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "CODE_OBJ", NAME = "Код объекта", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =0, INSERTABLE = 1, EDITABLE = 1,DOMAINCODE ="CODE",DIMCODE = "WFL_DIM_LISTSUSP",DIMNAME = "Код объекта"},
                new REP_Models.ColumnMetadata { CODE = "CODE_OBJ#N", NAME = "Код объекта", DATATYPE = "VARCHAR2", DOMAINCODE = "Name", DATALENGTH = 1000, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "LIST_TASK", NAME = "Список задач", DATATYPE = "CLOB", VIEWVISIBLE =1, IS_SYSTEM = 1,TYPE_SYSTEM = "WFL_W_LIST"},
                new REP_Models.ColumnMetadata { CODE = "LNK_TASK", NAME = "Связь между задачами", DATATYPE = "CLOB", VIEWVISIBLE =1,IS_SYSTEM = 1, TYPE_SYSTEM = "WFL_W_LNK"}
                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>
                {
                    new REP_Models.ParamMetadata { CODE = "P_TYPE_OBJ", NAME = "Тип объекта", DOMAINCODE ="Code",  DATATYPE = "VARCHAR2", DATALENGTH = 30}
                };
            }

        }
        public static class WFL_WORKFLOW
        {
            public static string GetSqlQuery()
            {
                return "select id, code, name, list_obj, lnk_obj,start_param " + Environment.NewLine
                      + "  from wrfl_workflow " + Environment.NewLine
                      + " order by CODE";
            }
            public static (string sqlA, string sqlE, string slqD) GetActQuery()
            {
                return ("insert into wrfl_workflow (id,code,name,list_obj,lnk_obj,start_param)" + Environment.NewLine
                       +"values(ewa_row_seq.nextval,'wf_'||:IN_CODE, :IN_NAME, :IN_LIST_OBJ, :IN_LNK_OBJ, :IN_START_PARAM)"+Environment.NewLine
                       + "RETURNING id INTO :OUT_ID",
                        "UPDATE wrfl_workflow" + Environment.NewLine
                       + "   SET name = :IN_name," + Environment.NewLine
                       + "       list_obj = :IN_LIST_OBJ," + Environment.NewLine
                       + "       lnk_obj = :IN_LNK_OBJ," + Environment.NewLine
                       + "     start_param = :IN_START_PARAM" + Environment.NewLine
                       + "   WHERE id = :IN_ID",
                        "Delete from wrfl_workflow where Id = :IN_ID");
            }
            public static List<REP_Models.CfgData> GetCfgData()
            {
                return new List<REP_Models.CfgData>
                {
                new REP_Models.CfgData {CODE = "USE_FORM", NAME = "Использовать форму", VALUE = "WFLForm"},
                new REP_Models.CfgData {CODE = "USE_FORM", NAME = "Использовать форму", VALUE = "WFLParam"}
                };
            }
            public static List<REP_Models.GRID_Menu> GetMenudata()
            {
                return new List<REP_Models.GRID_Menu>
                {
                new REP_Models.GRID_Menu { CODE_CHILD = "WACTIVATION", CODE_PARENT = "MAIN", CODE_OBJECT = "WACTIVATION", NAME_OBJECT = "Журнал выполнения задания", KIND = 1, TYPE_OBJECT = "SHORTCUT" }
                };
            }
            public static List<REP_Models.ParamMetadata> GetTransitionParams(string code)
            {
                switch (code)
                {
                    case "WACTIVATION":
                        return new List<REP_Models.ParamMetadata>
                        {
                            new REP_Models.ParamMetadata { CODE = "P_ID_WRFL", NAME = "P_ID_WRFL",
                                         LINKEDCOLUMNCODE = "ID", LINKEDCOLUMNNAME = "ID",
                                              HIDDENVALUE = 1, ISMANDATORY = 0, ISSYSTEM =  0,
                                          DOMAINCODE = "ID", DATATYPE = "NUMBER", DATALENGTH = 30
                            }
                        };
                    default:
                        return new List<REP_Models.ParamMetadata>();
                }
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Процессы", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (true, true, true);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 32, IS_PK = 1 },
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "Код процесса", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1,INSERTABLE = 1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "Наименование процесса", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1,INSERTABLE = 1, EDITABLE = 1},
                new REP_Models.ColumnMetadata { CODE = "LIST_OBJ", NAME = "Список объектов", DATATYPE = "CLOB", VIEWVISIBLE =1, IS_SYSTEM = 1,TYPE_SYSTEM = "WFL_W_LIST"},
                new REP_Models.ColumnMetadata { CODE = "LNK_OBJ", NAME = "Связь между объектами", DATATYPE = "CLOB", VIEWVISIBLE =1,IS_SYSTEM = 1, TYPE_SYSTEM = "WFL_W_LNK"},
                new REP_Models.ColumnMetadata { CODE = "START_PARAM", NAME = "Параметры запуска", DATATYPE = "CLOB", VIEWVISIBLE =1,IS_SYSTEM = 1, TYPE_SYSTEM = "WFL_W_PARAM"}
                };
            }
        }

        public static class WFL_ACTIVATION
        {
            public static string GetSqlQuery()
            {
                return "select ID,ID_WRFL,CODE_WRFL,DTS,DTE, STATUS as STATUS_ID, (select name from wrfl_setting_data r where r.type_set = 'STATUS' and r.value = STATUS) as STATUS ,ERR" + Environment.NewLine
                      + " from wrfl_workflow_activation wfl" + Environment.NewLine
                      + "where (:P_ID_WRFL = wfl.ID_WRFL or :P_ID_WRFL is null )" + Environment.NewLine
                      + "order by ID,DTS";
            }
            public static List<REP_Models.GRID_Menu> GetMenudata()
            {
                return new List<REP_Models.GRID_Menu>
                {
                new REP_Models.GRID_Menu { CODE_CHILD = "WACTIVATION_DET", CODE_PARENT = "MAIN", CODE_OBJECT = "WACTIVATION_DET", NAME_OBJECT = "Детализация", KIND = 1, TYPE_OBJECT = "SHORTCUT" }
                };
            }
            public static List<REP_Models.ParamMetadata> GetTransitionParams(string code)
            {
                switch (code)
                {
                    case "WACTIVATION_DET":
                        return new List<REP_Models.ParamMetadata>
                        {
                            new REP_Models.ParamMetadata { CODE = "P_IDACTIV", NAME = "P_IDACTIV",
                                         LINKEDCOLUMNCODE = "ID", LINKEDCOLUMNNAME = "ID",
                                              HIDDENVALUE = 1, ISMANDATORY = 0, ISSYSTEM =  0,
                                          DOMAINCODE = "ID", DATATYPE = "NUMBER", DATALENGTH = 30
                            }
                        };
                    default:
                        return new List<REP_Models.ParamMetadata>();
                }
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Журнал выполнения заданий", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (false, false, false);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 32, IS_PK = 1 },
                new REP_Models.ColumnMetadata { CODE = "ID_WRFL", NAME = "ID_WRFL", DATATYPE = "NUMBER", DATALENGTH = 32 },
                new REP_Models.ColumnMetadata { CODE = "CODE_WRFL", NAME = "Код процесса", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "DTS", NAME = "Дата начала", DATATYPE = "TIMESTAMP", VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "DTE", NAME = "Дата окончания",DATATYPE = "TIMESTAMP", VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "STATUS_ID", NAME = "Статус ID", DATATYPE = "NUMBER", DATALENGTH = 32, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "STATUS", NAME = "Статус выполнения", DATATYPE = "VARCHAR2", DATALENGTH = 32, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "ERR", NAME = "Ошибка", DATATYPE = "CLOB", VIEWVISIBLE =1}
                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>
                {
                    new REP_Models.ParamMetadata { CODE = "P_ID_WRFL", NAME = "ID WRFL", DOMAINCODE ="ID",  DATATYPE = "NUMBER", DATALENGTH = 32}
                };
            }
        }
        public static class WFL_ACTIVATION_DET
        {
            public static string GetSqlQuery()
            {
                return "select wfl.id as ID_ACTIVATION, wfl.code_wrfl as CODE_WRFL, wrk.code_wrfl as CODE_WLT" + Environment.NewLine
                      + "      ,wrk_t.id_t as ID_ACT_TSK, wrk_t.code_task as CODE_TSK" + Environment.NewLine
                      + "      ,(select name from wrfl_setting_data r where r.type_set = 'STATUS' and r.value = wrk_t.status) as STATUS_TSK ,wrk_t.dts as DTS_TSK,wrk_t.dte as DTE_TSK ,wrk_t.err as ERR_TSK,wrk_t.data_action as ACTION_TSK" + Environment.NewLine
                      + "  from wrfl_workflow_activation wfl" + Environment.NewLine
                      + "  left join wrfl_task_log wrk_t on wrk_t.id = wfl.id" + Environment.NewLine
                      + "  left join wrfl_worklet_log wrk on wrk.id = wfl.id and wrk_t.id_w = wrk.id_w " + Environment.NewLine
                      + $"  where wfl.id = :P_IDACTIV " + Environment.NewLine
                      + " order by wrk_t.dte desc";

                
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Детализация", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (false, false, false);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID_ACTIVATION", NAME = "ID активации", DATATYPE = "NUMBER", DATALENGTH = 32, IS_PK = 1 ,VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE_WRFL", NAME = "Код воркфлоу", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "CODE_WLT", NAME = "Код ворклета", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "ID_ACT_TSK", NAME = "ID активации узла", DATATYPE = "NUMBER", DATALENGTH = 32,VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE_TSK", NAME = "Код узла", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "STATUS_TSK", NAME = "Статус выполнения", DATATYPE = "VARCHAR2", DATALENGTH = 32, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "DTS_TSK", NAME = "Дата начала", DATATYPE = "TIMESTAMP", VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "DTE_TSK", NAME = "Дата окончания",DATATYPE = "TIMESTAMP", VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "ERR_TSK", NAME = "Список ошибок", DATATYPE = "CLOB",VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "ACTION_TSK", NAME = "SQL запрос", DATATYPE = "CLOB", VIEWVISIBLE =1}};
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>
                {
                    new REP_Models.ParamMetadata { CODE = "P_IDACTIV", NAME = "ID ACTIV", DOMAINCODE ="ID",  DATATYPE = "NUMBER", DATALENGTH = 32}
                };
            }
        }
        public static class W_DIM_TYPE_OBJ
        {
            public static string GetSqlQuery()
            {
                return "select ID, CODE, NAME from wrfl_task_type";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список типов узлов", _typeobj);
            }

            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 16, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "CODE", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ID NAME", DATATYPE = "VARCHAR2", DATALENGTH = 450, VIEWVISIBLE =1}
                };
            }

        }
        public static class W_DIM_LISTOBJ
        {
            public static string GetSqlQuery()
            {
                return "select ID, CODE, NAME from WRFL_LIST_OBJ_VIEW";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список объектов для узла", _typeobj);
            }

            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 16, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "CODE", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ID NAME", DATATYPE = "VARCHAR2", DATALENGTH = 450, VIEWVISIBLE =1}
                };
            }
        }
        public static class W_DIM_LISTSUSP
        {
            public static string GetSqlQuery()
            {
                return "select ID, CODE, NAME from WRFL_LIST_SUSP_VIEW";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список объектов для ворклета", _typeobj);
            }

            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 16, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "CODE", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ID NAME", DATATYPE = "VARCHAR2", DATALENGTH = 450, VIEWVISIBLE =1}
                };
            }
        }
    }
}
