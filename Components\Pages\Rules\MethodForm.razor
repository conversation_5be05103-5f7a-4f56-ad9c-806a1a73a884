﻿@attribute [Authorize]

@using System
@using System.Collections.Generic
@using System.Linq
@using System.Text
@using System.Text.Json
@using System.Threading.Tasks
@using System.Text.RegularExpressions
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Radzen
@using Radzen.Blazor
@using Microsoft.AspNetCore.Identity
@using EWA.Models
@using EWA.Services
@inject TooltipService tooltipService
@inject AMLService _amlserv



<RadzenStack>
    <RadzenFieldset Text="Выходной параметр" Collapsible="true" Style="width: 100%; height: 100%;">
        <RadzenDataGrid @ref="selectFieldGrid"
                        Data="@selectFieldItems"
                        TItem="AML_Models.SelectField"
                        ColumnWidth="200px"
                        AllowPaging="true"
                        AllowSorting="true"
                        ShowPagingSummary="true"
                        Style="width: 100%; margin-bottom: 20px;">
        <HeaderTemplate >
                <RadzenStack Orientation="Orientation.Horizontal" Gap="0rem">
            <RadzenButton Icon="reset_wrench"
                                      Click="@RestOutData"
                                      Variant="Variant.Text"
                                      ButtonStyle="ButtonStyle.Base"
                                      MouseEnter="@(args => ShowTooltip(args, "восстановить выходные данные", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
            </RadzenStack>
           
            </HeaderTemplate>
            <Columns>
                <RadzenDataGridColumn Title="Удалить">
                    <Template Context="selectField">
                        <RadzenButton Icon="delete" Size="ButtonSize.Small" Click="() => RemoveSelectField(selectField)" />
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="Field" Title="Значение" />
                <RadzenDataGridColumn Property="Alias" Title="Код" />
                
            </Columns>
        </RadzenDataGrid>
    </RadzenFieldset>

    <RadzenFieldset Text="Список параметров" Collapsible="true" Style="width: 100%; height: 100%;">
        <RadzenDataGrid @ref="paramsGrid"
                        Data="@paramsList"
                        TItem="AML_Models.Params"
                        AllowPaging="true"
                        AllowSorting="true"
                        ShowPagingSummary="true"
                        Style="width: 100%; margin-bottom: 20px;">
            <HeaderTemplate>
                <RadzenStack Orientation="Orientation.Horizontal" Gap="0rem">
                    <RadzenButton Icon="reset_wrench"
                                  Click="@RestParamtData"
                                  Variant="Variant.Text"
                                  ButtonStyle="ButtonStyle.Base"
                                  MouseEnter="@(args => ShowTooltip(args, "восстановить параметры", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                </RadzenStack>

            </HeaderTemplate>

            <Columns>
                <RadzenDataGridColumn Title="Удалить">
                    <Template Context="param">
                        <RadzenButton Icon="delete" Size="ButtonSize.Small" Click="() => RemoveParam(param)" />
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="Value" Title="Значение" />
                <RadzenDataGridColumn Property="Parameter" Title="Параметр" />
                <RadzenDataGridColumn Title="Тип значения">
                    <Template Context="param">
                            <RadzenDropDown @bind-Value="param.TypeVal"
                                            Data="@(param.Parameter == "IND_PARAM" ? type_PARAM.Where(t => t.Key == "I") : type_PARAM)"
                                            TextProperty="Value"
                                            ValueProperty="Key"
                                            Style="width: 100%;"
                                            Change="@(args => OnChooseTypeAttr(param, args))"
                                            Placeholder="Выберите или введите значение"
                                            AllowClear="true"/>
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="Значение (редактируемое)">
                    <Template Context="param">
                        @if (param.TypeVal != "V" && param.TypeVal != "W")
		                    {
			                    <RadzenDropDown @bind-Value="param.Value"
                                Data="@AttrsVal"
                                            TextProperty="Value"
                                            ValueProperty="Key"
                                Style="width:200px; display:block;"
                                Placeholder="Выберите атрибут"
                                TValue="string"
                                AllowFiltering="true"
                                FilterCaseSensitivity=FilterCaseSensitivity.CaseInsensitive />
                            }
                        else if (param.TypeVal == "V" || param.TypeVal == "W")
		                    {
                            <RadzenTextBox @bind-Value="param.Value" Style="width:100%; display:block;" />
		                    }		
                    </Template>
                </RadzenDataGridColumn>


            </Columns>
        </RadzenDataGrid>
    </RadzenFieldset>
    <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End" Gap="0.5rem" Style="margin-top: 1rem;">
        <RadzenButton ButtonType="ButtonType.Submit" Icon="save" Text="Сохранить" Click="@SaveClick" Variant="Variant.Flat" />
        <RadzenButton ButtonStyle="ButtonStyle.Light" Text="Отмена" Click="@CancelClick" Variant="Variant.Flat" />
    </RadzenStack>
</RadzenStack>

@code {
    public AML_Models.MethodMetaResult in_data = new();

    private List<AML_Models.SelectField> selectFieldItems = new();
    private List<AML_Models.Filter> filters = new();
    private List<AML_Models.Params> paramsList = new();

    private List<AML_Models.SelectField> in_seldata = new();
    private List<AML_Models.Filter> in_filtdata = new();
    private List<AML_Models.Params> in_paramdata = new();
    private string in_codeFLTM = string.Empty;

    [Parameter]
    public decimal MethodID { get; set; }
    [Parameter]
    public string MethodCode { get; set; }
    [Parameter]
    public Dictionary<string, string> metaALG { get; set; }
    [Parameter]
    public Dictionary<string, object> metaALGData { get; set; }
    public Dictionary<string, object> metaALGData_OUT = new Dictionary<string, object>();

    RadzenDataGrid<AML_Models.SelectField> selectFieldGrid;
    RadzenDataGrid<AML_Models.Filter> filtersGrid;
    RadzenDataGrid<AML_Models.Params> paramsGrid;
    [Inject]
    protected DialogService DialogService { get; set; }
    private List<DropDownAttr> Attrs = new List<DropDownAttr>();
    private List<DropDownAttr> AttrsVal = new List<DropDownAttr>();

    private List<KeyValuePair<string, string>> type_PARAM = new()
{
    new KeyValuePair<string, string>("A", "Атрибут"),
    new KeyValuePair<string, string>("I", "Показатель"),
    new KeyValuePair<string, string>("V", "Значение"),
    new KeyValuePair<string, string>("F", "Константа"),
    new KeyValuePair<string, string>("M", "Мемори параметр"),
    new KeyValuePair<string, string>("W", "Параметр процесса"),
};

    void ShowTooltip(ElementReference elementReference, string buttomName, TooltipOptions options = null) => tooltipService.Open(elementReference, buttomName, options);

    protected override async Task OnInitializedAsync()

    {
        foreach (var item in metaALGData)
        {
            try
            {
                if (item.Key == "MTD_SLCT")
                {
                    in_seldata = JsonSerializer.Deserialize<List<AML_Models.SelectField>>(item.Value.ToString());
                }
                else if (item.Key == "MTD_PARAMS")
                {
                    in_paramdata = JsonSerializer.Deserialize<List<AML_Models.Params>>(item.Value.ToString());
                }
                else if (item.Key == "MTD_INDF")
                {
                    in_codeFLTM = item.Value.ToString();
                }
            }
            catch (Exception e)
            {
                var err = e.Message;
            }
        }



        var resmeta = await _amlserv.GetMethodMeta(MethodID);
        if (resmeta.status == 1)
        {
            in_data = resmeta.result;
        }
        if (in_seldata.Count > 0 || in_paramdata.Count > 0)
        {
            selectFieldItems = new List<AML_Models.SelectField>(in_seldata);
            paramsList = in_paramdata.Select(p => new AML_Models.Params
                                      {
                                        Parameter = p.Parameter,
                                        TypeVal = p.TypeVal,
                                        Value = p.Value
                                      }).ToList();
        }
        else 
        {
            selectFieldItems = new List<AML_Models.SelectField>(resmeta.result.SelectTab);
            paramsList = resmeta.result.Params
                                        .Select(p => new AML_Models.Params
                                            {
                                                Parameter = p.Parameter,
                                                TypeVal = p.TypeVal,
                                                Value = p.Value
                                            }).ToList();
        }
        if (!string.IsNullOrEmpty(in_codeFLTM))
        {
            paramsList.Add(new AML_Models.Params
                {
                    Parameter = "IND_PARAM",
                    TypeVal = "I",
                    Value = in_codeFLTM
                });
        }
        else
        {
        paramsList.Add(new AML_Models.Params
                {
                    Parameter = "IND_PARAM",
                    TypeVal = null,
                    Value = null
                });
        }
        var AttSpr = await _amlserv.GetAttrAsync();
        Attrs = AttSpr.Select(kv => new DropDownAttr
            {
                Key = kv.Key,
                Value = kv.Value.Value,
                Tattr = kv.Value.tattr
            }).ToList();
    }
    private async Task RestOutData()
    {
        var res = await DialogService.Confirm("Восстановить выходные параметры?", "Откат", new ConfirmOptions() { OkButtonText = "Да", CancelButtonText = "Нет" });
        if (res == true)
        {
            selectFieldItems = in_data.SelectTab;
            await selectFieldGrid.Reload();
            StateHasChanged();
        }
    }
    
    private async Task RestParamtData()
    {
        var res = await DialogService.Confirm("Восстановить параметры?", "Откат", new ConfirmOptions() { OkButtonText = "Да", CancelButtonText = "Нет" });
        if (res == true)
        {
            paramsList = in_data.Params;
            await paramsGrid.Reload();
            StateHasChanged();
        }
    }

    private void RemoveSelectField(AML_Models.SelectField selectField)
    {

        selectFieldItems.Remove(selectField);
        selectFieldGrid.Reload();
        StateHasChanged();
    }

    private void RemoveParam(AML_Models.Params param)
    {
        paramsList.Remove(param);
        paramsGrid.Reload();
        StateHasChanged();
    }
    private void OnChooseTypeAttr(AML_Models.Params param, object value)
    {
        if (value != null)
        {
            param.TypeVal = value.ToString();
            param.Value = null;
            if (param.TypeVal != "V" || param.TypeVal != "W")
            {
                AttrsVal = new List<DropDownAttr>(Attrs
                                                    .Where(x => x.Tattr == param.TypeVal)
                                                    .Select(x => new DropDownAttr
                                                            {
                                                                Key = x.Key,
                                                                Value = x.Value,
                                                                Tattr = x.Tattr
                                                            })
                                                    );
            }
        }
        else
        { param.TypeVal = null;
            param.Value = null;
        }



        StateHasChanged();
    }
    public class DropDownAttr
    {
        public string Key { get; set; }
        public string Value { get; set; }
        public string Tattr { get; set; }

    }
    private void SaveClick()
    {
        metaALGData_OUT.Clear();

        var indParam = paramsList.FirstOrDefault(p => p.Parameter == "IND_PARAM");
        string indParamValue = string.Empty;
        if (indParam != null)
        {
            indParamValue = indParam.Value;
            paramsList.Remove(indParam);
        }


        foreach (var kvp in metaALG)
        {
            if (kvp.Value == "MTD_PARAMS")
            {
                metaALGData_OUT.Add(kvp.Key, JsonSerializer.Serialize(paramsList));
            }
            else if (kvp.Value == "MTD_SLCT")
            {
                metaALGData_OUT.Add(kvp.Key, JsonSerializer.Serialize(selectFieldItems));
            }
            else if (kvp.Value == "MTD_INDF")
            {
                metaALGData_OUT.Add(kvp.Key, indParamValue);
            }
            
        }

        DialogService.Close(new { status = 1, out_Data = metaALGData_OUT });
    }
    private void CancelClick()
    {
        metaALGData_OUT.Clear();
        foreach (var kvp in metaALG)
        {
            metaALGData_OUT.Add(kvp.Key, null);
        }
        DialogService.Close(new { status = 0, out_Data = metaALGData_OUT });
    }
}
