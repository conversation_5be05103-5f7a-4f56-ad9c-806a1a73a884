﻿using EWA.Models;
using static EWA.Models.SIB_Models;
using static EWA.Models.REP_Models;
using System.Reflection;
using static EWA.Services.SIBService;
using Parlot.Fluent;

namespace EWA.Services
{
    public class SIBModule
    {
        
        private readonly IConfiguration _configuration;
        private readonly ConnectionStringProvider _ConService;
        public SIBModule(IConfiguration configuration, ConnectionStringProvider ConService)
        {
          
            _configuration = configuration;
            _ConService = ConService;
            dbService = new DBService(_ConService.GetConnectionString("EWAREP"));
        }
        private DBService dbService;
        
        /*дефолты*/
        static string _typeobj = "STables";
        readonly string _namespace = "EWA.Services.SIBModule+";
        /*справочник объектов*/
        public static readonly List<SIBRepData> SIBRepRegistory = new()
        {
                new SIBRepData
                {
                    Alias = "SUSERS",
                    Code = "SIB_Users",
                    Methods = new List<string> { "GetTransitionParams", "GetSqlQuery", "GetObjInfo","GetActions","GetColumnMetadata","GetMenudata","GetParamMetadata","GetActQuery" }
                },
                new SIBRepData
                {
                    Alias = "SSPRAD",
                    Code = "SIB_AD_SPR",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo","GetActions","GetColumnMetadata","GetActQuery" }
                },
                new SIBRepData
                {
                    Alias = "SGRPAD",
                    Code = "SIB_AD_ASSGRP",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo","GetActions","GetColumnMetadata", "GetParamMetadata", "GetActQuery" }
                },
                
                new SIBRepData
                {
                    Alias = "SOBJECT",
                    Code = "SIB_Object",
                    Methods = new List<string> { "GetTransitionParams","GetSqlQuery","GetObjInfo","GetActions","GetColumnMetadata","GetMenudata","GetParamMetadata","GetActQuery" }
                },
                new SIBRepData
                {
                    Alias = "SOBJECTATTR",
                    Code = "SIB_Object_Attr",
                    Methods = new List<string> { "GetSqlQuery","GetObjInfo","GetActions","GetColumnMetadata","GetParamMetadata","GetActQuery" }
                },
                new SIBRepData
                {
                    Alias = "SUSERSATTR",
                    Code = "SIB_Users_Attr",
                    Methods = new List<string> { "GetSqlQuery","GetObjInfo","GetActions","GetColumnMetadata","GetParamMetadata","GetActQuery" }
                },
                new SIBRepData
                {
                    Alias = "SROLES",
                    Code = "SIB_Roles",
                    Methods = new List<string> { "GetTransitionParams", "GetSqlQuery","GetObjInfo","GetActions","GetColumnMetadata","GetMenudata","GetParamMetadata","GetActQuery" }
                },
                new SIBRepData
                {
                    Alias = "SMODULES",
                    Code = "SIB_Modules",
                    Methods = new List<string> { "GetTransitionParams", "GetSqlQuery","GetObjInfo","GetActions","GetColumnMetadata","GetMenudata","GetParamMetadata","GetActQuery" }
                },
                new SIBRepData
                {
                    Alias = "SRULES",
                    Code = "SIB_Rules",
                    Methods = new List<string> { "GetTransitionParams","GetSqlQuery","GetObjInfo","GetActions","GetColumnMetadata","GetParamMetadata","GetMenudata","GetActQuery" }
                },
                new SIBRepData
                {
                    Alias = "SUROLES",
                    Code = "SIB_ViewUR",
                    Methods = new List<string> { "GetSqlQuery","GetObjInfo","GetColumnMetadata","GetParamMetadata" }
                },
                
                new SIBRepData
                {
                    Alias = "SUMODULES",
                    Code = "SIB_ViewUM",
                    Methods = new List<string> { "GetSqlQuery","GetObjInfo","GetColumnMetadata","GetParamMetadata" }
                },
                new SIBRepData
                {
                    Alias = "SPOLICY",
                    Code = "SIB_Policy",
                    Methods = new List<string> { "GetSqlQuery","GetObjInfo","GetActions","GetColumnMetadata","GetParamMetadata","GetActQuery" }
                },
                new SIBRepData
                {
                    Alias = "SATTRSPR",
                    Code = "SIB_AttrSpr",
                    Methods = new List<string> { "GetSqlQuery","GetObjInfo","GetActions","GetColumnMetadata","GetParamMetadata","GetActQuery" }
                },
                new SIBRepData
                {
                    Alias = "SCONFIGS",
                    Code = "SIB_CONFIG",
                    Methods = new List<string> { "GetSqlQuery","GetObjInfo","GetActions","GetColumnMetadata","GetParamMetadata","GetActQuery" }
                },

                //Сюда Dim добавляем для удобства
                
                new SIBRepData
                {
                    Alias = "SIB_DIM_SINCH_TYPE",
                    Code = "S_DIM_SINCH_TYPE",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo", "GetColumnMetadata" }
                },
                new SIBRepData
                {
                    Alias = "SIB_DIM_AD_SPR",
                    Code = "S_DIM_AD_SPR",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo", "GetColumnMetadata" }
                },
                new SIBRepData
                {
                    Alias = "SIB_DIM_EWA_SPR",
                    Code = "S_DIM_EWA_SPR",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo", "GetColumnMetadata" }
                },
                new SIBRepData
                {
                    Alias = "SIB_DIM_CFG_TYPE",
                    Code = "S_DIM_CFG_TYPE",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo", "GetColumnMetadata" }
                },
                new SIBRepData
                {
                    Alias = "SIB_DIM_EWA_TYPE",
                    Code = "S_DIM_EWA_TYPE",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo", "GetColumnMetadata" }
                },
                new SIBRepData
                {
                    Alias = "SIB_DIM_AD_TYPE",
                    Code = "S_DIM_AD_TYPE",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo", "GetColumnMetadata" }
                },
                new SIBRepData
                { 
                    Alias = "SIB_DIM_MODULES",
                    Code = "S_DIM_MOUDLES",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo", "GetColumnMetadata" }
                },
                new SIBRepData
                {
                    Alias = "SIB_DIM_ROLES",
                    Code = "S_DIM_ROLES",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo", "GetColumnMetadata" }
                },
                new SIBRepData
                {
                    Alias = "SIB_DIM_OBJ_ATTR",
                    Code = "S_DIM_OBJ_ATTR",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo", "GetColumnMetadata" }
                },
                new SIBRepData
                {
                    Alias = "SIB_DIM_USR_ATTR",
                    Code = "S_DIM_USER_ATTR",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo", "GetColumnMetadata" }
                },
                new SIBRepData
                {
                    Alias = "SIB_DIM_TATTR",
                    Code = "S_DIM_TYPEATTR",
                    Methods = new List<string> { "GetSqlQuery", "GetObjInfo", "GetColumnMetadata" }
                }
        };

        public static Task<IEnumerable<MenuInfo>> GetMenuData()

        {

            var menuInfos = new List<MenuInfo>
                {
                    new MenuInfo { CODE_CHILD = "F_USERS", CODE_PARENT = "MAIN", CODE_OBJECT = "F_USERS", NAME_OBJECT = "Пользователи", TYPE_OBJECT = "FOLDER"},
                    new MenuInfo { CODE_CHILD = "F_AD", CODE_PARENT = "MAIN", CODE_OBJECT = "F_AD", NAME_OBJECT = "Настройка Active Directory", TYPE_OBJECT = "FOLDER"},
                    new MenuInfo { CODE_CHILD = "F_RULES", CODE_PARENT = "MAIN", CODE_OBJECT = "F_RULES", NAME_OBJECT = "Настройка доступа", TYPE_OBJECT = "FOLDER" },
                    new MenuInfo { CODE_CHILD = "F_SYSTEM", CODE_PARENT = "MAIN", CODE_OBJECT = "F_SYSTEM", NAME_OBJECT = "Настройка системы", TYPE_OBJECT = "FOLDER" },
                    //F_USERS
                    new MenuInfo { CODE_CHILD = "SUsers", CODE_PARENT = "F_USERS", CODE_OBJECT = "SUsers", NAME_OBJECT = "Список пользователей", TYPE_OBJECT = _typeobj, IS_AUTOEXECUTE = 1  },
                    new MenuInfo { CODE_CHILD = "SRoles", CODE_PARENT = "F_USERS", CODE_OBJECT = "SRoles", NAME_OBJECT = "Список ролей", TYPE_OBJECT = _typeobj, IS_AUTOEXECUTE = 1  },
                    new MenuInfo { CODE_CHILD = "SModules", CODE_PARENT = "F_USERS", CODE_OBJECT = "SModules", NAME_OBJECT = "Список модулей", TYPE_OBJECT = _typeobj , IS_AUTOEXECUTE = 1 },
                    new MenuInfo { CODE_CHILD = "SUROLES", CODE_PARENT = "F_USERS", CODE_OBJECT = "SUROLES", NAME_OBJECT = "Матрица доступа к ролям", TYPE_OBJECT = _typeobj, IS_AUTOEXECUTE = 1  },
                    new MenuInfo { CODE_CHILD = "SUMODULES", CODE_PARENT = "F_USERS", CODE_OBJECT = "SUMODULES", NAME_OBJECT = "Матрица доступа к модулям", TYPE_OBJECT = _typeobj, IS_AUTOEXECUTE = 1  },
                    //F_AD
                    new MenuInfo { CODE_CHILD = "SSPRAD", CODE_PARENT = "F_AD", CODE_OBJECT = "SSPRAD", NAME_OBJECT = "Справочник объектов AD", TYPE_OBJECT = _typeobj, IS_AUTOEXECUTE = 1  },
                    new MenuInfo { CODE_CHILD = "SGRPAD", CODE_PARENT = "F_AD", CODE_OBJECT = "SGRPAD", NAME_OBJECT = "Матрица объектов AD", TYPE_OBJECT = _typeobj, IS_AUTOEXECUTE = 1  },
                    //F_RULES
                    new MenuInfo { CODE_CHILD = "SRules", CODE_PARENT = "F_RULES", CODE_OBJECT = "SRules", NAME_OBJECT = "Правила доступа", TYPE_OBJECT = _typeobj, IS_AUTOEXECUTE = 1  },
                    new MenuInfo { CODE_CHILD = "SPolicy", CODE_PARENT = "F_RULES", CODE_OBJECT = "SPolicy", NAME_OBJECT = "Матрица доступа к объектам", TYPE_OBJECT = _typeobj, IS_AUTOEXECUTE = 1  },
                    new MenuInfo { CODE_CHILD = "SAttrSpr", CODE_PARENT = "F_RULES", CODE_OBJECT = "SAttrSpr", NAME_OBJECT = "Список атрибутов", TYPE_OBJECT = _typeobj, IS_AUTOEXECUTE = 1 },
                    new MenuInfo { CODE_CHILD = "SObject", CODE_PARENT = "F_RULES", CODE_OBJECT = "SObject", NAME_OBJECT = "Список объектов", TYPE_OBJECT = _typeobj, IS_AUTOEXECUTE = 1  },
                    //F_SYSTEM
                    new MenuInfo { CODE_CHILD = "SCONFIGS", CODE_PARENT = "F_SYSTEM", CODE_OBJECT = "SCONFIGS", NAME_OBJECT = "Настройка параметров безопасности", TYPE_OBJECT = _typeobj, IS_AUTOEXECUTE = 1  }
                };
            return Task.FromResult(menuInfos.AsEnumerable());
        }
        public async Task<IEnumerable<SPRShort>> GetSPRData(string code_spr)
        {
            IEnumerable<SPRShort> _sprMetadata;
            REP_Models.IMetadataObject metaObject = null;
            RepositoryInfo repositoryInfo = new RepositoryInfo();
            string query = string.Empty;
            string param_meta = string.Empty;
            List<REP_Models.ParamMetadata> _metadata_param = new List<REP_Models.ParamMetadata>();
            List<REP_Models.ColumnMetadata> _metadata_column = new List<REP_Models.ColumnMetadata>();

            _sprMetadata = Enumerable.Empty<SPRShort>();
            repositoryInfo = GetRepositoryInfo(code_spr);
            query = repositoryInfo.Query;
            _metadata_param = repositoryInfo.Param;
            _metadata_column = repositoryInfo.Column;
            var parameters = new Dictionary<string, object>();
            foreach (var kvp in _metadata_param.Where(x => x.CODE != "p_NameFilter"))
            {
                string paramName = kvp.CODE;
                object paramValue = null;

                parameters.Add(paramName, paramValue);
            }
            var (items, errorMessage) = await dbService.GetDataSPRShort(query, parameters);

            _sprMetadata = items;
            return (_sprMetadata);
        }
        public RepositoryInfo GetRepositoryInfo(string code)
        {
            RepositoryInfo rep = new RepositoryInfo();
            rep.Query = GetSqlQuery(code);
            rep.Code = code;
            (rep.Title, rep.TypeObject) = GetObjInfo(code);
            (rep.ColAdd, rep.ColUpd, rep.ColDel) =GetActions(code);
            rep.Column = GetColumnMetadata(code);
            rep.Param = GetParamMetadata(code);
            (rep.AddSql, rep.UpdSql, rep.DelSql) = GetActQuery(code);
            rep.Dbname_Query = _ConService.GetConnectionString("EWAREP");
            rep.MenuGrid = GetMenudata(code);
            rep.AppCode = "SIB";

            return rep;

        }
        public (string sqlA, string sqlE, string slqD) GetActQuery(string code)
        {
            (string sqlA, string sqlE, string sqlD) def_val = (null, null, null);
            var meta = SIBRepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetActQuery"))
                return def_val;

            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetActQuery", BindingFlags.Public | BindingFlags.Static);
            
            if (method == null)
                return def_val;

            var result = method.Invoke(null, null);

            return result is ValueTuple<string, string, string> tupleResult
                ? tupleResult
                : def_val;
        }
        public string GetSqlQuery(string code)
        {
            string def_val = "SELECT 'NO DATA' as COLUM from DUAL";

            var meta = SIBRepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetSqlQuery"))
                return def_val;
            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetSqlQuery", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;
            
            var result = method.Invoke(null, null);
            return result as string ?? def_val;
        }
        public (string title, string typeobj) GetObjInfo(string code)
        {
            (string title, string typeobj) def_val = ("No Data", "Undef");

            var meta = SIBRepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetObjInfo"))
                return def_val;
            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetObjInfo", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;

            var result = method.Invoke(null, null);
            return result as (string, string)? ?? def_val;
        }

        public (bool actA, bool actE, bool actD) GetActions(string code)
        {
            (bool actA, bool actE, bool actD) def_val = (false, false, false);

            var meta = SIBRepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetActions"))
                return def_val;
            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetActions", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;

            var result = method.Invoke(null, null);
            return result as (bool, bool, bool)? ?? def_val;

        }
        public List<REP_Models.ColumnMetadata> GetColumnMetadata(string code)
        {

            List<REP_Models.ColumnMetadata> def_val = new List<REP_Models.ColumnMetadata>{
                new REP_Models.ColumnMetadata
                {
                    CODE = "COLUM",
                    NAME = "COLUM",
                    DATATYPE = "VARCHAR2",
                    DATALENGTH = 30,
                    COL_REF = null,
                    COL_REF_DATA = null,
                    IS_PK = 0
                }
            };

            var meta = SIBRepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetColumnMetadata"))
                return def_val;
            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetColumnMetadata", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;

            var result = method.Invoke(null, null);
            return result as List<REP_Models.ColumnMetadata> ?? def_val;
            
        }

        public IEnumerable<REP_Models.GRID_Menu> GetMenudata(string code)
        {
            IEnumerable<GRID_Menu> def_val = Enumerable.Empty<REP_Models.GRID_Menu>();
            
            var meta = SIBRepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetMenudata"))
                return def_val;
            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetMenudata", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;

            var result = method.Invoke(null, null);
            return result as IEnumerable<GRID_Menu> ?? def_val;
        }


        public List<REP_Models.ParamMetadata> GetParamMetadata(string code)
        {

            List<REP_Models.ParamMetadata> def_val = new List<REP_Models.ParamMetadata>(); 

            var meta = SIBRepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetParamMetadata"))
                return def_val;
            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetParamMetadata", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;

            var result = method.Invoke(null, null);
            return result as List<REP_Models.ParamMetadata> ?? def_val;

        }
        public List<REP_Models.ParamMetadata> GetTransitionParams(string code, string code_child)
        {
            List<REP_Models.ParamMetadata> def_val = new List<REP_Models.ParamMetadata>();

            var meta = SIBRepRegistory.FirstOrDefault(x =>
                string.Equals(x.Alias, code, StringComparison.OrdinalIgnoreCase));

            if (meta == null || !meta.Methods.Contains("GetTransitionParams"))
                return def_val;
            var typeName = _namespace + meta.Code;
            var type = Type.GetType(typeName);

            if (type == null)
                return def_val;

            var method = type.GetMethod("GetTransitionParams", BindingFlags.Public | BindingFlags.Static);

            if (method == null)
                return def_val;

            var result = method.Invoke(null, [code_child]);
            return result as List<REP_Models.ParamMetadata> ?? def_val;

        }

        public static class SIB_Users
        {
            public static List<REP_Models.ParamMetadata> GetTransitionParams(string code)
            {
                switch (code)
                {
                    case "SUSERSATTR":
                        return new List<REP_Models.ParamMetadata>
                        {
                            new REP_Models.ParamMetadata { CODE = "P_USER_ID", NAME = "P_USER_ID",
                                         LINKEDCOLUMNCODE = "ID", LINKEDCOLUMNNAME =  "USER_ID",
                                              HIDDENVALUE = 1, ISMANDATORY = 0, ISSYSTEM =  0,
                                          DOMAINCODE = "Id", DATATYPE = "NUMBER", DATALENGTH = 32
                            }
                        };
                    case "SUROLES":
                        return new List<REP_Models.ParamMetadata>
                        {
                            new REP_Models.ParamMetadata { CODE = "P_USER_ID", NAME = "P_USER_ID",
                                         LINKEDCOLUMNCODE = "ID", LINKEDCOLUMNNAME =  "USER_ID",
                                              HIDDENVALUE = 1, ISMANDATORY = 0, ISSYSTEM =  0,
                                          DOMAINCODE = "Id", DATATYPE = "NUMBER", DATALENGTH = 32
                            }
                        };
                    case "SUMODULES":
                        return new List<REP_Models.ParamMetadata>
                        {
                            new REP_Models.ParamMetadata { CODE = "P_USER_ID", NAME = "P_USER_ID",
                                         LINKEDCOLUMNCODE = "ID", LINKEDCOLUMNNAME =  "USER_ID",
                                              HIDDENVALUE = 1, ISMANDATORY = 0, ISSYSTEM =  0,
                                          DOMAINCODE = "Id", DATATYPE = "NUMBER", DATALENGTH = 32
                            }
                        };
                    case "SPOLICY":
                        return new List<REP_Models.ParamMetadata>
                        {
                            new REP_Models.ParamMetadata { CODE = "P_USR_CODE", NAME = "P_USR_CODE",
                                         LINKEDCOLUMNCODE = "CODE", LINKEDCOLUMNNAME =  "CODE",
                                              HIDDENVALUE = 1, ISMANDATORY = 0, ISSYSTEM =  0,
                                          DOMAINCODE = "Code", DATATYPE = "VARCHAR2", DATALENGTH = 30
                            }
                        };
                    default:
                        return new List<REP_Models.ParamMetadata>();
                }
            }
            public static string GetSqlQuery()
            {
                return "SELECT s.id, s.code, s.name, s.ext_login, s.ext_domain, s.is_admin,"+Environment.NewLine
                      +"       s.position,s.department,s.phone,s.email,s.dt_last_login,s.dt_create,"+Environment.NewLine
                      +"       s.dt_change,s.user_create,s.user_change,s.is_active,s.is_block,s.dt_block,s.type_block,s.login_fail_count,"+Environment.NewLine
                      +"       s.is_techn_pass, s.techn_pass" + Environment.NewLine
                      +"  FROM SIB_USERS s" +Environment.NewLine
                      +" WHERE (:P_USER_CODE IS NULL OR UPPER(S.CODE) LIKE '%'||UPPER(:P_USER_CODE)||'%')"+Environment.NewLine
                      +"   AND (:P_USER_NAME IS NULL OR UPPER(S.NAME) LIKE '%'||UPPER(:P_USER_NAME)||'%')"+Environment.NewLine
                      +"   AND (trunc(s.dt_create) BETWEEN NVL(:P_USER_DTFROM,TO_DATE('01011980','DDMMYYYY')) AND NVL(:P_USER_DTTO,TO_DATE('01013001','DDMMYYYY')))";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список Пользователей", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (true, true, false);
            }

            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                    new REP_Models.ColumnMetadata { CODE = "ID", NAME = "Уникальный идентификатор клиента", DATATYPE = "NUMBER", DATALENGTH = 32, IS_PK = 1,VIEWVISIBLE = 0 },
                    new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "Логин пользователя", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1, ISMANDATORY = 1, INSERTABLE = 1 },
                    new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ФИО", DATATYPE = "VARCHAR2", DATALENGTH = 1000, EDITABLE = 1, VIEWVISIBLE =1, ISMANDATORY = 1, INSERTABLE = 1 },
                    new REP_Models.ColumnMetadata { CODE = "EXT_LOGIN", NAME = "Внешний логин пользователя", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1, ISMANDATORY = 0},
                    new REP_Models.ColumnMetadata { CODE = "EXT_DOMAIN", NAME = "Имя домена пользователя", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1, ISMANDATORY = 0},
                    new REP_Models.ColumnMetadata { CODE = "IS_ADMIN", NAME = "Признак суперадмина", DATATYPE = "NUMBER", DATALENGTH = 1, EDITABLE =0, VIEWVISIBLE =1,  DOMAINCODE ="Bool",INSERTABLE = 0 },
                    new REP_Models.ColumnMetadata { CODE = "POSITION", NAME = "Должность", DATATYPE = "VARCHAR2", DATALENGTH = 250, EDITABLE =1, VIEWVISIBLE =1, INSERTABLE = 1 },
                    new REP_Models.ColumnMetadata { CODE = "DEPARTMENT", NAME = "Подразделение", DATATYPE = "VARCHAR2", DATALENGTH = 250, EDITABLE =1, VIEWVISIBLE =1, INSERTABLE = 1  },
                    new REP_Models.ColumnMetadata { CODE = "PHONE", NAME = "Контактный телефон", DATATYPE = "VARCHAR2", DATALENGTH = 50, EDITABLE =1, VIEWVISIBLE =1, INSERTABLE = 1  },
                    new REP_Models.ColumnMetadata { CODE = "EMAIL", NAME = "Адрес электронной почты", DATATYPE = "VARCHAR2", DATALENGTH = 100, EDITABLE =1, VIEWVISIBLE =1, INSERTABLE = 1 },
                    new REP_Models.ColumnMetadata { CODE = "DT_LAST_LOGIN", NAME = "Дата и время последнего входа в приложение", DATATYPE = "DATE", DATALENGTH = null, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "DT_CREATE", NAME = "Дата создания", DATATYPE = "DATE", DATALENGTH = null, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "DT_CHANGE", NAME = "Дата изменения", DATATYPE = "DATE", DATALENGTH = null, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "USER_CREATE", NAME = "Кем создан", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "USER_CHANGE", NAME = "Кем изменен", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "IS_ACTIVE", NAME = "Признак активного пользователя ", DATATYPE = "NUMBER", DATALENGTH = 1, EDITABLE =0, VIEWVISIBLE =1, DOMAINCODE ="Bool" },
                    new REP_Models.ColumnMetadata { CODE = "IS_BLOCK", NAME = "Признак блокировки", DATATYPE = "NUMBER", DATALENGTH = 1, VIEWVISIBLE =1, DOMAINCODE ="Bool" },
                    new REP_Models.ColumnMetadata { CODE = "DT_BLOCK", NAME = "Дата блокировки пользователя", DATATYPE = "DATE", DATALENGTH = null, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "TYPE_BLOCK", NAME = "Тип блокировки", DATATYPE = "VARCHAR2", DATALENGTH = 10, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "LOGIN_FAIL_COUNT", NAME = "Кол-во неудачных попыток входа0", DATATYPE = "NUMBER", DATALENGTH = 10, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "IS_TECHN_PASS", NAME = "Признак использования технического пароля", DATATYPE = "NUMBER", DATALENGTH = 1, EDITABLE =0, VIEWVISIBLE =1, DOMAINCODE ="Bool" },
                    new REP_Models.ColumnMetadata { CODE = "TECHN_PASS", NAME = "Значение технического пароля", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1 }
                };
            }
            public static List<REP_Models.ColumnMetadata> GetPasswordMetadata(List<REP_Models.ColumnMetadata> columnMetadata)
            {
                columnMetadata.Add(new REP_Models.ColumnMetadata { CODE = "NEWPASSWORD", NAME = "Пароль", DATATYPE = "VARCHAR2", DATALENGTH = 2000, EDITABLE = 0, ISMANDATORY = 1, INSERTABLE = 1, VIEWVISIBLE = 1 });
                columnMetadata.Add(new REP_Models.ColumnMetadata { CODE = "CONFIRMPASSWORD", NAME = "Подтверждение пароля", DATATYPE = "VARCHAR2", DATALENGTH = 2000, EDITABLE = 0, ISMANDATORY = 1, INSERTABLE = 1, VIEWVISIBLE = 1 }); ; 
                return columnMetadata;
            }

            public static List<REP_Models.GRID_Menu> GetMenudata()
            {
                return new List<REP_Models.GRID_Menu>
                {
                new REP_Models.GRID_Menu { CODE_CHILD = "SUsersAttr", CODE_PARENT = "MAIN", CODE_OBJECT = "SUsersAttr", NAME_OBJECT = "Атрибуты пользователя", KIND = 1, TYPE_OBJECT = "SHORTCUT" },
                new REP_Models.GRID_Menu { CODE_CHILD = "SPolicy", CODE_PARENT = "MAIN", CODE_OBJECT = "SPolicy", NAME_OBJECT = "Правила и объекты пользователя", KIND = 1, TYPE_OBJECT = "SHORTCUT" },
                new REP_Models.GRID_Menu { CODE_CHILD = "SURoles", CODE_PARENT = "MAIN", CODE_OBJECT = "SURoles", NAME_OBJECT = "Роли пользователя", KIND = 1, TYPE_OBJECT = "SHORTCUT" },
                new REP_Models.GRID_Menu { CODE_CHILD = "SUModules", CODE_PARENT = "MAIN", CODE_OBJECT = "SUModules", NAME_OBJECT = "Модули пользователя", KIND = 1, TYPE_OBJECT = "SHORTCUT" }
                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>
                {
                new REP_Models.ParamMetadata { CODE = "P_USER_CODE", NAME = "Логин пользователя", DOMAINCODE ="Code",  DATATYPE = "VARCHAR2", DATALENGTH = 30, HIDDENVALUE = 0},
                new REP_Models.ParamMetadata { CODE = "P_USER_NAME", NAME = "ФИО", DOMAINCODE ="Code",  DATATYPE = "VARCHAR2", DATALENGTH = 1000, HIDDENVALUE = 0},
                new REP_Models.ParamMetadata { CODE = "P_USER_DTFROM", NAME = "Дата с...", DOMAINCODE ="DATE",  DATATYPE = "DATE", HIDDENVALUE = 0},
                new REP_Models.ParamMetadata { CODE = "P_USER_DTTO", NAME = "Дата по...", DOMAINCODE ="DATE",  DATATYPE = "DATE",  HIDDENVALUE = 0}
                };
            }
            public static (string sqlA, string sqlE, string slqD) GetActQuery()
            {
                return (//insert
                         "begin" + Environment.NewLine
                       + "insert into SIB_USERS(ID,CODE,NAME,IS_ADMIN," + Environment.NewLine
                       + "   POSITION,DEPARTMENT,PHONE,EMAIL,DT_CREATE," + Environment.NewLine
                       + "    DT_CHANGE,USER_CREATE,USER_CHANGE,IS_ACTIVE,IS_BLOCK," + Environment.NewLine
                       + "    IS_TECHN_PASS,TECHN_PASS,PASSWORDHASH)" + Environment.NewLine
                       + "values(:IN_ID,:IN_CODE,:IN_NAME,:IN_IS_ADMIN," + Environment.NewLine
                       + "       :IN_POSITION,:IN_DEPARTMENT,:IN_PHONE,:IN_EMAIL," + Environment.NewLine
                       + "       :IN_DT_CREATE,:IN_DT_CHANGE,:IN_USER_CREATE,:IN_USER_CHANGE,:IN_IS_ACTIVE," + Environment.NewLine
                       + "       :IN_IS_BLOCK,:IN_IS_TECHN_PASS,:IN_TECHN_PASS,:IN_PASSWORDHASH);" + Environment.NewLine
                       + "if :IN_ROLES is not null then " + Environment.NewLine
                       + "  for r in ( SELECT x.id FROM XMLTABLE( ('\"' || REPLACE(:IN_ROLES, ',', '\",\"') || '\"') " + Environment.NewLine
                       + "                              COLUMNS id NUMBER PATH '.') x )" + Environment.NewLine
                       + "      loop" + Environment.NewLine
                       + "           insert into SIB_USERROLES_LNK(ID_USER,ID_ROLE,DT_CHANGE,USER_CHANGE)" + Environment.NewLine
                       + "           values(:IN_ID, r.id,:IN_DT_CREATE,:IN_USER_CREATE );" + Environment.NewLine
                       + "       end loop;" + Environment.NewLine
                       + "end if;" + Environment.NewLine
                       + "if :IN_MODULES is not null then" + Environment.NewLine
                       + "  for m in ( SELECT x.id FROM XMLTABLE( ('\"' || REPLACE(:IN_MODULES, ',', '\",\"') || '\"')" + Environment.NewLine
                       + "                              COLUMNS id NUMBER PATH '.') x )" + Environment.NewLine
                       + "      loop" + Environment.NewLine
                       + "           insert into SIB_USERMODULES_LNK(ID_USER,ID_MODULE,DT_CHANGE,USER_CHANGE)" + Environment.NewLine
                       + "           values(:IN_ID, m.id,:IN_DT_CREATE,:IN_USER_CREATE ); " + Environment.NewLine
                       + "      end loop; " + Environment.NewLine
                       + "end if; " + Environment.NewLine
                       + "commit; " + Environment.NewLine
                       + "end; ",
                       //update
                        "begin" + Environment.NewLine
                       + "UPDATE SIB_USERS SET" + Environment.NewLine
                       + "NAME = :IN_NAME, POSITION = :IN_POSITION," + Environment.NewLine
                       + "DEPARTMENT = :IN_DEPARTMENT,PHONE = :IN_PHONE,EMAIL = :IN_EMAIL," + Environment.NewLine
                       + "DT_CHANGE = :IN_DT_CHANGE, USER_CHANGE = :IN_USER_CHANGE" + Environment.NewLine
                       + "Where Id = :IN_ID; " + Environment.NewLine
                       + "if :IN_ROLES is not null then " + Environment.NewLine
                       + "  delete from SIB_USERROLES_LNK where id_user = :IN_ID and " + Environment.NewLine
                       + "  ID_ROLE not in ( SELECT x.id FROM XMLTABLE( ('\"' || REPLACE(:IN_ROLES, ',', '\",\"') || '\"')" + Environment.NewLine
                       + "                                      COLUMNS id NUMBER PATH '.') x );" + Environment.NewLine
                       + "  for r in ( SELECT x.id FROM XMLTABLE( ('\"' || REPLACE(:IN_ROLES, ',', '\",\"') || '\"') " + Environment.NewLine
                       + "                              COLUMNS id NUMBER PATH '.') x )" + Environment.NewLine
                       + "      loop" + Environment.NewLine
                       + "           merge into SIB_USERROLES_LNK l" + Environment.NewLine
                       + "            using (select :IN_ID as ID_USER, r.id as ID_ROLE from dual) i" + Environment.NewLine
                       + "            on (l.ID_USER = i.ID_USER and l.ID_ROLE = i.ID_ROLE)" + Environment.NewLine
                       + "            when not matched then insert(ID_USER,ID_ROLE,DT_CHANGE,USER_CHANGE)" + Environment.NewLine
                       + "           values(:IN_ID, r.id,:IN_DT_CHANGE,:IN_USER_CHANGE );" + Environment.NewLine
                       + "       end loop;" + Environment.NewLine
                       + "end if;" + Environment.NewLine
                       + "if :IN_MODULES is not null then" + Environment.NewLine
                       + "  delete from SIB_USERMODULES_LNK where id_user = :IN_ID and " + Environment.NewLine
                       + "  ID_MODULE not in ( SELECT x.id FROM XMLTABLE( ('\"' || REPLACE(:IN_MODULES, ',', '\",\"') || '\"')" + Environment.NewLine
                       + "                                      COLUMNS id NUMBER PATH '.') x );" + Environment.NewLine
                       + "  for m in ( SELECT x.id FROM XMLTABLE( ('\"' || REPLACE(:IN_MODULES, ',', '\",\"') || '\"')" + Environment.NewLine
                       + "                              COLUMNS id NUMBER PATH '.') x )" + Environment.NewLine
                       + "      loop" + Environment.NewLine
                       + "           merge into SIB_USERMODULES_LNK l" + Environment.NewLine
                       + "            using (select :IN_ID as ID_USER, m.id as ID_MODULE from dual) i" + Environment.NewLine
                       + "            on (l.ID_USER = i.ID_USER and l.ID_MODULE = i.ID_MODULE)" + Environment.NewLine
                       + "            when not matched then insert(ID_USER,ID_MODULE,DT_CHANGE,USER_CHANGE)" + Environment.NewLine
                       + "           values(:IN_ID, m.id,:IN_DT_CHANGE,:IN_USER_CHANGE ); " + Environment.NewLine
                       + "      end loop; " + Environment.NewLine
                       + "end if; " + Environment.NewLine
                       + "commit; " + Environment.NewLine
                       + "end; ",
                        null);
            }

        }
        public static class SIB_Object
        {
            public static List<REP_Models.ParamMetadata> GetTransitionParams(string code)
            {
                switch (code)
                {
                    case "SOBJECTATTR":
                        return new List<REP_Models.ParamMetadata>
                        {
                            new REP_Models.ParamMetadata { CODE = "P_OBJECT_ID", NAME = "P_OBJECT_ID",
                                         LINKEDCOLUMNCODE = "ID", LINKEDCOLUMNNAME =  "ID",
                                              HIDDENVALUE = 1, ISMANDATORY = 0, ISSYSTEM =  0,
                                          DOMAINCODE = "ID", DATATYPE = "NUMBER", DATALENGTH = 30
                            }
                        };
                    case "SPOLICY":
                        return new List<REP_Models.ParamMetadata>
                        {
                            new REP_Models.ParamMetadata { CODE = "P_OBJ_CODE", NAME = "P_OBJ_CODE",
                                         LINKEDCOLUMNCODE = "CODE", LINKEDCOLUMNNAME =  "CODE",
                                              HIDDENVALUE = 0, ISMANDATORY = 0, ISSYSTEM =  0,
                                          DOMAINCODE = "CODE", DATATYPE = "VARCHAR2", DATALENGTH = 30
                            }
                        };
                    default:
                        return new List<REP_Models.ParamMetadata>();
                }
            }
            public static string GetSqlQuery()
            {
                return @"select O.ID,O.CODE,O.NAME,O.TYPE_OBJECT,T.NAME_TYPE,O.CREATE_DT,O.CHANGE_DT,O.CREATE_USER,O.CHANGE_USER,O.APP_CODE
                           from REP_OBJECT O
                           join REP_OBJECT_TYPE T on T.CODE_TYPE = O.TYPE_OBJECT";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список Объектов", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (false, false, false);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                    new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID объекта", DATATYPE = "NUMBER", DATALENGTH = 32, IS_PK = 1 },
                    new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "Код объекта", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "Наименование", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "TYPE_OBJECT", NAME = "Тип объекта", DATATYPE = "VARCHAR2", DATALENGTH = 30},
                    new REP_Models.ColumnMetadata { CODE = "NAME_TYPE", NAME = "Наименование типа объекта", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "CREATE_DT", NAME = "Дата создания", DATATYPE = "DATE", DATALENGTH = null , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "CHANGE_DT", NAME = "Дата изменения", DATATYPE = "DATE", DATALENGTH = null , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "CREATE_USER", NAME = "Кем создан", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "CHANGE_USER", NAME = "Кем изменен", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "APP_CODE", NAME = "Код приложения", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1}
                };
            }
            public static List<REP_Models.GRID_Menu> GetMenudata()
            {
                return new List<REP_Models.GRID_Menu>
                {
                new REP_Models.GRID_Menu { CODE_CHILD = "SObjectAttr", CODE_PARENT = "MAIN", CODE_OBJECT = "SObjectAttr", NAME_OBJECT = "Атрибуты объекта", KIND = 1, TYPE_OBJECT = "SHORTCUT" },
                new REP_Models.GRID_Menu { CODE_CHILD = "SPolicy", CODE_PARENT = "MAIN", CODE_OBJECT = "SPolicy", NAME_OBJECT = "Пользовательский доступ к объекту", KIND = 1, TYPE_OBJECT = "SHORTCUT" }
                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>();
            }
            public static (string sqlA, string sqlE, string slqD) GetActQuery()
            {
                return (null,
                        null,
                        null);
            }

        }
        public static class SIB_Object_Attr
        {
            public static string GetSqlQuery()
            {
                return "select o.id as ID_OBJECT, o.code as CODE_OBJECT, s.id as ID_ATTR, s.code as CODE_ATTR, s.name as ID_ATTR#N, a.value_attr, a.dt_change, a.user_change" + Environment.NewLine
                      +"  from SIB_OBJECT_ATTR A  "+ Environment.NewLine
                      + "  join SIB_ATTR_SPR S on upper(S.ID) = upper(A.ID_ATTR) and S.TYPE_ATTR = 'O' " + Environment.NewLine
                      + "  join REP_OBJECT o on o.ID = A.ID_OBJECT "+ Environment.NewLine
                      +" WHERE o.ID = :P_OBJECT_ID or :P_OBJECT_ID IS NULL";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Связанные Атрибуты Объектов", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (true, true, true);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID_OBJECT", NAME = "ID объекта", DATATYPE = "NUMBER", DATALENGTH = 22, IS_PK = 1,VIEWVISIBLE =0 },
                new REP_Models.ColumnMetadata { CODE = "CODE_OBJECT", NAME = "Код объекта", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "ID_ATTR", NAME = "Наименование атрибута", DATATYPE = "NUMBER", DOMAINCODE = "ID",DIMCODE = "SIB_DIM_OBJ_ATTR",DIMNAME = "Список атрибутов объекта" ,DATALENGTH = 100, IS_PK = 1, VIEWVISIBLE =1, INSERTABLE = 1},
                new REP_Models.ColumnMetadata { CODE = "CODE_ATTR", NAME = "Код атрибута", DATATYPE = "VARCHAR2",VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "ID_ATTR#N", NAME = "Наименование атрибута", DATATYPE = "VARCHAR2", DOMAINCODE = "Name", DATALENGTH = 100, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "VALUE_ATTR", NAME = "Значение атрибута", DATATYPE = "VARCHAR2", DATALENGTH = 100, EDITABLE = 1, VIEWVISIBLE =1, INSERTABLE = 1},
                new REP_Models.ColumnMetadata { CODE = "DT_CHANGE", NAME = "Дата изменения", DATATYPE = "DATE", DATALENGTH = null , VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "USER_CHANGE", NAME = "Кем изменен", DATATYPE = "VARCHAR2", DATALENGTH = 100 , VIEWVISIBLE =1}
                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>
                {
                new REP_Models.ParamMetadata { CODE = "P_OBJECT_ID", NAME = "Object ID", DOMAINCODE ="ID",  DATATYPE = "NUMBER", DATALENGTH = 30, HIDDENVALUE = 0}
                };
            }
            public static (string sqlA, string sqlE, string slqD) GetActQuery()
            {
                return ( "insert into SIB_OBJECT_ATTR(ID_OBJECT,ID_ATTR,VALUE_ATTR,DT_CHANGE,USER_CHANGE)" + Environment.NewLine
                       + "values( :INP_P_OBJECT_ID,:IN_ID_ATTR,:IN_VALUE_ATTR,:EWA_IN_CURDATE,:EWA_IN_USERCODE)" + Environment.NewLine
                       + "returning ID_OBJECT,ID_ATTR into :OUT_ID_OBJECT,:OUT_ID_ATTR",
                        "UPDATE SIB_OBJECT_ATTR SET VALUE_ATTR = :IN_VALUE_ATTR," + Environment.NewLine
                       + "                            DT_CHANGE = :EWA_IN_CURDATE," + Environment.NewLine
                       + "                          USER_CHANGE = :EWA_IN_USERCODE " + Environment.NewLine 
                       + " WHERE ID_OBJECT = :IN_ID_OBJECT AND ID_ATTR = :IN_ID_ATTR",
                        "DELETE FROM SIB_OBJECT_ATTR WHERE ID_OBJECT = :IN_ID_OBJECT AND ID_ATTR = :IN_ID_ATTR ");
            }

        }
        public static class SIB_Users_Attr
        {
            public static string GetSqlQuery()
            {
                return "select a.id_user as ID_USER, U.CODE as CODE_USER,S.CODE as CODEA, S.ID as ID_ATTR, S.NAME as ID_ATTR#N, A.value_attr, A.DT_CHANGE,A.USER_CHANGE " + Environment.NewLine
                       +" from SIB_USERS_ATTR A " +Environment.NewLine
                       +" join SIB_USERS U on U.ID = A.id_user " +Environment.NewLine
                       +" join SIB_ATTR_SPR S on S.ID = A.ID_ATTR and S.TYPE_ATTR = 'U' " +Environment.NewLine
                       +"WHERE U.ID = :P_USER_ID or :P_USER_ID IS NULL";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Связанные Атрибуты Пользователей", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (true, true, true);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID_USER", NAME = "ID пользователя", DATATYPE = "NUMBER", DATALENGTH = 32, IS_PK = 1 },
                new REP_Models.ColumnMetadata { CODE = "CODE_USER", NAME = "Логин пользователя", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE=1},
                new REP_Models.ColumnMetadata { CODE = "CODEA", NAME = "Код атрибута", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE=1},
                new REP_Models.ColumnMetadata { CODE = "ID_ATTR", NAME = "Наименование атрибута", DATATYPE = "NUMBER", DATALENGTH = 32, IS_PK = 1, DOMAINCODE = "ID",DIMCODE = "SIB_DIM_USR_ATTR",DIMNAME = "Список атрибутов пользователя", VIEWVISIBLE=1, INSERTABLE = 1},
                new REP_Models.ColumnMetadata { CODE = "ID_ATTR#N", NAME = "Наименование атрибута", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE=0,DOMAINCODE = "Name"},
                new REP_Models.ColumnMetadata { CODE = "VALUE_ATTR", NAME = "Значение атрибута", DATATYPE = "VARCHAR2", DATALENGTH = 100, EDITABLE = 1, VIEWVISIBLE=1, INSERTABLE = 1},
                new REP_Models.ColumnMetadata { CODE = "DT_CHANGE", NAME = "Дата изменения", DATATYPE = "DATE", DATALENGTH = null , VIEWVISIBLE=1},
                new REP_Models.ColumnMetadata { CODE = "USER_CHANGE", NAME = "Кем изменен", DATATYPE = "VARCHAR2", DATALENGTH = 100 , VIEWVISIBLE=1}
                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>
                {
                new REP_Models.ParamMetadata { CODE = "P_USER_ID", NAME = "User ID", DOMAINCODE ="Id",  DATATYPE = "NUMBER", DATALENGTH = 32, HIDDENVALUE = 0}
                };
            }
            public static (string sqlA, string sqlE, string slqD) GetActQuery()
            {
                return ("insert into SIB_USERS_ATTR(ID_USER,ID_ATTR,VALUE_ATTR,DT_CHANGE,USER_CHANGE)" + Environment.NewLine
                       + "values(:INP_P_USER_ID,:IN_ID_ATTR,:IN_VALUE_ATTR,:EWA_IN_CURDATE,:EWA_IN_USERCODE)" + Environment.NewLine  
                       +"returning ID_USER,ID_ATTR into :OUT_ID_USER,:OUT_ID_ATTR",
                        "UPDATE SIB_USERS_ATTR SET VALUE_ATTR = :IN_VALUE_ATTR," + Environment.NewLine 
                       + "                           DT_CHANGE = :EWA_IN_CURDATE," + Environment.NewLine 
                       + "                         USER_CHANGE = :EWA_IN_USERCODE" + Environment.NewLine 
                       +" WHERE ID_USER = :IN_ID_USER AND ID_ATTR = :IN_ID_ATTR",
                        "DELETE FROM SIB_USERS_ATTR WHERE ID_USER = :IN_ID_USER AND ID_ATTR = :IN_ID_ATTR");
            }

        }
        
        public static class SIB_Modules
        {
            public static string GetSqlQuery()
            {
                return "select ID, CODE, NAME, DT_CHANGE,USER_CHANGE from SIB_MODULES " + Environment.NewLine
                      + " WHERE(:P_MOUDLE_CODE IS NULL OR UPPER(CODE) LIKE '%' || UPPER(:P_MOUDLE_CODE) || '%')";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список модулей", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (true, true, true);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID модуля", DATATYPE = "NUMBER", DATALENGTH = 32, IS_PK = 1,VIEWVISIBLE =0 },
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "Код модуля", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1, INSERTABLE = 1,ISMANDATORY = 1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "Наименование модуля", DATATYPE = "VARCHAR2", DATALENGTH = 100, INSERTABLE = 1,EDITABLE = 1 , VIEWVISIBLE =1,ISMANDATORY = 0},
                new REP_Models.ColumnMetadata { CODE = "DT_CHANGE", NAME = "Дата изменения", DATATYPE = "DATE", DATALENGTH = 100, INSERTABLE = 0,EDITABLE = 0 , VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "USER_CHANGE", NAME = "Кем изменен", DATATYPE = "VARCHAR2", DATALENGTH = 100, INSERTABLE = 0,EDITABLE = 0 , VIEWVISIBLE =1}
                };
            }
            public static List<REP_Models.GRID_Menu> GetMenudata()
            {
                return new List<REP_Models.GRID_Menu>
                {
                new REP_Models.GRID_Menu { CODE_CHILD = "SUModules", CODE_PARENT = "MAIN", CODE_OBJECT = "SUModules", NAME_OBJECT = "Пользователи с доступом к модулю", KIND = 1, TYPE_OBJECT = "SHORTCUT" },
                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>
                {
                new REP_Models.ParamMetadata { CODE = "P_MOUDLE_CODE", NAME = "Код модуля", DOMAINCODE ="Code",  DATATYPE = "VARCHAR2", DATALENGTH = 30, HIDDENVALUE = 1, ISMANDATORY = 0}
                };
            }
            public static (string sqlA, string sqlE, string slqD) GetActQuery()
            {
                return ("INSERT INTO SIB_MODULES (ID, CODE, NAME, DT_CHANGE, USER_CHANGE) VALUES(ewa_row_seq.nextval, :IN_CODE, :IN_NAME, :EWA_IN_CURDATE,:EWA_IN_USERCODE) RETURNING ID INTO :OUT_ID",
                        "UPDATE SIB_MODULES SET NAME = :IN_NAME, DT_CHANGE = :EWA_IN_CURDATE, USER_CHANGE = :EWA_IN_USERCODE WHERE ID = :IN_ID",
                        "DELETE FROM SIB_MODULES WHERE ID = :IN_ID");
            }
            
            public static List<REP_Models.ParamMetadata> GetTransitionParams(string code)
            {
                switch (code)
                {
                    case "SUMODULES":
                        return new List<REP_Models.ParamMetadata>
                        {
                            new REP_Models.ParamMetadata { CODE = "P_MODULE_CODE", NAME = "P_MODULE_CODE",
                                         LINKEDCOLUMNCODE = "CODE", LINKEDCOLUMNNAME =  "CODE",
                                              HIDDENVALUE = 1, ISMANDATORY = 0, ISSYSTEM =  0,
                                          DOMAINCODE = "Code", DATATYPE = "VARCHAR2", DATALENGTH = 30
                            }
                        };
                    default:
                        return new List<REP_Models.ParamMetadata>();
                }
            }
        }
        public static class SIB_Roles
        {
            public static string GetSqlQuery()
            {
                return "select ID, CODE, NAME, DT_CHANGE,USER_CHANGE from SIB_ROLES " +Environment.NewLine
                      +" WHERE(:P_ROLE_CODE IS NULL OR UPPER(CODE) LIKE '%' || UPPER(:P_ROLE_CODE) || '%')";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список ролей", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (true, true, true);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID роли", DATATYPE = "NUMBER", DATALENGTH = 32, IS_PK = 1,VIEWVISIBLE =0 },
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "Код роли", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1, INSERTABLE = 1,ISMANDATORY = 1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "Наименование роли", DATATYPE = "VARCHAR2", DATALENGTH = 100, INSERTABLE = 1,EDITABLE = 1 , VIEWVISIBLE =1,ISMANDATORY = 1},
                new REP_Models.ColumnMetadata { CODE = "DT_CHANGE", NAME = "Дата изменения", DATATYPE = "DATE", DATALENGTH = 100, INSERTABLE = 0,EDITABLE = 0 , VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "USER_CHANGE", NAME = "Кем изменен", DATATYPE = "VARCHAR2", DATALENGTH = 100, INSERTABLE = 0,EDITABLE = 0 , VIEWVISIBLE =1}
                };
            }
            public static List<REP_Models.GRID_Menu> GetMenudata()
            {
                return new List<REP_Models.GRID_Menu>
                {
                new REP_Models.GRID_Menu { CODE_CHILD = "SURoles", CODE_PARENT = "MAIN", CODE_OBJECT = "SURoles", NAME_OBJECT = "Пользователи с доступом к роли", KIND = 1, TYPE_OBJECT = "SHORTCUT" },
                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>
                {
                new REP_Models.ParamMetadata { CODE = "P_ROLE_CODE", NAME = "Код роли", DOMAINCODE ="Code",  DATATYPE = "VARCHAR2", DATALENGTH = 30, HIDDENVALUE = 1}
                };
            }
            public static (string sqlA, string sqlE, string slqD) GetActQuery()
            {
                return ("INSERT INTO SIB_ROLES (ID, CODE, NAME, DT_CHANGE, USER_CHANGE) VALUES(ewa_row_seq.nextval, :IN_CODE, :IN_NAME, :EWA_IN_CURDATE,:EWA_IN_USERCODE) RETURNING ID INTO :OUT_ID",
                        "UPDATE SIB_ROLES SET NAME = :IN_NAME, DT_CHANGE = :EWA_IN_CURDATE, USER_CHANGE = :EWA_IN_USERCODE WHERE ID = :IN_ID",
                        "DELETE FROM SIB_ROLES WHERE ID = :IN_ID");
            }
            public static List<REP_Models.ParamMetadata> GetTransitionParams(string code)
            {
                switch (code)
                {
                    case "SUROLES":
                        return new List<REP_Models.ParamMetadata>
                        {
                            new REP_Models.ParamMetadata { CODE = "P_ROLE_CODE", NAME = "P_ROLE_CODE",
                                         LINKEDCOLUMNCODE = "CODE", LINKEDCOLUMNNAME =  "CODE",
                                              HIDDENVALUE = 1, ISMANDATORY = 0, ISSYSTEM =  0,
                                          DOMAINCODE = "Code", DATATYPE = "VARCHAR2", DATALENGTH = 30
                            }
                        };
                    default:
                        return new List<REP_Models.ParamMetadata>();
                }
            }
        }
        public static class SIB_AD_SPR
        {
            public static string GetSqlQuery()
            {
                return @"select ID,CODE,NAME,TYPE_ATTR, DECODE(TYPE_ATTR,'GROUP','Группа AD','ATTR','Атрибут AD') TYPE_ATTR#N, DT_CREATE,DT_CHANGE,USER_CREATE,USER_CHANGE from sib_ad_spr";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Справочник атрибутов AD", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (true, true, false);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 32, IS_PK = 1,VIEWVISIBLE =0 },
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "Код", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1, INSERTABLE = 1,ISMANDATORY = 1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "Наименование", DATATYPE = "VARCHAR2", DATALENGTH = 100, INSERTABLE = 1,EDITABLE = 1 , VIEWVISIBLE =1,ISMANDATORY = 1},
                new REP_Models.ColumnMetadata { CODE = "TYPE_ATTR", NAME = "Тип", DATATYPE = "VARCHAR2", DATALENGTH = 100, DOMAINCODE = "CODE",DIMCODE = "SIB_DIM_AD_TYPE",DIMNAME = "Список типов объектов AD", INSERTABLE = 1,EDITABLE = 0 , VIEWVISIBLE =1,ISMANDATORY = 1},
                new REP_Models.ColumnMetadata { CODE = "TYPE_ATTR#N", NAME = "Тип", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE=0,DOMAINCODE = "Name"},
                new REP_Models.ColumnMetadata { CODE = "DT_CREATE", NAME = "Дата создания", DATATYPE = "DATE", DATALENGTH = null, VIEWVISIBLE =1 },
                new REP_Models.ColumnMetadata { CODE = "DT_CHANGE", NAME = "Дата изменения", DATATYPE = "DATE", DATALENGTH = null, VIEWVISIBLE =1 },
                new REP_Models.ColumnMetadata { CODE = "USER_CREATE", NAME = "Кем создан", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1 },
                new REP_Models.ColumnMetadata { CODE = "USER_CHANGE", NAME = "Кем изменен", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1 },
                };
            }
            public static (string sqlA, string sqlE, string slqD) GetActQuery()
            {
                return (//insert
                        "declare " + Environment.NewLine
                       + "l_id number(32); " + Environment.NewLine
                       + "l_cnt number(32); " + Environment.NewLine
                       + "l_error clob; " + Environment.NewLine
                       + "l_errcnt number(32):= 0; " + Environment.NewLine
                       + "begin " + Environment.NewLine
                       + " select count(1) into l_cnt from sib_ad_spr where upper(code) = upper(:IN_CODE); " + Environment.NewLine
                       + " if l_cnt > 0 " + Environment.NewLine
                       + "   then " + Environment.NewLine
                       + "     l_errcnt:= l_errcnt + 1; " + Environment.NewLine
                       + "     l_error:= l_error || ' Значение ' || :IN_CODE || ' существует! '; " + Environment.NewLine
                       + " end if; " + Environment.NewLine
                       + " if l_errcnt = 0 " + Environment.NewLine
                       + "   then " + Environment.NewLine
                       + "     l_id := ewa_row_seq.nextval; " + Environment.NewLine
                       + "     insert into sib_ad_spr(id,code,name,type_attr,dt_create,dt_change,user_create,user_change) " + Environment.NewLine
                       + "     values(l_id, upper(:IN_CODE), :IN_NAME, :IN_TYPE_ATTR, :EWA_IN_CURDATE,:EWA_IN_CURDATE,:EWA_IN_USERCODE,:EWA_IN_USERCODE) " + Environment.NewLine
                       + "     returning ID into :OUT_ID; " + Environment.NewLine
                       + "   else " + Environment.NewLine
                       + "     :EWA_OUT_RESULT:= -1; " + Environment.NewLine
                       + "     :EWA_OUT_MESSAGE:= l_error; " + Environment.NewLine
                       + " end if; " + Environment.NewLine
                       + "end;",
                        //update
                        "UPDATE sib_ad_spr c " + Environment.NewLine
                       + "   set c.NAME = :IN_NAME " + Environment.NewLine
                       + "      ,c.DT_CHANGE = :EWA_IN_CURDATE " + Environment.NewLine
                       + "      ,c.USER_CHANGE = :EWA_IN_USERCODE " + Environment.NewLine
                       + "where c.ID = :IN_ID" + Environment.NewLine
                       + "returning ID into :IN_ID",
                        //delete
                        null);
            }

        }

        public static class SIB_AD_ASSGRP
        {
            public static string GetSqlQuery()
            {
                return "select ID_AD, CODE_AD, NAME_AD as ID_AD#N, TYPE_AD,DECODE(TYPE_AD,'GROUP','Группа AD','ATTR','Атрибут AD') as TYPE_AD#N,"+Environment.NewLine
                      +" ID_SIB_OBJ, CODE_SIB_OBJ, NAME_SIB_OBJ as ID_SIB_OBJ#N, TYPE_SIB_OBJ, TYPE_SIB_OBJ#N, dt_create, dt_change, user_create, user_change,"+Environment.NewLine
                      + "is_sinch,DECODE(is_sinch,0,'Без синхронизации',1,'Ручная синхронизация',2,'Автоматическая синхронизация') as is_sinch#N" + Environment.NewLine
                      +"from ("+Environment.NewLine
                      + "select aspr.id as ID_AD, aspr.code as CODE_AD, aspr.name as NAME_AD, aspr.type_attr as TYPE_AD," + Environment.NewLine
                      +" s.id as ID_SIB_OBJ, s.code as CODE_SIB_OBJ, s.name as NAME_SIB_OBJ, 'EWAMODULE' as TYPE_SIB_OBJ,'Модуль EWA' as TYPE_SIB_OBJ#N," +Environment.NewLine
                      +" ad.dt_create, ad.dt_change, ad.user_create, ad.user_change, ad.is_sinch  " +Environment.NewLine
                      +"  from SIB_AD_MODULE_LNK ad" +Environment.NewLine
                      +"  join sib_ad_spr aspr on aspr.id = ad.id_ad" +Environment.NewLine
                      +"  join SIB_MODULES s on s.id = ad.id_module" +Environment.NewLine
                      +"union all  " +Environment.NewLine
                      +"select aspr.id as ID_AD,aspr.code as CODE_AD,aspr.name as NAME_AD,aspr.type_attr as TYPE_AD," +Environment.NewLine
                      + " s.id as ID_SIB_OBJ, s.code as CODE_SIB_OBJ, s.name as NAME_SIB_OBJ,'EWAROLE' as TYPE_SIB_OBJ,'Роль EWA' as TYPE_SIB_OBJ#N," + Environment.NewLine
                      +" ad.dt_create, ad.dt_change, ad.user_create, ad.user_change, ad.is_sinch" +Environment.NewLine
                      +" from SIB_AD_ROLE_LNK ad" +Environment.NewLine
                      +" join sib_ad_spr aspr on aspr.id = ad.id_ad" +Environment.NewLine
                      +" join SIB_ROLES s on s.id = ad.id_role" +Environment.NewLine
                      +"union all" +Environment.NewLine
                      +"select aspr.id as ID_AD, aspr.code as CODE_AD, aspr.name as NAME_AD, aspr.type_attr as TYPE_AD," +Environment.NewLine
                      + " null as ID_SIB_OBJ, ad.Target as CODE_SIB_OBJ, ad.code_Attr as NAME_SIB_OBJ, 'EWAATTR' as TYPE_SIB_OBJ,'Атрибут EWA' as TYPE_SIB_OBJ#N, " + Environment.NewLine
                      +" ad.dt_create, ad.dt_change,ad.user_create,ad.user_change,ad.is_sinch" +Environment.NewLine
                      +" from sib_ad_attr_lnk ad" +Environment.NewLine
                      +" join sib_ad_spr aspr on aspr.id = ad.id_ad"+Environment.NewLine
                      +") WHERE (:P_AD_TYPE IS NULL OR :P_AD_TYPE = TYPE_AD)"+Environment.NewLine
                      + "AND (:P_EWA_TYPE IS NULL OR :P_EWA_TYPE = TYPE_SIB_OBJ) ";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Матрица объектов AD", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (true, true, true);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID_AD", NAME = "Наименование объекта AD", DATATYPE = "NUMBER", DATALENGTH = 32, DOMAINCODE = "ID",DIMCODE = "SIB_DIM_AD_SPR",DIMNAME = "Список объетов AD", VIEWVISIBLE=1, INSERTABLE = 1,ISMANDATORY = 1,IS_PK = 1},
                new REP_Models.ColumnMetadata { CODE = "CODE_AD", NAME = "Код объекта AD", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1, INSERTABLE = 0,ISMANDATORY = 0},
                new REP_Models.ColumnMetadata { CODE = "ID_AD#N", NAME = "Наименование объекта AD",DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE=0,DOMAINCODE = "Name"},
                new REP_Models.ColumnMetadata { CODE = "TYPE_AD", NAME = "Тип объекта AD", DATATYPE = "VARCHAR2", DATALENGTH = 100, DOMAINCODE = "CODE",DIMCODE = "SIB_DIM_AD_TYPE",DIMNAME = "Список типов AD", VIEWVISIBLE=1, INSERTABLE = 0,ISMANDATORY = 0},
                new REP_Models.ColumnMetadata { CODE = "TYPE_AD#N", NAME = "Тип объекта AD",DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE=0,DOMAINCODE = "Name"},
                new REP_Models.ColumnMetadata { CODE = "ID_SIB_OBJ", NAME = "Наименование объекта EWA", DATATYPE = "NUMBER", DATALENGTH = 32, DOMAINCODE = "ID",DIMCODE = "SIB_DIM_EWA_SPR",DIMNAME = "Список объетов EWA", VIEWVISIBLE=1, INSERTABLE = 1,ISMANDATORY = 1,IS_PK = 1},
                new REP_Models.ColumnMetadata { CODE = "CODE_SIB_OBJ", NAME = "Код объекта EWA", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1, INSERTABLE = 0,ISMANDATORY = 0},
                new REP_Models.ColumnMetadata { CODE = "ID_SIB_OBJ#N", NAME = "Наименование объекта EWA", DATATYPE = "VARCHAR2", DATALENGTH = 100,VIEWVISIBLE=0,DOMAINCODE = "Name"},
                new REP_Models.ColumnMetadata { CODE = "TYPE_SIB_OBJ", NAME = "Тип объекта EWA", DATATYPE = "VARCHAR2", DATALENGTH = 100, DOMAINCODE = "CODE",DIMCODE = "SIB_DIM_EWA_TYPE",DIMNAME = "Список типов EWA", VIEWVISIBLE=1, INSERTABLE = 1,ISMANDATORY = 1},
                new REP_Models.ColumnMetadata { CODE = "TYPE_SIB_OBJ#N", NAME = "Тип объекта EWA",DATATYPE = "VARCHAR2", DATALENGTH = 100,  VIEWVISIBLE=0,DOMAINCODE = "Name"},
                new REP_Models.ColumnMetadata { CODE = "DT_CREATE", NAME = "Дата создания", DATATYPE = "DATE", DATALENGTH = null, VIEWVISIBLE =1 },
                new REP_Models.ColumnMetadata { CODE = "DT_CHANGE", NAME = "Дата изменения", DATATYPE = "DATE", DATALENGTH = null, VIEWVISIBLE =1 },
                new REP_Models.ColumnMetadata { CODE = "USER_CREATE", NAME = "Кем создан", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1 },
                new REP_Models.ColumnMetadata { CODE = "USER_CHANGE", NAME = "Кем изменен", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1 },
                new REP_Models.ColumnMetadata { CODE = "IS_SINCH", NAME = "Тип синхронизации", DATATYPE = "NUMBER", DATALENGTH = 30, DOMAINCODE = "ID",DIMCODE = "SIB_DIM_SINCH_TYPE",DIMNAME = "Список типов синхронизации AD", VIEWVISIBLE=1, INSERTABLE = 1,EDITABLE = 1,ISMANDATORY = 1,IS_PK = 1},
                new REP_Models.ColumnMetadata { CODE = "IS_SINCH#N", NAME = "Тип синхронизации", DATATYPE = "VARCHAR2", DATALENGTH = 100,VIEWVISIBLE=0,DOMAINCODE = "Name"},
                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>
                {
                    new REP_Models.ParamMetadata { CODE = "P_AD_TYPE", NAME = "Тип объекта AD", DOMAINCODE ="CODE",DIMCODE ="SIB_DIM_AD_TYPE",  DATATYPE = "VARCHAR2", DATALENGTH = 100, HIDDENVALUE = 0},
                    new REP_Models.ParamMetadata { CODE = "P_EWA_TYPE", NAME = "Тип объекта EWA", DOMAINCODE ="CODE",DIMCODE ="SIB_DIM_EWA_TYPE",  DATATYPE = "VARCHAR2", DATALENGTH = 100, HIDDENVALUE = 0}
                };
            }
            public static (string sqlA, string sqlE, string slqD) GetActQuery()
            {
                return (//insert
                        "DECLARE "+ Environment.NewLine
                       +"l_type_ewa varchar2(100); "+ Environment.NewLine
                       +"l_cnt number(32); "+ Environment.NewLine
                       +"l_error clob; "+ Environment.NewLine
                       +"l_errcnt number(32):= 0; "+ Environment.NewLine
                       +"begin "+ Environment.NewLine
                       +" l_type_ewa := :IN_TYPE_SIB_OBJ; "+ Environment.NewLine
                       +" if l_type_ewa = 'EWAMODULE' "+ Environment.NewLine
                       +"   then select count(1) into l_cnt from SIB_AD_MODULE_LNK where ID_AD = :IN_ID_AD and ID_MODULE = :IN_ID_SIB_OBJ; "+ Environment.NewLine
                       +" elsif l_type_ewa = 'EWAROLE' "+ Environment.NewLine
                       +"   then select count(1) into l_cnt from SIB_AD_ROLE_LNK where ID_AD = :IN_ID_AD and ID_ROLE = :IN_ID_SIB_OBJ; "+ Environment.NewLine
                       +"   else "+ Environment.NewLine
                       +"     l_errcnt:= l_errcnt + 1; "+ Environment.NewLine
                       +"     l_error:= l_error || ' Тип ' || l_type_ewa || ' не обрабатывается !'; "+ Environment.NewLine
                       +" end if; "+ Environment.NewLine
                       +" if l_cnt > 0 "+ Environment.NewLine
                       +"   then "+ Environment.NewLine
                       +"     l_errcnt:= l_errcnt + 1; "+ Environment.NewLine
                       +"     l_error:= l_error || ' Такая запись существует!'; "+ Environment.NewLine
                       +" end if; "+ Environment.NewLine
                       +" if l_errcnt = 0 "+ Environment.NewLine
                       +"   then "+ Environment.NewLine
                       +"     if l_type_ewa = 'EWAMODULE' "+ Environment.NewLine
                       +"       then "+ Environment.NewLine
                       +"         insert into SIB_AD_MODULE_LNK(ID_AD, ID_MODULE, DT_CREATE, DT_CHANGE, USER_CREATE, USER_CHANGE, IS_SINCH) "+ Environment.NewLine
                       +"         values(:IN_ID_AD, :IN_ID_SIB_OBJ, :EWA_IN_CURDATE,:EWA_IN_CURDATE,:EWA_IN_USERCODE,:EWA_IN_USERCODE, :IN_IS_SINCH) "+ Environment.NewLine
                       +"         returning ID_AD, ID_MODULE into: OUT_ID_AD, :OUT_ID_SIB_OBJ; "+ Environment.NewLine
                       +"     elsif l_type_ewa = 'EWAROLE' "+ Environment.NewLine
                       +"       then "+ Environment.NewLine
                       +"         insert into SIB_AD_ROLE_LNK(ID_AD, ID_ROLE, DT_CREATE, DT_CHANGE, USER_CREATE, USER_CHANGE, IS_SINCH) "+ Environment.NewLine
                       +"         values(:IN_ID_AD, :IN_ID_SIB_OBJ, :EWA_IN_CURDATE,:EWA_IN_CURDATE,:EWA_IN_USERCODE,:EWA_IN_USERCODE, :IN_IS_SINCH) "+ Environment.NewLine
                       +"         returning ID_AD, ID_ROLE into: OUT_ID_AD, :OUT_ID_SIB_OBJ; "+ Environment.NewLine
                       +"     end if; "+ Environment.NewLine
                       +"   else "+ Environment.NewLine
                       +"     :EWA_OUT_RESULT:= -1; "+ Environment.NewLine
                       +"     :EWA_OUT_MESSAGE:= l_error; "+ Environment.NewLine
                       +" end if; "+ Environment.NewLine
                       +"end;" ,
                        //update
                        "DECLARE "+ Environment.NewLine
                       +"l_type_ewa varchar2(100); "+ Environment.NewLine
                       +"l_error clob; "+ Environment.NewLine
                       +"l_errcnt number(32):= 0; "+ Environment.NewLine
                       +"begin "+ Environment.NewLine
                       +" l_type_ewa := :IN_TYPE_SIB_OBJ; "+ Environment.NewLine
                       +" if l_type_ewa NOT IN ('EWAMODULE','EWAROLE') "+ Environment.NewLine
                       +"   then "+ Environment.NewLine
                       +"     l_errcnt:= l_errcnt + 1; "+ Environment.NewLine
                       +"     l_error:= l_error || ' Тип ' || l_type_ewa || ' не обрабатывается !'; "+ Environment.NewLine
                       +" end if; "+ Environment.NewLine
                       +" if l_errcnt = 0 "+ Environment.NewLine
                       +"   then "+ Environment.NewLine
                       +"     if l_type_ewa = 'EWAMODULE' "+ Environment.NewLine
                       +"       then "+ Environment.NewLine
                       +"         UPDATE SIB_AD_MODULE_LNK set "+ Environment.NewLine
                       +"           DT_CHANGE = :EWA_IN_CURDATE "+ Environment.NewLine
                       +"          ,USER_CHANGE = :EWA_IN_USERCODE "+ Environment.NewLine
                       +"          ,IS_SINCH = :IN_IS_SINCH "+ Environment.NewLine
                       +"         WHERE ID_AD = :IN_ID_AD and ID_MODULE = :IN_ID_SIB_OBJ "+ Environment.NewLine
                       +"        returning ID_AD, ID_MODULE into: OUT_ID_AD, :OUT_ID_SIB_OBJ; "+ Environment.NewLine
                       +"     elsif l_type_ewa = 'EWAROLE' "+ Environment.NewLine
                       +"       then "+ Environment.NewLine
                       +"         UPDATE SIB_AD_ROLE_LNK set "+ Environment.NewLine
                       +"           DT_CHANGE = :EWA_IN_CURDATE "+ Environment.NewLine
                       +"          ,USER_CHANGE = :EWA_IN_USERCODE "+ Environment.NewLine
                       +"          ,IS_SINCH = :IN_IS_SINCH "+ Environment.NewLine
                       +"         WHERE ID_AD = :IN_ID_AD and ID_ROLE = :IN_ID_SIB_OBJ "+ Environment.NewLine
                       +"        returning ID_AD, ID_ROLE into: OUT_ID_AD, :OUT_ID_SIB_OBJ; "+ Environment.NewLine
                       +"     end if; "+ Environment.NewLine
                       +"   else "+ Environment.NewLine
                       +"     :EWA_OUT_RESULT:= -1; "+ Environment.NewLine
                       +"     :EWA_OUT_MESSAGE:= l_error; "+ Environment.NewLine
                       +" end if; "+ Environment.NewLine
                       + "end;",
                        //delete
                        "DECLARE "+ Environment.NewLine
                       +"l_type_ewa varchar2(100); "+ Environment.NewLine
                       +"l_error clob; "+ Environment.NewLine
                       +"l_errcnt number(32):= 0; "+ Environment.NewLine
                       +"begin "+ Environment.NewLine
                       +" l_type_ewa := :IN_TYPE_SIB_OBJ; "+ Environment.NewLine
                       +" if l_type_ewa NOT IN ('EWAMODULE','EWAROLE') "+ Environment.NewLine
                       +"   then "+ Environment.NewLine
                       +"     l_errcnt:= l_errcnt + 1; "+ Environment.NewLine
                       +"     l_error:= l_error || ' Тип ' || l_type_ewa || ' не обрабатывается !'; "+ Environment.NewLine
                       +" end if; "+ Environment.NewLine
                       +" if l_errcnt = 0 "+ Environment.NewLine
                       +"   then "+ Environment.NewLine
                       +"     if l_type_ewa = 'EWAMODULE' "+ Environment.NewLine
                       +"       then "+ Environment.NewLine
                       +"         DELETE FROM SIB_AD_MODULE_LNK "+ Environment.NewLine
                       +"         WHERE ID_AD = :IN_ID_AD and ID_MODULE = :IN_ID_SIB_OBJ;"+ Environment.NewLine
                       +"     elsif l_type_ewa = 'EWAROLE' "+ Environment.NewLine
                       +"       then "+ Environment.NewLine
                       +"         DELETE FROM SIB_AD_ROLE_LNK "+ Environment.NewLine
                       +"         WHERE ID_AD = :IN_ID_AD and ID_ROLE = :IN_ID_SIB_OBJ;"+ Environment.NewLine
                       +"     end if; "+ Environment.NewLine
                       +"   else "+ Environment.NewLine
                       +"     :EWA_OUT_RESULT:= -1; "+ Environment.NewLine
                       +"     :EWA_OUT_MESSAGE:= l_error; "+ Environment.NewLine
                       +" end if; "+ Environment.NewLine
                       +"end;");
            }

        }

        public static class SIB_Rules
        {
            public static List<REP_Models.ParamMetadata> GetTransitionParams(string code)
            {
                switch (code)
                {
                    case "SPOLICY":
                        return new List<REP_Models.ParamMetadata>
                        {
                            new REP_Models.ParamMetadata { CODE = "P_RULE_CODE", NAME = "P_RULE_CODE",
                                         LINKEDCOLUMNCODE = "CODE", LINKEDCOLUMNNAME =  "CODE",
                                              HIDDENVALUE = 1, ISMANDATORY = 0, ISSYSTEM =  0,
                                          DOMAINCODE = "Code", DATATYPE = "VARCHAR2", DATALENGTH = 30
                            }
                        };
                    default:
                        return new List<REP_Models.ParamMetadata>();
                }
            }


            public static string GetSqlQuery()
            {
                return "select ID,CODE,RULE,RULE_J,ACCESS_TYPE,DT_CREATE,DT_CHANGE,USER_CREATE,USER_CHANGE,"+Environment.NewLine
                      +"       IS_ACTIVE,DESCRIPTION,USER_ATTRS,OBJECT_ATTRS,ACTION_USE,"+Environment.NewLine
                      +"       ACTION_ADD, ACTION_EDIT, ACTION_DEL "+Environment.NewLine
                      +"  from SIB_RULES "+Environment.NewLine
                      +" WHERE (UPPER(CODE) LIKE '%'||UPPER(:P_RULE_CODE)||'%' or :P_RULE_CODE IS NULL)"+Environment.NewLine
                      +"   AND (IS_ACTIVE = 1 OR NVL(:P_IS_ACT,0) = 0)";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список Правил", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (true, true, true);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                    new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID правила", DATATYPE = "NUMBER", DATALENGTH = 22, IS_PK = 1 },
                    new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "Код правила", DATATYPE = "VARCHAR2", DATALENGTH = 100 , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "RULE", NAME = "Правило", DATATYPE = "VARCHAR2", DATALENGTH = 1000 , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "RULE_J", NAME = "Правило в формате JSON", DATATYPE = "CLOB", DATALENGTH = 1000 , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "ACCESS_TYPE", NAME = "Тип доступа", DATATYPE = "VARCHAR2", DATALENGTH = 20 , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "DT_CREATE", NAME = "Дата создания", DATATYPE = "DATE", DATALENGTH = null , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "DT_CHANGE", NAME = "Дата изменения", DATATYPE = "DATE", DATALENGTH = null , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "USER_CREATE", NAME = "Кем создан", DATATYPE = "VARCHAR2", DATALENGTH = 100 , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "USER_CHANGE", NAME = "Кем изменен", DATATYPE = "VARCHAR2", DATALENGTH = 100 , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "IS_ACTIVE", NAME = "Признак активности правила",DOMAINCODE="Bool", DATATYPE = "NUMBER", DATALENGTH = 22 , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "DESCRIPTION", NAME = "Описание", DATATYPE = "VARCHAR2", DATALENGTH = 1000 , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "USER_ATTRS", NAME = "Атрибуты пользователя", DATATYPE = "VARCHAR2", DATALENGTH = 1000 , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "OBJECT_ATTRS", NAME = "Атрибуты объекта", DATATYPE = "VARCHAR2", DATALENGTH = 1000 , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "ACTION_USE", NAME = "Просмотр",DOMAINCODE="Bool", DATATYPE = "NUMBER", DATALENGTH = 22 , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "ACTION_ADD", NAME = "Добавление",DOMAINCODE="Bool", DATATYPE = "NUMBER", DATALENGTH = 22 , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "ACTION_EDIT", NAME = "Редактирование",DOMAINCODE="Bool", DATATYPE = "NUMBER", DATALENGTH = 22 , VIEWVISIBLE =1},
                    new REP_Models.ColumnMetadata { CODE = "ACTION_DEL", NAME = "Удаление",DOMAINCODE="Bool", DATATYPE = "NUMBER", DATALENGTH = 22 , VIEWVISIBLE =1}
                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>
                {
                new REP_Models.ParamMetadata { CODE = "P_RULE_CODE", NAME = "Код правила", DOMAINCODE ="Code",  DATATYPE = "VARCHAR2", DATALENGTH = 30, HIDDENVALUE = 0},
                new REP_Models.ParamMetadata { CODE = "P_IS_ACT", NAME = "Только активные", DOMAINCODE ="Bool",  DATATYPE = "NUMBER", DATALENGTH = 1},
                };
            }
            public static List<REP_Models.GRID_Menu> GetMenudata()
            {
                return new List<REP_Models.GRID_Menu>
                {
                new REP_Models.GRID_Menu { CODE_CHILD = "SPolicy", CODE_PARENT = "MAIN", CODE_OBJECT = "SPolicy", NAME_OBJECT = "Пользователи и объекты правила", KIND = 1, TYPE_OBJECT = "SHORTCUT" },
                };
            }
            public static (string sqlA, string sqlE, string slqD) GetActQuery()
            {
                return ("INSERT INTO SIB_RULES(ID, CODE, RULE, RULE_J, ACCESS_TYPE,DT_CREATE, DT_CHANGE, USER_CREATE, USER_CHANGE,"+Environment.NewLine
                       +"IS_ACTIVE, DESCRIPTION, USER_ATTRS, OBJECT_ATTRS,ACTION_USE, ACTION_ADD, ACTION_EDIT, ACTION_DEL)"+Environment.NewLine
                       +"VALUES(:IN_ID,:IN_CODE,:IN_RULE,:IN_RULE_J,:IN_ACCESS_TYPE,:IN_DT_CREATE,:IN_DT_CHANGE,:IN_USER_CREATE,:IN_USER_CHANGE,"+Environment.NewLine
                       + ":IN_IS_ACTIVE,:IN_DESCRIPTION,:IN_USER_ATTRS,:IN_OBJECT_ATTRS,:IN_ACTION_USE,:IN_ACTION_ADD,:IN_ACTION_EDIT,:IN_ACTION_DEL)",
                        "UPDATE SIB_RULES set RULE = :IN_RULE ,RULE_J = :IN_RULE_J,ACCESS_TYPE = :IN_ACCESS_TYPE ,DT_CHANGE = :IN_DT_CHANGE" + Environment.NewLine
                       +"                    ,USER_CHANGE = :IN_USER_CHANGE ,IS_ACTIVE = :IN_IS_ACTIVE,DESCRIPTION = :IN_DESCRIPTION" + Environment.NewLine
                       +"                    ,USER_ATTRS = :IN_USER_ATTRS,OBJECT_ATTRS = :IN_OBJECT_ATTRS,ACTION_USE = :IN_ACTION_USE" + Environment.NewLine
                       +"                    ,ACTION_ADD = :IN_ACTION_ADD,ACTION_EDIT = :IN_ACTION_EDIT,ACTION_DEL = :IN_ACTION_DEL" + Environment.NewLine
                       +" WHERE ID = :IN_ID AND CODE = :IN_CODE",
                        "DELETE FROM SIB_RULES WHERE ID = :IN_ID");
            }
        }
        
        public static class SIB_ViewUM
        {
            public static string GetSqlQuery()
            {
                return "select ID_USER,CODE_USER,NAME_USER,ID_MODULE,CODE_MODULE,NAME_MODULE, DT_CHANGE,USER_CHANGE from (" + Environment.NewLine
                     + "  select UM.ID_USER,U.CODE as CODE_USER, U.NAME as NAME_USER, UM.ID_MODULE ,M.CODE as CODE_MODULE, M.NAME as NAME_MODULE, UM.DT_CHANGE, UM.USER_CHANGE" + Environment.NewLine
                     + "    from SIB_USERMODULES_LNK UM" + Environment.NewLine
                      +"     join SIB_USERS U on U.ID = UM.ID_USER" + Environment.NewLine
                      +"     join SIB_MODULES M on M.ID = UM.ID_MODULE" + Environment.NewLine
                      +"    WHERE :P_USER_ID IS NULL or U.ID = :P_USER_ID )" + Environment.NewLine
                      +" WHERE (:P_MODULE_CODE IS NULL or CODE_MODULE = :P_MODULE_CODE)" + Environment.NewLine
                     + "   AND (:P_USER_CODE IS NULL or UPPER(CODE_USER) = UPPER(:P_USER_CODE) ) " + Environment.NewLine
                     + "   AND (:P_USER_NAME IS NULL or UPPER(NAME_USER) = UPPER(:P_USER_NAME) )";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Связанные модули с пользователем", _typeobj);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID_USER", NAME = "ID пользователя", DATATYPE = "NUMBER", DATALENGTH = 32},
                new REP_Models.ColumnMetadata { CODE = "CODE_USER", NAME = "Логин пользователя", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME_USER", NAME = "Наименование пользователя", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "ID_MODULE", NAME = "ID модуля", DATATYPE = "NUMBER", DATALENGTH = 32, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE_MODULE", NAME = "Код модуля", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME_MODULE", NAME = "Наименование модуля", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "DT_CHANGE", NAME = "Дата изменения", DATATYPE = "DATE", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "USER_CHANGE", NAME = "Кем изменен", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1}
                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>
                {
                    new REP_Models.ParamMetadata { CODE = "P_USER_ID", NAME = "User ID", DOMAINCODE ="Id",  DATATYPE = "NUMBER", DATALENGTH = 32, HIDDENVALUE = 1},
                    new REP_Models.ParamMetadata { CODE = "P_USER_CODE", NAME = "Логин пользователя", DOMAINCODE ="Code",  DATATYPE = "VARCHAR2", DATALENGTH = 30},
                    new REP_Models.ParamMetadata { CODE = "P_USER_NAME", NAME = "Наименование пользователя", DOMAINCODE ="Code",  DATATYPE = "VARCHAR2", DATALENGTH = 100},
                    new REP_Models.ParamMetadata { CODE = "P_MODULE_CODE", NAME = "Наименование модуля", DOMAINCODE ="CODE",  DATATYPE = "VARCHAR2", DATALENGTH = 30, DIMCODE = "SIB_DIM_MODULES",DIMNAME = "Модули СИБ" , ISMANDATORY = 0}
                };
            }

        }
        public static class SIB_ViewUR
        {
            public static string GetSqlQuery()
            {
                return "select ID_USER,CODE_USER,NAME_USER,ID_ROLE,CODE_ROLE,NAME_ROLE, DT_CHANGE,USER_CHANGE from (" + Environment.NewLine
                     + "  select UR.ID_USER,U.CODE as CODE_USER, U.NAME as NAME_USER, UR.ID_ROLE ,R.CODE as CODE_ROLE, R.NAME as NAME_ROLE, UR.DT_CHANGE, UR.USER_CHANGE" + Environment.NewLine
                     + "    from SIB_USERROLES_LNK UR" + Environment.NewLine
                      +"     join SIB_USERS U on U.ID = UR.ID_USER" + Environment.NewLine
                      +"     join SIB_ROLES R on R.ID = UR.ID_ROLE" + Environment.NewLine
                      +"    WHERE :P_USER_ID IS NULL or U.ID = :P_USER_ID )" + Environment.NewLine
                      +" WHERE (:P_ROLE_CODE IS NULL or CODE_ROLE = :P_ROLE_CODE)" + Environment.NewLine
                     + "   AND (:P_USER_CODE IS NULL or UPPER(CODE_USER) = UPPER(:P_USER_CODE) ) " + Environment.NewLine
                     + "   AND (:P_USER_NAME IS NULL or UPPER(NAME_USER) = UPPER(:P_USER_NAME) )";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Связанные роли с пользователем", _typeobj);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID_USER", NAME = "ID пользователя", DATATYPE = "NUMBER", DATALENGTH = 32},
                new REP_Models.ColumnMetadata { CODE = "CODE_USER", NAME = "Логин пользователя", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME_USER", NAME = "Наименование пользователя", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "ID_ROLE", NAME = "ID роли", DATATYPE = "NUMBER", DATALENGTH = 32, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE_ROLE", NAME = "Код роли", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME_ROLE", NAME = "Наименование роли", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "DT_CHANGE", NAME = "Дата изменения", DATATYPE = "DATE", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "USER_CHANGE", NAME = "Кем изменен", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1}
                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>
                {
                    new REP_Models.ParamMetadata { CODE = "P_USER_ID", NAME = "User ID", DOMAINCODE ="Id",  DATATYPE = "NUMBER", DATALENGTH = 32, HIDDENVALUE = 1},
                    new REP_Models.ParamMetadata { CODE = "P_USER_CODE", NAME = "Логин пользователя", DOMAINCODE ="Code",  DATATYPE = "VARCHAR2", DATALENGTH = 30},
                    new REP_Models.ParamMetadata { CODE = "P_USER_NAME", NAME = "Наименование пользователя", DOMAINCODE ="Code",  DATATYPE = "VARCHAR2", DATALENGTH = 100},
                    new REP_Models.ParamMetadata { CODE = "P_ROLE_CODE", NAME = "Наименование роли", DOMAINCODE ="CODE",  DATATYPE = "VARCHAR2", DATALENGTH = 30, DIMCODE = "SIB_DIM_ROLES",DIMNAME = "Роли СИБ"}
                };
            }

        }
        public static class SIB_Policy
        {
            public static string GetSqlQuery()
            {
                return "select p.id_user,u.code as code_user,p.id_object,o.code as code_object,p.id_rule,r.code as code_rule, "+ Environment.NewLine
                      +"       p.access_type,p.action_get,p.action_add,p.action_edit,p.action_del,p.dt_change,p.user_change "+ Environment.NewLine
                      +"  from SIB_POLICY P "+ Environment.NewLine
                      +"  join SIB_USERS u on u.id = p.id_user"+ Environment.NewLine
                      +"  join SIB_RULES r on r.id = p.id_rule"+ Environment.NewLine
                      +"  join REP_OBJECT o on o.id = p.id_object"+ Environment.NewLine
                      +" Where (upper(o.code)  = upper(:P_OBJ_CODE) or :P_OBJ_CODE IS NULL)"+ Environment.NewLine
                      +"   and (upper(u.code) = upper(:P_USR_CODE) or :P_USR_CODE IS NULL) "+ Environment.NewLine
                      +"   and (upper(r.code) = upper(:P_RULE_CODE) or :P_RULE_CODE IS NULL)";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список доступов", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (false, false, false);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID_USER", NAME = "ID пользователя", DATATYPE = "NUMBER", DATALENGTH = 32, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE_USER", NAME = "Логин пользователя", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "ID_OBJECT", NAME = "ID объекта", DATATYPE = "NUMBER", DATALENGTH = 32, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE_OBJECT", NAME = "Код объекта", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "ID_RULE", NAME = "ID правила", DATATYPE = "NUMBER", DATALENGTH = 32, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE_RULE", NAME = "Код правила", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "ACCESS_TYPE", NAME = "Тип доступа", DATATYPE = "VARCHAR2", DATALENGTH = 10 , VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "ACTION_GET", NAME = "Просмотр", DATATYPE = "NUMBER", DATALENGTH = 1 , VIEWVISIBLE =1,DOMAINCODE="Bool"},
                new REP_Models.ColumnMetadata { CODE = "ACTION_ADD", NAME = "Добавление", DATATYPE = "NUMBER", DATALENGTH = 1 , VIEWVISIBLE =1,DOMAINCODE="Bool"},
                new REP_Models.ColumnMetadata { CODE = "ACTION_EDIT", NAME = "Редактирование", DATATYPE = "NUMBER", DATALENGTH = 1 , VIEWVISIBLE =1,DOMAINCODE="Bool"},
                new REP_Models.ColumnMetadata { CODE = "ACTION_DEL", NAME = "Удаление", DATATYPE = "NUMBER", DATALENGTH = 1 , VIEWVISIBLE =1,DOMAINCODE="Bool"},
                new REP_Models.ColumnMetadata { CODE = "DT_CHANGE", NAME = "Дата изменения", DATATYPE = "DATE", DATALENGTH = null , VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "USER_CHANGE", NAME = "Кем изменен", DATATYPE = "VARCHAR2", DATALENGTH = 30 , VIEWVISIBLE =1}
                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>
                {
                new REP_Models.ParamMetadata { CODE = "P_OBJ_CODE", NAME = "Код объекта", DOMAINCODE ="Code",  DATATYPE = "VARCHAR2", DATALENGTH = 30, HIDDENVALUE = 0},
                new REP_Models.ParamMetadata { CODE = "P_USR_CODE", NAME = "Логин пользователя", DOMAINCODE ="Code",  DATATYPE = "VARCHAR2", DATALENGTH = 30, HIDDENVALUE = 0},
                new REP_Models.ParamMetadata { CODE = "P_RULE_CODE", NAME = "Код правила", DOMAINCODE ="Code",  DATATYPE = "VARCHAR2", DATALENGTH = 30, HIDDENVALUE = 0}
                };
            }
            public static (string sqlA, string sqlE, string slqD) GetActQuery()
            {
                return (null,null,null);
            }
        }
        public static class SIB_AttrSpr
        {
            public static string GetSqlQuery()
            {
                return "select ID, CODE, NAME, TYPE_ATTR, "+ Environment.NewLine
                      +"  case when TYPE_ATTR='U' then 'Атрибут пользователя' "+ Environment.NewLine
                      +"       else 'Атрибут объекта' "+ Environment.NewLine
                      +"   end TYPE_ATTR#N, DT_CHANGE, USER_CHANGE "+ Environment.NewLine
                      +"  from SIB_ATTR_SPR";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Справочник атрибутов", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (true, true, true);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "Id атрибута", DATATYPE = "NUMBER", DATALENGTH = 16, IS_PK = 1},
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "Код атрибута", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE = 1,INSERTABLE = 1,ISMANDATORY = 1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "Наименование атрибута", DATATYPE = "VARCHAR2", DATALENGTH = 100 , INSERTABLE = 1,EDITABLE = 1, VIEWVISIBLE =1, ISMANDATORY = 1},
                new REP_Models.ColumnMetadata { CODE = "TYPE_ATTR", NAME = "Тип атрибута", DATATYPE = "VARCHAR2", DATALENGTH = 10 , INSERTABLE = 1,EDITABLE = 1,DOMAINCODE = "CODE", DIMCODE = "SIB_DIM_TATTR" , VIEWVISIBLE =1,ISMANDATORY = 1},
                new REP_Models.ColumnMetadata { CODE = "TYPE_ATTR#N", NAME = "Тип атрибута", DATATYPE = "VARCHAR2", DATALENGTH = 10 , VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "DT_CHANGE", NAME = "Дата изменения", DATATYPE = "DATE", DATALENGTH = null , VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "USER_CHANGE", NAME = "Кем изменен", DATATYPE = "VARCHAR2", DATALENGTH = 30 , VIEWVISIBLE =1}

                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {

                return new List<REP_Models.ParamMetadata>();
            }
            public static (string sqlA, string sqlE, string slqD) GetActQuery()
            {
                return ("INSERT INTO SIB_ATTR_SPR(ID,CODE,NAME,TYPE_ATTR,DT_CHANGE,USER_CHANGE)" + Environment.NewLine
                       + "VALUES ( ewa_row_seq.nextval,:IN_CODE,:IN_NAME,:IN_TYPE_ATTR,:EWA_IN_CURDATE,:EWA_IN_USERCODE)" + Environment.NewLine
                       + "RETURNING ID INTO :OUT_ID",
                        "UPDATE SIB_ATTR_SPR" + Environment.NewLine
                       + "   SET NAME = :IN_NAME," + Environment.NewLine
                       + "       TYPE_ATTR = :IN_TYPE_ATTR," + Environment.NewLine
                       + "       DT_CHANGE = :EWA_IN_CURDATE," + Environment.NewLine
                       + "     USER_CHANGE = :EWA_IN_USERCODE" + Environment.NewLine
                       + "   WHERE ID = :IN_ID",
                        "declare " + Environment.NewLine
                       + "    cnt integer; " + Environment.NewLine
                       + "begin " + Environment.NewLine
                       + " select count(1) into cnt from SIB_RULES " + Environment.NewLine
                       + "  where instr(rule, :IN_TYPE_ATTR || '_' ||:IN_CODE || ' ') > 0; " + Environment.NewLine
                       + " if cnt > 0 then " + Environment.NewLine
                       + "   :EWA_OUT_RESULT:= -1; " + Environment.NewLine
                       + "   :EWA_OUT_MESSAGE:= 'Удаление атрибута запрещено!'; " + Environment.NewLine
                       + "   else delete from SIB_ATTR_SPR where ID = :IN_ID; " + Environment.NewLine
                       + " end if; " + Environment.NewLine
                       + "end; "
                       );
            }

        }
        public static class SIB_CONFIG
        {
            public static string GetSqlQuery()
            {
                return "select c.CFG_GROUP,c.CFG_CODE, decode(c.CFG_CODE,'BLCK','Автоматическая блокировка пользователя','PSWD','Парольная политика','Прочие') as CFG_CODE_NAME,c.CFG_PARAM,c.CFG_NAME,c.CFG_VALUE,c.DT_CHANGE,c.USER_CHANGE "+Environment.NewLine
                      +"  from glb_config c where upper(CFG_GROUP) ='SIB' "+Environment.NewLine
                      +"   and (:P_CFG_CODE IS NULL OR c.CFG_CODE = :P_CFG_CODE) order by c.cfg_group,c.cfg_code,c.cfg_param";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Настройка параметров безопасности", _typeobj);
            }
            public static (bool actA, bool actE, bool actD) GetActions()
            {
                return (false, true, false);
            }
            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                    new REP_Models.ColumnMetadata { CODE = "CFG_GROUP", NAME = "Группа конфигурации", DATATYPE = "VARCHAR2", DATALENGTH = 100,ISMANDATORY = 1,VIEWVISIBLE =0,INSERTABLE = 0,IS_PK = 1 },
                    new REP_Models.ColumnMetadata { CODE = "CFG_CODE", NAME = "Код настройки", DATATYPE = "VARCHAR2", DATALENGTH = 100,ISMANDATORY = 1, VIEWVISIBLE =1,INSERTABLE = 1,IS_PK = 1  },
                    new REP_Models.ColumnMetadata { CODE = "CFG_CODE_NAME", NAME = "Наименование настройки", DATATYPE = "VARCHAR2", DATALENGTH = 100,ISMANDATORY = 1, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "CFG_PARAM", NAME = "Код параметра", DATATYPE = "VARCHAR2", DATALENGTH = 100,ISMANDATORY = 1, VIEWVISIBLE =1,INSERTABLE = 1,IS_PK = 1  },
                    new REP_Models.ColumnMetadata { CODE = "CFG_NAME", NAME = "Наименование параметра", DATATYPE = "VARCHAR2", DATALENGTH = 1000,VIEWVISIBLE =1,INSERTABLE = 1 },
                    new REP_Models.ColumnMetadata { CODE = "CFG_VALUE", NAME = "Значение", DATATYPE = "CLOB", VIEWVISIBLE =1,ISMANDATORY = 1,INSERTABLE = 1, EDITABLE = 1 },
                    new REP_Models.ColumnMetadata { CODE = "DT_CHANGE", NAME = "Дата изменения", DATATYPE = "DATE", DATALENGTH = null, VIEWVISIBLE =1 },
                    new REP_Models.ColumnMetadata { CODE = "USER_CHANGE", NAME = "Кем изменен", DATATYPE = "VARCHAR2", DATALENGTH = 30, VIEWVISIBLE =1 }
                };
            }
            public static List<REP_Models.ParamMetadata> GetParamMetadata()
            {
                return new List<REP_Models.ParamMetadata>
                {
                new REP_Models.ParamMetadata { CODE = "P_CFG_CODE", NAME = "Наименование настройки", DOMAINCODE ="CODE",DIMCODE ="SIB_DIM_CFG_TYPE",  DATATYPE = "VARCHAR2", DATALENGTH = 100, HIDDENVALUE = 0}
                };
            }
            public static (string sqlA, string sqlE, string slqD) GetActQuery()
            {
                return (//insert
                        "declare "+Environment.NewLine
                       +"l_group varchar2(100); "+Environment.NewLine
                       +"l_code varchar2(100); "+Environment.NewLine
                       +"l_param varchar2(100); "+Environment.NewLine
                       +"l_cnt number(32):= 0; "+Environment.NewLine
                       +"l_error clob; "+Environment.NewLine
                       +"l_errcnt number(32):= 0; "+Environment.NewLine
                       +"begin "+Environment.NewLine
                       +" l_group := upper(:IN_CFG_GROUP); "+Environment.NewLine
                       +" l_code:= upper(:IN_CFG_CODE); "+Environment.NewLine
                       +" l_param:= :IN_CFG_PARAM; "+Environment.NewLine
                       +" if (l_group is null or l_code is null or l_param is null) "+Environment.NewLine
                       +"   then "+Environment.NewLine
                       +"     l_errcnt:= l_errcnt + 1; "+Environment.NewLine
                       +"     l_error:= l_error || ' Незаполнены обязательные параметры: CFG_GROUP, CFG_CODE, CFG_PARAM! '; "+Environment.NewLine
                       +" end if; "+Environment.NewLine
                       +" if l_group not in ('CON_STR') "+Environment.NewLine
                       +"   then "+Environment.NewLine
                       +"     l_errcnt:= l_errcnt + 1; "+Environment.NewLine
                       +"     l_error:= l_error || ' Добавление настройки ' || l_group || ' запрещено! '; "+Environment.NewLine
                       +" end if; "+Environment.NewLine
                       + " select count(1) into l_cnt from glb_Config where upper(CFG_GROUP) = l_group and upper(CFG_CODE) = l_code and upper(CFG_PARAM) = upper(l_param); " + Environment.NewLine
                       +" if l_cnt > 0 "+Environment.NewLine
                       +"   then "+Environment.NewLine
                       +"     l_errcnt:= l_errcnt + 1; "+Environment.NewLine
                       +"     l_error:= l_error || ' Настройка ' || l_group || '#' || l_code || '#' || l_param || ' существует! '; "+Environment.NewLine
                       +" end if; "+Environment.NewLine
                       +" if l_errcnt = 0 "+Environment.NewLine
                       +"   then "+Environment.NewLine
                       +"     insert into GLB_CONFIG(CFG_GROUP, CFG_CODE, CFG_PARAM, CFG_NAME, CFG_VALUE, DT_CHANGE, USER_CHANGE) "+Environment.NewLine
                       +"     values(l_group, l_code, l_param, :IN_CFG_NAME, :IN_CFG_VALUE,:EWA_IN_CURDATE,:EWA_IN_USERCODE) "+Environment.NewLine
                       +"     returning CFG_GROUP, CFG_CODE, CFG_PARAM into :OUT_CFG_GROUP,:OUT_CFG_CODE,:OUT_CFG_PARAM; "+Environment.NewLine
                       +"   else "+Environment.NewLine
                       +"     :EWA_OUT_RESULT:= -1; "+Environment.NewLine
                       +"     :EWA_OUT_MESSAGE:= l_error; "+Environment.NewLine
                       +"     :OUT_CFG_GROUP:= l_group; "+Environment.NewLine
                       +"     :OUT_CFG_CODE:= l_code; "+Environment.NewLine
                       +"     :OUT_CFG_PARAM:= l_param; "+Environment.NewLine
                       +" end if; "+Environment.NewLine
                       +"end;"  ,
                       //update
                        "UPDATE glb_config c " + Environment.NewLine
                       +"   set c.cfg_value = :IN_CFG_VALUE "+Environment.NewLine
                       +"      ,c.DT_CHANGE = :EWA_IN_CURDATE "+Environment.NewLine
                       +"      ,c.USER_CHANGE = :EWA_IN_USERCODE "+Environment.NewLine
                       +"where UPPER(CFG_GROUP) = UPPER(:IN_CFG_GROUP) and UPPER(CFG_CODE) = UPPER(:IN_CFG_CODE) and UPPER(CFG_PARAM) = UPPER(:IN_CFG_PARAM) "+Environment.NewLine
                       +"returning CFG_GROUP, CFG_CODE, CFG_PARAM into :OUT_CFG_GROUP,:OUT_CFG_CODE,:OUT_CFG_PARAM " ,
                        //delete
                        null);
            }

        }
        public static class S_DIM_AD_SPR
        {
            public static string GetSqlQuery()
            {
                return "select ID,CODE,NAME from sib_ad_spr";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список объектов AD", _typeobj);
            }

            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 16, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "CODE", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ID NAME", DATATYPE = "VARCHAR2", DATALENGTH = 450, VIEWVISIBLE =1}
                };
            }

        }
        public static class S_DIM_SINCH_TYPE
        {
            public static string GetSqlQuery()
            {
                return "select 0 as ID , 'NONE' as CODE, 'Без синхронизации' as NAME from dual union "+Environment.NewLine
                      +"select 1 as ID , 'SYNCH_M' as CODE, 'Ручная синхронизация' as NAME from dual union "+Environment.NewLine
                      +"select 2 as ID , 'SYNCH_A' as CODE, 'Автоматическая синхронизация' as NAME from dual";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Тип синронизации AD", _typeobj);
            }

            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 16, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "CODE", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ID NAME", DATATYPE = "VARCHAR2", DATALENGTH = 450, VIEWVISIBLE =1}
                };
            }

        }
        public static class S_DIM_EWA_SPR
        {
            public static string GetSqlQuery()
            {
                return "select ID,CODE,NAME from sib_modules union"+Environment.NewLine
                      +"select ID,CODE,NAME from sib_roles";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список объектов EWA", _typeobj);
            }

            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 16, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "CODE", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ID NAME", DATATYPE = "VARCHAR2", DATALENGTH = 450, VIEWVISIBLE =1}
                };
            }

        }

        public static class S_DIM_AD_TYPE
        {
            public static string GetSqlQuery()
            {
                return "select 1 as ID, 'GROUP' as CODE, 'Группа AD' as NAME from dual union" +Environment.NewLine
                      +"select 2 as ID, 'ATTR' as CODE, 'Атрибут AD' as NAME from dual";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список типов объектов AD", _typeobj);
            }

            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 16, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "CODE", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ID NAME", DATATYPE = "VARCHAR2", DATALENGTH = 450, VIEWVISIBLE =1}
                };
            }

        }
        public static class S_DIM_EWA_TYPE
        {
            public static string GetSqlQuery()
            {
                return "select 1 as ID, 'EWAMODULE' as CODE, 'Модуль EWA' as NAME from dual union" + Environment.NewLine
                      +"select 2 as ID, 'EWAROLE' as CODE, 'Роль EWA' as NAME from dual union" + Environment.NewLine
                      +"select 3 as ID, 'EWAATTR' as CODE, 'Атрибут EWA' as NAME from dual";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список типов объектов EWA", _typeobj);
            }

            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 16, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "CODE", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ID NAME", DATATYPE = "VARCHAR2", DATALENGTH = 450, VIEWVISIBLE =1}
                };
            }

        }
        public static class S_DIM_CFG_TYPE
        {
            public static string GetSqlQuery()
            {
                return "select 1 as ID, 'PSWD' as CODE, 'Парольная политика' as NAME from dual union" + Environment.NewLine
                      +"select 2 as ID, 'BLCK' as CODE, 'Автоматическая блокировка пользователя' as NAME from dual";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Тип настроек SIB", _typeobj);
            }

            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 16, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "CODE", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ID NAME", DATATYPE = "VARCHAR2", DATALENGTH = 450, VIEWVISIBLE =1}
                };
            }

        }
        public static class S_DIM_MOUDLES
        {
            public static string GetSqlQuery()
            {
                return "select ID, CODE, NAME from SIB_MODULES";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список модулей приложения", _typeobj);
            }

            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 16, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "CODE", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ID NAME", DATATYPE = "VARCHAR2", DATALENGTH = 450, VIEWVISIBLE =1}
                };
            }

        }
        public static class S_DIM_ROLES
        {
            public static string GetSqlQuery()
            {
                return "select ID, CODE, NAME from SIB_ROLES";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список ролей приложения", _typeobj);
            }

            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 16, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "CODE", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ID NAME", DATATYPE = "VARCHAR2", DATALENGTH = 450, VIEWVISIBLE =1}
                };
            }

        }
        public static class S_DIM_USER_ATTR
        {
            public static string GetSqlQuery()
            {
                return @"select ID, CODE, NAME from sib_attr_spr where type_attr = 'U' ";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список атрибутов пользователя", _typeobj);
            }

            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 16, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "CODE", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ID NAME", DATATYPE = "VARCHAR2", DATALENGTH = 450, VIEWVISIBLE =1}
                };
            }

        }
        public static class S_DIM_OBJ_ATTR
        {
            public static string GetSqlQuery()
            {
                return @"select ID, CODE, NAME from sib_attr_spr where type_attr = 'O' ";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список атрибутов объекта", _typeobj);
            }

            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 16, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "CODE", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ID NAME", DATATYPE = "VARCHAR2", DATALENGTH = 450, VIEWVISIBLE =1}
                };
            }

        }
        public static class S_DIM_TYPEATTR
        {
            public static string GetSqlQuery()
            {
                return "select 1 as ID, 'U' as CODE, 'Атрибут пользователя' as NAME from dual union" + Environment.NewLine
                      +"select 2 as ID, 'O' as CODE, 'Атрибут объекта' as NAME from dual";
            }
            public static (string title, string typeobj) GetObjInfo()
            {
                return ("Список типов атрибутов", _typeobj);
            }

            public static List<REP_Models.ColumnMetadata> GetColumnMetadata()
            {
                return new List<REP_Models.ColumnMetadata>
                {
                new REP_Models.ColumnMetadata { CODE = "ID", NAME = "ID", DATATYPE = "NUMBER", DATALENGTH = 16, VIEWVISIBLE =0},
                new REP_Models.ColumnMetadata { CODE = "CODE", NAME = "CODE", DATATYPE = "VARCHAR2", DATALENGTH = 100, VIEWVISIBLE =1},
                new REP_Models.ColumnMetadata { CODE = "NAME", NAME = "ID NAME", DATATYPE = "VARCHAR2", DATALENGTH = 450, VIEWVISIBLE =1}
                };
            }

        }
        /* удалить
        public static class SIBSpr
        {
            public static readonly Dictionary<string, string> AttributeTypes = new()
            {
                { "U", "Атрибут пользователя" },
                { "O", "Атрибут объекта" }
            };
        }
        */
    }
}
