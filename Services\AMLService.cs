﻿using DocumentFormat.OpenXml.Drawing.Diagrams;
using DocumentFormat.OpenXml.EMMA;
using DocumentFormat.OpenXml.Office2010.Excel;
using EWA.Data;
using FastReport.Utils;
using EWA.Models;
using static EWA.Models.AML_Models;
using System.Text.RegularExpressions;
using System.Text.Json;
using static EWA.Services.UIService;
using DocumentFormat.OpenXml.Drawing.Charts;
using Microsoft.OData.Edm.Vocabularies;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Drawing.Spreadsheet;
using System.CodeDom.Compiler;
using System.Windows.Forms;
using System;
using Microsoft.CodeAnalysis;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.Reflection;
using Microsoft.OData.UriParser;
using Parlot.Fluent;
using DocumentFormat.OpenXml.Vml;
using DocumentFormat.OpenXml.Drawing;



namespace EWA.Services
{
    public class AMLService
    {
        private DBService dbService;
        private readonly DBContext _context;
        private readonly RepService _repService;

        public AMLService(DBContext context, RepService repService)
        {
            _context = context;
            _repService = repService;
        }
        public async Task<Dictionary<string, (string sql_out,string sql_paramList, bool is_err, string mess)>> GetSQLQuery(string code, string in_dts, string in_dte)
        {
            var result = new Dictionary<string, (string sql_out, string sql_paramList, bool is_err, string mess)>();
            string sql_query = "select * from dual";
            string type_object = string.Empty;
            string sql_delete = string.Empty;
            string sql_param = string.Empty;
            string sql_cursor = string.Empty;
            string sql_insert = string.Empty;
            string sql_method = string.Empty;
            string sql_paramList = string.Empty;
            Dictionary<string, string> paramList = new Dictionary<string, string>();
            string sql_join = string.Empty;
            try
            {
                var oinfo = await GetObjInfo(code);
                if (oinfo.Count > 0)
                {
                    if (oinfo.TryGetValue(code, out var value) && value.IS_ERR)
                    {
                        result[code] = (null,null, true, value.ERR_MESS);
                        return result;
                    }
                    else if (value.TypeObject == null)
                    {
                        result[code] = (null, null, true, $"Тип не задан для {code}");
                        return result;
                    }
                    else
                    {
                        type_object = value.TypeObject;
                    }
                }
                else
                {
                    result[code] = (null, null, true, $"{code} не найден в справочнике объектов");
                    return result;
                }
                if (type_object == "INDICATOR")
                {
                    var ind_res = await GetIndInfo(code);
                    if (ind_res.Count == 0)
                    {
                        result[code] = (null, null, true, $"{code} не найден в справочнике показателей");
                        return result;
                    }
                    if (ind_res.TryGetValue(code, out var value) && value.IS_ERR)
                    {
                        result[code] = (null, null, true, value.ERR_MESS);
                        return result;
                    }
                    var typeInd = value.TypeInd;
                    var cust_side = value.CustSide;
                    var acct_side = value.AcctSide;
                    var indID = value.ID;
                    var indCode = value.Code;
                    var filt = value.ALG;
                    var idMetd = value.IDMethod;
                    var codeMetd = value.CodeMethod;
                    var type_data = value.TypeData;
                    List<string> attr_list = value.AttrList.Split(',')
                                                           .Select(s => s.Trim())
                                                           .Where(s => !string.IsNullOrEmpty(s))
                                                           .ToList();
                    List<string> ind_list = value.IndList.Split(',')
                                                         .Select(s => s.Trim())
                                                         .Where(s => !string.IsNullOrEmpty(s))
                                                         .ToList();
                    
                    if (attr_list.Count > 0 && ind_list.Count > 0)
                    {
                        result[code] = (null, null, true, $@"Для {code} неверная настройка: использованы показатели и атрибуты!");
                        return result;

                    }
                    sql_delete = GetDelSql(code, (int)typeInd);
                    
                    if (attr_list.Count > 0)
                    {
                        sql_insert = await GetIndDLSql(code, (int)typeInd, filt, (int)cust_side, (int)acct_side, in_dts, in_dte, (int)indID, indCode, attr_list);
                        sql_query = "BEGIN" + Environment.NewLine
                                   + $"{sql_delete}" + Environment.NewLine
                                   + $"{sql_insert};" + Environment.NewLine
                                   + "END;";
                    }

                    if (ind_list.Count > 0)
                    {
                        if (type_data == "BOOL")
                        {
                            (sql_insert, sql_cursor, sql_param) = await GetIndBoolSql(value.IndList, ind_list, (int)typeInd, (int)indID, indCode, filt, (int)cust_side, (int)acct_side);
                        }
                        if (type_data == "NUMBER" || type_data == "DATE" || type_data == "STRING")
                        {
                            if (idMetd == null || codeMetd == null)
                            {
                                result[code] = (null, null, true, $"Для {code} неуказан метод");
                                return result;
                            }

                            var m_res = await GetMthdSql(codeMetd);
                            if (m_res.is_err)
                            {
                                result[code] = (null, null, true, $"Метод {codeMetd} имеет ошибки: {m_res.err_mess}");
                                return result;
                            }

                            var i_val_items = m_res.ind_val.Split(", ", StringSplitOptions.RemoveEmptyEntries)
                                                           .Select(x => x.Trim())
                                                           .Distinct();
                            foreach (var item in i_val_items)
                            {
                                if (!ind_list.Contains(item))
                                {
                                    ind_list.Add(item);
                                }
                            }
                            string ind_vals = string.Join(", ", ind_list);

                            sql_method = m_res.sql_method;
                            (sql_insert, sql_cursor, sql_param) = await GetIndCalcSql(m_res.sql_add_cur, m_res.mdt_data, ind_vals, ind_list, (int)typeInd, (int)indID, indCode, filt, (int)cust_side, (int)acct_side);
                        }

                        sql_query =  "Declare " + Environment.NewLine
                                   +$"{sql_param}" + Environment.NewLine
                                   + "Begin" + Environment.NewLine
                                   +$" {sql_delete}" + Environment.NewLine
                                   +$" for cur_data in ({sql_cursor})" + Environment.NewLine
                                   + "  loop" + Environment.NewLine
                                   + $"   {sql_method}" + Environment.NewLine
                                   + $"   {sql_insert}" + Environment.NewLine
                                   + "  end loop;" + Environment.NewLine
                                   + "end;";
                    }

                        result[code] = (sql_query, null, false, null);

                }
                else if (type_object == "METHOD")
                { 
                    var mtd_res = await GetMthdInfo(code);
                    if (mtd_res.Count == 0)
                    {
                        result[code] = (null, null, true, $"{code} не найден в справочнике показателей");
                        return result;
                    }
                    if (mtd_res.TryGetValue(code, out var value) && value.IS_ERR)
                    {
                        result[code] = (null, null, true, value.ERR_MESS);
                        return result;
                    }
                    var type_data = value.TypeData;
                    var type_mtd = value.TypeMethod;
                    if (type_mtd == "GLOBAL")
                    {
                        
                        string mainTabJson = value.MainTab;
                        string customDataJson = value.CustomData;
                        string customParamJson = value.CustomParam;

                        List<AML_Models.MainTab> mainTab = JsonSerializer.Deserialize<List<AML_Models.MainTab>>(mainTabJson);
                        List<AML_Models.SelectField> selectFields = JsonSerializer.Deserialize<List<AML_Models.SelectField>>(customDataJson);
                        List<AML_Models.Params> parameters = JsonSerializer.Deserialize<List<AML_Models.Params>>(customParamJson);
                        
                        string col_name = string.Empty;
                        
                        (col_name, sql_param) = type_data switch
                        {
                            "DATE" => ("date_value", "f_val date;"),
                            "NUMBER" => ("number_value", "f_val number;"),
                            _ => ("string_value", "f_val varchar2(4000);")
                        };

                        (sql_method, paramList) = GetMthdSqlM(mainTab[0], selectFields, parameters, in_dts, in_dte);

                        sql_insert = $"insert into aml.mem_global_value(code,{col_name}, type_value,dt_modified)"+Environment.NewLine
                                    +$"values('{code}',f_val,'{type_data}',sysdate);";
                        sql_paramList  = JsonSerializer.Serialize(paramList, new JsonSerializerOptions { WriteIndented = true });
                        
                    }
                    else 
                    {
                        result[code] = (null, null, true, $"{code} с типом {type_mtd} не обрабатывается.");
                        return result;
                    }
                    sql_delete = $"Delete from aml.mem_global_value where upper(code) = upper('{code}');";
                    

                    

                    sql_query = "Declare " + Environment.NewLine
                                   + $"{sql_param}" + Environment.NewLine
                                   + "Begin" + Environment.NewLine
                                   + $" {sql_delete}" + Environment.NewLine
                                   + $" {sql_method}" + Environment.NewLine
                                   + $" {sql_insert}" + Environment.NewLine
                                   + "end;";



                }
                else
                {
                    result[code] = (null, null, true, $"Тип {type_object} не обрабатывается!");
                    return result;
                }

                result[code] = (sql_query, sql_paramList, false, null);
            }
            catch (Exception ex)
            {
                result[code] = (null, null, true, ex.Message);
            }

            return result;
        }

        public async Task<(string sql_method,string sql_add_cur, string mdt_data, string ind_val, bool is_err, string err_mess)> GetMthdSql(string code)
        {
            string sql_method = string.Empty;
            string sql_add_cur = string.Empty;
            string mdt_data = string.Empty;
            bool is_err = false;
            string err_mess = string.Empty;
            List<AML_ATTR> Attr_SPR_FILTR = new();
            string ind_val = string.Empty;

            var m_info = await GetMthdInfo(code);
            if (m_info.TryGetValue(code, out var mval) && mval.IS_ERR)
            {
                is_err = true;
                err_mess = mval.ERR_MESS;
                return (sql_method, sql_add_cur, mdt_data, ind_val, is_err, err_mess);
            }
            
            string mainTabJson = mval.MainTab;
            string addTabJson = mval.AddTab;
            string customDataJson = mval.CustomData;
            string customParamJson = mval.CustomParam;
            string customFilterJson = mval.FilterData;
            
            

            long? indFiltM = mval.id_ind_filt;
            string indFilter = string.Empty;
            string indFilterLst = string.Empty;
            List<string> AttrFilterLst = new();
            long? indFilterTP = null;
            string indFilterTPD = string.Empty;

            if (indFiltM != null)
            {
                var indf_info = await GetIndInfoByID((long)indFiltM);
                if (indf_info.TryGetValue((long)indFiltM, out var ival) && ival.IS_ERR)
                {
                    is_err = true;
                    err_mess = ival.ERR_MESS;
                    return (sql_method, sql_add_cur, mdt_data, ind_val, is_err, err_mess);
                }
                indFilter = ival.ALG;
                indFilterLst = ival.IndList;
                AttrFilterLst = ival.AttrList.Split(',')
                                                   .Select(s => s.Trim())
                                                   .Where(s => !string.IsNullOrEmpty(s))
                                                   .ToList();
                indFilterTP = ival.TypeInd;
                indFilterTPD = ival.TypeData;
                
                if (AttrFilterLst.Count > 0)
                {
                    foreach (var item in AttrFilterLst)
                    {
                        var res_list = await getAttrData(item);
                        if (res_list.TryGetValue(item, out var val) && val.IS_ERR)
                        {
                            is_err = true;
                            err_mess = val.ERR_MESS;
                            return (sql_method, sql_add_cur, mdt_data, ind_val, is_err, err_mess);
                        }
                        else
                        {
                            var existingAttr = Attr_SPR_FILTR.FirstOrDefault(a => a.CodeAttr == val.Code);

                            if (existingAttr != null)
                            {
                                existingAttr.AliasTab = val.AliasTab;
                                existingAttr.SQLTab = val.SQLTab;
                                existingAttr.SQLTablColumn = val.SQLTablColumn;
                                existingAttr.AliasTabLnk = val.AliasTabLnk;
                                existingAttr.SQLTabLnk = val.SQLTabLnk;
                                existingAttr.SQLTablColumnLmk = val.SQLTablColumnLmk;
                            }
                            else
                            {
                                Attr_SPR_FILTR.Add(new AML_ATTR
                                {
                                    CodeAttr = val.Code,
                                    AliasTab = val.AliasTab,
                                    SQLTab = val.SQLTab,
                                    SQLTablColumn = val.SQLTablColumn,
                                    AliasTabLnk = val.AliasTabLnk,
                                    SQLTabLnk = val.SQLTabLnk,
                                    SQLTablColumnLmk = val.SQLTablColumnLmk
                                });
                            }
                        }
                    }
                }
            }
            List<AML_Models.MainTab> mainTab = JsonSerializer.Deserialize<List<AML_Models.MainTab>>(mainTabJson);
            List<AML_Models.Join> joins = JsonSerializer.Deserialize<List<AML_Models.Join>>(addTabJson);
            List<AML_Models.SelectField> selectFields = JsonSerializer.Deserialize<List<AML_Models.SelectField>>(customDataJson);
            List<AML_Models.Params> parameters = JsonSerializer.Deserialize<List<AML_Models.Params>>(customParamJson);
            List<AML_Models.Filter> filters = JsonSerializer.Deserialize<List<AML_Models.Filter>>(customFilterJson);

            ind_val = string.Join(", ", parameters
                                        .Where(p => p.TypeVal == "I" && !string.IsNullOrWhiteSpace(p.Value))
                                        .Select(p => p.Value)
                                        .Distinct());


            sql_method = GetMthdIndSql(mainTab[0], selectFields, joins, filters, parameters, indFilter, indFilterLst, Attr_SPR_FILTR, indFilterTP, indFilterTPD);
            
            var t_param_list = new List<string>();
            var addedCodes = new List<string>();
            foreach (var param in parameters.Where(p => p.TypeVal == "F" && !string.IsNullOrEmpty(p.Value)))
            {
                var dataDict = await GetMtdGlbData(param.Value);

                foreach (var kvp in dataDict)
                {
                    string mvalue = kvp.Value.TypeVal switch
                    {
                        "NUMBER" => kvp.Value.NumVal?.ToString(),
                        "STRING" => $"'{kvp.Value.StrVal}'",
                        "DATE" => $"to_date('{kvp.Value.DateVal?.Substring(0, 10)}','dd.mm.yyyy')",
                        _ => null
                    };

                    if (!string.IsNullOrEmpty(mvalue) && !addedCodes.Contains(kvp.Value.Code))
                    {
                        t_param_list.Add($",{mvalue} as {kvp.Value.Code}");
                        addedCodes.Add(kvp.Value.Code);
                    }
                }
            }
            sql_add_cur = string.Join(" ", t_param_list);
            mdt_data = mval.TypeData;

            return (sql_method, sql_add_cur, mdt_data, ind_val, is_err, err_mess);
        }

        public async Task<(string sql_add, List<(string AliasTab, string SQLTab, string AliasTabLnk, string SQLTabLnk, string SQLTablColumnLmk)> uniqueValues)> GetAttrAdds(List<string> attr_list, int typeInd, int cust_side, int acct_side)
        {
            var t_res = new Dictionary<string, (bool is_res, string mess)>();
            List<AML_ATTR> Attr_SPR = new();
            var uniqueValues = new List<(string AliasTab, string SQLTab, string AliasTabLnk, string SQLTabLnk, string SQLTablColumnLmk)>();
            string sql_add = string.Empty;
            string sql_join = string.Empty;
            foreach (var item in attr_list)
            {
                var res_list = await getAttrData(item);
                if (res_list.TryGetValue(item, out var val) && val.IS_ERR)
                {
                    t_res[item] = (false, val.ERR_MESS);
                }
                else
                {
                    var existingAttr = Attr_SPR.FirstOrDefault(a => a.CodeAttr == val.Code);

                    if (existingAttr != null)
                    {
                        existingAttr.AliasTab = val.AliasTab;
                        existingAttr.SQLTab = val.SQLTab;
                        existingAttr.SQLTablColumn = val.SQLTablColumn;
                        existingAttr.AliasTabLnk = val.AliasTabLnk;
                        existingAttr.SQLTabLnk = val.SQLTabLnk;
                        existingAttr.SQLTablColumnLmk = val.SQLTablColumnLmk;
                    }
                    else
                    {
                        Attr_SPR.Add(new AML_ATTR
                        {
                            CodeAttr = val.Code,
                            AliasTab = val.AliasTab,
                            SQLTab = val.SQLTab,
                            SQLTablColumn = val.SQLTablColumn,
                            AliasTabLnk = val.AliasTabLnk,
                            SQLTabLnk = val.SQLTabLnk,
                            SQLTablColumnLmk = val.SQLTablColumnLmk
                        });
                    }

                    var brokenItems = Attr_SPR.Where(v => v == null ||
                                                 v.AliasTab == null ||
                                                   v.SQLTab == null ||
                                              v.AliasTabLnk == null ||
                                                v.SQLTabLnk == null ||
                                         v.SQLTablColumnLmk == null)
                                             .ToList();

                    foreach (var v in brokenItems)
                    {
                        Console.WriteLine("Найден объект с null-полями:");
                        Console.WriteLine($"v == null: {v == null}");
                        if (v != null)
                        {
                            Console.WriteLine($"AliasTab: {v.AliasTab}");
                            Console.WriteLine($"SQLTab: {v.SQLTab}");
                            Console.WriteLine($"AliasTabLnk: {v.AliasTabLnk}");
                            Console.WriteLine($"SQLTabLnk: {v.SQLTabLnk}");
                            Console.WriteLine($"SQLTablColumnLmk: {v.SQLTablColumnLmk}");
                        }
                    }


                    sql_add = string.Join("," + Environment.NewLine, Attr_SPR.Select(val => $"{val.AliasTab}.{val.SQLTablColumn} as {val.CodeAttr}"));
                    uniqueValues.AddRange(Attr_SPR.Select(v => (
                                                                v.AliasTab.ToString(),
                                                                v.SQLTab.ToString(),
                                                                v.AliasTabLnk.ToString(),
                                                                v.SQLTabLnk.ToString(),
                                                                v.SQLTablColumnLmk.ToString()
                                                           ))
                                                  .Distinct()
                                                  .ToList());
                    if (typeInd == 3)
                    {
                        string custId = ((cust_side == 1) ? "ID_CUST_PAY" : "ID_CUST_RCP");
                        string AcctId = ((cust_side == 1) ? "ID_ACCT_DEB" : "ID_ACCT_CRE");
                        var foundCust = uniqueValues.FirstOrDefault(item =>item.SQLTab == "AML.HUB_CUSTOMER" && item.AliasTab == (cust_side == 1 ? "pc" : "rc"));
                        if (foundCust == default)
                        {
                            var r_list = await getAttrData(custId);
                            if (r_list.TryGetValue(custId, out var tval) && tval.IS_ERR)
                            {
                                t_res[custId] = (false, tval.ERR_MESS);
                            }
                            else
                            {
                                uniqueValues.Add((tval.AliasTab,tval.SQLTab,tval.AliasTabLnk,tval.SQLTabLnk,tval.SQLTablColumnLmk));
                            }
                        }
                        var foundAcct = uniqueValues.FirstOrDefault(item => item.SQLTab == "AML.HUB_ACCOUNT" && item.AliasTab == (acct_side == 1 ? "da" : "ca"));
                        if (foundAcct == default)
                        {
                            var r_list = await getAttrData(AcctId);
                            if (r_list.TryGetValue(AcctId, out var tval) && tval.IS_ERR)
                            {
                                t_res[AcctId] = (false, tval.ERR_MESS);
                            }
                            else
                            {
                                uniqueValues.Add((tval.AliasTab, tval.SQLTab, tval.AliasTabLnk, tval.SQLTabLnk, tval.SQLTablColumnLmk));
                            }
                        }
                        sql_join = string.Join(Environment.NewLine, uniqueValues
                                         .Where(val => !string.IsNullOrEmpty(val.SQLTab) && !string.IsNullOrEmpty(val.SQLTabLnk))
                                         .Select(val => $"left join {val.SQLTab} {val.AliasTab} on {val.AliasTab}.id = {val.AliasTabLnk}.{val.SQLTablColumnLmk}"));
                    }
                }
            }
            return (sql_add, uniqueValues);
        }
        public async Task<string> GetIndDLSql(string code, int typeC, string filt, int cust_side, int acct_side, string in_dts, string in_dte, int indID, string indCode, List<string> attr_list)
        {
            string sql_query = string.Empty;
            string tab_name = string.Empty;
            string sql_add = string.Empty;
            string sql_join = string.Empty;
            string ins_tab_name = string.Empty;
            string sql_select = string.Empty;
            string sql_insert = string.Empty;
            string inscolAdd = string.Empty;
            string dtcol = string.Empty;
            string sql_where = string.Empty;
            if (typeC >= 1 && typeC <= 3)
            {
                dtcol = (typeC == 3) ? ", DT" : string.Empty;
                tab_name = (typeC == 1) ? "AML.HUB_CUSTOMER cust" :
                           (typeC == 2) ? "AML.HUB_ACCOUNT acct" :
                           (typeC == 3) ? "AML.HUB_TRANSACTION trn" : string.Empty;
                ins_tab_name = (typeC == 1) ? "AML.MEM_OBJECT_CUST" :
                           (typeC == 2) ? "AML.MEM_OBJECT_ACCT" :
                           (typeC == 3) ? "AML.MEM_OBJECT_VALUE" : string.Empty;
                inscolAdd = (typeC == 1) ? "ID_CUST,CODE_CUST" :
                           (typeC == 2) ? "ID_ACCT, CODE_ACCT,ACCT_SIDE,ID_CUST" :
                           (typeC == 3) ? "ID_CUST,CODE_CUST,ID_TRANSACTION,CODE_TRANSACTION,ID_ACCT,CODE_ACCT" : string.Empty;

                sql_insert = $"INSERT INTO {ins_tab_name} (id_obj,code_obj,type_obj {dtcol},number_value," + Environment.NewLine
                             + $"type_value,{inscolAdd}," + Environment.NewLine
                             + "cust_side,acct_side)";

                sql_select = (typeC == 1) ? "AML.HUB_CUSTOMER cust" :
                             (typeC == 2) ? "AML.HUB_ACCOUNT acct" :
                             (typeC == 3) ? " trn.id id_transaction, trn.code code_transaction, trn.dt_trn as DT" + Environment.NewLine
                                            + $",{(cust_side == 1 ? "pc.id" : cust_side == 0 ? "rc.id" : null)} ID_CUST" + Environment.NewLine
                                            + $",{(cust_side == 1 ? "pc.code" : cust_side == 0 ? "rc.code" : null)} CODE_CUST" + Environment.NewLine
                                            + $",{(acct_side == 1 ? "da.id" : acct_side == 0 ? "ca.id" : null)} ID_ACCT" + Environment.NewLine
                                            + $",{(acct_side == 1 ? "da.code" : acct_side == 0 ? "ca.code" : null)} CODE_ACCT" :
                             string.Empty;

                sql_where = (typeC == 3) ? $"Where trn.dt_trn between {in_dts} and {in_dte}" : string.Empty;
                
                var attr_res = await GetAttrAdds(attr_list, (int)typeC, (int)cust_side, (int)acct_side);
                if (attr_res.sql_add.Length > 0)
                {
                    sql_add = $",{attr_res.sql_add}";
                }
                sql_join = string.Join(Environment.NewLine, attr_res.uniqueValues
                                                               .Where(val => !string.IsNullOrEmpty(val.SQLTab) && !string.IsNullOrEmpty(val.SQLTabLnk))
                                                               .Select(val => $"left join {val.SQLTab} {val.AliasTab} on {val.AliasTab}.id = {val.AliasTabLnk}.{val.SQLTablColumnLmk}")
                                                               .Distinct());
                sql_query =  $"{sql_insert}" + Environment.NewLine
                           + $"select {indID} as ID_IND,'{indCode}' as CODE_IND,'INDICATOR' as TYPE_OBJ {dtcol},1," + Environment.NewLine
                           + $"'B',{inscolAdd}," + Environment.NewLine
                           + $"{cust_side} cust_side,{acct_side} acct_side" + Environment.NewLine
                           + $"from ( select {sql_select}" + Environment.NewLine
                           + $"              {sql_add}" + Environment.NewLine
                           + $"         from {tab_name}" + Environment.NewLine
                           + $"         {sql_join}" + Environment.NewLine
                           + $"        {sql_where}" + Environment.NewLine
                           + $"      ) WHERE {filt} " + Environment.NewLine;
            }
            return sql_query;


        }
        public async Task<(string sqlInsert, string sqlCursor, string sqlParam)> GetIndBoolSql(string Inds, List<string> ind_list, int typeInd, int indID, string indCode,string filt, int cust_side, int acct_side)

        {
            var t_res = new Dictionary<string, (bool is_res, string mess)>();
            
            var TValues = new Dictionary<string, (string SQLTab, string SQLAlias)>();
            List<string> sql_cases = new List<string>();


            string result = string.Empty;

            string sql_cursor = string.Empty;
            string sql_insert = string.Empty;
            string sql_param = string.Empty;
            string ins_tab_name = string.Empty;
            string dtcol = string.Empty;
            string dtcolSel = string.Empty;
            string dtcolIns = string.Empty;
            string inscolAdd = string.Empty;
            string inscolAddVal = string.Empty;
            string ins_side = string.Empty;
            string sideVal = string.Empty;
            string sql_where = string.Empty;
            string where_val = string.Empty;
            string where_cust = string.Empty;
            string where_acct = string.Empty;
            string sql_tab = string.Empty;
            string sql_join = string.Empty;
            string sel_ind = string.Empty;
            string maincol = string.Empty;
            string mainallias = string.Empty;
            string filterIns = string.Empty;

            
            
            string filterFind = string.Join(", ", ind_list.Select(s => $"'{s}'"));
            
            foreach (string ind in ind_list)
            {
                var tmp_ind = await GetIndInfo(ind);
                if (tmp_ind.Count == 0)
                {
                    t_res[ind] = (true, $"{ind} не найден в справочнике показателей");
                    return (sql_insert, sql_cursor, sql_param);
                }
                if (tmp_ind.TryGetValue(ind, out var tmp_val) && tmp_val.IS_ERR)
                {
                    t_res[ind] = (true, $"{ind}: {tmp_val.ERR_MESS}");
                    return (sql_insert, sql_cursor, sql_param);
                }
                var tmp_data = tmp_val.TypeData;
                var tmp_type = tmp_val.TypeInd;
                string tmp_incol = string.Empty;
                string tmp_res = string.Empty;
                string t_tab = string.Empty;
                string t_tabAS = string.Empty;

                if (!TValues.ContainsKey(ind))
                {   
                   t_tab = (tmp_type == 1) ? "AML.MEM_OBJECT_CUST" :
                               (tmp_type == 2) ? "AML.MEM_OBJECT_ACCT" :
                               (tmp_type == 3) ? "AML.MEM_OBJECT_VALUE" : string.Empty;

                    t_tabAS = (tmp_type == 1) ? "c" :
                                  (tmp_type == 2) ? "a" :
                                  (tmp_type == 3) ? "v" : string.Empty;
                    TValues[ind] = (t_tab, t_tabAS);
                }


                if (tmp_data == "DATE")
                {
                    tmp_incol = "date_value";
                }
                else if (tmp_data == "NUMBER" || tmp_data == "BOOL")
                {
                    tmp_incol = "number_value";
                }
                else
                {
                    tmp_incol = "string_value";
                }
                   
                string tmp_str = $"MAX(CASE WHEN {t_tabAS}.code_obj = '{ind}' THEN {t_tabAS}.{tmp_incol} END)";

                if (tmp_data == "NUMBER" || tmp_data == "BOOL")
                {
                    tmp_str = $"NVL({tmp_str}, 0)";
                }
                sql_cases.Add($"{tmp_str} AS {ind}");
            }
            where_val = string.Join(",", TValues.Where(x => x.Value.SQLTab == "AML.MEM_OBJECT_VALUE")
                                                  .Select(x => $"'{x.Key}'")
                                                  .Distinct()); 
            where_cust = string.Join(",", TValues.Where(x => x.Value.SQLTab == "AML.MEM_OBJECT_CUST")
                                                  .Select(x => $"'{x.Key}'")
                                                  .Distinct());
            where_acct = string.Join(",", TValues.Where(x => x.Value.SQLTab == "AML.MEM_OBJECT_ACCT")
                                                  .Select(x => $"'{x.Key}'")
                                                  .Distinct());
            if (!string.IsNullOrEmpty(where_val))
            {
                if (!string.IsNullOrEmpty(sql_where)) { sql_where += " AND "; }
                sql_where += $"v.code_obj in ({where_val})";
            }
            if (!string.IsNullOrEmpty(where_cust))
            {
                if (!string.IsNullOrEmpty(sql_where)) { sql_where += " AND "; }
                sql_where += $"c.code_obj in ({where_cust})";
            }

            if (!string.IsNullOrEmpty(where_acct))
            {
                if (!string.IsNullOrEmpty(sql_where)) { sql_where += " AND "; }
                sql_where += $"a.code_obj in ({where_acct})";
            }
            
            var firstVal = TValues.FirstOrDefault(x => x.Value.SQLAlias == "v");

            if (firstVal.Equals(default(KeyValuePair<string, (string, string)>)))
                firstVal = TValues.FirstOrDefault(x => x.Value.SQLAlias == "a");

            if (firstVal.Equals(default(KeyValuePair<string, (string, string)>)))
                firstVal = TValues.FirstOrDefault(x => x.Value.SQLAlias == "c");

            sql_tab = firstVal.Equals(default(KeyValuePair<string, (string, string)>))? "": 
                      $"{firstVal.Value.SQLTab} {firstVal.Value.SQLAlias}";
            
            var baseAlias = firstVal.Value.SQLAlias;

            var sqlJoinList = TValues
                                    .Where(x => x.Value.SQLAlias != baseAlias)
                                    .Select(x =>
                                    {
                                        var alias = x.Value.SQLAlias;
                                        var table = x.Value.SQLTab;
                                        return alias switch
                                        {
                                            "c" => baseAlias switch
                                            {
                                                "v" => $"left join {table} {alias} on {alias}.id_cust = {baseAlias}.id_cust",
                                                "a" => $"left join {table} {alias} on {alias}.id_cust = {baseAlias}.id_cust",
                                                _ => null
                                            },
                                            "a" => baseAlias switch
                                            {
                                                "v" => $"left join {table} {alias} on {alias}.id_acct = {baseAlias}.id_acct",
                                                _ => null
                                            },
                                            _ => null
                                        };
                                    })
                                    .Where(x => x != null)
                                    .Distinct();
            sql_join = string.Join(Environment.NewLine, sqlJoinList);

            dtcol = (typeInd == 3) ? ", DT" : string.Empty;
            dtcolSel = (typeInd == 3) ? ", max(v.DT) as DT" : string.Empty;
            dtcolIns = (typeInd == 3) ? ", cur_data.DT" : string.Empty;
            sel_ind = string.Join(", ", sql_cases);

            maincol = (typeInd == 1) ? "ID_CUST" :
                      (typeInd == 2) ? "ID_ACCT" :
                      (typeInd == 3) ? "ID_TRANSACTION" : string.Empty;
                        
            mainallias = (typeInd == 1) ? "ID_CUST" :
                         (typeInd == 2) ? "ID_ACCT" :
                         (typeInd == 3) ? "ID_TRANSACTION" : string.Empty;
                        
            filterIns = $"AND v.{maincol} = cur_data.{mainallias}";
            ins_side = (typeInd == 1) ? "cust_side" :
                       (typeInd == 2) ? "cust_side,acct_side" :
                       (typeInd == 3) ? "cust_side,acct_side" : string.Empty;
            sideVal = (typeInd == 1) ? "cur_data.cust_side" :
                       (typeInd == 2) ? "cur_data.cust_side,cur_data.acct_side" :
                       (typeInd == 3) ? "cur_data.cust_side,cur_data.acct_side" : string.Empty;


            ins_tab_name = (typeInd == 1) ? "AML.MEM_OBJECT_CUST" :
                           (typeInd == 2) ? "AML.MEM_OBJECT_ACCT" :
                           (typeInd == 3) ? "AML.MEM_OBJECT_VALUE" : string.Empty;
            
            inscolAdd = (typeInd == 1) ? "ID_CUST,CODE_CUST" :
                        (typeInd == 2) ? "ID_ACCT, CODE_ACCT,ID_CUST,CODE_CUST" :
                        (typeInd == 3) ? "ID_CUST,CODE_CUST,ID_TRANSACTION,CODE_TRANSACTION,ID_ACCT,CODE_ACCT" : string.Empty;

            inscolAddVal = (typeInd == 1) ? "cur_data.ID_CUST,cur_data.CODE_CUST" :
                        (typeInd == 2) ? "cur_data.ID_ACCT, cur_data.CODE_ACCT,cur_data.ID_CUST,cur_data.CODE_CUST" :
                        (typeInd == 3) ? "cur_data.ID_CUST,cur_data.CODE_CUST,cur_data.ID_TRANSACTION,cur_data.CODE_TRANSACTION,cur_data.ID_ACCT,cur_data.CODE_ACCT" : string.Empty;

            var columns_main = inscolAdd.Split(',', StringSplitOptions.RemoveEmptyEntries)
                       .Select(col => col.Trim())
                       .ToList();
            var columnExpr = columns_main.Select(col =>
            {
                if (col == maincol)
                {
                    return $"{baseAlias}.{maincol}";
                }
                else
                {
                    return typeInd switch
                    {
                        1 => $"max(c.{col}) as {col}",
                        2 => $"max(a.{col}) as {col}",
                        3 => $"max(v.{col}) as {col}",
                        _ => col
                    };
                }
            });
            string main_cols = string.Join(", ", columnExpr);
            var sidecols = ins_side.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(p => p.Trim())
                        .Select(p => p == "cust_side" ? $"{cust_side} as cust_side" :
                                      p == "acct_side" ? $"{acct_side} as acct_side" : string.Empty);

            string main_sides = string.Join(", ", sidecols);
            string groupBY = maincol switch
            {
                "ID_ACCT" when baseAlias == "v" => "v.ID_ACCT",
                "ID_ACCT" => "a.ID_ACCT",

                "ID_CUST" when baseAlias == "v" => "v.ID_CUST",
                "ID_CUST" when baseAlias == "a" => "a.ID_CUST",
                "ID_CUST" => "c.ID_CUST",

                _ => $"{baseAlias}.{maincol}"
            };


            sql_cursor =$"select {inscolAdd}, {main_sides}, {Inds} {dtcol}" + Environment.NewLine
                        + "from( " + Environment.NewLine
                        + $"select {sel_ind}, {main_cols} {dtcolSel}" + Environment.NewLine
                        + $"from {sql_tab}" + Environment.NewLine
                        + $"{sql_join}" + Environment.NewLine
                        + $"where {sql_where}" + Environment.NewLine
                        + $"group by {groupBY}" + Environment.NewLine
                        + $") where {filt}" + Environment.NewLine;

            sql_insert = $"INSERT INTO {ins_tab_name} (id_obj,code_obj,type_obj {dtcol},number_value," + Environment.NewLine
                       + $"type_value,{inscolAdd},{ins_side})" + Environment.NewLine
                       + $"VALUES({indID},'{indCode}','INDICATOR' {dtcolIns},1,'BOOL'," + Environment.NewLine
                       + $"{inscolAddVal},{sideVal});" + Environment.NewLine;

            sql_param = "f_val varchar2(1);";
            return (sql_insert, sql_cursor, sql_param);
        }
        public async Task<(string sqlInsert, string sqlCursor, string sqlParam)> GetIndCalcSql(string add_cur, string mtd_data, string Inds, List<string> ind_list, int typeInd, int indID, string indCode, string filt, int cust_side, int acct_side)

        {
            var t_res = new Dictionary<string, (bool is_res, string mess)>();

            var TValues = new Dictionary<string, (string SQLTab, string SQLAlias)>();
            List<string> sql_cases = new List<string>();


            string result = string.Empty;

            string sql_cursor = string.Empty;
            string sql_insert = string.Empty;
            string sql_param = string.Empty;
            string ins_tab_name = string.Empty;
            string dtcol = string.Empty;
            string dtcolSel = string.Empty;
            string dtcolIns = string.Empty;
            string inscolAdd = string.Empty;
            string inscolAddVal = string.Empty;
            string ins_side = string.Empty;
            string sideVal = string.Empty;
            string sql_where = string.Empty;
            string where_val = string.Empty;
            string where_cust = string.Empty;
            string where_acct = string.Empty;
            string sql_tab = string.Empty;
            string sql_join = string.Empty;
            string sel_ind = string.Empty;
            string maincol = string.Empty;
            string mainallias = string.Empty;
            string filterIns = string.Empty;



            string filterFind = string.Join(", ", ind_list.Select(s => $"'{s}'"));

            foreach (string ind in ind_list)
            {
                var tmp_ind = await GetIndInfo(ind);
                if (tmp_ind.Count == 0)
                {
                    t_res[ind] = (true, $"{ind} не найден в справочнике показателей");
                    return (sql_insert, sql_cursor, sql_param);
                }
                if (tmp_ind.TryGetValue(ind, out var tmp_val) && tmp_val.IS_ERR)
                {
                    t_res[ind] = (true, $"{ind}: {tmp_val.ERR_MESS}");
                    return (sql_insert, sql_cursor, sql_param);
                }
                var tmp_data = tmp_val.TypeData;
                var tmp_type = tmp_val.TypeInd;
                string tmp_incol = string.Empty;
                string tmp_res = string.Empty;
                string t_tab = string.Empty;
                string t_tabAS = string.Empty;

                if (!TValues.ContainsKey(ind))
                {
                    t_tab = (tmp_type == 1) ? "AML.MEM_OBJECT_CUST" :
                                (tmp_type == 2) ? "AML.MEM_OBJECT_ACCT" :
                                (tmp_type == 3) ? "AML.MEM_OBJECT_VALUE" : string.Empty;

                    t_tabAS = (tmp_type == 1) ? "c" :
                                  (tmp_type == 2) ? "a" :
                                  (tmp_type == 3) ? "v" : string.Empty;
                    TValues[ind] = (t_tab, t_tabAS);
                }


                if (tmp_data == "DATE")
                {
                    tmp_incol = "date_value";
                }
                else if (tmp_data == "NUMBER" || tmp_data == "BOOL")
                {
                    tmp_incol = "number_value";
                }
                else
                {
                    tmp_incol = "string_value";
                }

                string tmp_str = $"MAX(CASE WHEN {t_tabAS}.code_obj = '{ind}' THEN {t_tabAS}.{tmp_incol} END)";

                if (tmp_data == "NUMBER" || tmp_data == "BOOL")
                {
                    tmp_str = $"NVL({tmp_str}, 0)";
                }
                sql_cases.Add($"{tmp_str} AS {ind}");
            }
            where_val = string.Join(",", TValues.Where(x => x.Value.SQLTab == "AML.MEM_OBJECT_VALUE")
                                                  .Select(x => $"'{x.Key}'")
                                                  .Distinct());
            where_cust = string.Join(",", TValues.Where(x => x.Value.SQLTab == "AML.MEM_OBJECT_CUST")
                                                  .Select(x => $"'{x.Key}'")
                                                  .Distinct());
            where_acct = string.Join(",", TValues.Where(x => x.Value.SQLTab == "AML.MEM_OBJECT_ACCT")
                                                  .Select(x => $"'{x.Key}'")
                                                  .Distinct());
            if (!string.IsNullOrEmpty(where_val))
            {
                if (!string.IsNullOrEmpty(sql_where)) { sql_where += " AND "; }
                sql_where += $"v.code_obj in ({where_val})";
            }
            if (!string.IsNullOrEmpty(where_cust))
            {
                if (!string.IsNullOrEmpty(sql_where)) { sql_where += " AND "; }
                sql_where += $"c.code_obj in ({where_cust})";
            }

            if (!string.IsNullOrEmpty(where_acct))
            {
                if (!string.IsNullOrEmpty(sql_where)) { sql_where += " AND "; }
                sql_where += $"a.code_obj in ({where_acct})";
            }

            var firstVal = TValues.FirstOrDefault(x => x.Value.SQLAlias == "v");

            if (firstVal.Equals(default(KeyValuePair<string, (string, string)>)))
                firstVal = TValues.FirstOrDefault(x => x.Value.SQLAlias == "a");

            if (firstVal.Equals(default(KeyValuePair<string, (string, string)>)))
                firstVal = TValues.FirstOrDefault(x => x.Value.SQLAlias == "c");

            sql_tab = firstVal.Equals(default(KeyValuePair<string, (string, string)>)) ? "" :
                      $"{firstVal.Value.SQLTab} {firstVal.Value.SQLAlias}";

            var baseAlias = firstVal.Value.SQLAlias;

            var sqlJoinList = TValues
                                    .Where(x => x.Value.SQLAlias != baseAlias)
                                    .Select(x =>
                                    {
                                        var alias = x.Value.SQLAlias;
                                        var table = x.Value.SQLTab;
                                        return alias switch
                                        {
                                            "c" => baseAlias switch
                                            {
                                                "v" => $"left join {table} {alias} on {alias}.id_cust = {baseAlias}.id_cust",
                                                "a" => $"left join {table} {alias} on {alias}.id_cust = {baseAlias}.id_cust",
                                                _ => null
                                            },
                                            "a" => baseAlias switch
                                            {
                                                "v" => $"left join {table} {alias} on {alias}.id_acct = {baseAlias}.id_acct",
                                                _ => null
                                            },
                                            _ => null
                                        };
                                    })
                                    .Where(x => x != null)
                                    .Distinct();
            sql_join = string.Join(Environment.NewLine, sqlJoinList);

            dtcol = (typeInd == 3) ? ", DT" : string.Empty;
            dtcolSel = (typeInd == 3) ? ", max(v.DT) as DT" : string.Empty;
            dtcolIns = (typeInd == 3) ? ", cur_data.DT" : string.Empty;
            sel_ind = string.Join(", ", sql_cases);

            maincol = (typeInd == 1) ? "ID_CUST" :
                      (typeInd == 2) ? "ID_ACCT" :
                      (typeInd == 3) ? "ID_TRANSACTION" : string.Empty;

            mainallias = (typeInd == 1) ? "ID_CUST" :
                         (typeInd == 2) ? "ID_ACCT" :
                         (typeInd == 3) ? "ID_TRANSACTION" : string.Empty;

            filterIns = $"AND v.{maincol} = cur_data.{mainallias}";
            ins_side = (typeInd == 1) ? "cust_side" :
                       (typeInd == 2) ? "cust_side,acct_side" :
                       (typeInd == 3) ? "cust_side,acct_side" : string.Empty;
            sideVal = (typeInd == 1) ? "cur_data.cust_side" :
                       (typeInd == 2) ? "cur_data.cust_side,cur_data.acct_side" :
                       (typeInd == 3) ? "cur_data.cust_side,cur_data.acct_side" : string.Empty;


            ins_tab_name = (typeInd == 1) ? "AML.MEM_OBJECT_CUST" :
                           (typeInd == 2) ? "AML.MEM_OBJECT_ACCT" :
                           (typeInd == 3) ? "AML.MEM_OBJECT_VALUE" : string.Empty;

            inscolAdd = (typeInd == 1) ? "ID_CUST,CODE_CUST" :
                        (typeInd == 2) ? "ID_ACCT, CODE_ACCT,ID_CUST,CODE_CUST" :
                        (typeInd == 3) ? "ID_CUST,CODE_CUST,ID_TRANSACTION,CODE_TRANSACTION,ID_ACCT,CODE_ACCT" : string.Empty;

            inscolAddVal = (typeInd == 1) ? "cur_data.ID_CUST,cur_data.CODE_CUST" :
                        (typeInd == 2) ? "cur_data.ID_ACCT, cur_data.CODE_ACCT,cur_data.ID_CUST,cur_data.CODE_CUST" :
                        (typeInd == 3) ? "cur_data.ID_CUST,cur_data.CODE_CUST,cur_data.ID_TRANSACTION,cur_data.CODE_TRANSACTION,cur_data.ID_ACCT,cur_data.CODE_ACCT" : string.Empty;

            var columns_main = inscolAdd.Split(',', StringSplitOptions.RemoveEmptyEntries)
                       .Select(col => col.Trim())
                       .ToList();
            var columnExpr = columns_main.Select(col =>
            {
                if (col == maincol)
                {
                    return $"{baseAlias}.{maincol}";
                }
                else
                {
                    return  $"max({baseAlias}.{col}) as {col}";
                    
                }
            });
            string main_cols = string.Join(", ", columnExpr);
            var sidecols = ins_side.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(p => p.Trim())
                        .Select(p => p == "cust_side" ? $"{cust_side} as cust_side" :
                                      p == "acct_side" ? $"{acct_side} as acct_side" : string.Empty);

            string main_sides = string.Join(", ", sidecols);
            string groupBY = maincol switch
            {
                "ID_ACCT" when baseAlias == "v" => "v.ID_ACCT",
                "ID_ACCT" => "a.ID_ACCT",

                "ID_CUST" when baseAlias == "v" => "v.ID_CUST",
                "ID_CUST" when baseAlias == "a" => "a.ID_CUST",
                "ID_CUST" => "c.ID_CUST",

                _ => $"{baseAlias}.{maincol}"
            };
            string inscol = string.Empty;


            (inscol, sql_param) = mtd_data switch
            {
                "DATE" => ("date_value", "f_val date;"),
                "NUMBER" => ("number_value", "f_val number;"),
                _ => ("string_value", "f_val varchar2(4000);")
            };


            sql_cursor = $"select {inscolAdd} {add_cur}, {main_sides}, {Inds} {dtcol}" + Environment.NewLine
                        + "from( " + Environment.NewLine
                        + $"select {sel_ind}, {main_cols} {dtcolSel}" + Environment.NewLine
                        + $"from {sql_tab}" + Environment.NewLine
                        + $"{sql_join}" + Environment.NewLine
                        + $"where {sql_where}" + Environment.NewLine
                        + $"group by {groupBY}" + Environment.NewLine
                        + $") where {filt}" + Environment.NewLine;

            sql_insert = $"INSERT INTO {ins_tab_name} (id_obj,code_obj,type_obj {dtcol},{inscol}," + Environment.NewLine
                       + $"type_value,{inscolAdd},{ins_side})" + Environment.NewLine
                       + $"VALUES({indID},'{indCode}','INDICATOR' {dtcolIns},f_val,'{mtd_data}'," + Environment.NewLine
                       + $"{inscolAddVal},{sideVal});" + Environment.NewLine;

            return (sql_insert, sql_cursor, sql_param);
        }
        public static string GetDelSql(string code, int typeC)
        {
            string sql_del = string.Empty;
            string tab_name = string.Empty;
            if (typeC >= 1 && typeC <= 3)
            {
                tab_name = (typeC == 1) ? "AML.MEM_OBJECT_CUST" :
                           (typeC == 2) ? "AML.MEM_OBJECT_ACCT" :
                           (typeC == 3) ? "AML.MEM_OBJECT_VALUE" : string.Empty;

                sql_del = " Declare" + Environment.NewLine
                         + "  v_partition_name varchar2(50):= null;" + Environment.NewLine
                         + " Begin" + Environment.NewLine
                         + "  SELECT o.SUBOBJECT_NAME into v_partition_name" + Environment.NewLine
                         + "    FROM all_objects o" + Environment.NewLine
                         + "   WHERE o.data_object_id = (" + Environment.NewLine
                         + "       						  SELECT dbms_rowid.rowid_object(rid)" + Environment.NewLine
                         + "         						FROM ( SELECT max(rowid) AS rid" + Environment.NewLine
                         + $"                 						 FROM {tab_name} " + Environment.NewLine
                         + $"                					    WHERE CODE_OBJ = '{code}' " + Environment.NewLine
                         + "                  						  AND rownum = 1 )) " + Environment.NewLine
                         + "     AND object_type = 'TABLE PARTITION';" + Environment.NewLine
                         + $"  execute immediate 'ALTER TABLE {tab_name} drop PARTITION ' || v_partition_name; " + Environment.NewLine
                         + "  EXCEPTION" + Environment.NewLine
                         + "  When no_data_found then  null;" + Environment.NewLine
                         + " End;" + Environment.NewLine;

            }
            return sql_del;
        }
        //получаем инфо об объекте СОП
        public async Task<Dictionary<string, (string Code, long? ID, string TypeObject, bool IS_ERR, string ERR_MESS)>> GetObjInfo(string code)
        {
            string dbnameAML = await _repService.GetConnectionStringAsync("AML");
            var result = new Dictionary<string, (string Code, long? ID, string TypeObject, bool IS_ERR, string ERR_MESS)>();
            string sql_query = $@"select ID,CODE,TYPE_OBJECT from aml.cfg_object where upper(CODE) = upper('{code}')";

            dbService = new DBService(dbnameAML);

            //проверяем тип объекта
            var (res, errormes) = await dbService.GetDataSimple(sql_query);
            if (!string.IsNullOrEmpty(errormes))
            {
                result[code] = (code, null, null, true, errormes);
                return result;
            }
            else
            {
                foreach (var item in res)
                {
                    var ID = item.ContainsKey("ID") && item["ID"] != null ? Convert.ToInt64(item["ID"]) : (long?)null;
                    var Code = item.ContainsKey("CODE") ? item["CODE"]?.ToString() : null;
                    var TypeObj = item.ContainsKey("TYPE_OBJECT") ? item["TYPE_OBJECT"]?.ToString() : null;

                    if (Code != null && TypeObj != null)
                    {
                        result[code] = (code, ID, TypeObj, false, errormes);
                    }
                    else
                    {
                        result[code] = (code, null, null,true, "Нет данных");
                    }
                }
                return result;
            }
        }
        public async Task<Dictionary<string, (string Code, long? ID, int? TypeInd, string TypeData, string ALG, string ALGJson,
                int? CustSide, int? AcctSide, string AttrList, string IndList, long? IDMethod, string CodeMethod, bool IS_ERR, string ERR_MESS)>> GetIndInfo(string code)
        {
            string dbnameAML = await _repService.GetConnectionStringAsync("AML");
            var result = new Dictionary<string, (string Code, long? ID, int? TypeInd, string TypeData, string ALG, string ALGJson,
                int? CustSide, int? AcctSide, string AttrList, string IndList, long? IDMethod, string CodeMethod, bool IS_ERR, string ERR_MESS)>();
            
            string sql_query = $@"select code, id, type_ind, type_data,alg,alg_json, nvl(cust_side,2) as cust_side, nvl(acct_side,2) as acct_side, attr_list, ind_list, id_method, code_method  
                                   from aml.cfg_indicator 
                                  where upper(code) = upper('{code}')";

            dbService = new DBService(dbnameAML);
            var (items, errorMessage) = await dbService.GetDataSimple(sql_query);
            if (!string.IsNullOrEmpty(errorMessage))
            {
                result[code] = (null, null, null, null, null, null, null,null
                                , null, null, null, null, true, errorMessage);
                return result;
            }
            else
            {
                foreach (var item in items)
                {
                    try
                    {

                        var Code = item.ContainsKey("CODE") ? item["CODE"]?.ToString() : null;
                        var AttrList = item.ContainsKey("ATTR_LIST") ? item["ATTR_LIST"]?.ToString() : null;
                        var IndList = item.ContainsKey("IND_LIST") ? item["IND_LIST"]?.ToString() : null;
                        var CodeMethod = item.ContainsKey("CODE_METHOD") ? item["CODE_METHOD"]?.ToString() : null;
                        var TypeData = item.ContainsKey("TYPE_DATA") ? item["TYPE_DATA"]?.ToString() : null;
                        var ALG = item.ContainsKey("ALG") ? item["ALG"]?.ToString() : null;
                        var ALGJson = item.ContainsKey("ALG_JSON") ? item["ALG_JSON"]?.ToString() : null;
                        var CustSide = item.ContainsKey("CUST_SIDE") && item["CUST_SIDE"] != null ? Convert.ToInt32(item["CUST_SIDE"]) : (int?)null;
                        var AcctSide = item.ContainsKey("ACCT_SIDE") && item["ACCT_SIDE"] != null ? Convert.ToInt32(item["ACCT_SIDE"]) : (int?)null;
                        var ID = item.ContainsKey("ID") && item["ID"] != null ? Convert.ToInt64(item["ID"]) : (long?)null;
                        var TypeInd = item.ContainsKey("TYPE_IND") && item["TYPE_IND"] != null ? Convert.ToInt32(item["TYPE_IND"]) : (int?)null;
                        var IDMethod = item.ContainsKey("ID_METHOD") && item["ID_METHOD"] != DBNull.Value && item["ID_METHOD"] != null ? Convert.ToInt64(item["ID_METHOD"]) : (long?)null;
                        if (Code != null && ID != null)
                        {
                            result[code] = (Code, ID, TypeInd, TypeData, ALG, ALGJson, CustSide
                                    , AcctSide, AttrList, IndList, IDMethod, CodeMethod, false, null);
                        }
                        else
                        {
                            result[code] = (null, null, null, null, null, null, null, null
                                        , null, null, null, null, true, "Прочие ошибки");
                        }
                    }
                    catch (Exception ex)
                    {
                        result[code] = (null, null, null, null, null, null, null, null
                                        , null, null, null, null, true, ex.Message);
                    }

                    
                    
                }
            }
            return result;
        }

        public async Task<Dictionary<long, (string Code, long? ID, int? TypeInd, string TypeData, string ALG, string ALGJson,
                int? CustSide, int? AcctSide, string AttrList, string IndList, long? IDMethod, string CodeMethod, bool IS_ERR, string ERR_MESS)>> GetIndInfoByID(long idind)
        {
            string dbnameAML = await _repService.GetConnectionStringAsync("AML");
            var result = new Dictionary<long, (string Code, long? ID, int? TypeInd, string TypeData, string ALG, string ALGJson,
                int? CustSide, int? AcctSide, string AttrList, string IndList, long? IDMethod, string CodeMethod, bool IS_ERR, string ERR_MESS)>();

            string sql_query = $@"select code, id, type_ind, type_data,alg,alg_json, cust_side, acct_side, attr_list, ind_list, id_method, code_method  
                                   from aml.cfg_indicator 
                                  where id = {idind}";

            dbService = new DBService(dbnameAML);
            var (items, errorMessage) = await dbService.GetDataSimple(sql_query);
            if (!string.IsNullOrEmpty(errorMessage))
            {
                result[idind] = (null, null, null, null, null, null, null, null
                                , null, null, null, null, true, errorMessage);
                return result;
            }
            else
            {
                foreach (var item in items)
                {
                    try
                    {

                        var Code = item.ContainsKey("CODE") ? item["CODE"]?.ToString() : null;
                        var AttrList = item.ContainsKey("ATTR_LIST") ? item["ATTR_LIST"]?.ToString() : null;
                        var IndList = item.ContainsKey("IND_LIST") ? item["IND_LIST"]?.ToString() : null;
                        var CodeMethod = item.ContainsKey("CODE_METHOD") ? item["CODE_METHOD"]?.ToString() : null;
                        var TypeData = item.ContainsKey("TYPE_DATA") ? item["TYPE_DATA"]?.ToString() : null;
                        var ALG = item.ContainsKey("ALG") ? item["ALG"]?.ToString() : null;
                        var ALGJson = item.ContainsKey("ALG_JSON") ? item["ALG_JSON"]?.ToString() : null;
                        var CustSide = item.ContainsKey("CUST_SIDE") && item["CUST_SIDE"] != null ? Convert.ToInt32(item["CUST_SIDE"]) : (int?)null;
                        var AcctSide = item.ContainsKey("ACCT_SIDE") && item["ACCT_SIDE"] != null ? Convert.ToInt32(item["ACCT_SIDE"]) : (int?)null;
                        var ID = item.ContainsKey("ID") && item["ID"] != null ? Convert.ToInt64(item["ID"]) : (long?)null;
                        var TypeInd = item.ContainsKey("TYPE_IND") && item["TYPE_IND"] != null ? Convert.ToInt32(item["TYPE_IND"]) : (int?)null;
                        var IDMethod = item.ContainsKey("ID_METHOD") && item["ID_METHOD"] != DBNull.Value && item["ID_METHOD"] != null ? Convert.ToInt64(item["ID_METHOD"]) : (long?)null;
                        if (Code != null && ID != null)
                        {
                            result[idind] = (Code, ID, TypeInd, TypeData, ALG, ALGJson, CustSide
                                    , AcctSide, AttrList, IndList, IDMethod, CodeMethod, false, null);
                        }
                        else
                        {
                            result[idind] = (null, null, null, null, null, null, null, null
                                        , null, null, null, null, true, "Прочие ошибки");
                        }
                    }
                    catch (Exception ex)
                    {
                        result[idind] = (null, null, null, null, null, null, null, null
                                        , null, null, null, null, true, ex.Message);
                    }



                }
            }
            return result;
        }


        public async Task<Dictionary<string, (long? ID, string Code, string TypeData
                                                , string CustomData, string CustomParam, string CustomFilter
                                                , long? IdInd, string CodeInd, long? id_ind_filt, string TypeMethod
                                                , string OutData, string MainTab, string AddTab, string FilterData, string ParamData
                                                , string BaseSQL, bool IS_ERR, string ERR_MESS)>> 
            GetMthdInfo(string code)
        {
            string dbnameAML = await _repService.GetConnectionStringAsync("AML");
            
            var result = new Dictionary<string, (long? ID, string Code, string TypeData
                                                ,string CustomData, string CustomParam, string CustomFilter
                                                , long? IdInd, string CodeInd, long? id_ind_filt, string TypeMethod
                                                ,string OutData, string MainTab, string AddTab, string FilterData, string ParamData
                                                ,string BaseSQL
                                                ,bool IS_ERR, string ERR_MESS)>();

            string sql_query = $@"select m.id ID, m.code CODE,m.tp_data as TYPEDATA
                                 ,m.type_data as CUSTOM_ODATA, m.param_data CUSTOM_PARAM, m.filter_data as CUSTOM_FILTER
                                 ,m.id_ind, m.code_ind, m.id_ind_filtr
                                 ,i.type_method, i.select_tab OUT_DATA
                                 ,i.main_tab as MAIN_TAB, i.join_tabs as ADD_TAB, i.filter_tabs as FILTER_DATA, i.params as PARAM_DATA
                                 ,i.base_sql as SQLB
                            from aml.cfg_method m
                            join aml.cfg_method_meta i on i.id = m.id_method
                           where upper(m.code) = upper('{code}')";
            dbService = new DBService(dbnameAML);
            
            var (items, errorMessage) = await dbService.GetDataSimple(sql_query);
            if (!string.IsNullOrEmpty(errorMessage))
            {
                result[code] = (null, null, null, null, null, null, null, null
                                , null, null, null, null,null, null, null,null, true, errorMessage);
                return result;
            }
            else
            {
                foreach (var item in items)
                {
                    var ID = item.ContainsKey("ID") && item["ID"] != null ? Convert.ToInt64(item["ID"]) : (long?)null;
                    var Code = item.ContainsKey("CODE") ? item["CODE"]?.ToString() : null;
                    var TypeData = item.ContainsKey("TYPEDATA") ? item["TYPEDATA"]?.ToString() : null;
                    var CustomData = item.ContainsKey("CUSTOM_ODATA") ? item["CUSTOM_ODATA"]?.ToString() : null;
                    var CustomParam = item.ContainsKey("CUSTOM_PARAM") ? item["CUSTOM_PARAM"]?.ToString() : null;
                    var CustomFilter = item.ContainsKey("CUSTOM_FILTER") ? item["CUSTOM_FILTER"]?.ToString() : null;
                    var IdIdn = item.ContainsKey("ID_IND") && item["ID_IND"] != DBNull.Value && item["ID_IND"] != null? Convert.ToInt64(item["ID_IND"]): (long?)null;
                    var CodeInd = item.ContainsKey("CODE_IND") ? item["CODE_IND"]?.ToString() : null;
                    var TypeMethod = item.ContainsKey("TYPE_METHOD") ? item["TYPE_METHOD"]?.ToString() : null;
                    var OutData = item.ContainsKey("OUT_DATA") ? item["OUT_DATA"]?.ToString() : null;
                    var MainTab = item.ContainsKey("MAIN_TAB") ? item["MAIN_TAB"]?.ToString() : null;
                    var AddTab = item.ContainsKey("ADD_TAB") ? item["ADD_TAB"]?.ToString() : null;
                    var FilterData = item.ContainsKey("FILTER_DATA") ? item["FILTER_DATA"]?.ToString() : null;
                    var ParamData = item.ContainsKey("PARAM_DATA") ? item["PARAM_DATA"]?.ToString() : null;
                    var SQLB = item.ContainsKey("SQLB") ? item["SQLB"]?.ToString() : null;
                    var IdIndFilt = item.ContainsKey("ID_IND_FILTR") && item["ID_IND_FILTR"] != DBNull.Value && item["ID_IND_FILTR"] != null ? Convert.ToInt64(item["ID_IND_FILTR"]) : (long?)null;


                    if (Code != null && ID != null)
                    {
                        result[code] = (ID, Code, TypeData, CustomData, CustomParam, CustomFilter, IdIdn
                                , CodeInd, IdIndFilt, TypeMethod, OutData, MainTab, AddTab, FilterData, ParamData, SQLB, false, null);
                    }
                    else
                    {
                        result[code] = (null, null, null, null, null, null, null, null
                                , null, null, null, null, null, null, null, null, true, "Прочие ошибки");
                    }
                }
            }
            return result;
        }
        public async Task<Dictionary<string, (string Code, string AliasTab, string SQLTab, string SQLTablColumn
                                                         , string AliasTabLnk, string SQLTabLnk, string SQLTablColumnLmk
                                                         , bool IS_ERR, string ERR_MESS)>> 
            getAttrData(string code)
        {
            string dbnameAML = await _repService.GetConnectionStringAsync("AML");
            var result = new Dictionary<string, (string Code, string AliasTab, string SQLTab, string SQLTablColumn
                                                         , string AliasTabLnk, string SQLTabLnk, string SQLTablColumnLmk
                                                         , bool IS_ERR, string ERR_MESS)>();
            string sql_query = $@"select CODE, 
                                    ALIAS_TABLE, SQL_TABLE, SQL_COLUMN, 
                                    ALIAS_LINK_TABLE, SQL_LINK_TABLE,SQL_LINK_COLUMN 
                                   from aml.cfg_attribute 
                                  where upper(CODE) = upper('{code}')";
            dbService = new DBService(dbnameAML);

            var (items, errorMessage) = await dbService.GetDataSimple(sql_query);
            if (!string.IsNullOrEmpty(errorMessage))
            {
                result[code] = (null, null, null, null, null, null, null, true, errorMessage);
                return result;
            }
            else
            {
                foreach (var item in items)
                {
                    var Code = item.ContainsKey("CODE") ? item["CODE"]?.ToString() : null;
                    var AliasTab = item.ContainsKey("ALIAS_TABLE") ? item["ALIAS_TABLE"]?.ToString() : null;
                    var SQLTab = item.ContainsKey("SQL_TABLE") ? item["SQL_TABLE"]?.ToString() : null;
                    var SQLTablColumn = item.ContainsKey("SQL_COLUMN") ? item["SQL_COLUMN"]?.ToString() : null;
                    var AliasTabLnk = item.ContainsKey("ALIAS_LINK_TABLE") ? item["ALIAS_LINK_TABLE"]?.ToString() : null;
                    var SQLTabLnk = item.ContainsKey("SQL_LINK_TABLE") ? item["SQL_LINK_TABLE"]?.ToString() : null;
                    var SQLTablColumnLmk = item.ContainsKey("SQL_LINK_COLUMN") ? item["SQL_LINK_COLUMN"]?.ToString() : null;

                    if (Code != null && SQLTab != null)
                    {
                        result[code] = (Code, AliasTab, SQLTab, SQLTablColumn, AliasTabLnk, SQLTabLnk, SQLTablColumnLmk
                                , false, null);
                    }
                    else
                    {
                        result[code] = (null, null, null, null, null, null, null, true, "Прочие ошибки");
                    }
                }
            }
            return result;
        }
        public async Task<Dictionary<string, (string Code, string TypeVal, long? NumVal, string StrVal
                                                         , string DateVal
                                                         , bool IS_ERR, string ERR_MESS)>>
            GetMtdGlbData(string code)
        {
            string dbnameAML = await _repService.GetConnectionStringAsync("AML");
            var result = new Dictionary<string, (string Code, string TypeVal, long? NumVal, string StrVal
                                                         , string DateVal
                                                         , bool IS_ERR, string ERR_MESS)>();
            string sql_query = $@"select Code, Type_value, number_value, string_value,date_value 
                                    from aml.mem_global_value where upper(code) = upper('{code}')";
            dbService = new DBService(dbnameAML);

            var (items, errorMessage) = await dbService.GetDataSimple(sql_query);
            if (!string.IsNullOrEmpty(errorMessage))
            {
                result[code] = (null, null, null, null, null, true, errorMessage);
                return result;
            }
            else
            {
                foreach (var item in items)
                {
                    var Code = item.ContainsKey("CODE") ? item["CODE"]?.ToString() : null;
                    var TypeVal = item.ContainsKey("TYPE_VALUE") ? item["TYPE_VALUE"]?.ToString() : null;
                    var NumVal = item.ContainsKey("NUMBER_VALUE") && item["NUMBER_VALUE"] != DBNull.Value && item["NUMBER_VALUE"] != null ? Convert.ToInt64(item["NUMBER_VALUE"]) : (long?)null;
                    var StrVal = item.ContainsKey("STRING_VALUE") ? item["STRING_VALUE"]?.ToString() : null;
                    var DateVal = item.ContainsKey("DATE_VALUE") ? item["DATE_VALUE"]?.ToString() : null;

                    if (Code != null)
                    {
                        result[code] = (Code, TypeVal, NumVal, StrVal, DateVal
                                , false, null);
                    }
                    else
                    {
                        result[code] = (null, null, null, null, null, true, "Прочие ошибки");
                    }
                }
            }
            return result;
        }
        public static string BuildSQLGlobalMethod(AML_Models.MainTab mainTable,
                                            List<AML_Models.SelectField> selectFields,
                                            List<AML_Models.Join> joins,
                                            List<AML_Models.Filter> filters,
                                            List<AML_Models.Params> parameters)
        {
            if (mainTable == null || string.IsNullOrEmpty(mainTable.Tab))
            {
                throw new Exception("Основная таблица не указана.");
            }

            Dictionary<string, string> paramValues = parameters
                .Where(p => p.TypeVal == "V")
                .ToDictionary(p => $"{{_{p.Parameter}_}}", p => p.Value);

            foreach (var field in selectFields)
            {
                foreach (var param in paramValues)
                {
                    field.Field = field.Field.Replace(param.Key, param.Value);
                }
            }

            foreach (var filter in filters)
            {
                for (int i = 0; i < filter.Values.Count; i++)
                {
                    string valueStr = filter.Values[i].ToString();
                    if (paramValues.ContainsKey(valueStr))
                    {
                        filter.Values[i] = paramValues[valueStr];
                    }
                }
            }

            string selectClause = selectFields.Count > 0
                ? string.Join(", ", selectFields.Select(c => $"{c.Field} AS FVAL"))
                : "*";

            string fromClause = $"{mainTable.Tab} {mainTable.Alias}";

            string joinClause = joins.Count > 0
                ? string.Join(" ", joins.Select(j => $"{j.JoinType} JOIN {j.Tab} {j.Alias} ON {j.Condition}"))
                : "";

            string whereClause = filters.Count > 0
                ? "WHERE " + string.Join(" AND ", filters.Select(f =>
                    f.Operator.ToUpper() == "BETWEEN"
                        ? $"{f.Field} BETWEEN {f.Values[0]} AND {f.Values[1]}"
                        : $"{f.Field} {f.Operator} {string.Join(", ", f.Values)}"))
                : "";

            string sqlQuery = $"SELECT {selectClause} FROM {fromClause} {joinClause} {whereClause}";
            return sqlQuery;
        }
        public static string GetMthdIndSql(AML_Models.MainTab mainTable,
                                            List<AML_Models.SelectField> selectFields,
                                            List<AML_Models.Join> joins,
                                            List<AML_Models.Filter> filters,
                                            List<AML_Models.Params> parameters,
                                            string indFiltr,
                                            string indFiltrLst,
                                            List<AML_Models.AML_ATTR> AttrSPRFILTR, 
                                            long? indFilterTP, 
                                            string indFilterTPD
                                            )
        {
            string sql_join_ind = string.Empty;
            
            if (mainTable == null || string.IsNullOrEmpty(mainTable.Tab))
            {
                throw new Exception("Основная таблица не указана.");
            }
            var paramDict = parameters.ToDictionary(p => $"{{_{p.Parameter}_}}", p => new { p.Value, p.TypeVal });

            Dictionary<string, string> paramValues = parameters
                .Where(p => (p.TypeVal == "V" || p.TypeVal == "I" || p.TypeVal == "F" || p.TypeVal == "M"))
                .ToDictionary(p => $"{{_{p.Parameter}_}}", p => p.Value);

            foreach (var field in selectFields)
            {
                foreach (var param in paramValues)
                {
                    string? valueStr = param.Key;

                    if (!string.IsNullOrEmpty(valueStr) && paramDict.ContainsKey(valueStr))
                    {
                        var paramData = paramDict[valueStr];

                        string paramValue = (paramData.TypeVal == "I" || paramData.TypeVal == "F")
                            ? $"cur_data.{paramData.Value}"
                            : paramData.Value;

                        field.Field = field.Field.Replace(param.Key, paramValue);
                    }
                }
            }
            

            foreach (var filter in filters)
            {
                for (int i = 0; i < filter.Values.Count; i++)
                {
                    string? valueStr = filter.Values[i]?.ToString();

                    if (!string.IsNullOrEmpty(valueStr) && paramDict.ContainsKey(valueStr))
                    {
                        var param = paramDict[valueStr];

                        filter.Values[i] = (param.TypeVal == "F" || param.TypeVal == "M") ? $"cur_data.{param.Value}" : param.Value;

                    }
                }
                if (paramDict.ContainsKey(filter.Field))
                {
                    var param = paramDict[filter.Field];
                    filter.Field = (param.TypeVal == "F" || param.TypeVal == "M") ? $"cur_data.{param.Value}" : param.Value;
                }
            }

            foreach (var join in joins)
            {
                foreach (var param in paramValues)
                {
                    string? valueStr = param.Key;

                    if (!string.IsNullOrEmpty(valueStr) && paramDict.ContainsKey(valueStr))
                    {
                        var paramData = paramDict[valueStr];

                        string paramValue = (paramData.TypeVal == "I" || paramData.TypeVal == "F" || paramData.TypeVal == "M")
                            ? $"cur_data.{paramData.Value}"
                            : paramData.Value;

                        join.Tab = join.Tab.Replace(param.Key, paramValue);
                        join.Condition = join.Condition.Replace(param.Key, paramValue);
                    }
                }
            }

            string selectClause = selectFields.Count > 0
                ? string.Join(", ", selectFields.Select(c => $"{c.Field} into F_VAL"))
                : "*";

            string fromClause = $"{mainTable.Tab} {mainTable.Alias}";

            string joinClause = joins.Count > 0
                ? string.Join(" ", joins.Select(j => $"{j.JoinType} JOIN {j.Tab} {j.Alias} ON {j.Condition}"))
                : "";
            if (AttrSPRFILTR.Count > 0)
            {
                var uniqueValues = AttrSPRFILTR
                    .Where(v => !string.IsNullOrEmpty(v.SQLTab) && !string.IsNullOrEmpty(v.SQLTabLnk))
                    .Where(v => !joins.Any(j => j.Tab == v.SQLTabLnk && j.Alias == v.AliasTab))
                    .Select(v => new { v.AliasTab, v.SQLTab, v.AliasTabLnk, v.SQLTabLnk, v.SQLTablColumnLmk })
                    .Distinct()
                    .ToList();
                sql_join_ind = string.Join(Environment.NewLine, uniqueValues
                                                .Where(v => !string.IsNullOrEmpty(v.SQLTab) && !string.IsNullOrEmpty(v.SQLTabLnk))
                                                .Select(val => $"left join {val.SQLTab} {val.AliasTab} on {val.AliasTab}.id = {val.AliasTabLnk}.{val.SQLTablColumnLmk}"));

                if (!string.IsNullOrEmpty(indFiltr))
                {
                    indFiltr = "AND " + indFiltr;
                }
                foreach (var attr in AttrSPRFILTR)
                {
                    if (!string.IsNullOrEmpty(attr.CodeAttr))
                    { var new_code = $"{attr.AliasTab}.{attr.SQLTablColumn}";
                        indFiltr = indFiltr.Replace(attr.CodeAttr, new_code);
                    }
                }

            }
                string whereClause_old = filters.Count > 0
                ? "WHERE " + string.Join(" AND ", filters.Select(f =>
                    f.Operator.ToUpper() == "BETWEEN"
                        ? $"{f.Field} BETWEEN {f.Values[0]} AND {f.Values[1]}"
                        : $"{f.Field} {f.Operator} {string.Join(", ", f.Values)}"))
                : "";
                string whereClause = filters.Count > 0
                ? "WHERE " + string.Join(" AND ", filters.Select(f =>
                {
                    string op = f.Operator.ToUpper();
                    if (op == "BETWEEN")
                    {
                        return $"{f.Field} BETWEEN {f.Values[0]} AND {f.Values[1]}";
                    }
                    else if (op == "IN")
                    {
                        return $"{f.Field} IN ({string.Join(", ", f.Values)})";
                    }
                    else
                    {
                        return $"{f.Field} {f.Operator} {f.Values[0]}";
                    }
                })): "";
            string sqlQuery = $"SELECT {selectClause} FROM {fromClause} {joinClause} {sql_join_ind} {whereClause} {indFiltr};";
            return sqlQuery;
        }
        public static (string Sql, Dictionary<string, string> ParamList) GetMthdSqlM(AML_Models.MainTab mainTable,
                                    List<AML_Models.SelectField> selectFields,
                                    List<AML_Models.Params> parameters,
                                    string in_dts, string in_dte
                                    )
        {
            if (mainTable == null || string.IsNullOrEmpty(mainTable.Tab))
            {
                throw new Exception("Основная таблица не указана.");
            }
            var paramDict = parameters.ToDictionary(p => $"{{_{p.Parameter}_}}", p => new { p.Value, p.TypeVal });

            Dictionary<string, string> paramValues = parameters
                .Where(p => (p.TypeVal == "W"))
                .ToDictionary(p => $"{{_{p.Parameter}_}}", p => p.Value);

            Dictionary<string, string> paramList = parameters
                .Where(p => p.TypeVal == "W")
                .ToDictionary(p => "Code", p => p.Value);
    
            foreach (var field in selectFields)
            {
                foreach (var param in paramValues)
                {
                    string? valueStr = param.Key;

                    if (!string.IsNullOrEmpty(valueStr) && paramDict.ContainsKey(valueStr))
                    {
                        string pval = string.Empty;
                        var paramData = paramDict[valueStr];

                        if (paramData.TypeVal == "W")
                        {
                            pval = string.Empty;
                            if (paramData.Value == "EWA_DTS")
                            {
                                pval = in_dts;
                            }
                            else if (paramData.Value == "EWA_DTE")
                            {
                                pval = in_dte;
                            }
                            else
                            {
                                pval = paramData.Value;
                            }
                        
                        }
                        

                        field.Field = field.Field.Replace(param.Key, pval);
                    }
                }
            }


            

            string selectClause = selectFields.Count > 0
                ? string.Join(", ", selectFields.Select(c => $"{c.Field} into F_VAL"))
                : "*";

            string fromClause = $"{mainTable.Tab} {mainTable.Alias}";

            string sqlQuery = $"SELECT {selectClause} FROM {fromClause};";
            return (sqlQuery, paramList);
        }
        //парсер метода
        public (int Code, Dictionary<string, object> JsonResult) ParseQuery(string query)
        {
            var result = new QueryStructure
                {
                    SelectFields = new List<AML_Models.SelectField>(),
                    MainTab = new List<AML_Models.MainTab>(),
                    Joins = new List<AML_Models.Join>(),
                    Filters = new List<AML_Models.Filter>(),
                    Parameters = new List<AML_Models.Params>()
                };
            try {
                var selectPattern = @"select\s+(.*?)\s+from";
                var selectMatch = Regex.Match(query, selectPattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);
                if (selectMatch.Success)
                {
                    var selectPart = selectMatch.Groups[1].Value;
                    var fields = selectPart.Split(',');

                    foreach (var field in fields)
                    {
                        var trimmedField = field.Trim();

                        string alias = null;
                        int aliasIndex = trimmedField.ToLower().IndexOf(" as ");
                        if (aliasIndex != -1)
                        {
                            alias = trimmedField.Substring(aliasIndex + 4).Trim();
                            trimmedField = trimmedField.Substring(0, aliasIndex).Trim();
                        }
                        else
                        {
                            var lastSpaceIndex = trimmedField.LastIndexOf(' ');
                            if (lastSpaceIndex != -1)
                            {
                                alias = trimmedField.Substring(lastSpaceIndex + 1).Trim();
                                trimmedField = trimmedField.Substring(0, lastSpaceIndex).Trim();
                            }
                        }

                        result.SelectFields.Add(new AML_Models.SelectField
                        {
                            Field = trimmedField,
                            Alias = alias
                        });
                    }
                }

                var fromPattern = @"from\s+(.*?)\s+(left\s+join|where|$)";
                //var fromPattern = @"from\s+([\w\d_]+(?:\s+[\w\d_]+)?)\s+(left\s+join|where|$)";
                //var fromPattern = @"from\s+((?:[\w\d_]+(?:\.[\w\d_]+)?(?:\s+[\w\d_]+)?)(?:\s+(left|right|inner|outer)\s+join\s+[\w\d_]+\s+on\s+.*?)*)(?=\s+(where|$))";
                var fromMatch = Regex.Match(query, fromPattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);
                if (fromMatch.Success)
                {
                    var fromPart = fromMatch.Groups[1].Value;
                    var tables = fromPart.Split(',');
                    foreach (var table in tables)
                    {
                        var parts = table.Trim().Split(' ');
                        result.MainTab.Add(new MainTab
                        {
                            Tab = parts[0].Trim(),
                            Alias = parts.Length > 1 ? parts[1].Trim() : null
                        });
                    }
                }

                var joinPattern = @"(left|right|full|inner|outer)\s+join\s+(.*?)\s+on\s+(.*?)\s+and";
                var joinMatches = Regex.Matches(query, joinPattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);
                foreach (Match joinMatch in joinMatches)
                {
                    var joinType = joinMatch.Groups[1].Value.ToUpper();
                    var table = joinMatch.Groups[2].Value;
                    var condition = joinMatch.Groups[3].Value;
                    var parts = table.Trim().Split(' ');

                    result.Joins.Add(new Join
                    {
                        Tab = parts[0].Trim(),
                        Alias = parts.Length > 1 ? parts[1].Trim() : null,
                        Condition = condition,
                        JoinType = joinType
                    });
                }

                var wherePattern = @"where\s+(.*)";
                var whereMatch = Regex.Match(query, wherePattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);
                if (whereMatch.Success)
                {
                    var wherePart = whereMatch.Groups[1].Value;
                    var conditions = wherePart.Split(new[] { "and" }, StringSplitOptions.None);
                    foreach (var condition in conditions)
                    {
                        var parts = condition.Trim().Split(new[] { ' ' }, 3);
                        if (parts.Length >= 3)
                        {
                            var filter = new AML_Models.Filter
                            {
                                Field = parts[0].Trim(),
                                Operator = parts[1].Trim()
                            };

                            if (filter.Operator.Equals("BETWEEN", StringComparison.OrdinalIgnoreCase))
                            {
                                var values = parts[2].Trim().Split(new[] { "AND" }, StringSplitOptions.None);
                                foreach (var value in values)
                                {
                                    filter.Values.Add(value.Trim());
                                }
                            }
                            else
                            {
                                filter.Values.Add(parts[2].Trim());
                            }

                            result.Filters.Add(filter);
                        }
                    }
                }

                var parameterPattern = @"\{_(.*?)_\}";
                var parameterMatches = Regex.Matches(query, parameterPattern);
                foreach (Match paramMatch in parameterMatches)
                {
                    var paramName = paramMatch.Groups[1].Value;
                    if (!result.Parameters.Any(p => p.Parameter == paramName))
                    {
                        result.Parameters.Add(new AML_Models.Params
                        {
                            Parameter = paramName
                        });
                    }
                  
                }

                var options = new JsonSerializerOptions { WriteIndented = true };
                string jsonSelectFields = JsonSerializer.Serialize(result.SelectFields, options);
                string jsonMainTab = JsonSerializer.Serialize(result.MainTab, options);
                string jsonJoins = JsonSerializer.Serialize(result.Joins, options);
                string jsonFilters = JsonSerializer.Serialize(result.Filters, options);
                string jsonParameters = JsonSerializer.Serialize(result.Parameters, options);
                var jsonResult = new Dictionary<string, object>
                {
                    { "MTDT_SLCT", jsonSelectFields },
                    { "MTDT_MTAB", jsonMainTab },
                    { "MTDT_JTB", jsonJoins },
                    { "MTDT_FLTR", jsonFilters },
                    { "MTDT_PARAMS", jsonParameters }
                };

                return (0, jsonResult); 
            }
            catch (Exception ex)
            {
                var ErrResult = new Dictionary<string, object>
                {
                    { "ERROR",  ex.Message }
                };
                return (1, ErrResult);
            }


        }

        public async Task<(int status, string err, AML_Models.MethodMetaResult result )> GetMethodMeta(decimal id_p)
        {
            string dbnameAML = await _repService.GetConnectionStringAsync("AML");

            string sqlQuery = $"SELECT filter_tabs, select_tab, params,type_method FROM aml.cfg_method_meta WHERE id = {id_p}";

            dbService = new DBService(dbnameAML);
            var (items, errorMessage) = await dbService.GetDataSimple(sqlQuery);
             
            if (!string.IsNullOrEmpty(errorMessage))
            {
                return (0, "NOT FOUND", null);
            }

            var row = items.First();
            try
            {

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                var ftab = JsonSerializer.Deserialize<List<AML_Models.Filter>>(row["FILTER_TABS"]?.ToString() ?? "[]", options);
                var stab = JsonSerializer.Deserialize<List<AML_Models.SelectField>>(row["SELECT_TAB"]?.ToString() ?? "[]", options);
                var parm = JsonSerializer.Deserialize<List<AML_Models.Params>>(row["PARAMS"]?.ToString() ?? "[]", options);
                var tpmtd = row["TYPE_METHOD"]?.ToString();

                var res = new AML_Models.MethodMetaResult
                {
                    FilterTabs = ftab,
                    SelectTab = stab,
                    Params = parm,
                    TypeMethod = tpmtd
                };

                return (1, null, res);
            }
            catch (Exception ex)
            {
                return (0, ex.Message,null);
            }
            //return (FilterTabs, SelectTab, ParamsTab, TypeMethod);

        }
        public async Task<Dictionary<string, (string Value, string tattr)>> GetAttrAsync()
        {
            var result = new Dictionary<string, (string Value, string tattr)>();
            string dbnameAML = await _repService.GetConnectionStringAsync("AML");
            string base_sql = string.Empty;
            base_sql = "select code as CODE, name as VALUE ,'I' as TATTR, 'IND' as GATTR from aml.cfg_indicator union ALL" +Environment.NewLine
                      +"select CODE as CODE, NAME as VALUE, 'A'  as TATTR, sql_table GATTR from aml.cfg_attribute union ALL" +Environment.NewLine
					  +"select m.CODE as CODE, m.NAME as VALUE, 'F'  as TATTR, 'FUNC' as GATTR from aml.cfg_method m" +Environment.NewLine 
                      +"                                                                       join aml.cfg_method_meta mm on mm.id = m.id_method and upper(mm.type_method) ='GLOBAL' union ALL" +Environment.NewLine
                      +"select CODE, VALUE, 'M' as TATTR, 'MEMO' as GATTR from(" +Environment.NewLine
                      +"      select 'ID_CUST' as CODE, 'Клиент' as VALUE from dual union" +Environment.NewLine
                      +"      select 'ID_ACCT' as CODE, 'Счет' as VALUE from dual union" +Environment.NewLine
                      +"      select 'ID_TRN' as CODE, 'Операция' as VALUE from dual)"/* union" +Environment.NewLine
                      +"select t.code as CoDE, t.name as VALUE, 'W' as TATTR, 'WRFL' as GATTR from WRFL_SETTING_DATA t where type_set = 'PARAM'"*/
                      ;
            DBService dbService = new DBService(dbnameAML);
            var (items, errorMessage) = await dbService.GetDataSimple(base_sql);

            if (!string.IsNullOrEmpty(errorMessage))
            {
                Console.WriteLine($"Error: {errorMessage}");
            }
            else
            {
                foreach (var item in items)
                {
                    var code = item.ContainsKey("CODE") ? item["CODE"]?.ToString() : null;
                    var value = item.ContainsKey("VALUE") ? item["VALUE"]?.ToString() : null;
                    var tattr = item.ContainsKey("TATTR") ? item["TATTR"]?.ToString() : null;

                    if (code != null && value != null && tattr != null)
                    {
                        result[code] = (value, tattr);
                    }
                }
            }
            return result;
        }
        public async Task<Dictionary<string, (int id, string code, string name)>> GetIndDiagram()
        {
            var result = new Dictionary<string, (int id, string code, string name)>();
            string dbnameAML = await _repService.GetConnectionStringAsync("AML");
            string base_sql = string.Empty;
            base_sql = @"select ID as ID, code as CODE, name as NAME from aml.cfg_indicator order by CODE";
            DBService dbService = new DBService(dbnameAML);
            var (items, errorMessage) = await dbService.GetDataSimple(base_sql);

            if (!string.IsNullOrEmpty(errorMessage))
            {
                Console.WriteLine($"Error: {errorMessage}");
            }
            else
            {
                foreach (var item in items)
                {
                    var id = item.ContainsKey("ID") && item["ID"] != null ? Convert.ToInt32(item["ID"]) : 0;
                    var code = item.ContainsKey("CODE") ? item["CODE"]?.ToString() : null;
                    var name = item.ContainsKey("NAME") ? item["NAME"]?.ToString() : null;


                    if (code != null)
                    {
                        result[code] = (id, code, name);
                    }
                }
            }
            return result;
        }

    }

}

