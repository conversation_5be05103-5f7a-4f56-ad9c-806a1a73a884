﻿@attribute [Authorize]
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Ra<PERSON>zen
@using Radzen.Blazor
@using EWA.Services
@using System
@using System.Collections.Generic
@using System.Linq
@using System.Threading.Tasks

<RadzenCard class=" rz-pt-0" Variant="Variant.Text">
    <RadzenStack Orientation="Orientation.Vertical">

        @if (user != null)
        {
            @if (user.UserRoles != null)
            {
                <RadzenDataGrid Data="@user.UserRoles" TItem="Models.SIB_Models.SIB_USERROLES_LNK" ShowPagingSummary="false" AllowPaging="false" AllowFiltering="false" AllowSorting="false">
                    <Columns>
                        <RadzenDataGridColumn Title="Код роли" Property="Roles.CODE"/>
                        <RadzenDataGridColumn Title="Имя роли" Property="Roles.NAME"/>
                    </Columns>
                </RadzenDataGrid>
            }
            @if (user.UserModules != null)
            {
                <RadzenDataGrid Data="@user.UserModules" TItem="Models.SIB_Models.SIB_USERMODULES_LNK" ShowPagingSummary="false" AllowPaging="false" AllowFiltering="false" AllowSorting="false">
                    <Columns>
                        <RadzenDataGridColumn Title="Код модуля" Property="Modules.CODE"/>
                        <RadzenDataGridColumn Title="Имя роли" Property="Modules.NAME" />
                    </Columns>
                </RadzenDataGrid>
            }
        }
    </RadzenStack>
</RadzenCard>

@code {
    [Inject]
    protected DialogService DialogService { get; set; }
    [Inject]
    protected SIBService.SecurityService Security { get; set; }
    [Parameter] public EWA.Models.SIB_Models.SIB_USERS user { get; set; }
}