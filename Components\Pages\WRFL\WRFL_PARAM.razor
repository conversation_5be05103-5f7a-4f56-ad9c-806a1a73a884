﻿@using EWA.Models
@using EWA.Services
@using System.Text.Json
@inject TooltipService tooltipService

<RadzenStack Orientation="Orientation.Horizontal">
    <RadzenDataGrid @ref="grid"
                    EmptyText="Нет записей для отображения"
                    SelectionMode="DataGridSelectionMode.Single"
                    TItem="WFL_Models.WFL_GRID_PARAM"
                    Density="Density.Compact"
                    Data="@objects"
                    RowUpdate="@OnUpdateRow"
                    RowCreate="@OnCreateRow"
                    AllowPaging="true"
                    PageSize="10"
                    ColumnWidth="100px"
                    Style="width:100%; height:420px;"
                    AllowFiltering="true"
                    AllowSorting="true"
                    @bind-Value=@selectedItems
                    EditMode="DataGridEditMode.Single"
                    >
        <HeaderTemplate>
                <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween">
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="2rem">
                        <RadzenColumn>
                        <RadzenButton Icon="Clear" 
                                     Click="ClearData" 
                                  Disabled="@(objects.Count == 0)"
                                   Variant="Variant.Text"
                               ButtonStyle="ButtonStyle.Base"
                                MouseLeave="TooltipService.Close"
                                MouseEnter="@(args => ShowTooltip(args,"очистить параметры", new TooltipOptions(){ Position = TooltipPosition.Top }))" />

                            <RadzenDropDown @bind-Value="selectedParamCode"
                                    Disabled="@(RowToInsert.Count() > 0)"
                                    Data="@ParamList.Keys.ToList()"
                                    Placeholder="Добавить параметр"
                                    Style="width: 300px; margin-bottom: 15px;"
                                    Change="@InsertRow"
                                        MouseLeave="TooltipService.Close"
                                        MouseEnter="@(args => ShowTooltip(args, "Добавить параметр", new TooltipOptions(){ Position = TooltipPosition.Top }))" />

                    </RadzenColumn>
                    </RadzenStack>
                </RadzenStack>
            </HeaderTemplate>
        <Columns>
            <RadzenDataGridColumn Title="Действие" Frozen="true" FrozenPosition="FrozenColumnPosition.Right" MinWidth="50px">
                <Template Context="item">
                    <RadzenButton Icon="edit"
                                  Click="@EditRow"
                                  Disabled="@(selectedItems.Count == 0)"
                                  Variant="Variant.Text"
                                  ButtonStyle="ButtonStyle.Base"
                                  MouseLeave="TooltipService.Close"
                                  MouseEnter="@(args => ShowTooltip(args,"Редактировать параметр", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                    <RadzenButton Icon="delete"
                                  Click="@RemoveRow"
                                  Disabled="@(selectedItems.Count == 0)"
                                  Variant="Variant.Text"
                                  ButtonStyle="ButtonStyle.Base"
                                  MouseLeave="TooltipService.Close"
                                  MouseEnter="@(args => ShowTooltip(args, "Удалить параметр", new TooltipOptions(){ Position = TooltipPosition.Top }))" />
                </Template>
                <EditTemplate Context="item">
                    

                    
                    
                    <RadzenButton Size="ButtonSize.ExtraSmall"
                                  Icon="check"
                                  Click="@(args => grid.UpdateRow(item))" />
                    <RadzenButton Size="ButtonSize.ExtraSmall"
                                  Icon="close"
                                  Click="@((args) => CancelEdit(item))"
                                  Style="margin-left: 5px;" />
                </EditTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="Code" Title="Код параметра" Frozen="true"/>
            <RadzenDataGridColumn Property="Name" Title="Наименование параметра" Frozen="true"/>
            <RadzenDataGridColumn Property="Type" Title="Тип параметра" Frozen="true"/>
            <RadzenDataGridColumn Property="Value" Title="Значение" Frozen="true">
                <EditTemplate Context="param">
                    @if (param.AvailableValues?.Count > 0)
                    {
                        <RadzenDropDown @bind-Value="param.Value"
                                        Data="param.AvailableValues"
                                        Style="width:100%;" />
                    }
                    else
                    {
                        <RadzenTextBox @bind-Value="param.Value" Style="width:100%;" />
                    }
                </EditTemplate>
                <Template Context="param">
                    @param.Value
                </Template>
            </RadzenDataGridColumn>
        </Columns>
    </RadzenDataGrid>

</RadzenStack>
<RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End" Gap="0.5rem" Style="margin-top: 1rem;">
    <RadzenButton ButtonType="ButtonType.Submit" Icon="save" Text="Сохранить" Click="@SaveClick" Variant="Variant.Flat" />
    <RadzenButton ButtonStyle="ButtonStyle.Light" Text="Отмена" Click="@CancelClick" Variant="Variant.Flat" />
</RadzenStack>

@code {
    [Parameter]
    public string _Code { get; set; }
    [Parameter]
    public string _DBname { get; set; }
    [Parameter]
    public Dictionary<string, string> metaALG { get; set; }
    [Parameter]
    public Dictionary<string, object> metaALGData { get; set; }

    public Dictionary<string, WFL_Models.WFL_Param> ParamList { get; set; } = new();

    [Inject]
    protected TooltipService TooltipService { get; set; }
    [Inject]
    protected DialogService DialogService { get; set; }

    RadzenDataGrid<WFL_Models.WFL_GRID_PARAM> grid = new();
    private IList<WFL_Models.WFL_GRID_PARAM> objects = new List<WFL_Models.WFL_GRID_PARAM>();
    private IList<WFL_Models.WFL_GRID_PARAM> selectedItems = new List<WFL_Models.WFL_GRID_PARAM>();
    private List<WFL_Models.WFL_GRID_PARAM> RowToInsert = new List<WFL_Models.WFL_GRID_PARAM>();
    private List<WFL_Models.WFL_GRID_PARAM> RowToUpdate = new List<WFL_Models.WFL_GRID_PARAM>();


    private WFL_Models.WFL_GRID_PARAM selectedRow;
    List<WFL_Models.WFL_GRID_PARAM> _in_param = new List<WFL_Models.WFL_GRID_PARAM>();
    List<WFL_Models.WFL_GRID_PARAM> _out_param = new List<WFL_Models.WFL_GRID_PARAM>();
    private string selectedParamCode;



    Dictionary<string, object> metaALGData_OUT = new Dictionary<string, object>();
    private bool isFirstRender = true;

    void ShowTooltip(ElementReference elementReference, string buttomName, TooltipOptions options = null) => tooltipService.Open(elementReference, buttomName, options);

    void Reset()
    {
        RowToInsert.Clear();
        RowToUpdate.Clear();
    }
    void OnUpdateRow(WFL_Models.WFL_GRID_PARAM row)
    {
        grid.Reload();
        StateHasChanged();
    }
    void OnCreateRow(WFL_Models.WFL_GRID_PARAM row)
    {
        objects.Add(row);
        RowToInsert.Clear();
        grid.Reload();
        StateHasChanged();
    }
    void CancelEdit(WFL_Models.WFL_GRID_PARAM row)
    {
        grid.CancelEditRow(row);
    }
    void ClearData()
    {
        objects.Clear();
        grid.Reload();
        StateHasChanged();
    }
    void InsertRow()
    {
        Reset();

        if (selectedParamCode is string code && ParamList.TryGetValue(code, out var param))
        {
            if (objects.Any(o => o.Code == code))
                return;

            var row = new WFL_Models.WFL_GRID_PARAM
                {
                    Code = code,
                    Name = param.Name,
                    Type = param.DataType,
                    Value = "",
                    AvailableValues = param.Options != null ? param.Options.Keys.ToList() : new List<string>()
                };
            RowToInsert.Add(row);
            grid.InsertRow(row);
        }

        StateHasChanged();
    }

    void EditRow()
    {
        selectedRow = selectedItems[0];
        grid.EditRow(selectedRow);

    }
    private async Task RemoveRow()
    {
        var res = await DialogService.Confirm("Удалить строку?", "Удаление", new ConfirmOptions() { OkButtonText = "Да", CancelButtonText = "Нет" });
        if (res == true)
        {
            selectedRow = selectedItems[0];
            objects.Remove(selectedRow);
            await grid.Reload();
            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        string in_param = string.Empty;
        ParamList = WFLModule.WFLParam;
        try
        {
            foreach (var kvp in metaALGData)
            {
                if (kvp.Key == "WFL_W_PARAM")
                {
                    in_param = kvp.Value.ToString();
                }
            }
            var jsnoptions = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

            if (!string.IsNullOrEmpty(in_param))
            {
                _in_param = JsonSerializer.Deserialize<List<WFL_Models.WFL_GRID_PARAM>>(in_param, jsnoptions);
                foreach (var param in _in_param)
                {
                    if (ParamList.TryGetValue(param.Code, out var wflParam) && wflParam.Options != null)
                    {
                        param.AvailableValues = wflParam.Options.Keys.ToList();
                    }
                }

            }
        }
        catch (Exception ex)
        {
            _in_param.Clear();
        }
        StateHasChanged();
    }
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            isFirstRender = false;
            await LoadDataGrid();
        }
    }
    protected async Task LoadDataGrid()
    {
        if (_in_param.Count > 0)
        {
            foreach (var item in _in_param)
            {
                objects.Add(new WFL_Models.WFL_GRID_PARAM
                    {
                        Code = item.Code,
                        Name = item.Name,
                        Type = item.Type,
                        Value = item.Value,
                        AvailableValues = item.AvailableValues
                    });
            }
            await grid.Reload();
        }
    }
    private void SaveClick()
    {
        foreach (var item in objects)
        {
            
            _out_param.Add(new WFL_Models.WFL_GRID_PARAM
            {
                    Code = item.Code,
                    Name = item.Name,
                    Type = item.Type,
                    Value = item.Value
            });
        }
        
        string out_param = JsonSerializer.Serialize(_out_param, new JsonSerializerOptions { WriteIndented = true });
        metaALGData_OUT.Clear();
        foreach (var kvp in metaALG)
        {
            if (kvp.Key == "START_PARAM")
            {
                metaALGData_OUT.Add(kvp.Key, out_param);
            }
        }
        DialogService.Close(new { status = 1, r_metaData = metaALGData_OUT });
    }
    private void CancelClick()
    {
        metaALGData_OUT.Clear();
        foreach (var kvp in metaALG)
        {
            metaALGData_OUT.Add(kvp.Key, null);
        }
        DialogService.Close(new { status = 0, r_metaData = metaALGData_OUT });
    }
}
