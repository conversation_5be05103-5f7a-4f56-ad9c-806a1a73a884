@inherits LayoutComponentBase
@inject IConfiguration Configuration
@inject SIBService.EncrpServHelper _diagEncService
@using System.Net.Http
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using <PERSON><PERSON>zen
@using Radzen.Blazor
@using EWA.Services
@inject NavigationManager Navigation

<PageTitle>Вход в EWA</PageTitle>

<RadzenDialog />
<RadzenNotification />
<RadzenTooltip />
<RadzenContextMenu />

<RadzenLayout style="grid-template-areas: 'rz-header rz-sidebar' 'rz-sidebar rz-body';">
    <RadzenHeader >
        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="Radzen.JustifyContent.Center">
            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween" Gap="0" style="width:1000px">

                <RadzenStack AlignItems="AlignItems.Start" JustifyContent="JustifyContent.Center" class="rz-py-4 rz-py-lg-6" >
                    <RadzenImage  Path="images/logo.png" style="width: 50%;" />
                </RadzenStack>
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center">
                    <RadzenButton Text="Шифратор" ButtonStyle="ButtonStyle.Base" Variant="Variant.Text" Icon="encrypted" Click=@EncService />
                </RadzenStack>
            </RadzenStack>
        </RadzenStack>
    </RadzenHeader>
    <RadzenBody>
        <RadzenStack JustifyContent="Radzen.JustifyContent.Center" Class="rz-mx-auto rz-p-6" Style="max-width: 600px; height: 100%; ">
            <RadzenCard class="rz-shadow-5 rz-border-radius-4" 
                        style="padding: 0; overflow: hidden; background-image: linear-gradient(to  left,   var(--rz-base-400), var(--rz-secondary-light) )">
                <RadzenColumn Size="12" SizeSM="6" class="rz-p-12 ">
                    @Body
                </RadzenColumn>

            </RadzenCard>
        </RadzenStack>
    </RadzenBody>
    <RadzenFooter Style="height: 75px">
        <RadzenStack AlignItems="Radzen.AlignItems.Center" Gap="0" Style="padding: var(--rz-panel-menu-item-padding); height: 50px">
            <RadzenText Text="@Configuration["AppInfo:Version"]" TextStyle="Radzen.Blazor.TextStyle.Caption" TagName="Radzen.Blazor.TagName.P" TextAlign="Radzen.TextAlign.Center" />
            <RadzenText Text="Copyright Ⓒ 2024 Exon IT" TextStyle="Radzen.Blazor.TextStyle.Overline" class="rz-mb-0" TagName="Radzen.Blazor.TagName.P" TextAlign="Radzen.TextAlign.End" />
        </RadzenStack>
    </RadzenFooter>

</RadzenLayout>

@code{
    private async Task EncService()
    {
        await _diagEncService.OpenEncryptDialogAsync();
    }
}