﻿using EWA.Enums;
using EWA.Models;
using static EWA.Models.REP_Models;

namespace EWA.Services
{
    public class UIService
    {
        public class TabService
        {
            public Action<string, string, string, string> OnAddTopTab { get; set; }

            public Action<TabDataPrm> OnAddBottomTab { get; set; }
            public Action<string, string, string, Models.TabData, List<REP_Models.ParamMetadata>, Dictionary<string, string>, string, string> OnAddBottomTabT { get; set; }

            public Action<IDictionary<string, object>, IDictionary<string, Rep_Param>, Models.TabData> OnChangeSelectedRow { get; set; }

            public Action<int, Models.TabData> OnChangeLoadState { get; set; }

            public void AddTopTab(string namegrid, string codeobj, string nameobj, string main_app_Code)
            {
                OnAddTopTab?.Invoke(namegrid, codeobj, nameobj, main_app_Code);
            }

            public void AddBottomTab(TabDataPrm tb)
            {
                OnAddBottomTab?.Invoke(tb);
            }

            public void AddBottomTabT(string namegrid, string codeobj, string nameobj, Models.TabData parentTab, List<REP_Models.ParamMetadata> transitionParams, Dictionary<string, string> linkedParam, string visibilityFormula, string lstpage)
            {
                OnAddBottomTabT?.Invoke(namegrid, codeobj, nameobj, parentTab, transitionParams, linkedParam, visibilityFormula, lstpage);
            }
      
            public void ChangeSelectedRow(IDictionary<string, object> rowData, IDictionary<string, Rep_Param> paramData, Models.TabData tab)
            {
                OnChangeSelectedRow?.Invoke(rowData, paramData, tab);
            }

            public void ChangeLoadState(int loadstate, Models.TabData tab)
            {
                OnChangeLoadState?.Invoke(loadstate, tab);
            }
        }
    }
}
