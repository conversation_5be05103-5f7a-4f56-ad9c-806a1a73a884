﻿@rendermode InteractiveServer
@inject EWA.Services.RepService.ClipboardService ClipboardService
@inject NavigationManager Navigation
@inject EWA.Services.WFKLService _wflService
@inject DialogService DialogService
@inject IJSRuntime JSRuntime



<div>
    Добавление задания @TaskCode.<br />
    @status
</div>


@code {
    [Parameter] public string TaskCode { get; set; }
    [Parameter] public EventCallback OnClose { get; set; }

    private string status = "Ожидайте...";

    protected override async Task OnInitializedAsync()
    {
        var vtask = await _wflService.StartWRFKL(TaskCode);
        status = vtask.is_err ? "❌ Добавлено с ошибками" : "✅ Успешно добавлено";
        await InvokeAsync(StateHasChanged);
    }
}


