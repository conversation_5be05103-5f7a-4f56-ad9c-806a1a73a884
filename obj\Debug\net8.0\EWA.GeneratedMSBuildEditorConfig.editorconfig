is_global = true
build_property.TargetFramework = net8.0
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = EWA
build_property.RootNamespace = EWA
build_property.ProjectDir = D:\Projects\Blazor\ewa_augment\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = D:\Projects\Blazor\ewa_augment
build_property._RazorSourceGeneratorDebug = 

[D:/Projects/Blazor/ewa_augment/Components/App.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xBcHAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Layout/LoginLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTG9naW5MYXlvdXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Layout/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTWFpbkxheW91dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/AUTH/Encrypt.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBVVRIXEVuY3J5cHQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/AUTH/FreeTime.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBVVRIXEZyZWVUaW1lLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/AUTH/Login.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBVVRIXExvZ2luLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/AUTH/SessionLock.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBVVRIXFNlc3Npb25Mb2NrLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/AUTH/WelcomeDialog.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBVVRIXFdlbGNvbWVEaWFsb2cucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/EWAComp/EWApassword.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFV0FDb21wXEVXQXBhc3N3b3JkLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/EXT/Clock.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFWFRcQ2xvY2sucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/EXT/ExcelReport.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFWFRcRXhjZWxSZXBvcnQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/EXT/FReport.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFWFRcRlJlcG9ydC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/GLOBAL/ColumnDetail.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xHTE9CQUxcQ29sdW1uRGV0YWlsLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/GLOBAL/EditGrid.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xHTE9CQUxcRWRpdEdyaWQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/GLOBAL/Error.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xHTE9CQUxcRXJyb3IucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/GLOBAL/ErrorGrid.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xHTE9CQUxcRXJyb3JHcmlkLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/GLOBAL/FormGrid.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xHTE9CQUxcRm9ybUdyaWQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/GLOBAL/MainTreeMenu.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xHTE9CQUxcTWFpblRyZWVNZW51LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/Index.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xJbmRleC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/MainTabs.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xNYWluVGFicy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/Repos/RepObject.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xSZXBvc1xSZXBPYmplY3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/Rules/CustomDiagr.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xSdWxlc1xDdXN0b21EaWFnci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/Rules/DiagramForm.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xSdWxlc1xEaWFncmFtRm9ybS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/Rules/MethodForm.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xSdWxlc1xNZXRob2RGb3JtLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/Rules/RuleForm.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xSdWxlc1xSdWxlRm9ybS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/Rules/SVG_V1.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xSdWxlc1xTVkdfVjEucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/Rules/Test.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xSdWxlc1xUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/SIB/SIBButtons.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTSUJcU0lCQnV0dG9ucy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/SIB/SIBDomainUserList.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTSUJcU0lCRG9tYWluVXNlckxpc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/SIB/SIBRuleAdd.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTSUJcU0lCUnVsZUFkZC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/SIB/SIBRuleEdit.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTSUJcU0lCUnVsZUVkaXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/SIB/SIBRuleForm.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTSUJcU0lCUnVsZUZvcm0ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/SIB/SIBUserAdd.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTSUJcU0lCVXNlckFkZC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/SIB/SIBUserEdit.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTSUJcU0lCVXNlckVkaXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/SIB/SIBUserPass.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTSUJcU0lCVXNlclBhc3MucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/SIB/UserCard/ChangePassword.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTSUJcVXNlckNhcmRcQ2hhbmdlUGFzc3dvcmQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/SIB/UserCard/ContactInfo.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTSUJcVXNlckNhcmRcQ29udGFjdEluZm8ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/SIB/UserCard/PersonInfo.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTSUJcVXNlckNhcmRcUGVyc29uSW5mby5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/SIB/UserCard/UserCard.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTSUJcVXNlckNhcmRcVXNlckNhcmQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/SIB/UserCard/UserRoles.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTSUJcVXNlckNhcmRcVXNlclJvbGVzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/WRFL/WFLServControl.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xXUkZMXFdGTFNlcnZDb250cm9sLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/WRFL/WRFLButtons.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xXUkZMXFdSRkxCdXR0b25zLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/WRFL/WRFL_DIAG.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xXUkZMXFdSRkxfRElBRy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/WRFL/WRFL_PARAM.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xXUkZMXFdSRkxfUEFSQU0ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/WRFL/WRFL_StatusDiag.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xXUkZMXFdSRkxfU3RhdHVzRGlhZy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Pages/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xfSW1wb3J0cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/RedirectToLogin.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xSZWRpcmVjdFRvTG9naW4ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/Routes.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xSb3V0ZXMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Projects/Blazor/ewa_augment/Components/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xfSW1wb3J0cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 
