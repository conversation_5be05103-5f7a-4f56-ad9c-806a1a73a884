@inject NavigationManager NavigationManager
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="@NavigationManager.BaseUri" />
    <RadzenTheme @rendermode="@InteractiveServer" Theme="Software" Wcag=true />
    <link rel="stylesheet" href="css/site.css" />
    <link rel="icon" href="favicon.ico" />
    <HeadOutlet @rendermode="@InteractiveServer" />
	<link href="_content/Z.Blazor.Diagrams/style.min.css" rel="stylesheet" />
    <link href="_content/Z.Blazor.Diagrams/default.styles.min.css" rel="stylesheet" />
</head>
<body>
    <Routes @rendermode="@InteractiveServer" />
    <script src="_framework/blazor.web.js"></script>
    <script src="_content/Radzen.Blazor/Radzen.Blazor.js?v=@(typeof(Radzen.Colors).Assembly.GetName().Version)"></script>
    <script src="js/downloadFile.js"></script>
    <script src="js/sess.js"></script>
    <script src="js/sess_sync.js"></script>
	<script src="_content/Z.Blazor.Diagrams/script.min.js"></script>
</body>
</html>

@code {
    [CascadingParameter]
    private HttpContext HttpContext { get; set; }

    [Inject]
    private ThemeService ThemeService { get; set; }

    protected override void OnInitialized()
    {
        base.OnInitialized();

        if (HttpContext != null)
        {
            var theme = HttpContext.Request.Cookies["EWATheme"];

            if (!string.IsNullOrEmpty(theme))
            {
                ThemeService.SetTheme(theme, false);
            }
        }
    }
}